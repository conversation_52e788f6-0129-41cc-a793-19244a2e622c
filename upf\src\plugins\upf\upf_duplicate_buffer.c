#include <vppinfra/error.h>
#include <vppinfra/hash.h>
#include <vnet/vnet.h>
#include <vnet/ip/ip.h>
#include <vnet/fib/ip4_fib.h>
#include <vnet/fib/ip6_fib.h>
#include <vnet/ethernet/ethernet.h>

#include "upf_duplicate_buffer.h"
#include <upf/upf.h>
#include <upf/upf_pfcp.h>
#include <upf/upf_5glan.h>
#include "upf_pfcp_api.h"

extern uword *g_duplicate_gtpu_Key;
// Add for 5glan multicast N19 duplicating packet by liupeng on 2022-02-22 below

void ip_udp_gtpu_duplicate_rewrite (upf_far_duplicate_t *fd, int is_ip4)
{
    if (fd == NULL) 
    {
        upf_err("The pointer fd is null.");
        return;
    }
    
    union
    {
        ip4_gtpu_header_t *h4;
        ip6_gtpu_header_t *h6;
        u8 *rw;
    } r = {.rw = 0};

    int len = is_ip4 ? sizeof *r.h4 : sizeof *r.h6;
    u32 flow_hash;

    len = len + GTPU_V1_HDR_LEN_PDU_SESSION_CONTAINER; // Include the gtpu extension hdr length

    vec_validate_aligned (r.rw, len, CLIB_CACHE_LINE_BYTES);

    udp_header_t *udp;
    gtpu_header_t *gtpu;
    /* Fixed portion of the (outer) ip header */
    if (is_ip4)
    {
        ip4_header_t *ip = &r.h4->ip4;
        udp = &r.h4->udp;
        gtpu = &r.h4->gtpu;
        ip->ip_version_and_header_length = 0x45;
        ip->ttl = 254;
        ip->protocol = IP_PROTOCOL_UDP;

        ip->dst_address = fd->outer_header_creation.ip.ip4;

        ip46_address_t *dst_ip = NULL;
        ip46_address_t *src_ip = NULL;
        dst_ip = clib_mem_alloc(sizeof(ip46_address_t));
        clib_memset(dst_ip, 0, sizeof(ip46_address_t));

        clib_memcpy(&dst_ip->ip4, &ip->dst_address, sizeof(ip46_address_t));
        hash_pair_t *hp = NULL;
        
        hp = hash_get_pair (g_duplicate_gtpu_Key, dst_ip);
        if (hp == NULL)
        {
            upf_trace ("the dst_ip is not exist.");
        }
        else
        {
            src_ip = (void *)(hp->value[0]);
            ip->src_address = src_ip->ip4;
        }

        /* we fix up the ip4 header length and checksum after-the-fact */
        ip->checksum = ip4_header_checksum (ip);
        flow_hash = ip4_compute_flow_hash (ip, IP_FLOW_HASH_DEFAULT);
    }
    else
    {
        ip6_header_t *ip = &r.h6->ip6;
        udp = &r.h6->udp;
        gtpu = &r.h6->gtpu;
        ip->ip_version_traffic_class_and_flow_label =
        clib_host_to_net_u32 (6 << 28);
        ip->hop_limit = 255;
        ip->protocol = IP_PROTOCOL_UDP;

        ip->dst_address = fd->outer_header_creation.ip.ip6;

        ip46_address_t *dst_ip = NULL;
        ip46_address_t *src_ip = NULL;
        dst_ip = clib_mem_alloc(sizeof(ip46_address_t));
        clib_memset(dst_ip, 0, sizeof(ip46_address_t));

        clib_memcpy(&dst_ip->ip6, &ip->dst_address, sizeof(ip46_address_t));
        hash_pair_t *hp = NULL;
        
        hp = hash_get_pair (g_duplicate_gtpu_Key, dst_ip);
        if (hp == NULL)
        {
            upf_trace ("the dst_ip is not exist.");
        }
        else
        {
            src_ip = (void *)(hp->value[0]);
            ip->src_address = src_ip->ip6;
        }

        flow_hash = ip6_compute_flow_hash (ip, IP_FLOW_HASH_DEFAULT);
    }

    /* UDP header, randomize src port on something, maybe? */
    udp->src_port = clib_host_to_net_u16 ((u16)flow_hash);
    udp->dst_port = clib_host_to_net_u16 (UDP_DST_PORT_GTPU);

    /* GTPU header */
    gtpu->ver_flags = GTPU_V1_VER | GTPU_PT_GTP | GTPU_E_BIT;
    gtpu->type = GTPU_TYPE_GTPU;
    gtpu->teid = clib_host_to_net_u32 (fd->outer_header_creation.teid);

    fd->rewrite = r.rw;

    _vec_len (fd->rewrite) = len;

    return;
}

int handle_far_dupli_para_policy( pfcp_forwarding_policy_t forwarding_policy, upf_far_duplicate_t *duplicating)
{
    upf_main_t *gtm = &g_upf_main;

    uword *p = NULL;
    upf_forward_policy_t *forward_policy = NULL;
    duplicating->flags |= FAR_D_FORWARDING_POLICY;
    duplicating->table_id = 0;
    duplicating->forward_policy_index = ~0;
    
    if (vec_len (forwarding_policy.identifier) != 0)
    {
        p = hash_get_mem (
            gtm->forward_policy_by_name, forwarding_policy.identifier);
        if (!p)
        {
            return -1;
        }

        if (PREDICT_TRUE(!pool_is_free_index(gtm->forward_policys, p[0])))
        {
            forward_policy = pool_elt_at_index (gtm->forward_policys, p[0]);
        
            duplicating->table_id = forward_policy->vrf;
            duplicating->forward_policy_index = p[0];
            if (0 != upf_fib_index_by_table_id(duplicating->table_id, &duplicating->fib_index))
            {
                upf_debug ("vrf(table_id) %u in not (yet) defined", duplicating->table_id);
            }
        }
    }

    return 0;
}

int handle_far_duplicating_para(upf_session_t *sx, upf_far_t *far, pfcp_create_far_t *create)
{
    if ((sx == NULL) || (far == NULL) || (create == NULL))
    {
        upf_err("the pointer far or create or failed_rule_id is null.");
        return -1;
    }

    int ret = 0;
    pfcp_duplicating_parameters_t *duplicating_parameters = NULL;
    upf_far_duplicate_t *duplicating_tmp = NULL;

    if ((far->apply_action & F_APPLY_DUPL) 
        && !(create->grp.fields & CREATE_FAR_DUPLICATING_PARAMETERS))
    {
        upf_err ("FAR: %d, FAR apply_action is DUPLICATE, no DUPLICATING_PARAMETERS included!\n", create->far_id);
        ret = -1;
        return ret;
    }

    vec_foreach (duplicating_parameters, create->duplicating_parameters)
    {
        vec_alloc (far->duplicates, 1);
        duplicating_tmp = vec_end (far->duplicates);

        if (ISSET_BIT (duplicating_parameters->grp.fields, DUPLICATING_PARAMETERS_DESTINATION_INTERFACE))
        {
            duplicating_tmp->flags |= FAR_D_DEST_INTF;
            duplicating_tmp->dst_intf = duplicating_parameters->destination_interface;            
        }
                
        if (ISSET_BIT (duplicating_parameters->grp.fields, DUPLICATING_PARAMETERS_FORWARDING_POLICY))
        {

            if (-1 == handle_far_dupli_para_policy(duplicating_parameters->forwarding_policy, duplicating_tmp))
            {
                upf_err ("FAR: %d, Parameter with unknown forwarding policy:%s\n",
                          create->far_id, (char *)duplicating_parameters->forwarding_policy.identifier);
                ret = -1;
                break;
             }
        }

        if (ISSET_BIT (duplicating_parameters->grp.fields, DUPLICATING_PARAMETERS_OUTER_HEADER_CREATION))
        {
            pfcp_outer_header_creation_t *ohc = &duplicating_parameters->outer_header_creation;
            u32 fib_index;
            int is_ip4 = !!(ohc->description & OUTER_HEADER_CREATION_ANY_IP4);

            if (ip46_address_is_zero (&ohc->ip))
            {
                upf_err ("FAR: %d, outer header creation IP is zero\n", create->far_id);
                ret = -1;
                break;
            }

            if (!is_ip4 && ip6_address_is_link_local_unicast(&ohc->ip.ip6))
            {
                upf_err ("FAR: %d, outer header creation IP is fe80\n", create->far_id);
                ret = -1;
                break;
            }

            if (upf_lookup_upf_ip_blacklist(&ohc->ip))
            {
                upf_err ("FAR: %d, outer header creation IP in upf ip blacklist\n", create->far_id);
                ret = -1;
                break;
            }

            duplicating_tmp->flags |= FAR_D_OUTER_HEADER_CREATION;
            duplicating_tmp->outer_header_creation = duplicating_parameters->outer_header_creation;

            fib_index = upf_ip46_fib_index_get_by_table_id (duplicating_tmp->table_id, is_ip4);
            if (~0 == fib_index)
            {
                upf_err ("FAR: %d, Network instance with invalid VRF for IPv%d\n",
                         create->far_id, is_ip4 ? 4 : 6);
                ret = -1;
                break;
            }

            if (ohc->description & OUTER_HEADER_CREATION_GTP_ANY)
            {
                ip_udp_gtpu_duplicate_rewrite (duplicating_tmp, is_ip4);
            }
            else  // Todo: temp not enter this branch
            {
                upf_err("it is invalid outer header creation.");
                continue;
            }
        }
        
        if (ISSET_BIT (create->forwarding_parameters.grp.fields, DUPLICATING_PARAMETERS_TRANSPORT_LEVEL_MARKING))
        {
            duplicating_tmp->flags |= FAR_D_TRANSPORT_LEVEL_MARKING;
            duplicating_tmp->transport_level_marking =
                ((duplicating_parameters->transport_level_marking & 0xFF) &
                ((duplicating_parameters->transport_level_marking >> 8) & 0xFF));
        }

        _vec_len (far->duplicates)++;
    }

    return ret;
}

int handle_far_update_duplicating_para(upf_session_t *sx, upf_far_t *far, pfcp_update_far_t *update)
{
    if ((sx == NULL) || (far == NULL) || (update == NULL))
    {
        upf_err("the pointer far or update or failed_rule_id is null.");
        return -1;
    }

    int ret = 0;
    pfcp_update_duplicating_parameters_t *duplicating_parameters = NULL;
    upf_far_duplicate_t *duplicating_tmp = NULL;
    upf_main_t *gtm = &g_upf_main;

    if ((far->apply_action & F_APPLY_DUPL) 
        && !(update->grp.fields & UPDATE_FAR_UPDATE_DUPLICATING_PARAMETERS))
    {
        upf_info ("FAR: %d, FAR apply_action is DUPLICATE, no DUPLICATING_PARAMETERS included!\n", update->far_id);
        ret = -1;
        return ret;
    }

    vec_foreach (duplicating_parameters, update->update_duplicating_parameters)
    {
        vec_alloc (far->duplicates, 1);
        duplicating_tmp = vec_end (far->duplicates);
        if (ISSET_BIT (duplicating_parameters->grp.fields, UPDATE_DUPLICATING_PARAMETERS_DESTINATION_INTERFACE))
        {
            duplicating_tmp->flags |= FAR_D_DEST_INTF;
            duplicating_tmp->dst_intf = duplicating_parameters->destination_interface;
        }

        if (ISSET_BIT (duplicating_parameters->grp.fields, UPDATE_DUPLICATING_PARAMETERS_FORWARDING_POLICY))
        {
            uword *p;
            upf_forward_policy_t *forward_policy;
            duplicating_tmp->flags |= FAR_D_FORWARDING_POLICY;

            duplicating_tmp->table_id = 0;
            duplicating_tmp->forward_policy_index = ~0;

            if (vec_len (duplicating_parameters->forwarding_policy.identifier) != 0)
            {
                p = hash_get_mem (gtm->forward_policy_by_name, duplicating_parameters->forwarding_policy.identifier);
                if (!p)
                {
                    upf_info ("FAR: %d, Update duplicate Parameter with unknown forwarding policy\n", update->far_id);
                    ret = -1;
                    break;
                }

                if (PREDICT_TRUE(!pool_is_free_index(gtm->forward_policys, p[0])))
                {
                    forward_policy = pool_elt_at_index (gtm->forward_policys, p[0]);
                    duplicating_tmp->table_id = forward_policy->vrf;
                    duplicating_tmp->forward_policy_index = p[0];
                    if (0 != upf_fib_index_by_table_id(duplicating_tmp->table_id, &duplicating_tmp->fib_index))
                    {
                        upf_info ("vrf(table_id) %d in not (yet) defined", duplicating_tmp->table_id);
                        ret = -1;
                        break;
                    }
                }
            }
        }

        if (ISSET_BIT (duplicating_parameters->grp.fields, UPDATE_DUPLICATING_PARAMETERS_OUTER_HEADER_CREATION))
        {
            pfcp_outer_header_creation_t *ohc = &duplicating_parameters->outer_header_creation;
            u32 fib_index;
            int is_ip4 = !!(ohc->description & OUTER_HEADER_CREATION_ANY_IP4);

            if (ip46_address_is_zero (&ohc->ip))
            {
                upf_warn ("FAR: %d, outer header creation IP is zero\n", update->far_id);
                ret = -1;
                break;
            }

            if (!is_ip4 && ip6_address_is_link_local_unicast(&ohc->ip.ip6))
            {
                upf_err ("FAR: %d, outer header creation IP is fe80\n", update->far_id);
                ret = -1;
                break;
            }

            if (upf_lookup_upf_ip_blacklist(&ohc->ip))
            {
                upf_err ("FAR: %d, outer header creation IP in upf ip blacklist\n", update->far_id);
                ret = -1;
                break;
            }

            duplicating_tmp->flags |= FAR_F_OUTER_HEADER_CREATION;
            duplicating_tmp->outer_header_creation = *ohc;

            fib_index = upf_ip46_fib_index_get_by_table_id (duplicating_tmp->table_id, is_ip4);
            if (~0 == fib_index)
            {
                upf_info ("FAR: %d, Network instance with invalid VRF for IPv%d\n",
                           update->far_id, is_ip4 ? 4 : 6);
                ret = -1;
                break;
            }

            if (ohc->description & OUTER_HEADER_CREATION_GTP_ANY)
            {
                ip_udp_gtpu_duplicate_rewrite (duplicating_tmp, is_ip4);
            }
            else 
            {
                upf_err("it is invalid outer header creation.");
                continue;
            }

            // ToDo: gtm->pre_far dupliates
        }
        
        if (ISSET_BIT (duplicating_parameters->grp.fields, UPDATE_DUPLICATING_PARAMETERS_TRANSPORT_LEVEL_MARKING))
        {
            duplicating_tmp->flags |= FAR_F_TRANSPORT_LEVEL_MARKING;
            duplicating_tmp->transport_level_marking = (
            (duplicating_parameters->transport_level_marking & 0xFF) &
            ((duplicating_parameters->transport_level_marking >>8) & 0xFF));
        }

    }
    return ret;
}

void upf_add_duplicate_trace(vlib_main_t *vm, vlib_node_runtime_t *node, vlib_buffer_t *b0, 
                             upf_far_t *far0, upf_pdr_t *pdr0, u32 index, upf_session_t *s0)
{
    if ((b0 == NULL) || (far0 == NULL))
    {
        upf_err("The pointer b0 or far00 is null.");
        return;
    }
    if (PREDICT_FALSE (b0->flags & VLIB_BUFFER_IS_TRACED))
    {
        upf_dup_trace_t *tr = vlib_add_trace (vm, node, b0, sizeof (*tr));
        tr->up_seid = s0->up_seid;
        tr->cp_seid = s0->cp_seid;
        tr->pdr_id = pdr0 ? pdr0->id : ~0;
        tr->far_id = far0 ? far0->id : ~0;
        tr->cached = 0;
        tr->dupl_num = index;
        clib_memcpy (tr->packet_data, vlib_buffer_get_current (b0), sizeof (tr->packet_data));
    }

    return;
}


void duplicating_buffer_for_ipv4(vlib_main_t *vm, vlib_node_runtime_t *node, vlib_buffer_t *b0, 
                                upf_far_duplicate_t *dupl, upf_far_t *far0, upf_pdr_t *pdr0, 
                                u32 is_ip4, u32 index)
{
    upf_main_t *gtm = &g_upf_main;
    //upf_peer_t *peer0 = NULL;

    flowcache_upf_encap_ip_t *data = NULL;
    u16 pkt_is_ip4;

    ip4_header_t *ip4_0 = NULL;
    udp_header_t *udp0 = NULL;
    gtpu_header_t *gtpu0 = NULL;
    gtph_pdu_session_t *pdu_sess_info_p0 = NULL;
    u16 new_l0;
    upf_session_t *s0 = NULL;
    struct rules *r0 = NULL;

    u32 const csum_flags = is_ip4 ? VNET_BUFFER_F_OFFLOAD_IP_CKSUM |
          VNET_BUFFER_F_IS_IP4 | VNET_BUFFER_F_OFFLOAD_UDP_CKSUM :
          VNET_BUFFER_F_IS_IP6 | VNET_BUFFER_F_OFFLOAD_UDP_CKSUM;

    pkt_is_ip4 = is_v4_packet (vlib_buffer_get_current(b0));
    //peer0 = pool_elt_at_index (gtm->peers, far0->forward.peer_idx);
    //next = peer0->next_dpo.dpoi_next_node;
    data = upf_flowcache_add_action (b0, FLOWCACHE_ACTION_UPF_ENCAP_IP4, sizeof (flowcache_upf_encap_ip_t));
    //data->fib_index = peer0->encap_fib_index;
    //data->adj_index = peer0->next_dpo.dpoi_index;
    data->encap_next = (is_ip4 == pkt_is_ip4) ? 0 : FT_NEXT_IP_LOOKUP;

    s0 = &gtm->sessions[upf_buffer_opaque (b0)->upf.session_index];
    r0 = upf_get_rules (s0, SX_ACTIVE);

    if (PREDICT_FALSE (dupl->outer_header_creation.description & OUTER_HEADER_CREATION_UDP_IP4))
    {
        /* Apply the rewrite string. $$$$ vnet_rewrite? */
        vlib_buffer_advance ( b0, -(word)_vec_len (dupl->rewrite));
        ip4_0 = vlib_buffer_get_current (b0);
        udp0 = (udp_header_t *)(ip4_0 + 1);
        ip4_udp_header_t *ip4_udp0 = (ip4_udp_header_t *)ip4_0;
        clib_memcpy (ip4_udp0, dupl->rewrite, sizeof (ip4_udp_header_t));
    }
    else if (PREDICT_TRUE (dupl->outer_header_creation.description & OUTER_HEADER_CREATION_GTP_IP4))
    {
        pfcp_qfi_t qos_flow_identifier = 0;
        pfcp_rqi_t reflective_qos = 0;
        u32 *qer_id0;

        r0 = upf_get_rules (s0, SX_ACTIVE);
        vec_foreach (qer_id0, pdr0->qer_ids)
        {
            upf_qer_t *qer0 = upf_get_qer_by_id (r0, *qer_id0);

            if (!qer0)
            {
                continue;
            }

            if (qer0->flags & SX_QER_REFLECTIVE_QOS)
            {
                reflective_qos = qer0->reflective_qos;
            }
            if (qer0->flags & SX_QER_QOS_FLOW_IDENTIFIER)
            {
                qos_flow_identifier = qer0->qos_flow_identifier;
                break;
            }
        }

        if (0 != qos_flow_identifier)
        {
            /* Apply the rewrite string. $$$$ vnet_rewrite? */
            vlib_buffer_advance (b0, -(word)_vec_len (dupl->rewrite));
            ip4_0 = vlib_buffer_get_current (b0);
            udp0 = (udp_header_t *)(ip4_0 + 1);
            gtpu0 = (gtpu_header_t *)(udp0 + 1);

            clib_memcpy (ip4_0, dupl->rewrite, sizeof (ip4_gtpu_header_t) + sizeof (gtph_pdu_session_t));

            // GTPU add extensin hdr
            gtpu0->next_ext_type = GTP_EX_TYPE_PDU_SESS;

            pdu_sess_info_p0 = (gtph_pdu_session_t *)(gtpu0 + 1);
            pdu_sess_info_p0->length = 1;
            pdu_sess_info_p0->spare1 = 0;
            pdu_sess_info_p0->spare2 = 0;
            pdu_sess_info_p0->next = 0;
            
            if ((BUFFER_GTP_UDP_IP4 != upf_buffer_opaque (b0)->upf.flags) 
                && (BUFFER_GTP_UDP_IP6 != upf_buffer_opaque (b0)->upf.flags))
            {
                // From Non-GTP packet
                pdu_sess_info_p0->pdu_type = UPF_DL;
            }
            else
            {
                // From GTP packet
                pdu_sess_info_p0->pdu_type = upf_buffer_opaque (b0)->upf.gtp_flags.pdu_type;
            }
            pdu_sess_info_p0->qfi = qos_flow_identifier;
            pdu_sess_info_p0->rqi = reflective_qos;
        }
        if (NULL != pdu_sess_info_p0)
        {
            data->hdr_length = sizeof (ip4_gtpu_header_t) +
            sizeof (gtph_pdu_session_t);
            ASSERT (data->hdr_length <= sizeof (data->tunnel_hdr));
            clib_memcpy (&data->tunnel_hdr, ip4_0, data->hdr_length);
        }
        else
        {
            data->hdr_length = sizeof (ip4_gtpu_header_t) - 4;
            ASSERT (data->hdr_length <= sizeof (data->tunnel_hdr));
            // Apply the rewrite string. $$$$ vnet_rewrite? 
            vlib_buffer_advance (b0, -(word)data->hdr_length);
            ip4_0 = vlib_buffer_get_current (b0);
            udp0 = (udp_header_t *)(ip4_0 + 1);
            gtpu0 = (gtpu_header_t *)(udp0 + 1);

            clib_memcpy (ip4_0, dupl->rewrite, data->hdr_length);
            gtpu0->ver_flags &= ~GTPU_E_BIT;
            clib_memcpy (&data->tunnel_hdr, ip4_0, data->hdr_length);
        }
    }
    if (NULL == ip4_0)
    {
        //next = UPF_DUPLICATE_NEXT_DROP;
        goto trace;
    }
    /* Fix the IP4 checksum and length */

    new_l0 = /* old_l0 always 0, see the rewrite setup */
    clib_host_to_net_u16 (vlib_buffer_length_in_chain (vm, b0));
    ip4_0->length = new_l0;
    if (dupl->flags & FAR_D_TRANSPORT_LEVEL_MARKING)
    {
        flowcache_transport_level_marking_t *data;
        data = upf_flowcache_add_action (b0, FLOWCACHE_ACTION_TRANSPORT_LEVEL_MARKING, sizeof (flowcache_transport_level_marking_t));
        data->is_ip4 = is_ip4;
        data->transport_level_marking = dupl->transport_level_marking;

        /* Fix the IP4 checksum and tos */

        new_l0 = dupl->transport_level_marking; /* old_l0 always 0, see the rewrite setup */
        ip4_0->tos = new_l0;

        vnet_buffer2 (b0)->qos.source = QOS_SOURCE_VLAN;
        vnet_buffer2 (b0)->qos.bits = (dupl->transport_level_marking >> 2) & 0x3F;
        b0->flags |= VNET_BUFFER_F_QOS_DATA_VALID;
    }
    
    /* Fix UDP length and set source port */
    new_l0 = clib_host_to_net_u16 (vlib_buffer_length_in_chain (vm, b0) - sizeof (*ip4_0));
    udp0->length = new_l0;

    if (NULL != gtpu0)
    {
        /* Fix GTPU length */
        new_l0 = clib_host_to_net_u16 (vlib_buffer_length_in_chain (vm, b0) - 
                                       sizeof (*ip4_0) - sizeof (*udp0) - GTPU_V1_HDR_LEN);
        gtpu0->length = new_l0;
    }
    
    /*offload UDP checksum*/
    b0->flags |= csum_flags;
    vnet_buffer (b0)->l3_hdr_offset = (u8 *)ip4_0 - b0->data;
    vnet_buffer (b0)->l4_hdr_offset = (u8 *)udp0 - b0->data;

trace:

    return;
}

void duplicating_buffer_for_ipv6(vlib_main_t *vm, vlib_node_runtime_t *node, vlib_buffer_t *b0, 
                                upf_far_duplicate_t *dupl, upf_far_t *far0, upf_pdr_t *pdr0, 
                                u32 is_ip4, u32 index)
{
    upf_main_t *gtm = &g_upf_main;
    //u32 next;
    //upf_peer_t *peer0 = NULL;

    u16 pkt_is_ip4;

    ip6_header_t *ip6_0 = NULL;
    udp_header_t *udp0 = NULL;
    gtpu_header_t *gtpu0 = NULL;
    gtph_pdu_session_t *pdu_sess_info_p0 = NULL;
    u16 new_l0;
    upf_session_t *s0 = NULL;
    struct rules *r0 = NULL;
    int bogus = 0;

    u32 const csum_flags = is_ip4 ? VNET_BUFFER_F_OFFLOAD_IP_CKSUM |
    VNET_BUFFER_F_IS_IP4 | VNET_BUFFER_F_OFFLOAD_UDP_CKSUM :
    VNET_BUFFER_F_IS_IP6 | VNET_BUFFER_F_OFFLOAD_UDP_CKSUM;

    pkt_is_ip4 = is_v4_packet (vlib_buffer_get_current(b0));
    //peer0 = pool_elt_at_index (gtm->peers, far0->forward.peer_idx);
    //next = peer0->next_dpo.dpoi_next_node;

    flowcache_upf_encap_ip_t *data = NULL;
    //data = upf_flowcache_add_action (b0, FLOWCACHE_ACTION_UPF_ENCAP_IP6, sizeof (flowcache_upf_encap_ip6_t));
    //data->fib_index = peer0->encap_fib_index;
    //data->adj_index = peer0->next_dpo.dpoi_index;
    //data->encap_next = (is_ip4 == pkt_is_ip4) ? 0 : FT_NEXT_IP_LOOKUP;
    
    s0 = &gtm->sessions[upf_buffer_opaque (b0)->upf.session_index];
    r0 = upf_get_rules (s0, SX_ACTIVE);

    if (PREDICT_FALSE (dupl->outer_header_creation.description & OUTER_HEADER_CREATION_UDP_IP6))
    {
        data->hdr_length = sizeof (ip6_udp_header_t);
        ASSERT (data->hdr_length <= sizeof (data->tunnel_hdr));
        vlib_buffer_advance (b0, -(word) (data->hdr_length));
        ip6_udp_header_t *ip6_udp0 = (ip6_udp_header_t *)vlib_buffer_get_current (b0);
        clib_memcpy (ip6_udp0, dupl->rewrite, sizeof (ip6_udp_header_t));

        /* Fix IP6 payload length */
        new_l0 = clib_host_to_net_u16 (
        vlib_buffer_length_in_chain (vm, b0) - sizeof (ip6_header_t));
        ip6_udp0->ip6.payload_length = new_l0;

        clib_memcpy (&ip6_udp0->ip6.src_address, &s0->ue_address.ip6, sizeof (ip6_address_t));

        /* Fix UDP length  and set source port */
        ip6_udp0->udp.length = new_l0;

        /*offload UDP checksum*/
        u8 *l3_0 = (u8 *)&ip6_udp0->ip6;
        b0->flags |= csum_flags;
        vnet_buffer (b0)->l3_hdr_offset = l3_0 - b0->data;
        vnet_buffer (b0)->l4_hdr_offset = (u8 *)&ip6_udp0->udp - b0->data;

        clib_memcpy (&data->tunnel_hdr, ip6_udp0, data->hdr_length);
    }
    else
    {
        pfcp_qfi_t qos_flow_identifier = 0;
        pfcp_rqi_t reflective_qos = 0;
        u32 *qer_id0;

        if (s0->pdn_type == PDN_TYPE_ETHERNET)
        {
            vlib_buffer_advance (b0, -sizeof (ethernet_header_t));
        }

        vec_foreach (qer_id0, pdr0->qer_ids)
        {
            upf_qer_t *qer0 = upf_get_qer_by_id (r0, *qer_id0);

            if (!qer0)
            {
                continue;
            }

            if (qer0->flags & SX_QER_REFLECTIVE_QOS)
            {
                reflective_qos = qer0->reflective_qos;
            }
            
            if (qer0->flags & SX_QER_QOS_FLOW_IDENTIFIER)
            {
                qos_flow_identifier = qer0->qos_flow_identifier;
                break;
            }
        }
        if (qos_flow_identifier == 0)
        {
            data->hdr_length = sizeof (ip6_gtpu_header_t) - 4;
            ASSERT (data->hdr_length <= sizeof (data->tunnel_hdr));
        }
        else
        {
            data->hdr_length = sizeof (ip6_gtpu_header_t) + sizeof (gtph_pdu_session_t);
            ASSERT (data->hdr_length <= sizeof (data->tunnel_hdr));
        }

        vlib_buffer_advance (b0, -(word) (data->hdr_length));
        ip6_0 = vlib_buffer_get_current (b0);
        clib_memcpy (ip6_0, dupl->rewrite, data->hdr_length);

        /* Fix IP6 payload length */
        new_l0 = clib_host_to_net_u16 (
        vlib_buffer_length_in_chain (vm, b0) - sizeof (*ip6_0));
        ip6_0->payload_length = new_l0;

        /* Fix UDP length  and set source port */
        udp0 = (udp_header_t *)(ip6_0 + 1);
        udp0->length = new_l0;

        /* Fix GTPU length */
        gtpu0 = (gtpu_header_t *)(udp0 + 1);
        new_l0 = clib_host_to_net_u16 (
        vlib_buffer_length_in_chain (vm, b0) - sizeof (*ip6_0) -
        sizeof (*udp0) - GTPU_V1_HDR_LEN);
        gtpu0->length = new_l0;

        if (qos_flow_identifier)
        {
            // GTPU add extensin hdr
            gtpu0->next_ext_type = GTP_EX_TYPE_PDU_SESS;

            pdu_sess_info_p0 = (gtph_pdu_session_t *)(gtpu0 + 1);
            pdu_sess_info_p0->length = 1;
            pdu_sess_info_p0->spare1 = 0;
            pdu_sess_info_p0->spare2 = 0;
            pdu_sess_info_p0->next = 0;
            
            if ((BUFFER_GTP_UDP_IP4 != upf_buffer_opaque (b0)->upf.flags) 
                && (BUFFER_GTP_UDP_IP6 != upf_buffer_opaque (b0)->upf.flags))
            {
                // From Non-GTP packet
                pdu_sess_info_p0->pdu_type = UPF_DL;
            }
            else
            {
                // From GTP packet
                pdu_sess_info_p0->pdu_type = upf_buffer_opaque (b0)->upf.gtp_flags.pdu_type;
            }
            
            pdu_sess_info_p0->qfi = qos_flow_identifier;
            pdu_sess_info_p0->rqi = reflective_qos;
        }
        else
        {
            gtpu0->ver_flags &= ~GTPU_E_BIT;
        }
        
        clib_memcpy (&data->tunnel_hdr, ip6_0, data->hdr_length);
        
        if (dupl->flags & FAR_F_TRANSPORT_LEVEL_MARKING)
        {
            flowcache_transport_level_marking_t *data;
            data = upf_flowcache_add_action (b0, FLOWCACHE_ACTION_TRANSPORT_LEVEL_MARKING, sizeof (flowcache_transport_level_marking_t));
            data->is_ip4 = is_ip4;
            data->transport_level_marking = dupl->transport_level_marking;

            ip6_set_dscp_network_order (ip6_0, (data->transport_level_marking >> 2) & 0x3F);

            vnet_buffer2 (b0)->qos.source = QOS_SOURCE_VLAN;
            vnet_buffer2 (b0)->qos.bits = (dupl->transport_level_marking >> 2) & 0x3F;
            b0->flags |= VNET_BUFFER_F_QOS_DATA_VALID;
        }

        udp0->checksum = 0;
        udp0->checksum = ip6_tcp_udp_icmp_compute_checksum (vm, b0, ip6_0, &bogus);
        b0->flags &= ~VNET_BUFFER_F_OFFLOAD_TCP_CKSUM;
        b0->flags &= ~VNET_BUFFER_F_OFFLOAD_UDP_CKSUM;
        b0->flags &= ~VNET_BUFFER_F_OFFLOAD_IP_CKSUM;

        if (udp0->checksum == 0)
        {
            udp0->checksum = 0xffff;
        }
    }

    return;
}

void upf_buffer_rewrite_ethernet (vlib_main_t * vm, vlib_buffer_t *b, ip_adjacency_t *adj)
{
    ethernet_header_t *eh;

    // make some room
    eh = vlib_buffer_push_uninit (b, sizeof (ethernet_header_t));
    memset (eh, 0, sizeof(ethernet_header_t));

    vnet_rewrite_header_t *rw = (vnet_rewrite_header_t *)&adj->rewrite_header;
    memcpy(eh, rw->data, sizeof (ethernet_header_t));

}

void process_duplicate_buffer(vlib_main_t *vm, vlib_node_runtime_t *node,  
                       u32 is_ip4, vlib_buffer_t *b, upf_pdr_t *pdr0,
                       upf_far_t *far0, u32 is_flow_cache, clib_bihash_kv_64_8_t *kv, u32 flow_idx)
{
    if ((pdr0 == NULL) || (far0 == NULL))
    {
        upf_err("The para pdr0 or far0 is null.");
        return;
    }
    u32 index = 0;
    upf_trace("Enter duplcating_buffer_for_encap.");
    upf_far_duplicate_t *dupl_tmp = NULL;
    upf_main_t *gtm = &g_upf_main;
    
    vec_foreach(dupl_tmp, far0->duplicates)
    {
        vlib_buffer_t *c0 = NULL;
        vlib_buffer_t *hb = NULL;
        u32 ci0 = 0;
        c0 = vlib_buffer_copy (vm, b);
        if (c0 == NULL)
        {            
            continue;
        }
        
        hb = c0;
        VLIB_BUFFER_TRACE_TRAJECTORY_INIT (c0);
        ci0 = vlib_get_buffer_index (vm, c0);
        
        if (is_ip4)
        {
            duplicating_buffer_for_ipv4(vm, node, c0, dupl_tmp, far0, pdr0, is_ip4, index);
        }
        else 
        {
            duplicating_buffer_for_ipv6(vm, node, c0, dupl_tmp, far0, pdr0, is_ip4, index);
        }

        index++;
        //upf_flowcache_end_learning (c0);
        vnet_buffer (c0)->sw_if_index[VLIB_TX] = 0;
               
        u32 lb_index = 0;
        if (is_ip4)
        {
            lb_index = (u32)ip4_fib_forwarding_lookup (dupl_tmp->fib_index, &dupl_tmp->outer_header_creation.ip.ip4);
        }
        else
        {
            lb_index = (u32)ip6_fib_table_fwding_lookup (dupl_tmp->fib_index, &dupl_tmp->outer_header_creation.ip.ip6);
        }

        const dpo_id_t *dpo = load_balance_get_bucket_i (load_balance_get (lb_index), 0);
        
        if(dpo->dpoi_type == DPO_LOAD_BALANCE)
        {
            dpo = load_balance_get_bucket_i (load_balance_get (dpo->dpoi_index), 0);
        }
        if (dpo->dpoi_type == DPO_ADJACENCY)
        {
            ip_adjacency_t *adj = adj_get (dpo->dpoi_index);
            vnet_buffer (c0)->sw_if_index[VLIB_TX] = adj->rewrite_header.sw_if_index;
            if (is_flow_cache)
            {
                flow_trace_t *t = vlib_add_trace (vm, node, c0, sizeof (*t));
                clib_memcpy (&t->key, &kv[0].key, sizeof (t->key));
                t->flow_idx = flow_idx;
                t->sw_if_index = vnet_buffer (c0)->sw_if_index[VLIB_RX];
                t->next_index = FT_NEXT_IP_LOOKUP;
                t->dupl_num = index;
                t->pdr_index_0 = (u32)~0;
                t->pdr_index_1 = (u32)~0;
                clib_memcpy (t->packet_data, vlib_buffer_get_current (c0),sizeof (t->packet_data));
            }
            else
            {
                upf_session_t *s0 = NULL;
                s0 = &gtm->sessions[upf_buffer_opaque (c0)->upf.session_index];
                upf_add_duplicate_trace(vm, node, c0, far0, pdr0, index, s0);
            }
            
            upf_buffer_rewrite_ethernet(vm, hb, adj);
        }
        else
        {
            upf_err("can not find dpo,drop it");
            return;
        }

        vnet_main_t *vnm = vnet_get_main ();
        vnet_hw_interface_t *hi = vnet_get_sup_hw_interface (vnm, vnet_buffer (c0)->sw_if_index[VLIB_TX]);
        vlib_frame_t *f = vlib_get_frame_to_node (vm, hi->output_node_index);
        u32 *to_next_tmp = vlib_frame_vector_args (f);
        to_next_tmp[0] = ci0;
        f->n_vectors = 1;
        vlib_put_frame_to_node (vm, hi->output_node_index, f); 
    }    
}

// Add for 5glan multicast N19 duplicating packet by liupeng on 2022-02-22 above

