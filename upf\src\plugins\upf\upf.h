/*
 * upf.c - 3GPP TS 29.244 GTP-U UP plug-in header file
 *
 * Copyright (c) 2017 Travelping GmbH
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at:
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef __included_upf_h__
#define __included_upf_h__

#include <vppinfra/lock.h>
#include <vppinfra/error.h>
#include <vppinfra/hash.h>
#include <vppinfra/bihash_8_8.h>
#include <vppinfra/bihash_16_8.h>
#include <vppinfra/bihash_24_8.h>
#include <vppinfra/dlist.h>
#include <vppinfra/time_range.h>
#include <vppinfra/tw_timer_1t_3w_1024sl_ov.h>

#include <vnet/vnet.h>
#include <vnet/flow/flow.h>
#include <vnet/ip/ip.h>
#include <vnet/l2/l2_input.h>
#include <vnet/l2/l2_output.h>
#include <vnet/l2/l2_bd.h>
#include <vnet/ethernet/ethernet.h>
#include <vnet/arp/arp_packet.h>
#include <vnet/ip/ip4_packet.h>
#include <vnet/ip/ip6_packet.h>
#include <vnet/udp/udp.h>
#include <vnet/dpo/dpo.h>
#include <vnet/dpo/drop_dpo.h>
#include <vnet/adj/adj_types.h>
#include <vnet/fib/fib_table.h>
#include <vnet/policer/policer.h>
#include <vnet/qos/qos_types.h>

#include <svm/message_queue.h>

#include <nghttp2/nghttp2.h>

#include "hiredis/async.h"
#include "hiredis/hiredis.h"

#include "rcu.h"
#include "upf_stat.h"
#include <hs/hs.h>
#include "bdt.h"
#include "upf_log.h"
#include "acl/upf_acl.h"
#include "acl/acl.h"
#include "upf_multicast_proto.h"
#include "upf_statistics.h"
//#include <vlibapi/api_helper_macros.h>

#define ip4_address_initializer \
  {                             \
    0                           \
  }
#define ip6_address_initializer \
  {                             \
    {                           \
      0                         \
    }                           \
  }

#define BUFFER_HAS_GTP_HDR (1 << 0)
#define BUFFER_HAS_UDP_HDR (1 << 1)
#define BUFFER_HAS_IP4_HDR (1 << 2)
#define BUFFER_HAS_IP6_HDR (1 << 3)

#define BUFFER_HDR_MASK                                           \
  (BUFFER_HAS_GTP_HDR | BUFFER_HAS_UDP_HDR | BUFFER_HAS_IP4_HDR | \
   BUFFER_HAS_IP6_HDR)
#define BUFFER_GTP_UDP_IP4 \
  (BUFFER_HAS_GTP_HDR | BUFFER_HAS_UDP_HDR | BUFFER_HAS_IP4_HDR)
#define BUFFER_GTP_UDP_IP6 \
  (BUFFER_HAS_GTP_HDR | BUFFER_HAS_UDP_HDR | BUFFER_HAS_IP6_HDR)
#define BUFFER_UDP_IP4 (BUFFER_HAS_UDP_HDR | BUFFER_HAS_IP4_HDR)
#define BUFFER_UDP_IP6 (BUFFER_HAS_UDP_HDR | BUFFER_HAS_IP6_HDR)

#define UPF_GTPU_TUNNEL_MTU_DEFAULT 9000

#define UPF_MAX_POOL (1 << 20)
#define UPF_MID_POOL (1 << 12)
#define HOUR_TO_SECONDS 3600

/*
 * neil.fan@20211115 modify the timer length
 * TS29281: 7.2.1  ...Echo Request shall not be sent more often than every 60 s
 *    on each path. This doesn't prevent resending an Echo Request with the same
 *     sequence number according to the T3-RESPONSE timer.
 */
#define GTPU_ECHO_REQ_INTERVAL (60)
#define GTPU_HEADER_LENGTH (36)

/* neil.fan@20220726 add to fix encap error for cloned data when the packet length is larger than 128 currently,
 * we should copy more data if changes the data behand the b->current_data, for example, encapsulate with vlan,
 * and refer to VLIB_BUFFER_CLONE_HEAD_SIZE at the same time.
 */
#define UPF_BUFFER_CLONE_HEAD_SIZE (2 * CLIB_CACHE_LINE_BYTES)

#define UPF_STRING_LEN8     8
#define UPF_STRING_LEN16    16
#define UPF_STRING_LEN32    32
#define UPF_STRING_LEN64    64
#define UPF_STRING_LEN128   128
#define UPF_STRING_LEN255   255
#define UPF_STRING_LEN1024  1024
#define UPF_STRING_LEN4096  4096

#define UPF_PFCP_PROTOCOL_PORT 8805
#define UPF_GTP_PROTOCOL_PORT 2152

#define UPF_U16_MAX_SIZE 65536
#define UPF_U8_MAX_SIZE 255

#define UPF_PDR_DEFAULT_PRECEDENCE 255
#define UPF_PDR_MAX 30

#define UPF_ETH_DEFAULT_VLAN 4097
#define UPF_L2_INVALID_IDX_START    0x80000000
#define UPF_L2_FORDW_PORT_IDX_N6    0x80000001
#define UPF_L2_DST_MAC_LEARN        0x80000002

#define UPF_L2_EXTEND_SIZE 14 //mac header(14)
#define UPF_L1_EXTEND_SIZE 20 //physical layer extra fields(Preamble(7)+SFD(1)+IPG(12))

#define UPF_VPP_CORE_MAX 32  //the maximum number of cores that vpp can use

#define UPF_PKT_DIRECTION_IN 0
#define UPF_PKT_DIRECTION_OUT 1
#define UPF_PKT_DIRECTION_ANY 2

#define UPF_INVALID_PORT_VALUE     (u16)~0
#define UPF_INVALID_PDR            ~0

#define RULE_CNT (50)
#define MAX_IP_CNT (8)
#define HOSTNAME_LEN (256)

#define MATCH 1
#define NOT_MATCH 0

#define ADD 1
#define DELETE 0

#define SWITCH_OFF 0
#define SWITCH_ON  1

#define UPF_BUF_PKTS_NUM_PER_SESSION    (8) // Add for session level default buffing packets number by fanbinfeng on 20210817

/* Default size of the alarm hash table */
#define UPF_ALARM_DEFAULT_HASH_NUM_BUCKETS (64 * 1024)
#define UPF_ALARM_DEFAULT_HASH_MEMORY_SIZE (32<<20)

/* if vppctl set upf status down, then send assosiation update request */
#define UPF_STATUS_DOWN    0
#define UPF_STATUS_UP      1

#define UPF_INTERVAL_TIME 1

#define UPF_SINGLE_TRACE_INTF_N3 BIT(0)
#define UPF_SINGLE_TRACE_INTF_N4 BIT(1)
#define UPF_SINGLE_TRACE_INTF_N6 BIT(2)
#define UPF_SINGLE_TRACE_INTF_N9 BIT(3)

#define upf_traffic_stat(statistics, packet_length, is_send)                                           \
    if (is_send)                                                                                       \
    {                                                                                                  \
        if (packet_length < 0)                                                                         \
            statistics.drop_packets++, statistics.send_packets--, statistics.send_bandwidth.packets--; \
        else                                                                                           \
            statistics.send_packets++, statistics.send_bandwidth.packets++;                            \
        statistics.send_bytes += packet_length;                                                        \
        statistics.send_bandwidth.l3_bytes += packet_length;                                           \
        statistics.send_bandwidth.l2_bytes += (packet_length+UPF_L2_EXTEND_SIZE);                      \
        statistics.send_bandwidth.l1_bytes += (packet_length+UPF_L2_EXTEND_SIZE+UPF_L1_EXTEND_SIZE);   \
    }                                                                                                  \
    else                                                                                               \
    {                                                                                                  \
        if (packet_length < 0)                                                                         \
            statistics.drop_packets++, statistics.recv_packets--, statistics.recv_bandwidth.packets--; \
        else                                                                                           \
            statistics.recv_packets++, statistics.recv_bandwidth.packets++;                            \
        statistics.recv_bytes += packet_length;                                                        \
        statistics.recv_bandwidth.l3_bytes += packet_length;                                           \
        statistics.recv_bandwidth.l2_bytes += (packet_length+UPF_L2_EXTEND_SIZE);                      \
        statistics.recv_bandwidth.l1_bytes += (packet_length+UPF_L2_EXTEND_SIZE+UPF_L1_EXTEND_SIZE);   \
    }

#define upf_traffic_stat_ipv6(statistics, packet_length, is_send)                                                         \
        if (is_send)                                                                                                      \
        {                                                                                                                 \
            if (packet_length < 0)                                                                                        \
                statistics.drop_packets_ipv6++, statistics.send_packets_ipv6--, statistics.send_bandwidth_ipv6.packets--; \
            else                                                                                                          \
                statistics.send_packets_ipv6++, statistics.send_bandwidth_ipv6.packets++;                                 \
            statistics.send_bytes_ipv6 += packet_length;                                                                  \
            statistics.send_bandwidth_ipv6.l3_bytes += packet_length;                                                     \
            statistics.send_bandwidth_ipv6.l2_bytes += (packet_length+UPF_L2_EXTEND_SIZE);                                \
            statistics.send_bandwidth_ipv6.l1_bytes += (packet_length+UPF_L2_EXTEND_SIZE+UPF_L1_EXTEND_SIZE);             \
        }                                                                                                                 \
        else                                                                                                              \
        {                                                                                                                 \
            if (packet_length < 0)                                                                                        \
                statistics.drop_packets_ipv6++, statistics.recv_packets_ipv6--, statistics.recv_bandwidth_ipv6.packets--; \
            else                                                                                                          \
                statistics.recv_packets_ipv6++, statistics.recv_bandwidth_ipv6.packets++;                                 \
            statistics.recv_bytes_ipv6 += packet_length;                                                                  \
            statistics.recv_bandwidth_ipv6.l3_bytes += packet_length;                                                     \
            statistics.recv_bandwidth_ipv6.l2_bytes += (packet_length+UPF_L2_EXTEND_SIZE);                                \
            statistics.recv_bandwidth_ipv6.l1_bytes += (packet_length+UPF_L2_EXTEND_SIZE+UPF_L1_EXTEND_SIZE);             \
        }


//auxiliary command switch, off during performance test and on during function test
#define UPF_FLOWTABLE_OLD_SWITCH BIT(0)
#define UPF_LINUX_MALLOC_SWITCH BIT(1)
#define UPF_RULE_GROUP_SWITCH BIT(2)
#define UPF_STATISTICS_SWITCH BIT(3)
#define UPF_PERFORMANCE_SWITCH BIT(4)
#define UPF_ALL_ON_SWITCH (BIT(0) | BIT(1) | BIT(2) | BIT(3) | BIT(4))
#define UPF_ALL_OFF_SWITCH 0

enum
{
  DUPLICATE_REJECT  = 0,
  DUPLICATE_REPLACE = 1,
};

#define UPF_INVALID_INDEX  ~0

#define ETH_TRAFFIC     (1)
#define IP_TRAFFIC      (0)

#define VN_MARK_NONE    0
#define VN_MARK_N19     1
#define VN_MARK_N6      2

/* To distinguish the source of ethernet traffic that will enter "flowtable-eth-input" node. */
#define ETH_MARK_N6_IN          0 /* from l2-output(i.e. N6 port in) */
#define ETH_MARK_GTP_IN         1 /* from "flowtalbe-ip4-input" or "flowtalbe-ip6-input" */
#define ETH_MARK_FAR_IN         2 /* from "upf-ethernet-process" node(i.e. from far of ue sesion) */
#define ETH_MARK_NUM            3

#define MAC_UNICAST             0
#define MAC_BROADCAST           1
#define MAC_MULTICAST           2
#define MAC_TYPE_NUM            3

#define UPF_QMP_TIME_SYNCHRONIZED 0
#define UPF_QMP_NOT_TIME_SYNCHRONIZED 1

typedef struct {
  u8 gtpu : 1,
     pdu_sess_container : 1,  /* including GTP-U Extension Header type "PDU session Container" */
     format_illegal : 1,      /* parse gtp error */
     qmp : 1,                 /* Qos Monitor Packet (indication) */
     pdu_type : 4;            /* UPF_DL, UPF_UL, */
  u8 qfi : 6,
     psa_trigger : 1,         /* PSA UPF trigger "per Qos flow per UE Qos Monitor" build QMP */
     resv3 : 1;
  u16 container_len;
} gtpu_flags_t;

/* UPF buffer opaque definition */
typedef struct
{
  struct
  {
    u16 data_offset;
    u8 src_intf : 4, is_reverse : 1, stat_flag: 1, https: 1, http : 1;
    u8 resv0 : 4, igmp : 1, mld : 1, arp : 1, ins : 1;

    u8 flags;
    u8 flowcache_flag;
    u8 resv1[2];

    u32 teid;
    u32 session_index;
    u32 pdr_index;
    gtpu_flags_t gtp_flags;
    u32 flow_index;
    u32 flowcache_index;
  } upf;
} upf_buffer_opaque_t;

STATIC_ASSERT (sizeof (upf_buffer_opaque_t) <=
                   STRUCT_SIZE_OF (vnet_buffer_opaque2_t, unused),
               "upf_buffer_opaque_t too large for vnet_buffer_opaque2_t");

#define upf_buffer_opaque(b)                      \
  ((upf_buffer_opaque_t *)((u8 *)((b)->opaque2) + \
                           STRUCT_OFFSET_OF (vnet_buffer_opaque2_t, unused)))

/**
 *		Bits
 * Octets	8	7	6	5	4	3	2	1
 * 1		          Version	PT	(*)	E	S	PN
 * 2		Message Type
 * 3		Length (1st Octet)
 * 4		Length (2nd Octet)
 * 5		Tunnel Endpoint Identifier (1st Octet)
 * 6		Tunnel Endpoint Identifier (2nd Octet)
 * 7		Tunnel Endpoint Identifier (3rd Octet)
 * 8		Tunnel Endpoint Identifier (4th Octet)
 * 9		Sequence Number (1st Octet)1) 4)
 * 10		Sequence Number (2nd Octet)1) 4)
 * 11		N-PDU Number2) 4)
 * 12		Next Extension Header Type3) 4)
 **/

typedef struct
{
  u8 ver_flags;
  u8 type;
  u16 length; /* length in octets of the payload */
  u32 teid;
  u16 sequence;
  u8 pdu_number;
  u8 next_ext_type;
} gtpu_header_t;

/* refer to TS29281 5.2.1 "The Extension Header Length filed specifies the length of
 * the particular Extension header in 4 octets units";
 */
#define GTP_EXT_4_UNIT (4)
#define GTP_EXT_LEN(ext_hdr_len) ((ext_hdr_len) * GTP_EXT_4_UNIT)
#define GTP_EXT_NEXT_TYPE_OFFSET(ext_len)  ((ext_len) - 1)  /* 1 byte(len) */
typedef struct
{
  u8 len;
  u8 data[];
} gtpu_ext_header_t;

typedef struct gtp_header_pdu_session
{            /*    Descriptions from 3GPP 29281 */
  u8 length; /* Next extension header len */
  u8 spare1 : 4, pdu_type : 4;
  u8 qfi : 6, rqi : 1, spare2 : 1;
  u8 next; /* 12 Next extension header type. Empty = 0 */
} gtph_pdu_session_t;

/* refer to TS38415 ******* "DL PDU SESSION INFORMATION (PDU Type 0)" */
typedef struct
{
  u8 spare1         : 1,
     msnp           : 1,
     snp            : 1,
     qmp            : 1,
     pdu_type       : 4;
  u8 qfi            : 6,
     rqi            : 1,
     ppp            : 1;
} dl_pdu_sess_flags_t;

/* refer to TS38415 ******* "UL PDU SESSION INFORMATION (PDU Type 1)" */
typedef struct
{
  u8 snp            : 1,
     ul_delay_ind   : 1,
     dl_delay_ind   : 1,
     qmp            : 1,
     pdu_type       : 4;
  u8 qfi            : 6,
     new_ie_flag    : 1,
     n3_n9_delay_ind: 1;
} ul_pdu_sess_flags_t;

typedef union
{
  struct
  {
    u8 resv : 4, pdu_type : 4;
    u8 qfi : 6, resv0 : 2;
  };
  dl_pdu_sess_flags_t dl_flag;
  ul_pdu_sess_flags_t ul_flag;
  u8 as_u8[2];
  u16 as_u16;
} pdu_sess_info_flags_t; /* PDU Session Information */

/* refer to TS29281 5.2.2.7 "PDU Session Container", and refer to 38415 5.5.2 */
typedef CLIB_PACKED (struct
{
  u8 ext_hdr_len;
  pdu_sess_info_flags_t flags;
  u8 data[]; /* length = ext_hdr_len * 4 - 3, including next_ext_hdr_type */
}) pdu_sess_container_ex_t;

typedef union
{
  u8 spare : 5,
     ppi   : 3;       /* for Paging Policy Presence function */
  u8 resv1;
} dl_pdu_sess_info_octet2nd_t; /* octet second */

typedef struct
{
  pdu_sess_info_flags_t flags;
  dl_pdu_sess_info_octet2nd_t octet2;
  u64 dl_sending_ts; /* ts: time stamp */
  u8  dl_qfi_seq_num[3];
  u32 dl_mbs_qfi_seq_num;
} dl_pdu_sess_info_t;

typedef union
{
  struct
  {
    u8 new_flag0    : 1,
       new_flag1    : 1,
       new_flag2    : 1,
       new_flag3    : 1,
       new_flag4    : 1,
       new_flag5    : 1,
       new_flag6    : 1,
       new_flag7    : 1;
    u8 d1_ul_pdcp_delay_result_ind : 1,
       spare3       : 7;
  };
  u8 as_u8[2];
  u16 as_u16;
} pdu_sess_info_new_flags_t;

typedef struct
{
    u32 sec;
    u32 psec; /* 232 picoseconds  */
} upf_64bit_timestamp_t;

typedef struct
{
  pdu_sess_info_flags_t flags;
  pdu_sess_info_new_flags_t new_flags;
  struct
  {
    upf_64bit_timestamp_t dl_sending_ts_repeated; /* ts: time stamp */
    upf_64bit_timestamp_t dl_received_ts;
    upf_64bit_timestamp_t ul_sending_ts;
  } snp_content;
  u32 dl_delay_result;
  u32 ul_delay_result;
  u8  ul_qfi_seq_num[3];
  u8  resv;
  u32 n3_n9_delay_result;
} ul_pdu_sess_info_t;

typedef struct
{
  pdu_sess_info_flags_t flags;
  dl_pdu_sess_info_octet2nd_t dl_octet2nd;
  u8 forw_ext_hdr : 1,
     resv : 7;
} pdu_sess_info_qos_t;  /* get flags and data from QER */

/* GTP Extension hdr types.  */
enum
{
  GTP_EX_TYPE_NO_MORE_EX_HDR = 0x00,
  GTP_EX_TYPE_RESERVED1 = 0x01,
  GTP_EX_TYPE_RESERVED2 = 0x02,
  GTP_EX_TYPE_SCI = 0x20,
  GTP_EX_TYPE_UPD_PORT = 0x40,
  GTP_EX_TYPE_PDU_SESS = 0x85
};

#define GTPU_V1_HDR_LEN 8
#define GTPU_V1_HDR_LEN_LONG 12
#define GTPU_V1_HDR_PT_E_S_LEN 4
#define GTPU_V1_HDR_LEN_PDU_SESSION_CONTAINER 4
#define GTP_PDU_SESS_CONTAINER_BASIC  4 /* basic encapsulation: 1(length) + 2(flags) + 1(next extension type) */

#define GTPU_VER_MASK (7 << 5)
#define GTPU_PT_BIT (1 << 4)
#define GTPU_E_BIT (1 << 2)
#define GTPU_S_BIT (1 << 1)
#define GTPU_PN_BIT (1 << 0)
#define GTPU_E_S_PN_BIT (7 << 0)

#define GTPU_V1_VER (1 << 5)

#define GTPU_PT_GTP (1 << 4)
#define GTPU_TYPE_ECHO_REQUEST 1
#define GTPU_TYPE_ECHO_RESPONSE 2
#define GTPU_TYPE_ERROR_IND 26
#define GTPU_TYPE_END_MARKER 254
#define GTPU_TYPE_GTPU 255

/*GTPv1 IE type defination */
#define IE_RECOVERY 14
#define IE_TEID_I 16
#define IE_PEER_GSN_ADDR 133
#define IE_EXT_HEAD_TYPE 141
#define IE_PRIVATE_EXT 255

#define IE_TV1_LEN 2
#define IE_TV4_LEN 5
#define IE_TLV_LEN 3

#define GTPv1U_MAX_LEN 200

/* *INDENT-OFF* */
typedef CLIB_PACKED (struct {
  ip4_header_t ip4; /* 20 bytes */
  udp_header_t udp; /* 8 bytes */
}) ip4_udp_header_t;
/* *INDENT-ON* */

/* *INDENT-OFF* */
typedef CLIB_PACKED (struct {
  ip6_header_t ip6; /* 40 bytes */
  udp_header_t udp; /* 8 bytes */
}) ip6_udp_header_t;
/* *INDENT-ON* */

/* *INDENT-OFF* */
typedef CLIB_PACKED (struct {
  ip4_header_t ip4;   /* 20 bytes */
  udp_header_t udp;   /* 8 bytes */
  gtpu_header_t gtpu; /* 12 bytes */
}) ip4_gtpu_header_t;
/* *INDENT-ON* */

/* *INDENT-OFF* */
typedef CLIB_PACKED (struct {
  ip6_header_t ip6;   /* 40 bytes */
  udp_header_t udp;   /* 8 bytes */
  gtpu_header_t gtpu; /* 12 bytes */
}) ip6_gtpu_header_t;
/* *INDENT-ON* */

/* Packed so that the mhash key doesn't include uninitialized pad bytes */
/* *INDENT-OFF* */
typedef CLIB_PACKED (struct {
  ip46_address_t addr;
  u32 fib_index;
  u8 src_inf;
  u16 prefix;
}) ip46_address_fib_t;
/* *INDENT-ON* */

typedef struct
{
  ip46_address_t src;
  ip46_address_t dst;
}ip46_sd_t;

/* *INDENT-OFF* */
typedef CLIB_PACKED (struct {
  /*
   * Key fields: src intf and gtpu teid on incoming gtpu packet
   * all fields in NET byte order
   */
  union
  {
    struct
    {
      u32 src_intf;
      u32 teid;
    };
    u64 as_u64;
  };
}) gtpu_intf_tunnel_key_t;
/* *INDENT-ON* */
typedef CLIB_PACKED (struct {
  u8 type;
  u8 value;
}) IeTV1_t;

typedef CLIB_PACKED (struct {
  u8 type;
  u32 value;
}) IeTV4_t;

typedef CLIB_PACKED (struct {
  u8 type;
  u16 len;
}) IeTLV_t;

typedef struct
{
  union
  {
    struct
    {
      u32 session;
      u32 gbr : 1, id : 31;
    };
    u64 as_u64;
  };
} qer_policer_key_t;

typedef union
{
  struct
  {
    qer_policer_key_t k;
    u64 v;
  };
  clib_bihash_kv_8_8_t kv;
} qer_policer_kv_t;

//begin liukang add for ethernet mac address key 2022/03/18
typedef struct
{
  union
  {
    struct
    {
      ip46_address_t ip_addr;
      u32 nwi;    /* nwi index */
      u16 c_vid;
      u16 s_vid;
    };
    u64 as_u64[3];
  };
} gtpu_mac_address_by_ip_key_t;


typedef struct
{
  union
  {
    struct
    {
      u16 unused;
      u8 mac[6];
    };
    u64 as_u64;
  };
} gtpu_mac_address_by_ip_val_t;

typedef struct
{
  union
  {
    struct
    {
      gtpu_mac_address_by_ip_key_t k;
      gtpu_mac_address_by_ip_val_t v;
    };
    clib_bihash_kv_24_8_t kv;
  };
} gtpu_mac_address_by_ip_kv_t;
//end liukang add for ethernet mac address key 2022/03/18

typedef struct
{
  union
  {
    struct
    {
      u32 dst;
      u32 teid;
      u32 fib_index;
      u32 unused;
    };
    u64 as_u64[2];
  };
} gtpu4_tunnel_key_t;

typedef union
{
  struct
  {
    u32 src_intf;
    u32 session_index;
  };
  u64 as_u64;
} gtpu4_tunnel_val_t;

typedef union
{
  struct
  {
    gtpu4_tunnel_key_t k;
    gtpu4_tunnel_val_t v;
  };
  clib_bihash_kv_16_8_t kv;
} gtpu4_tunnel_kv_t;

typedef struct
{
  union
  {
    struct
    {
      ip6_address_t dst;
      u32 teid;
      u32 fib_index;
    };
    u64 as_u64[3];
  };
} gtpu6_tunnel_key_t;

typedef union
{
  struct
  {
    u32 src_intf;
    u32 session_index;
  };
  u64 as_u64;
} gtpu6_tunnel_val_t;

typedef union
{
  struct
  {
    gtpu6_tunnel_key_t k;
    gtpu6_tunnel_val_t v;
  };
  clib_bihash_kv_24_8_t kv;
} gtpu6_tunnel_kv_t;

/* *INDENT-OFF* */
typedef CLIB_PACKED (struct {
  gtpu4_tunnel_key_t gtpu4_tunnel_key;
  u8 inf;
  u8 is_far;
  u8 choose_id;
}) upf_gtpu4_tunnel_key_t;
/* *INDENT-ON* */

/* *INDENT-OFF* */
typedef CLIB_PACKED (struct {
  /*
   * Key fields: ip src and gtpu teid on incoming gtpu packet
   * all fields in NET byte order
   */
  gtpu6_tunnel_key_t gtpu6_tunnel_key;
  u8 inf;
  u8 is_far;
  u8 choose_id;
}) upf_gtpu6_tunnel_key_t;
/* *INDENT-ON* */

#define SRC_MAC     0
#define DST_MAC     1

typedef union
{
  struct
  {
    u32 nwi_idx;    /* nwi index */
    u16 c_vid;
    u16 s_vid;
    mac_address_t mac;
    u16 pad;
  };
  u64 as_u64[2];
} upf_l2_key_t;

#define L2_FORW_DYNAMIC 0
#define L2_FORW_STATIC  1

#define L2_FORW_DISABLE 0
#define L2_FORW_ENABLE  1
typedef struct
{
  upf_l2_key_t key;

  u32 port_index;  /* PDU session index or N6 port */
  u8  type;        /* L2_FORW_DYNAMIC; L2_FORW_STATIC */
  u8  available;   /* 1:enable; 0:disable */
  u8  pad1[2];
  u32 timestamp;   /* timestamp for aging, refer to l2fib_entry_result_t */
  i32 bcast_pkts;  // unkonwn unicastcast allow boradcast pkts
} upf_l2_forw_t;

typedef struct
{
	u32 session_index;
	u32 far_id;
} upf_clear_buff_to_pfcp_t;

typedef upf_l2_key_t upf_5glan_l2_keys_t;

typedef struct
{
    u8 field;
    u8 resv[3];

    upf_l2_forw_t l2_forw;
} upf_l2_msg_t;

/*add for upf l2 broadcast*/
typedef struct
{
    ip4_address_t ip4;
} upf_broadcast_key_t;

typedef struct
{
  upf_broadcast_key_t key;

  u32 sess_index;  /* PDU session index*/
} upf_broadcast_t;

typedef struct
{
    upf_broadcast_t  broadcast;
} upf_broadcast_msg_t;

typedef union
{
  struct
  {
    u32 nwi_idx;    /* nwi index */
    u16 c_vid;
    u16 s_vid;
    mac_address_t mac;
    u8 pad[2];
  };
  u64 as_u64[2];
} upf_eth_multicast_key_t;

typedef struct
{
  upf_eth_multicast_key_t key;

  u32 *index;  /* PDU session index or N6 port */
  u32 report_index; /* add member, used for worker-->>pfcp */
  u8  type;        /* L2_FORW_DYNAMIC; L2_FORW_STATIC */
  u8  available;   /* 1:enable; 0:disable */
  u8  pad1[2];
} upf_eth_multicast_t;

/* neil.fan@20220223 add for ip-session fucntion begin */
typedef union
{
  struct
  {
    ip46_address_t dst;
    u32 table_id; /* i.e. 5glan nwi vrf, fib_index may be not initialized when used */
    u8 mask;
    u8 pad1;
    u16 pad2;
  };
  u64 as_u64[3];
} ip_key_t;

typedef union
{
  struct
  {
    u32 ip_sxs_index; /* ip-sessions index */
    u32 unused;
  };
  u64 v;
} ip_val_t;

typedef union
{
  struct
  {
    ip_key_t k;
    ip_val_t v;
  };
  clib_bihash_kv_24_8_t kv;
} ip_sx_kv_t;

typedef struct
{
  u32 sx_index; /* session index */
  u32 ip_pdrs_index;
}sx_info_t;

typedef struct
{
  /* Required for pool_get_aligned  */
  CLIB_CACHE_LINE_ALIGN_MARK (cacheline0);

  ip46_address_fib_t vrf;
  u32 table_id;

  u32 flags;
#define VN_UPDATING BIT (0)
#define VN_DELETING BIT (1)

  int active;
#define VN_ACTIVE 0
#define VN_PENDING 1

  clib_spinlock_t lock;

  struct __attribute__ ((__aligned__ (CLIB_CACHE_LINE_BYTES))) rules_vnip
  {
    sx_info_t *sx_infos;
  } rules[2]; /* neil.fan@20220301 add to support active/pending mechanism */
} upf_vnip_sessions_t; /* 5glan virtual network ip sessions */
/* neil.fan@20220223 add for ip-session fucntion end */

typedef struct
{
  ip46_address_fib_t vrf;
  u32 *pdr_idx; /* 5G VN internal PDR index */
  u32 table_id;

  sx_info_t sx_info; /* member ip_pdrs_index is upf_5glan_ip_pdrs_t self position in 5glan session rules */

#define F_IP_EMPTY  BIT (0)
  u32 ip_sxs_field;
  u32 ip_sxs_index;
}upf_5glan_ip_pdrs_t;

typedef struct
{
  u32 teid;
  ip46_address_t addr;
  u16 port;
} gtp_error_ind_t;

typedef struct
{
  ip46_address_t address;
  ip46_address_t mask_address;
  u8 mask;
  u8 pad1;
  u16 pad2;
} ipfilter_address_t;

typedef struct
{
  u16 min;
  u16 max;
} ipfilter_port_t;

typedef struct
{
  enum
  {
    IPFILTER_WILDCARD = 0,
    IPFILTER_IPV4,
    IPFILTER_IPV6,
  } type;
  enum
  {
    ACL_PERMIT,
    ACL_DENY
  } action;
  enum
  {
    ACL_OUT,
    ACL_IN
  } direction;
  u8 proto;
  ipfilter_address_t address[2];
  ipfilter_port_t port[2];
  u16 tos;
  u16 flag; /*whether SDF_FD flag exist in this sdf*/
  u32 id;   /*only for bidirectional sdf_id*/
#if 0
  u32 ethernet_filter_id;
  pfcp_ethernet_filter_properties_t ethernet_filter_properties;
  mac_address_t src_mac;
  mac_address_t dst_mac;
  mac_address_t upper_src_mac;
  mac_address_t upper_dst_mac;
  u16 mac_flag;
  u16 ethertype;
  u16 c_tag;
  u16 s_tag;
#endif
  u32 app_index;
} acl_rule_t;

typedef struct
{
    struct pfcp_group grp;
    pfcp_ethernet_filter_id_t ethernet_filter_id;
    pfcp_ethernet_filter_properties_t ethernet_filter_properties;
    pfcp_mac_address_t *mac_address;
    pfcp_ethertype_t ethertype;
    pfcp_c_tag_t c_tag;
    pfcp_s_tag_t s_tag;

    acl_rule_t *acl;
} upf_ethernet_packet_filter_t; /* refer to pfcp_ethernet_packet_filter_t */

#define IPFILTER_RULE_FIELD_SRC 0
#define IPFILTER_RULE_FIELD_DST 1

#define ACL_FROM_ANY                               \
  (ipfilter_address_t)                             \
  {                                                \
    .address.as_u64 = {(u64)0, (u64)0}, .mask = 0, \
  }

#define acl_is_from_any(ip)               \
  (((ip)->address.as_u64[0] == (u64)0) && \
   ((ip)->address.as_u64[0] == (u64)0) && ((ip)->mask == 0))

#define ACL_TO_ASSIGNED                                \
  (ipfilter_address_t)                                 \
  {                                                    \
    .address.as_u64 = {(u64)0, (u64)0}, .mask = (u8)0, \
  }

#define acl_is_to_assigned(ip)            \
  (((ip)->address.as_u64[0] == (u64)0) && \
   ((ip)->address.as_u64[0] == (u64)0) && ((ip)->mask == (u8)0))

#define INTF_ACCESS 0
#define INTF_CORE 1
#define INTF_SGI_LAN 2
#define INTF_CP 3
#define INTF_LI 4
#define INTF_NUM (INTF_LI + 1)

enum
{
  UPF_DL = 0,
  UPF_UL,
  UPF_TOTAL,
  UPF_DIRECTION_MAX
};

#define IS_DL(_pdr, _far)                   \
                                  ((_pdr)->pdi.src_intf == SRC_INTF_CORE || \
                                   (_far)->forward.dst_intf == DST_INTF_ACCESS)
#define IS_UL(_pdr, _far)                     \
                                  ((_pdr)->pdi.src_intf == SRC_INTF_ACCESS || \
                                   (_far)->forward.dst_intf == DST_INTF_CORE)

#define UPF_ACL_FIELD_SRC 0
#define UPF_ACL_FIELD_DST 1
#define UPF_ACL_UL 1
#define UPF_ACL_DL 2
#define QFI_MAX 8

/* Packet Detection Information */
typedef struct
{
  u8 src_intf;
  uword nwi;

  u32 fields;
#define F_PDI_LOCAL_F_TEID BIT (0)
#define F_PDI_NWI BIT (1)
#define F_PDI_UE_IP_ADDR BIT (2)
#define F_PDI_SDF_FILTER BIT (3)
#define F_PDI_APPLICATION_ID BIT (4)
#define F_PDI_QFI BIT (5)
#define F_PDI_FRAMED_ROUTE BIT (6)
#define F_PDI_FRAMED_IPV6_ROUTE BIT (7)
#define F_PDI_SRC_INTF_TYPE BIT (8)
#define F_PDI_ETHERNET_PACKET_FILTER BIT (9)
#define F_PDI_IP_MULTICAST_ADDRESSING_INFO BIT (10)
#define F_PDI_IP_MULTICAST_ANY_FLAG BIT (11) /* just a pdi flag indicated "ip multicast any" exist */
#define F_PDI_ETH_PDU_SESSION_INFO BIT (12)

  pfcp_f_teid_t teid;
  pfcp_ue_ip_address_t ue_addr;
  acl_rule_t *acl; // sdf filters
  u32 app_index;
  u8 app_l7_rule; /* 1: APPID L7 rule eixt, i.e. F_PFD_L7_MASK, excluding F_PFD_C_FD(ACL);  0: not exist */
  u8 qfi;
  u8 eth_pdu_flag;
  pfcp_application_id_t app_name;
  fib_prefix_t *framed_route;
  pfcp_framed_routing_t framed_routing;
  fib_prefix_t *framed_ipv6_route;
  pfcp_tgpp_interface_type_t source_interface_type;
  upf_ethernet_packet_filter_t *eth_rule;
  pfcp_ip_multicast_addressing_info_t *ip_multicast_addressing_info;
} upf_pdi_t;

#define MAX_RULE_NUM 16000
#define MAX_ONE_RULE_LEN 256
#define MAX_URL_LEN 64
#define CT_FLAG 0x80000000 // for fd + custormer in appid

/* Packet Detection Rules */
typedef struct
{
  u32 id;
  u16 precedence;
  u8  is_active; /* indicating the the predefined PDR is active or not */

  upf_pdi_t pdi;
  u8 outer_header_removal;

   /* neil.fan@******** add for vn pdr the src/dst ip(SDF filter)/mac(MAC address) is not specified, refers to 29244
    * 5.13.3A and 5.2.1A.2A, but the packet from "5G VN Internal" may be forwarded to Access or Core,
    * so we need to know the intended interface of the VN pdr to distinguish the src/dst ip/mac of the pfcp IE
    */
  u8 is_to_vn_group;
  u32 far_id;
  u32 *urr_ids;
  u32 *qer_ids;
  // char hs_pdi[MAX_ONE_RULE_LEN];
  u32 *flow_table_ids;
  u8 *activate_predefined_rules;

  pfcp_downlink_data_service_information_t downlink_data_service_information;
  pfcp_pkt_detection_carry_on_info_t pkt_detection_carry_on_info;
  pfcp_ip_multicast_addressing_info_t *ip_multicast_addressing_info;
} upf_pdr_t;

/* Forward Action Rules - Forwarding Parameters */
typedef struct
{
  u16 flags;
#define FAR_F_REDIRECT_INFORMATION BIT (0)
#define FAR_F_OUTER_HEADER_CREATION BIT (1)
#define FAR_F_TRANSPORT_LEVEL_MARKING BIT (2)
#define FAR_F_NETWORK_INSTANCE BIT (3)
#define FAR_F_FORWARDING_POLICY BIT (4)
#define FAR_F_HEADER_ENRICHMENT BIT (5)
#define FAR_F_DEST_INTF_TYPE BIT (6)
#define FAR_F_PROXYING BIT (7)

  int dst_intf;

  u32 table_id;
  u32 fib_index;

  uword nwi;
  uword forward_policy_index;

  pfcp_redirect_information_t redirect_information;
  pfcp_outer_header_creation_t outer_header_creation;
  pfcp_header_enrichment_t *header_enrichment;
  pfcp_proxying_t proxying;

  u8 transport_level_marking;
  pfcp_tgpp_interface_type_t dest_interface_type;

  u32 peer_idx;
  u8 *rewrite;
} upf_far_forward_t;

// Add for 5glan multicast N19 duplicating packet by liupeng on 2022-02-22 below
/* Far Action Rules - Duplicating Parameters */
typedef struct
{
    u16 flags;
#define FAR_D_DEST_INTF          BIT (0)
#define FAR_D_OUTER_HEADER_CREATION   BIT (1)
#define FAR_D_TRANSPORT_LEVEL_MARKING BIT (2)
#define FAR_D_FORWARDING_POLICY       BIT (3)

    int dst_intf;
    u32 table_id;
    u32 fib_index;

    pfcp_outer_header_creation_t outer_header_creation;
    u8 transport_level_marking;
    uword forward_policy_index;
    u8 *rewrite;

} upf_far_duplicate_t;
// Add for 5glan multicast N19 duplicating packet by liupeng on 2022-02-22 above


#define FAR_BUFFERING_MAX 7 // /128

/* Forward Action Rules */
typedef struct
{
  u32 id;
  u16 apply_action;
  upf_far_forward_t forward;
  upf_far_duplicate_t *duplicates;
  u32 bar_id;

  u32 uplink_buf_n;
  u32 downlink_buf_n;
  u32 *buffer_bi[UPF_DIRECTION_MAX];
  u8 Redirect; /* Add by wangjunjie02 for redirect on 20210524 */
} upf_far_t;

/* Counter */

#define URR_OK 0
#define URR_QUOTA_EXHAUSTED BIT (0)
#define URR_THRESHOLD_REACHED BIT (1)
#define URR_START_TRAFFIC BIT (2)

/* TODO: measure if more optimize cache line aware layout
 *       of the counters and quotas has any performance impcat */
typedef struct
{
  u64 ul;
  u64 dl;
  u64 total;
  u64 fields; /* refer to PFCP_VOLUME_TOVOL ... */
} urr_counter_t;

typedef struct
{
  urr_counter_t packets;
  urr_counter_t bytes;
  urr_counter_t consumed;
} urr_measure_t;

typedef struct
{
  urr_measure_t measure;
  urr_counter_t threshold;
  urr_counter_t quota;
} urr_volume_t;

typedef struct
{
  f64 base;
  u32 period; /* relative duration in seconds */
  u32 handle;
  u32 total_time;
  u32 consumed;
} urr_time_t;

typedef struct
{
  f64 base;
  f64 period; /* relative duration in seconds - changed from u32 to f64 for more precise timing */
  u32 handle;

  // add last heartbeat reception timestamp, using f64 cause it is returned by unix_time_now()
  // added by caozhongwei 2025-07-11
  f64 last_received;
} pfcp_time_t;

typedef struct
{
  f64 base;
  u32 period; /* relative duration in ticks */
  u32 handle;
} oam_time_t;


typedef struct
{
  CLIB_CACHE_LINE_ALIGN_MARK (cacheline0);

  f64 first_seen;
  f64 last_seen;

  /* neil.fan@20220803 add pdr_index here temporarily to support APP ID report within the stop traffic,
   * it should be removed when the implement mechanism of the urr start/stop traffic report changed, the pdr_index
   * should be as parameter for the timer that started by the urr start traffic report.
   */
  /* neil.fan@20230103 modify: replace pdr-index by pdr-id */
  u32 pdr_id;
} upf_urr_traffic_t;

typedef struct
{
  u8 type;
  u8 has_reported:1,
     is_active:1,
     has_pdr:1,
     resv:5;
  u16 pdr_id;
}upf_urr_traf_inactive_detect_t;

/* Usage Reporting Rules */
typedef struct
{
  clib_spinlock_t lock;

  u32 id;
  u32 far_id;

  u16 methods;
#define SX_URR_TIME BIT (0)
#define SX_URR_VOLUME BIT (1)
#define SX_URR_EVENT BIT (2)

  u16 triggers;

  u64 status;
#define URR_OVER_QUOTA BIT (0)
#define URR_REACHED_THRESHOLD BIT (1)
#define URR_AFTER_MONITORING_TIME BIT (2)
#define URR_ACTIVE_TIME BIT (3)
#define URR_TRAFFIC BIT (4)

  u16 update_flags;
#define SX_URR_UPDATE_TIME_QUOTA BIT (0)
#define SX_URR_UPDATE_TIME_THRESHOLD BIT (1)
#define SX_URR_UPDATE_MONITORING_TIME BIT (2)
#define SX_URR_UPDATE_MEASUREMENT_PERIOD BIT (3)
#define SX_URR_UPDATE_SUB_VOL_THRESHOLD BIT (4)
#define SX_URR_UPDATE_SUB_VOL_QUOTA BIT (5)
#define SX_URR_UPDATE_SUB_TIME_THRESHOLD BIT (6)
#define SX_URR_UPDATE_SUB_TIME_QUOTA BIT (7)
#define SX_URR_UPDATE_QUOTA_HOLDING_TIME BIT (8)
#define SX_URR_UPDATE_IDT BIT (9)
#define SX_URR_UPDATE_STOP_TRAFFIC BIT (10)
#define SX_URR_UPDATE_VOL_QUOTA BIT (11)
#define SX_URR_UPDATE_VOL_THRESHOLD BIT (12)
#define SX_URR_UPDATE_ETH_INACT_TIMER BIT(13)
#define SX_URR_UPDATE_TRAFFIC_INACT_TIMER BIT(14)

  u32 seq_no;
  f64 start_time;

  urr_volume_t volume;
  urr_volume_t sub_volume;

  urr_time_t measurement_period; /* relative duration in seconds */
  urr_time_t time_threshold;     /* relative duration in seconds */
  urr_time_t time_quota;         /* relative duration in seconds */
  urr_time_t sub_time_threshold; /* relative duration in seconds */
  urr_time_t sub_time_quota;     /* relative duration in seconds */
  urr_time_t quota_holding_time; /* relative duration in seconds */
  urr_time_t monitoring_time; /* absolute UTC ts since 1900-01-01 00:00:00 */
  urr_time_t idt; /*based by time_quota,time_threshold,quota_holding_time*/
  urr_time_t stop_of_traffic;
  urr_time_t mac_detect;
  urr_time_t traf_inact_detect_timer;

  struct
  {
    f64 start_time;
    urr_measure_t volume;
    upf_urr_traffic_t traffic;
  } usage_before_monitoring_time;

  upf_urr_traffic_t traffic;
  u32 *linked_urr_ids; /* Andy linker urr id array */
  u8 measurement_information;
  u32 reported_times;

  upf_urr_traf_inactive_detect_t traf_inact_detect;
} upf_urr_t;

/* QoS Enforcement Rules */
typedef struct
{
  /* Required for pool_get_aligned  */
  CLIB_CACHE_LINE_ALIGN_MARK (cacheline0);

  policer_read_response_type_st policer[UPF_DIRECTION_MAX];

  u64 ref_cnt;
  pfcp_mbr_t mbr;
} upf_qer_policer_t;

/* DNN statistic Rules */
typedef struct
{
  /* Required for pool_get_aligned  */
  CLIB_CACHE_LINE_ALIGN_MARK (cacheline0);

  policer_read_response_type_st policer[UPF_DIRECTION_MAX];

  u32 ref_cnt;
} upf_dnn_policer_t;

/* S-NSSAI statistic Rules */
typedef struct
{
  /* Required for pool_get_aligned  */
  CLIB_CACHE_LINE_ALIGN_MARK (cacheline0);

  policer_read_response_type_st policer[UPF_DIRECTION_MAX];

  u32 ref_cnt;
} upf_s_nssai_policer_t;

/* flow overload protect Rules */
typedef struct
{
  /* Required for pool_get_aligned  */
  CLIB_CACHE_LINE_ALIGN_MARK (cacheline0);

  policer_read_response_type_st policer[UPF_DIRECTION_MAX];

  u32 ref_cnt;
} upf_flow_policer_t;

typedef struct
{
  clib_spinlock_t lock;

  u32 id;

  u8 flags;
#define SX_QER_MBR BIT (0)
#define SX_QER_DL_FLOW_LEVEL_MARKING BIT (1)
#define SX_QER_QOS_FLOW_IDENTIFIER BIT (2)
#define SX_QER_REFLECTIVE_QOS BIT (3)
#define SX_QER_GBR BIT (4)
#define SX_QER_AVERAGING_WINDOW BIT (5)
#define SX_QER_PACKET_RATE BIT (6)    /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 */

  u8 gate_status[UPF_DIRECTION_MAX];

  pfcp_mbr_t mbr;
  pfcp_gbr_t gbr;
  qer_policer_kv_t mbr_policer;
  u32 pol_mbr[UPF_DIRECTION_MAX];
  qer_policer_kv_t gbr_policer;

  pfcp_dl_flow_level_marking_t dl_flow_level_marking;
  pfcp_qfi_t qos_flow_identifier;
  pfcp_rqi_t reflective_qos;
  u32 averaging_window;
  /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 below */
  pfcp_packet_rate_t packet_rate;
  u16 ul_pkts;
  u16 dl_pkts;
  u16 a_ul_pkts;
  u16 a_dl_pkts;
  /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 above */
} upf_qer_t;

typedef struct
{
  u16 id;
  f64 dl_buffering_start_time;
  pfcp_downlink_data_notification_delay_t downlink_data_notification_delay;
  u32 dl_buffering_duration;
  pfcp_dl_buffering_suggested_packet_count_t
      dl_buffering_suggested_packet_count;
  pfcp_suggested_buffering_packets_count_t suggested_buffering_packets_count;
} upf_bar_t;

/*
 * trigger   : |---------------------------------------------------------------------------------------|-----
 * dummy_gtp : |------|(if no DL packet, send dummy GTP with QMP to GNB)
 * check     : |---------------|(if no UL packet with QMP, send failure report to SMF)
 */
typedef struct
{
    u8 measuring;
    u8 send_cnt;
    u8 recv_cnt;
    u8 is_report;
    u32 start_time;
    urr_time_t tx_trigger; /* periodic timer, relative duration in seconds */
    urr_time_t dummy_gtp; /* periodic timer, relative duration in milliseconds */
    urr_time_t fail_report; /* periodic timer, relative duration in milliseconds */
    u32 wait_timestamp; /* to fulfill "pfcp_minimum_wait_time_t" */
} upf_monitor_send_timer_t;

typedef struct
{
    pfcp_qfi_t qfi;

    u8 qmp_trigger_flag;
#define QMP_TRIG_PERIOD  BIT (0)
#define QMP_TRIG_DEFAULT BIT (1)

    pfcp_qos_monitoring_measurement_t last_measurement; /* record last result for "release report" */
    u32 last_measure_start_time;
    upf_monitor_send_timer_t periodic_report;
    upf_monitor_send_timer_t default_report; /* event report, release report, etc. */
} upf_per_qos_monitor_flow_control_t;

typedef struct
{
    u32 fields;

    pfcp_requested_qos_monitoring_t requested_qos_monitor;
    pfcp_reporting_frequency_t reporting_frequency;
    pfcp_packet_delay_thresholds_t packet_delay_threshold;
    pfcp_minimum_wait_time_t minimum_wait_time;
    pfcp_measurement_period_t measurement_period;

    upf_per_qos_monitor_flow_control_t *flow_ctrl;
} upf_per_qos_flow_control_t;

typedef struct
{
    u8 id;
    upf_per_qos_flow_control_t *per_qos_flow_ctrl;
} upf_srr_t;

typedef struct
{
  u16 id;
  acl_rule_t bidirectional_sdf;
} upf_sdf_t;

typedef struct
{
  u32 id;
  upf_ethernet_packet_filter_t bidirectional_eth;
} upf_eth_t;

typedef struct
{
    ip46_type_t iptype;
    ip46_address_t ueip;
} ipremote_address_t;

typedef struct
{
    u64 seid;
    ip46_address_t address;
}upf_fseid_t;

typedef struct
{
    upf_fseid_t up;
    upf_fseid_t cp;
}session_id_t;

typedef struct
{
  u16 start;
  u16 end;
}pdr_edge_t;

typedef union
{
  struct
  {
    u16 s_vlan;
    u16 c_vlan;
    u32 nwi_idx;
  };
  u64 key;
}upf_eth_broadcast_key_u;

typedef struct
{
  upf_eth_broadcast_key_u key;
  u32 sess_idx;
} upf_eth_broadcast_mem_report_t;

typedef struct
{
  upf_eth_broadcast_key_u key;
  u32 *sess_idxs;
} upf_eth_broadcast_domain_t;

typedef struct
{
    mac_address_t mac;
    u16 c_tag;
    u16 s_tag;
}upf_mac_info_t;

typedef struct
{
    upf_mac_info_t mac_info;

    u8 detect_is_reported;
    u8 is_active;
    u16 pdr_index;
}upf_mac_addr_detect_t;

typedef struct
{
    u32 type;
    u32 instance_id;
}upf_session_tunnel_pair_t;

typedef struct
{
  u64 ul;
  u64 dl;
  u64 total;
} upf_counter_t;

typedef struct
{
  clib_spinlock_t lock;
  u32 lock_flag;

  upf_counter_t mbr;
  upf_flow_policer_t flow_policer;
  u32 ref_cnt;
  u32 state;
} upf_dos_syn_t;

typedef struct
{
  u32 flags:8, seq:24;
  u32 vni_reserved;
} dssr_vxlan_header_t;

typedef struct
{
    ip4_header_t ip4;
    udp_header_t udp;
    dssr_vxlan_header_t vxlan;
} dssr_ip4_udp_vxaln_header_t;

typedef struct
{
    ip6_header_t ip6;
    udp_header_t udp;
    dssr_vxlan_header_t vxlan;
} dssr_ip6_udp_vxaln_header_t;

typedef struct
{
  /* Required for pool_get_aligned  */
  CLIB_CACHE_LINE_ALIGN_MARK (cacheline0);

  i32 fib_index;
  u32 thread_index;
  ip46_address_t up_address;
  u64 up_seid;
  u64 cp_seid;
  ip46_address_t cp_address;
  ip46_address_t ue_address;

  struct
  {
    u32 node;
    u32 next;
    u32 prev;
  } assoc;

  u32 flags;
#define SX_UPDATING     BIT (0)
#define SX_DELETING     BIT (1)
#define SX_USER_ID      BIT (2)
#define SX_UEIP_SET     BIT (3)
#define SX_NWI_DEFAULT  BIT (4)
#define SX_TUNNEL_ID    BIT (5)
#define SX_L2TP_TUNNEL_INFO    BIT (6)
#define SX_L2TP_SESSION_INFO    BIT (7)

  clib_spinlock_t lock;
  int active;

  struct __attribute__ ((__aligned__ (CLIB_CACHE_LINE_BYTES))) rules
  {
    u64 flags;
#define SX_SDF_IPV4 BIT (0)
#define SX_SDF_IPV6 BIT (1)
#define SX_APP BIT (2)

    /* vector of Packet Detection Rules */
    upf_pdr_t *pdr;
    pdr_edge_t pdr_edge[SRC_INTF_NUM];
    u32 padding; /* alignment */
    upf_5glan_ip_pdrs_t *ip4_pdrs;
    upf_5glan_ip_pdrs_t *ip6_pdrs;

    upf_far_t *far;
    upf_urr_t *urr;
    upf_qer_t *qer;
    upf_bar_t *bar;
    upf_srr_t *srr;
    upf_sdf_t *sdf; /*bidirectional sdf*/
    upf_eth_t *eth;    /* bidirectional ethernet packet filter */
    upf_5glan_l2_keys_t *l2_keys;

    ip46_address_fib_t *vrf_ip;
    upf_gtpu4_tunnel_key_t *local_v4_teid; /* pdr only, local teid */
    upf_gtpu6_tunnel_key_t *local_v6_teid; /* pdr only, local teid */
    upf_gtpu4_tunnel_key_t *peer_v4_teid;  /* far only, peer teid */
    upf_gtpu6_tunnel_key_t *peer_v6_teid;  /* far only, peer teid */

    u32 *send_end_marker;
    //hs_database_t *database;
    //hs_scratch_t *scratch;
    // upf_bdtree_t *bdt;
    // upf_bdtree_t *bdt6;
    struct upf_acl_ctx *acl;
    struct upf_acl_ctx *acl6;
  } rules[2];
#define SX_ACTIVE 0
#define SX_PENDING 1

  u32 *msgs_id;
  struct
  {
    u64 status;
#define DATA_ACTIVE BIT (0)
    u16 counter;
    u32 period;
    pfcp_time_t timer;
  } up_inactive_timer;

  f64 unix_time_start;
  u64 create_session_time;
  u64 update_session_time;
  u64 session_report_time;
  uword *hash_teid_by_chooseid;
  u8 *dnn;
  uword *hash_upip4_by_pool;
  uword *hash_upip6_by_pool;
  uword *hash_pdr_id_by_flow_id[2];

  pfcp_user_id_t user_id;
  u8 pdn_type;
  pfcp_node_id_t node_id;
  u32 pfcp_seq_no; /* neil.fan@20211216 add for identifying retry msg */

  u16 single_trace_list[UPF_STRING_LEN8];  // Modify for taskid uint16 by liupeng on 2021-06-09
  u32 single_trace_flag;
  u32 s_nssai;

  //pfcp_cmcc_l2tp_user_information_t l2tp_user_information;
  //pfcp_cmcc_l2tp_tunnel_information_t l2tp_tunnel_information;
  pfcp_cmcc_user_location_information_t user_location_information;
  pfcp_cmcc_rat_type_t rat_type; // Add for http/https headincrement by liupeng 2021-09-30

  /* 5G VN group */
  u8 eth_pdu_flag; /* ethernet pdu session information flag */
  u8 resv[2];
  u32 table_id;
  u8 *vn_nwi_name;
  uword vn_nwi_index;
  u32 *ip_sessions_idx;
  ip46_address_t *multicast_group_addr;

  /* ethernet traffic */
  upf_eth_broadcast_key_u *key_list;                  /* eth broadcast domain members */
  upf_l2_key_t *key_l2_list;                          /* eth l2 forward table members */
  upf_l2_key_t *key_vn_l2_list;
  upf_eth_multicast_key_t *key_multicast_list;        /* eth multicast table members */
  uword eth_n6_nwi;
  
  upf_broadcast_key_t *broadcast_ip_list;

  upf_mac_addr_detect_t *mac_address_tag_vec;
  upf_session_tunnel_pair_t tunnel;
  pfcp_l2tp_session_information_t upf_l2tp_session_information;  //liukang add for l2tp 2022/08/08
  pfcp_l2tp_tunnel_information_t upf_l2tp_tunnel_information;    //liukang add for l2tp 2022/08/08

  upf_dos_syn_t dos_syn;

  //dssr vxlan info
  u32 dssr_vni;
  dssr_ip4_udp_vxaln_header_t dssr_ip4_header;
  dssr_ip6_udp_vxaln_header_t dssr_ip6_header;

  // Add for 5G TSN function by liupeng on 2024-02-21 below
  mac_address_t ue_mac;
  // Add for 5G TSN function by liupeng on 2024-02-21 above

  u8 frer_dd_is_reverse;
  uword *bitmap_frer_dd[2];

  //added by libingxing for per ue stat on 2024-11-05
  u64 pdu_sess_statictis[UPF_END];
  upf_pdu_sess_stat_t pdu_sess_stat_t;
  bool handover_ongoing;
} upf_session_t;
typedef int (*sx_id_cmp) (const void *p1, const void *p2);

#define UPF_PDU_SESS_STATISTICS_ADD(sess,stat_type)  \
    if(sess)                                         \
        sess->pdu_sess_statictis[stat_type]++;       \

typedef struct
{
    u16 valid;
    mac_address_t mac;
    ip46_address_t m_addr;
    u32 *sx_indexs;
} upf_multicast_tree_t;

typedef struct
{
  u32 packets;
  u32 bytes;
} upf_5glan_traffic_stat_t;

typedef struct
{
  upf_5glan_traffic_stat_t stat;
} upf_5glan_per_cpu_t;

typedef struct
{
  u8 *name;
  u32 vrf;
  u32 ip4_fib_index;
  u32 ip6_fib_index;
  u32 n6_grp_sx_index; /* N6 group level session index */
  u32 *sx_indexs;
  upf_multicast_tree_t *multicast_tree;
  upf_5glan_per_cpu_t *per_cpu;
} upf_5glan_nwi_t;

#define vec_pdr_foreach(var, edge, vec) \
        for ((var) = (vec) + (edge)->start; (var) < (vec) + (edge)->end; (var)++)
#define has_edge_pdr(edge)  ((edge)->start < (edge)->end)

/* foreach outer interface pdr, including source interface access/core/sgi-lan/cp-functon */
#define vec_outer_pdr_foreach(var, arr_edge, vec) \
    for ((var) = (vec) + (arr_edge)[SRC_INTF_ACCESS].start; (var) < (vec) + (arr_edge)[SRC_INTF_CP].end; (var)++)

typedef enum
{
#define gtpu_error(n, s) GTPU_ERROR_##n,
#include <upf/gtpu_error.def>
#undef gtpu_error
  GTPU_N_ERROR,
} gtpu_input_error_t;

typedef struct
{
  /* Required for pool_get_aligned  */
  CLIB_CACHE_LINE_ALIGN_MARK (cacheline0);

  uword ref_cnt;

  fib_forward_chain_type_t forw_type;
  u32 encap_index;

  /* The FIB index for src/dst addresses (vrf) */
  u32 encap_fib_index;

  /* FIB DPO for IP forwarding of gtpu encap packet */
  dpo_id_t next_dpo;

  /**
   * Linkage into the FIB object graph
   */
  fib_node_t node;

  /* The FIB entry for sending unicast gtpu encap packets */
  fib_node_index_t fib_entry_index;

  /**
   * The tunnel is a child of the FIB entry for its destination. This is
   * so it receives updates when the forwarding information for that entry
   * changes.
   * The tunnels sibling index on the FIB entry's dependency list.
   */
  u32 sibling_index;

  pfcp_time_t timer;
  i32 n1;
  u32 t1;
  pfcp_time_t retry_timer;

  ip46_address_t ohc_ip;
  u8 *fwd_rewrite;
  u16 seq;
  u8 path_failure;
  u32 alarm_state;

  u32 qos_monitor_delay; /* ms */
  f64 qos_monitor_t0;
} upf_peer_t;

typedef struct
{
  ip46_address_fib_t key;
} upf_pfcp_endpoint_t;


#define MAX_PDU_SESS_NUM_PER_UE 16
typedef struct
{
  u8 imsi[16];
  u64 ue_create_time;
  u64 ue_delete_time;
  u64 up_seid_list[MAX_PDU_SESS_NUM_PER_UE];
  i16 active_pdu_sess_num;
  upf_traffic_stat_t n3_statistics;
  upf_traffic_stat_t n6_statistics;
} upf_ue_stat_t;

typedef struct
{
  u8 imsi[16];
} upf_ue_imsi_t;

#define UPF_GTP_ENDPOINT_N3             BIT (0)
#define UPF_GTP_ENDPOINT_N4             BIT (1)
#define UPF_GTP_ENDPOINT_N9A            BIT (2)
#define UPF_GTP_ENDPOINT_N9C            BIT (3)
#define UPF_GTP_ENDPOINT_N19            BIT (4)

typedef struct
{
  ip6_address_t ip6;
  ip4_address_t ip4;
  u32 nwi;
  u8 intf;
  u8 endpoint_type; /* N3/N4/N9a/N9c/N9/N19 */
  u16 resv;

  u32 teidri;
  u64 teid_range_acct;
  u64 teid_range_status_low; // every bit indicate a TEID range status:1,used 0
                             // not used. From 0 to 63
  u64 teid_range_status_high; // every bit indicate a TEID range status:1,used
                              // 0 not used. From 64 to 127
} upf_upip_res_t;

typedef struct
{
  urr_measure_t measure;
  urr_counter_t threshold;
  urr_counter_t mbr;        //max bandwidth rate
} nwi_volume_t;

typedef struct
{
  u8 *rule;
} nwi_rule_t;

typedef struct
{
  /* Required for pool_get_aligned  */
  CLIB_CACHE_LINE_ALIGN_MARK (cacheline0);

  policer_read_response_type_st policer[UPF_DIRECTION_MAX];

  u32 ref_cnt;
} upf_nwi_policer_t;

typedef struct
{
  u8 *name;
  u32 vrf;
  u32 ul_table_id;
  u32 dl_table_id;
  nwi_volume_t volume;
  nwi_rule_t *rule_id;
  upf_nwi_policer_t nwi_policer;
  u32 ref_cnt;
  u32 sw_if_idx;
  u8 is_vxlan4; // vxlan4 or vxlan6
  u8 vxlan_idx; // vxlan
  u32 s_nssai;
  u32 vlan_id;
} upf_nwi_t;

typedef struct
{
  u8 *name;
  u32 dpi_id;
} upf_dpi_t;

typedef struct
{
  u8 *name;
  u32 vrf;
} upf_forward_policy_t;

typedef struct
{
  ip4_address_t ip4;
  ip6_address_t ip6;
} upf_blacklist_res_t;

typedef struct
{
  clib_spinlock_t lock;
  u32 fib_index;
  uword *bitmap_ue_ip4_pool;
  uword *bitmap_ue_ip6_pool;
  ip4_address_t ip4_low;
  u32 size_v4;
  ip6_address_t ip6_low;
  u32 size_v6;
  u8 is_v4;
  u8 is_v6;
  u8 *pool_id;
  u8 *nwi;
} upf_ue_ip_pool_t;
typedef struct
{
  u8 is_v4;
  ip4_address_t ip4_low;
  u32 size_v4;
  u8 is_v6;
  ip6_address_t ip6_low;
  u32 size_v6;
} upf_ue_ip_pool_info_t;

typedef struct
{
  u8 name[30];
} upf_report_omc_node_t;

typedef struct
{
  /* Required for pool_get_aligned  */
  CLIB_CACHE_LINE_ALIGN_MARK (cacheline0);

  clib_spinlock_t lock;

  pfcp_node_id_t node_id;
  pfcp_recovery_time_stamp_t recovery_time_stamp;    /* CP recover time */
  pfcp_recovery_time_stamp_t up_recovery_time_stamp; /* UP recover time */
  pfcp_cp_function_features_t cp_feature;

  u32 fib_index;
  u32 release_ref;
  ip46_address_t rmt_addr;
  ip46_address_t lcl_addr;

  u32 sessions;
  pfcp_time_t HB_timer;
  pfcp_user_plane_ip_resource_information_t
      *user_plane_ip_resource_information;
} upf_node_assoc_t;

typedef u8 *regex_t;

typedef struct
{
  u32 id; /* bit 31 == 1 indicates PFD from CP */
  regex_t host;
  regex_t path;
} upf_adr_t;

typedef struct
{
  u8 *name;
  uword *rules_by_id; /* hash over rules id */
  upf_adr_t *rules;   /* vector of rules definition */
  u32 db_index;       /* index in ADR pool */
} upf_adf_app_t;

typedef struct
{
  regex_t host;
  regex_t path;
  ip46_address_t src_ip;
  ip46_address_t dst_ip;
} upf_rule_args_t;

#define UPF_MAPPING_BUCKETS 1024
#define UPF_MAPPING_MEMORY_SIZE 64 << 21

// struct configurations for handling N4 link anomalies begin
// added by caozhongwei 2025-07-11
typedef struct
{
  u8 enabled;                // whether to enable session persistence during N4 link anomalies
  u32 persist_time;          // session persistence duration during N4 link anomalies (seconds)
  u8 link_status;            // N4 link status: 1-normal, 0-anomaly
  f64 timeout_timestamp;     // session timeout timestamp during N4 link anomalies
  u32 missed_heartbeats;     // consecutive heartbeat loss count
  u32 max_missed_heartbeats; // maximum consecutive heartbeat loss count for link anomaly determination
} upf_n4_persist_t;
//struct configurations for handling N4 link anomalies end 

#define UPF_MEM_HASH_SIZE 10 * 1024

typedef struct upf_pfd_ctx
{
  u8 flags;
  acl_rule_t fd_filter;
  u8 *url;
  u8 *dn;
  u8 *ct;
  char hs_fd[MAX_ONE_RULE_LEN];
} upf_pfd_ctx_t;

typedef struct upf_app_id_pfd_ctx
{
  u8 *app_name;
  u16 precedence;
  u8 dns_sniffer_switch;
  upf_pfd_ctx_t *pfd_contents;
} upf_app_id_pfd_ctx_t;

typedef struct
{
  void *fp;
  void *arg;
} pfcp_rpc_t;

typedef struct
{
  u32 *session_indices;
  u32 *peer_indices;
  u32 *policer_indices;
  u32 *msg_indices;
  u32 *vnip_sxs_indices;
  svm_msg_q_t *event_queue;
  tw_timer_wheel_1t_3w_1024sl_ov_t timer;
} upf_per_pfcp_thread_t;

typedef struct
{
  svm_msg_q_t *event_queue;
  tw_timer_wheel_1t_3w_1024sl_ov_t timer;
} upf_per_oam_thread_t;

typedef struct
{
  u32 *session_indices;
  u32 *peer_indices;
  u32 *policer_indices;
  u32 *msg_indices;
  u32 *vnip_sxs_indices;
  svm_msg_q_t *event_queue;
  tw_timer_wheel_1t_3w_1024sl_ov_t timer;
} upf_redis_thread_t;

enum
{
  HA_STATUS_BACKUP = 0,
  HA_STATUS_ACTIVE = 1,
};

enum
{
  DATAGUARD_STATUS_BACKUP = 0,
  DATAGUARD_STATUS_ACTIVE = 1,
};

enum
{
  DATAGUARD_SWITCH_OFF = 0,
  DATAGUARD_SWITCH_ON = 1,
};

typedef struct
{
  u8 *app_id;
  u32 appid_index;
  u32 far_id;
  u32 *urr_id;
  u32 *qer_id;
} predef_appid_t;

#define IS_PREDEF_RULE_ID(_x) ((_x) & 0x80000000)
typedef struct
{
  u8 *rule_id;
  u8 *app_id;
  u32 far_id;
  u32 *urr_ids;
  u32 *qer_ids;
  uword *hash_app_by_appname;
  predef_appid_t *app_ids;
} predef_rule_t;

typedef struct
{
  u8 *rule_name;
} predef_rule_name_t;

typedef struct
{
  u8 *rule_group;
  uword *hash_rule_by_rule_name;
  predef_rule_name_t *rule_names;
} predef_rule_group_t;

typedef struct
{
  u8 *rule;
} dnn_rule_t;

typedef struct
{
  u8 dnn_name[UPF_STRING_LEN32];
  urr_measure_t measure;
  upf_bandwith_t ul_bandwidth;
  upf_bandwith_t dl_bandwidth;
  urr_counter_t threshold;
  urr_counter_t mbr;        //max bandwidth rate
} dnn_volume_t;

#define DNN_THRESHOLD_REACHED BIT (1)

typedef struct
{
  u32 mcc;
  u32 mnc;
  u8 plmn[6];
} upf_plmn_t;

typedef struct
{
  clib_spinlock_t lock;

  u8 *name;
  u32 urr_report_switch;
  u32 ul_table_id;
  u32 dl_table_id;
  dnn_volume_t volume;
  dnn_rule_t *rule_id;
  upf_dnn_policer_t dnn_policer;
  f64 ul_old_time;
  f64 dl_old_time;
  u32 ref_cnt;
} upf_dnn_t;

typedef struct
{
  u32 s_nssai;
  urr_measure_t measure;
  upf_bandwith_t ul_bandwidth;
  upf_bandwith_t dl_bandwidth;
  urr_counter_t threshold;
  urr_counter_t mbr;        //max bandwidth rate
} s_nssai_volume_t;

typedef struct
{
    /* previous statistis result to be query */
    u32 average_delay;
    u32 max_delay;

    /* current counting, depending on timer */
    u32 pkt_count;
    u32 curr_max_delay;
    u64 delay_sum;
} upf_time_delay_t;

typedef struct
{
    upf_time_delay_t ul_delay;
    upf_time_delay_t dl_delay;
    upf_time_delay_t rp_delay; /* round trip delay, i.e. PSA->RAN->UE->RAN->PSA-UPF, i.e. ul_delay + dl_delay */
} upf_psa_delay_per_snssai_t;

typedef struct
{
  clib_spinlock_t lock;

  u32 s_nssai;
  u32 urr_report_switch;
  s_nssai_volume_t volume;
  dnn_rule_t *rule_id;
  upf_s_nssai_policer_t s_nssai_policer;
  f64 ul_old_time;
  f64 dl_old_time;
  u32 ref_cnt;

  upf_psa_delay_per_snssai_t psa_delay; /* PSA-UPF measurement based on "Per Qos flow per UE QoS monitoring" function */
} upf_s_nssai_t;

// Add for Exact-Routing by liupeng on  2022-07-12 below
enum
{
    USER_ID_FLAG_IMSI   = 0,
    USER_ID_FLAG_MSISDN,
    USER_ID_FLAG_IMEI,
    USER_ID_FLAG_INVALID,
};

enum
{
    ACL_RULE_HTTP_L7   = 0,
    ACL_RULE_HTTPS_L7,
    ACL_RULE_L4,
    ACL_RULE_L3,
    ACL_RULE_INVALID,
};

#define EXACT_ROUTING_FIND     1
#define EXACT_ROUTING_NOT_FIND 0
typedef struct
{
    u8 *imsi;
    u8 *msisdn;
    u8 *imei;
    u8 flag; // 0 imsi; 1 msisdn; 2 imei
    u32 ul_table_id;
    u32 dl_table_id;
} upf_user_id_t;

typedef struct
{
    ip46_address_t dst_ip;
    u16 dst_port;
    u8 protocol;
    u8 *url;
    u8 flag; // 0 l7; 1 l4; 2 l3
    u32 ul_table_id;
    u32 dl_table_id;
} upf_acl_rule_t;

typedef struct
{
    u8 rat_type;
    u32 ul_table_id;
    u32 dl_table_id;
} upf_rat_type_t;

// Add for Exact-Routing by liupeng on  2022-07-12 above

typedef struct
{
  clib_spinlock_t lock;
  u32 lock_flag;

  urr_counter_t mbr;
  upf_flow_policer_t flow_policer;
  u32 ref_cnt;
} upf_flow_overload_protect_t;

//base interface flow overload protect
typedef struct
{
  upf_flow_overload_protect_t n3_int;
  upf_flow_overload_protect_t n6_int;
  upf_flow_overload_protect_t n9_int;
} upf_int_flow_ovlp_t;

// Add for Bw rule by liupeng on 2021-08-13 below
typedef struct
{
    ip46_address_t source_ip;
    u16 source_port;
    ip46_address_t dest_ip;
    u16 dest_port;
    u8 protocol;
} upf_bw_rule_sess;


#define BW_UE_FLAG_SRCIP                      BIT(0)
#define BW_UE_FLAG_SUPI                       BIT(1)
#define BW_UE_FLAG_GPSI                       BIT(2)
#define BW_UE_FLAG_SRCPORT                    BIT(3)
#define BW_UE_FLAG_DSTIP                      BIT(4)
#define BW_UE_FLAG_DSTPORT                    BIT(5)



typedef struct
{
    char bwAllocationRuleId[128];
    ip46_address_t source_ip;
    char supi[32];
    char gpsi[32];
    u16 source_port;
    ip46_address_t dest_ip;
    u16 dest_port;
    u32 priority;
    u32 protocol;
    u8 flags;
} upf_bw_rule_sess_t;


void upf_make_hs_rule_url (char *str, char *url);

typedef struct
{
    char url[MAX_URL_LEN];
} upf_bw_rule_t;

typedef struct
{
    upf_bandwith_t recv_bandwidth;
    upf_bandwith_t send_bandwidth;
    u32 old_time;
} upf_bw_rule_measurement_t;

typedef struct
{
    upf_flow_overload_protect_t flow_overload_protect;
    upf_bw_rule_measurement_t upf_bw_rule_stat;
} upf_bw_rule_value_t;

typedef struct
{
    upf_bw_rule_t bw_rule;
    upf_bw_rule_value_t bw_rule_value;
} upf_app_ip_vaule_t;

typedef struct
{
    upf_bw_rule_sess_t bw_rule;
    upf_bw_rule_value_t bw_rule_value;
} upf_sess_ip_value_t;

// Add for Bw rule by liupeng on 2021-08-13 above

// Add for Traffic-alloction by liupeng on 2021-09-27 below
typedef struct
{
    u32 starttime;
    u64 ctflowsize;
    u32 flowtimes;
    u64 seconds;
    u64 flowsize;
} sess_traffic_rule_t;
// Add for Traffic-alloction by liupeng on 2021-09-27 above

// Add for 5G TSN function by liupeng on 2024-02-21 below
typedef struct _dstt_uemac_sessidx_t
{
    mac_address_t ue_mac;
    u32 sess_index;
} dstt_uemac_sessidx_t;
// Add for 5G TSN function by liupeng on 2024-02-21 above



typedef struct
{
  clib_spinlock_t lock;
  u32 lock_flag;

  urr_counter_t mbr;
  upf_flow_policer_t flow_policer;
  u32 ref_cnt;
} upf_flow_overload_alarm_t;

typedef struct
{
    clib_spinlock_t lock;
    u32 lock_flag;

    urr_counter_t mbr;
    upf_flow_policer_t flow_policer;
} upf_flow_control_t;

typedef struct
{
    u32 n3_swidx;
    u32 n6_swidx;
    u32 pkt_len;
    u32 max_pps;
    u32 usage_threshold;
    upf_flow_control_t overload_alarm;
    upf_flow_control_t overload_protect;
} upf_cpu_overload_t;

enum
{
  TEID_ALLOC_BY_CP = 1,
  TEID_ALLOC_BY_UP = 2,
};

typedef union
{
  struct
  {
    gtpu4_tunnel_key_t k;
    u64 v;
  };
  clib_bihash_kv_16_8_t kv;
} v4_error_ind_kv_t;

typedef union
{
  struct
  {
    gtpu6_tunnel_key_t k;
    u64 v;
  };
  clib_bihash_kv_24_8_t kv;
} v6_error_ind_kv_t;

typedef struct
{
    u8 *name;
    ip46_address_t peer_upf_ip;
    u32 pkg_drop;
}upf_ip_blacklist_t;




typedef struct
{
   mac_address_t mac;
   u32 vlan_id;
} static_mac_address_key_t;

typedef struct
{
    u8 action;
} static_mac_address_val_t;

typedef struct
{
    static_mac_address_key_t key;
	static_mac_address_val_t val;
} static_mac_address_t;


typedef struct
{
   u32  nwi;
   u32 vlan_id;
} n6_vlan_list_key_t;

typedef struct
{
    u8 action;
} n6_vlan_list_val_t;

typedef struct
{
    n6_vlan_list_key_t key;
	n6_vlan_list_val_t val;
} n6_vlan_list_t;



typedef struct
{
    ip46_address_t remote_ip;
    u32 status;
}upf_asso_alarm_t;

typedef struct
{
    u32 flags;
#define F_WHITELIST_IP BIT (0)
#define F_WHITELIST_URL BIT (1)
#define F_WHITELIST_NUMSEG BIT (2)

    ip46_address_t ip;
	//u8 *url;
	//u8 *action;

    u8 url[64];
	u8 numseg[64];
	u8 isopen;
	u8 action[64];
}upf_whitelist_key;

typedef struct
{
    u8 *name;
	u8 fraud_check_flag;
	u8 sni_check_flag;
	u8 encryp_flag;
	u8 algorithm_flag;
	u8 encryp_key[64];
	u8 encryp_key_flag;

#define F_SUB_EXTENSTION_OPEN BIT (0)
#define F_SUB_EXTENSTION_MSISDN BIT (1)
#define F_SUB_EXTENSTION_USERIP BIT (2)
#define F_SUB_EXTENSTION_IMSI BIT (3)
#define F_SUB_EXTENSTION_IMEI BIT (4)
#define F_SUB_EXTENSTION_ULI BIT (5)
#define F_SUB_EXTENSTION_SGWCIP BIT (6)
#define F_SUB_EXTENSTION_SMFIP BIT (7)
#define F_SUB_EXTENSTION_TIMESTAMP BIT (8)
#define F_SUB_EXTENSTION_RATTYPE BIT (9)
#define F_SUB_EXTENSTION_DNN BIT (10)
	u16 sub_extension_flag;
	u8 extension_flag;
	u32 predefind_id;
	u8 rat_type[64];
	u8 uli[64];
	u16 encryp_extension_flag;
	u8 msisdn_enr_header_str[64];
	u8 user_ip_enr_header_str[64];
	u8 imsi_enr_header_str[64];
	u8 imei_enr_header_str[64];
	u8 uli_enr_header_str[64];
	u8 timestamp_enr_header_str[64];
	u8 rat_type_enr_header_str[64];
	u8 dnn_enr_header_str[64];
	u16 enr_header_flag;
    u8 tcp_number_modify_flag;
    u8 smf_ip_enr_header_str[64];
    u8 sgwc_ip_enr_header_str[64];

	upf_whitelist_key key;
}upf_whitelist_t;
typedef struct
{
  u32 is_used;
  ip4_address_t ueip;
  u32 uemask;
  ip4_address_t n3ip;
} upf_stacking_t;

//begin liukang add for gray list 2021/03/22
typedef struct
{
	char url[256];
	char dnn[32];
	char numseg[64];
	char action[64];
	u8 isopen;
	ip46_address_t ip;
	u32 flags;
} upf_graylist_key_t;

typedef struct
{
	u8 *name;
	upf_graylist_key_t key;
}upf_graylist_t;

#define F_GRAYLIST_IP   BIT (0)
#define F_GRAYLIST_URL  BIT (1)
#define F_GRAYLIST_NUMSEG  BIT (2)
#define F_GRAYLIST_DNN  BIT (3)
//end liukang add for gray list 2021/03/22

typedef struct
{
    //u16 usLen;
    u32 server_ip;
} dns_server_key_s;

typedef struct
{
    dns_server_key_s dns_server_key;
    u32 ipaddress;

} dns_server_s;


#define UPF_STACKING_MAXNUM 10


typedef struct
{
  u32 lb_switch;
  ip46_address_t n3ip;
  ip46_address_t n6ip;
  ip46_address_t n9ip;
  ip46_address_t n19ip;
} upf_lb_flow_t;

typedef upf_session_tunnel_pair_t session_by_tunnel_key_t;
typedef struct
{
  union
  {
    struct
    {
      u32 session_index;
      u32 reverse;
    };
    u64 as_u64;
  };
} session_by_tunnel_value_t;

typedef struct
{
  union
  {
    struct
    {
      session_by_tunnel_key_t k;
      session_by_tunnel_value_t v;
    };
    clib_bihash_kv_8_8_t kv;
  };
} session_by_tunnel_t;

//end liukang add for vn to tunnel 2022/07/15

typedef struct upf_dnn_tunnel_tag
{
    char dnn[64];
    char tunnel[32];
}upf_dnn_tunnel_t;

//begin liukang add for acl rule 2022/08/04

typedef struct upf_db_redis
{
    ip46_address_t ip;
    u16 port;
} upf_redis_t;

typedef struct tag_ue_ip4_address_mask
{
	u32 begin_ip4_address;
	u32 end_ip4_address;
	u8 mask;
}ue_ip4_address_mask_t;

typedef struct tag_ue_ip6_address_mask
{
	u32 begin_ip6_address[4];
	u32 end_ip6_address[4];
	u8 mask;
}ue_ip6_address_mask_t;
typedef struct tag_ue_tcp_port
{
	u16 begin_port;
	u16 end_port;

}ue_tcp_port_t;


typedef struct tag_ue_acl_rule
{
	char aclRuleId[64];

	u8 ipAddressType;
	ue_ip4_address_mask_t *ueAddress4;
	ue_ip4_address_mask_t *dnAddress4;
	ue_ip6_address_mask_t *ueAddress6;
	ue_ip6_address_mask_t *dnAddress6;
	ue_tcp_port_t *srcPort;
	ue_tcp_port_t *dstPort;
	u8    protocol;
}ue_acl_rule_t;
//end liukang add for acl rule 2022/08/04

typedef struct tag_upf_whitelist_access_ip
{
	char name[64];
	u8 ipAddressType;
	ue_ip4_address_mask_t ipAddress4;
	ue_ip6_address_mask_t ipAddress6;
}upf_whitelist_access_ip_t;

typedef struct tag_upf_whitelist_access_ip_key
{
	char name[64];
}upf_whitelist_access_ip_key_t;

typedef struct
{
    ip46_address_t ip;

    u8 state;
    u8 old_state;
    i16 send_cnt;
    i16 recv_cnt;
    u16 host_id;
    u32 alarm_state;
}upf_health_data_t;

typedef struct
{
  u32 num_pfcp_threads;
  u32 first_pfcp_thread_index;
  u32 redis_thread_index;
  u32 num_worker_threads;
  u32 first_worker_thread_index;
  u32 num_oam_threads;
  u32 first_oam_thread_index;

  svm_msg_q_t *event_queue;
  u64 key_log_switch; /* KL_SWITCH_NUM */

  /* ha */
  u32 ha_status;
  u32 *ha_interfaces;

  // struct configurations for handling N4 link anomalies
  // added by caozhongwei 2025-07-11
  upf_n4_persist_t n4_persist;

  upf_ue_stat_t *ue_stats;
  uword *ue_stat_index;

  upf_pfcp_endpoint_t *pfcp_endpoints;
  uword *pfcp_endpoint_index;

  upf_ip_blacklist_t *upf_ip_blacklist;
  uword *blacklist_index_by_upf_ip;

  upf_whitelist_t *upf_whitelist;
  uword *whitelist_index;
  upf_whitelist_access_ip_t *upf_access_whitelist;
  uword *whitelist_access_index;

  upf_graylist_t *upf_graylist;                 //liukang add for gray list 2021/03/22
  uword *graylist_index;                        //liukang add for gray list 2021/03/22

  upf_ip_blacklist_t *upf_device_blacklist;
  uword *device_blacklist_index;

  static_mac_address_t *static_mac_address;
  uword *static_mac_address_index;

  n6_vlan_list_t *n6_vlan_list;
  uword *n6_vlan_list_index;

  upf_asso_alarm_t *asso_alarm_status_list;
  uword *asso_alarm_index;

  dns_server_s *dns_server_list;                 //liukang add for dns server list 2021/03/31
  uword *dns_server_index;                        //liukang add for dns server list 2021/03/31

  /* vector of network instances */
  upf_nwi_t *nwis;
  uword *nwi_index_by_name;

  /* vector of UE IP pool*/
  upf_ue_ip_pool_t *ip_pools;
  uword *ip_pool_by_fib_indx;

  /* vector of dpi */
  upf_dpi_t *dpi;
  uword *dpi_index_by_id;

  /* vector of forward policy */
  upf_forward_policy_t *forward_policys;
  uword *forward_policy_by_name;

  u32 sw_if_index;

  /* vector of user plane ip resource */
  u32 teid_alloc_option; /*1: cp, 2: local upf*/
  upf_upip_res_t *upip_res;
  uword *upip_res_index;

  /* stacking 20G-50G */
  u32 stacking_switch;
  upf_stacking_t stacking[UPF_STACKING_MAXNUM];

  /* 锟斤拷锟斤拷ue ip锟斤拷锟叫革拷锟截撅拷锟斤拷(PS:锟斤拷时锟斤拷锟斤拷) */
  upf_lb_flow_t lb_flow;

  /* vector of encap tunnel instances */
  upf_session_t *sessions;
  clib_spinlock_t sessions_lock;

  /* lookup session by key */
  clib_bihash_8_8_t session_by_id;
  /* lookup session by F-SEID */
  clib_bihash_24_8_t session_by_fseid;

  clib_bihash_24_8_t mac_address_by_ip;     //liukang add for ethernet session 2022/3/18
  clib_bihash_8_8_t  session_by_tunnel;

  /* lookup tunnel by TEID */
  clib_bihash_16_8_t v4_tunnel_by_key; /* keyed session id */
  clib_bihash_24_8_t v6_tunnel_by_key; /* keyed session id */

  uword *session_by_imsi;
  // Add for get ue identity info by liupeng on 2021-09-06 below
  uword *session_by_msisdn;
  uword *session_by_imei;
  uword *session_by_ueip;
  ip46_address_t *session_ueip;
  // Add for get ue identity info by liupeng on 2021-09-06 above

  // Add for 5G TSN function by liupeng on 2024-02-21 below
  uword *session_by_uemac;   // l2 session by ue mac address
  // Add for 5G TSN function by liupeng on 2024-02-21 above

  /* per cpu */
  upf_per_pfcp_thread_t **per_pfcp_thread;
  upf_per_oam_thread_t **per_oam_thread;
  upf_redis_thread_t *redis_thread;

  /* policer pool, aligned */
  upf_qer_policer_t *qer_policers;
  clib_spinlock_t qer_lock;
  clib_bihash_8_8_t qer_by_id;

  /* list of remote GTP-U peer ref count used to stack FIB DPO objects */
  upf_peer_t *peers;
  clib_spinlock_t peers_lock;
  clib_bihash_24_8_t peer_index_by_ip; /* remote GTP-U peer keyed on it's ip
                                          addr and vrf */
  clib_bihash_8_8_t echo_req_by_seq;

  /* vector of associated PFCP nodes */
  upf_node_assoc_t *nodes;
  clib_spinlock_t nodes_lock;
  u32 upf_status;

  /* lookup PFCP nodes */
  uword *node_index_by_ip;
  uword *node_index_by_fqdn;

  /*predef rule*/
  upf_dnn_t *dnn;
  upf_s_nssai_t *nssai;
  uword *hash_rule_id_by_rule_name;
  predef_rule_t *pre_rule;
  upf_far_t *pre_far;
  upf_qer_t *pre_qer;
  upf_urr_t *pre_urr;
  uword *hash_rule_id_by_rule_group;
  predef_rule_group_t *pre_rule_group;

  /* vn ip sessions information */
  upf_vnip_sessions_t *vnip_sessions;
  clib_spinlock_t vnip_sxs_lock;
  clib_bihash_24_8_t vnip_sxs_by_ip;

#if 0
  uword *vtep4;
  uword *vtep6;
#endif
  /**
   * Node type for registering to fib changes.
   */
  fib_source_t fib_source;
  fib_node_type_t fib_node_type;
  dpo_type_t dpo_type;

  /* API message ID base */
  u16 msg_id_base;

  /* convenience */
  // vlib_main_t *vlib_main;
  vnet_main_t *vnet_main;
  ethernet_main_t *ethernet_main;

  /* adf apps hash */
  uword *upf_app_by_name;
  /* adf apps vector */
  upf_adf_app_t *upf_apps;

  uword *hash_app_by_appname;
  upf_app_id_pfd_ctx_t *pfd_list;
  hs_database_t *database;
  hs_scratch_t **scratch;

  i32 far_buffering_n;
  u16 gtpu_tunnel_mtu;
  u32 max_buffering_n;
  u32 ip4_reass_next;
  u32 ip6_reass_next;
  u32 ip4_frag_next_encap4;
  u32 ip4_frag_next_encap6;
  u32 ip6_frag_next_encap4;
  u32 ip6_frag_next_encap6;

  u8 heart_beat_disable;
  u8 dpi_enable;
  u8 ddp_enable;
  u8 pdr_search_perf;
  u8 gtp_req_enable : 1,
     gtp_n4_switch  : 1,
     gtp_retry_switch : 1,
     gtp_resv       : 5;
  u8 context_release_disable;
  u16 gtpu_echo_req_period;

  /* TCP MSS clamping */
  u16 mss_clamping;
  u16 mss_value_net;
  /*pfcp heartbeat interval*/
  u32 pfcp_hb_interval;
  /*message retry*/
  u32 pfcp_t1_timeout;
  u32 pfcp_retry_n1;
  /*load control*/
  u32 lc_max_pps;
  u32 lc_change_threshold;
  /* add by liudong in 20210510 for pfcp message overload begin */
  u32 threshold_value;
  u32 session_num;
  /* add by liudong in 20210510 for pfcp message overload end */

  /* lookup Error Indication  by TEID */
  clib_bihash_16_8_t v4_error_ind_rcv_by_key; /* keyed timestamp */
  clib_bihash_24_8_t v6_error_ind_rcv_by_key; /* keyed timestamp */

  pfcp_up_function_features_t upf_features;
  // Add for Bw rule by liupeng on 2021-08-13 below
  upf_bw_rule_t *bwrule_list;
  hs_database_t *database_bw;
  hs_scratch_t **scratch_bw;
  // Add for Bw rule by liupeng on 2021-08-13 above
  // Add for dnn/s-nssai timeval by liupeng on 2021-11-16 below
  u32 s_nssai_timeval; // default 5mins syn volume to agent
  u32 dnn_timeval;
  pfcp_time_t dnn_timer;
  pfcp_time_t s_nssai_timer;
  // Add for dnn/s-nssai timeval by liupeng on 2021-11-16 above
  uword *hash_5glan_id_by_nwi_name;
  upf_5glan_nwi_t *vn_nwis;

  pfcp_time_t dscp_shape_timer;
  pfcp_time_t dynmic_mac_refresh_timer;
  pfcp_time_t dynmic_mac_delete_timer;
  u32 dynmic_mac_refresh_timeval;
  u32 dynmic_mac_delete_timeval;
  u32 allow_bcast_pkts;

  uword *hash_eth_broadcast_id_by_key;
  upf_eth_broadcast_domain_t *eth_broadcast_domains;
  uword *hash_eth_unicast_by_mac;
  uword *hash_vn_eth_unicast_by_mac;
  uword *hash_eth_multicast_id_by_key;
  uword *hash_dssr_copy_by_key; //dl copy table
  upf_dnn_tunnel_t *dnn_tunnel;

  uword *hash_eth_broadcast_by_key;

  u8 dssr_dd_is_reverse;
  uword *bitmap_dssr_dd[2];//de_duplication

  // Add for Exact-Routing by liupeng on  2022-07-12 below
  upf_user_id_t *user_id;
  upf_acl_rule_t *acl_rule;
  upf_rat_type_t *rat_type;
  // Add for Exact-Routing by liupeng on  2022-07-12 above
  ue_acl_rule_t *ue_acl_rules;                 //liukang add for acl rules 2022/08/04

  pfcp_time_t run_independent_timer;

  u32 dataguard_gre_id_v4;
  u32 dataguard_gre_id_v6;
  u8 dataguard_status:1,
     dataguard_switch:1,
     flow_to_gre:1,
     upf_global_statistics_switch :1,
     unused:4;
  upf_health_data_t health_gre4;
  upf_health_data_t health_gre6;
  u8 tsn_enable;
  uword *hash_frer_sess_by_mac; //frer get sess idx by ue mac
  /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 below */
  pfcp_time_t pkt_rate_minute_timer;
  pfcp_time_t pkt_rate_tenth_of_hour_timer;
  pfcp_time_t pkt_rate_hour_timer;
  pfcp_time_t pkt_rate_day_timer;
  pfcp_time_t pkt_rate_week_timer;
  u32 *pkt_rate_sess_idxs;
  /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 above */

  upf_redis_t redis;

  u64 sess_report_interval;
  /* vector of report OMC nodes */
  upf_report_omc_node_t *report_omc_node;
  uword *hash_node_by_name;
} upf_main_t;

typedef struct
{
    void *arg;
    u32 count;
    ip46_type_t iptype;
    int dst_intf;
} sx_session_remoteip_t;

typedef struct
{
  void *arg;
  u32 count;
  u32 limit;
  ip46_address_t cp;
  u32 is_init;
  u32 is_sort;
  upf_session_t **sx;
} sx_session_show_t;

extern const fib_node_vft_t upf_pfcp_fib_vft;
extern const dpo_vft_t upf_pfcp_dpo_vft;
extern upf_main_t g_upf_main;
extern upf_cpu_overload_t g_upf_cpu_overload;
extern u32 g_upf_gtpu_header_len;

extern vlib_node_registration_t upf_if_input_node;
extern vlib_node_registration_t gtpu4_input_node;
extern vlib_node_registration_t gtpu6_input_node;
extern vlib_node_registration_t upf4_encap_node;
extern vlib_node_registration_t upf6_encap_node;

extern vlib_node_registration_t upf_process_ip4_node;
extern vlib_node_registration_t upf_process_ip6_node;

/* auther:by sff; data:2020.11.11; start */

//The alarm ID is assigned by OAM
enum
{
  UPF_ALARM_BASE = 100,
  UPF_ALARM_N3_DROP,
  UPF_ALARM_N6_DROP,
  UPF_ALARM_N9_DROP,
  UPF_ALARM_ASSOCIATION_TIMEOUT,
  UPF_ALARM_GTP_TUNNEL_TIMEOUT,
  UPF_ALARM_UL_UEIP_CHECK_FAIL,
  UPF_ALARM_DL_SESSION_CHECK_FAIL,
  UPF_ALARM_MAIN_STANDBY_SWAP,
  UPF_ALARM_FLOW_OVERLOAD_PROTECT,
  UPF_ALARM_CPU_OVERLOAD,
  UPF_ALARM_PREDEF_CONFLICT,
  UPF_ALARM_UNUSED,
  /* deleted by liuyu LICENSE_SCRAP_CODE 2022-02-17 */
  UPF_ALARM_ASSCO_SETUP_SUCC_RATIO_LOW = 113,
  UPF_ALARM_ASSCO_UPDATE_SUCC_RATIO_LOW,
  UPF_ALARM_ASSCO_RELEASE_SUCC_RATIO_LOW,
  UPF_ALARM_PFD_MANAGEMENT_SUCC_RATIO_LOW,
  UPF_ALARM_SESSION_EST_SUCC_RATIO_LOW,
  UPF_ALARM_SESSION_MODIFY_SUCC_RATIO_LOW,
  UPF_ALARM_SESSION_DELETE_SUCC_RATIO_LOW,
  /* deleted by liuyu LICENSE_SCRAP_CODE 2022-02-17 */
  /* begin added by liuyu LICENSE_FUNC 2021-12-17 */
  UPF_ALARM_LICENSE_WILL_EXPIRE,
  /* begin added by liuyu LICENSE_EXPIRE_ALARM 2022-02-14 */
  UPF_ALARM_LICENSE_HAS_EXPIRED,
  /* end added by liuyu LICENSE_EXPIRE_ALARM 2022-02-14 */
  /* end added by liuyu LICENSE_FUNC 2021-12-17 */

  UPF_ALARM_PREDEF_APPID_CONFLICT,
  UPF_ALARM_RUN_INDEPENDENT,
  // add for ipsec support send tunnel fault pass dpd by liupeng on 2022-11-10 below
  UPF_ALARM_IPSEC_TUNNEL_FAULT,
  // add for ipsec support send tunnel fault pass dpd by liupeng on 2022-11-10 above
  UPF_ALARM_CPU_OVERLOAD_PRIVATE,
  UPF_ALARM_MAX,
};

// alarm level
enum
{
    UPF_ALARM_LEVEL_CRITICAL = 1,
    UPF_ALARM_LEVEL_SERIOUS,
    UPF_ALARM_LEVEL_GENERAL,
    UPF_ALARM_LEVEL_WARNING,
    UPF_ALARM_LEVEL_EVENT,
};

// alarm type
typedef enum
{
    UPF_ALARM_TYPE_PRODUCE = 0,
    UPF_ALARM_TYPE_RECOVER,
}alarm_status_e;

/* alarm data */
typedef struct
{
  u32 alarm_id;
  u32 alarm_type;
  u32 alarm_level;
  u64 current_time;
  ip46_address_t om_ip;
  u8 ne_type;
  u32 ne_instance_id;
  u8 alarm_detail[UPF_STRING_LEN1024];
} upf_alarm_notify_t;

typedef struct
{
  ip46_address_t from_ip;
  ip46_address_t to_ip;
  u32 fail_id;
} upf_swap_alarm_t;

typedef struct
{
    u8 *nwi;
    upf_traffic_stat_t n3_stat;
	upf_traffic_stat_t n6_stat;
	upf_traffic_stat_t n9_stat;
	upf_traffic_stat_t ul_stat;
	upf_traffic_stat_t dl_stat;
    upf_pfcp_msg_count_t n4_stat;
}nwi_traffic_stat_t;

typedef struct
{
    u8 *dnn;
    upf_traffic_stat_t n3_stat;
	upf_traffic_stat_t n6_stat;
	upf_traffic_stat_t n9_stat;
	upf_traffic_stat_t ul_stat;
	upf_traffic_stat_t dl_stat;
    upf_pfcp_msg_count_t n4_stat;
}dnn_traffic_stat_t;

typedef struct
{
    u8 *s_nssai;
    upf_traffic_stat_t n3_stat;
	upf_traffic_stat_t n6_stat;
	upf_traffic_stat_t n9_stat;
	upf_traffic_stat_t ul_stat;
	upf_traffic_stat_t dl_stat;
    upf_pfcp_msg_count_t n4_stat;
}s_nssai_traffic_stat_t;


typedef struct
{
  u32 alarm_type;
  u32 alarm_state;
} upf_alarm_state_t;

typedef struct
{
  u32 time;
  BVT (clib_bihash) ul_ueip_check;
  BVT (clib_bihash) dl_session_check;
} upf_alarm_main_t;

/* worker thread notify pfcp thread alarm msg */
typedef struct
{
  u32 alarm_id;
  u32 alarm_type;
  u32 alarm_level;
  void *data;
} upf_alarm_msg_t;

/* worker thread notify pfcp thread msg */
typedef struct
{
  u32 msg_id;
  void *data;
} upf_send_msg_t;

typedef struct
{
#define HDR_LEN 8
  u32 msg_id;
  u32 msg_len; /* exclude HDR_LEN */
  union
  {
    void *data[0];
    u8 as_u8[0];
    u64 as_u64[0];
  };
} upf_thread_msg_t;

typedef struct
{
    u32 sess_idx;
    u32 pdr_idx;
    pfcp_join_ip_multicast_information_t *join_info;
    pfcp_leave_ip_multicast_information_t *leave_info;
} upf_report_ip_multicast_information_t;

typedef struct
{
    u32 is_detect; /* 1:detected, 0:remove */
    u32 sess_idx;
    u32 pdr_idx;

    pfcp_mac_addresses_t *addr;
} upf_report_mac_information_t;

typedef struct
{
    u32 sess_idx;
    u8 has_pdrid:1,
       has_urrid:1,
       has_srrid:1,
       resv:5;
    u8 srr_id;
    u16 pdr_id;
    u32 urr_id;
    union
    {
        struct
        {
            u8 type;
        }traf_inact_detect;

        pfcp_qos_monitoring_report_t per_qos_monitor_report;
    };
} upf_inner_report_info_t;

typedef struct
{
  u8 type;
  u8 code;
  u16 checksum;
} upf_igmp_header_t;

typedef struct
{
  /* Type 0x16. membership_report_v2 */
  upf_igmp_header_t header;
  /* Destination multicast group address. */
  ip4_address_t group_address;
} upf_igmp_membership_report_v2_t;

typedef struct
{
    ip6_hop_by_hop_ext_t ext_hdr;
    ip6_router_alert_option_t alert;
    ip6_padN_option_t pad;
    icmp46_header_t icmp;
    u16 max_rsp_delay;
    u16 rsvd;
    ip4_address_t group_address;
} upf_icmp6_multicast_listener_report_v1_t;

typedef struct
{
    ip6_hop_by_hop_ext_t ext_hdr;
    ip6_router_alert_option_t alert;
    ip6_padN_option_t pad;
    icmp46_header_t icmp;
    u16 rsvd;
    u16 num_addr_records;
    icmp6_multicast_address_record_t records[0];
} upf_icmp6_multicast_listener_report_v2_t;

typedef struct
{
  ip4_address_t ue_ip;
  ip4_address_t pkt_src_ip;
} upf_ip_check_t;

typedef struct
{
  ip4_address_t src_ip;
  ip4_address_t dst_ip;
} upf_pkt_ip_t;

typedef struct
{
    //u16 usLen;
    char acHost[HOSTNAME_LEN];
} dns_rule_key_t;

typedef struct
{
    dns_rule_key_t dns_rule_key;
    u32 uip[MAX_IP_CNT];
    u64 ulExpire;
} dns_rule_rr_t;

typedef struct
{
    u32 total_cnt;
	u32 success_cnt;
	u32 success_ratio;
	//u32 msg_produce_name[64];
}n4_msg_alarm_t;

/* begin added by zhangxin 2022-11-18 */
#define INTERFACE_MAX_NUM (2)
typedef struct
{
    ip4_address_t srcIp4Addr;
    ip4_address_t dstIp4Addr;
    ip6_address_t srcIp6Addr;
    ip6_address_t dstIp6Addr;
}iupf_relate_ipAddr_t;

extern iupf_relate_ipAddr_t g_upf_relate_IpAddr[INTERFACE_MAX_NUM];
/* end added by zhangxin 2022-11-18 */
extern upf_alarm_state_t g_upf_alarm_state[UPF_ALARM_MAX - UPF_ALARM_BASE];
extern u32 g_upf_alarm_switch;
extern u32 g_upf_alarm_id_switch[UPF_ALARM_MAX - UPF_ALARM_BASE];
extern u32 g_upf_alarm_threshold[UPF_ALARM_MAX - UPF_ALARM_BASE];
extern u32 g_upf_dpi_switch;
extern upf_alarm_main_t g_upf_alarm_db;
extern u32 g_upf_dnn_switch;
extern u32 g_upf_dnn_ue_ping_switch;
extern u32 g_upf_s_nssai_switch;
extern u32 g_upf_padding_switch;
extern upf_plmn_t g_home_plmn;
extern upf_flow_overload_protect_t g_upf_flow_overload_protect;
extern upf_flow_overload_alarm_t g_upf_flow_overload_alarm;
extern upf_dos_syn_t g_upf_dos_syn;
extern upf_int_flow_ovlp_t g_upf_int_flow_ovlp;
extern u32 g_upf_duplicated_ueip_delete;
extern u32 g_upf_auxiliary_switch;
extern u32 g_upf_rg_switch;
extern u32 g_upf_qer_mbr_offset;
extern u8 g_upf_message_repeat_times;
extern u32 g_upf_apndnn_check_switch;

extern u32 g_dns_ageing_ttl;
extern uword* g_dns_sniffer_rule_hash;
extern dns_rule_rr_t *g_dns_sniffer_rule;

extern u32 g_upf_anonymization_switch;
extern u32 g_statistics_update_timer_period;
extern u32 g_upf_global_statistics_timer_period;
extern u32 g_upf_status_report_timer_period;
#define ALARM_STATE(id)      (g_upf_alarm_state[(id) - UPF_ALARM_BASE - 1].alarm_state)
#define ALARM_IS_PRODUCE(id) ((g_upf_alarm_state[(id) - UPF_ALARM_BASE - 1].alarm_state) == UPF_ALARM_TYPE_PRODUCE)
#define ALARM_IS_RECOVER(id) ((g_upf_alarm_state[(id) - UPF_ALARM_BASE - 1].alarm_state) == UPF_ALARM_TYPE_RECOVER)

enum
{
  //N3
  UPF_MEASURE_N3_RCVE_PKTS = 1,
  UPF_MEASURE_N3_RCVE_BTYES,
  UPF_MEASURE_N3_SEND_PKTS,
  UPF_MEASURE_N3_SEND_BTYES,
  UPF_MEASURE_N3_DROP_PKTS,
  UPF_MEASURE_N3_DROP_PERCENT,
  UPF_MEASURE_N3_DROP_LIMIT,
  UPF_MEASURE_N3_RECV_BANDWITH_PKG_RATE,
  UPF_MEASURE_N3_RECV_BANDWITH_L3_RATE,
  UPF_MEASURE_N3_RECV_BANDWITH_L2_RATE,
  UPF_MEASURE_N3_RECV_BANDWITH_L1_RATE,
  UPF_MEASURE_N3_SEND_BANDWITH_PKG_RATE,
  UPF_MEASURE_N3_SEND_BANDWITH_L3_RATE,
  UPF_MEASURE_N3_SEND_BANDWITH_L2_RATE,
  UPF_MEASURE_N3_SEND_BANDWITH_L1_RATE,
  UPF_MEASURE_NS_N3_RCVE_PKTS = 51,
  UPF_MEASURE_NS_N3_RCVE_BTYES,
  UPF_MEASURE_NS_N3_SEND_PKTS,
  UPF_MEASURE_NS_N3_SEND_BTYES,
  UPF_MEASURE_NS_N3_DROP_PKTS,
  UPF_MEASURE_DNN_N3_RCVE_PKTS,
  UPF_MEASURE_DNN_N3_RCVE_BTYES,
  UPF_MEASURE_DNN_N3_SEND_PKTS,
  UPF_MEASURE_DNN_N3_SEND_BTYES,
  UPF_MEASURE_DNN_N3_DROP_PKTS,

  //N6
  UPF_MEASURE_N6_RCVE_PKTS = 101,
  UPF_MEASURE_N6_RCVE_BTYES,
  UPF_MEASURE_N6_SEND_PKTS,
  UPF_MEASURE_N6_SEND_BTYES,
  UPF_MEASURE_N6_DROP_PKTS,
  UPF_MEASURE_N6_DROP_PERCENT,
  UPF_MEASURE_N6_DROP_LIMIT,
  UPF_MEASURE_N6_RECV_BANDWITH_PKG_RATE,
  UPF_MEASURE_N6_RECV_BANDWITH_L3_RATE,
  UPF_MEASURE_N6_RECV_BANDWITH_L2_RATE,
  UPF_MEASURE_N6_RECV_BANDWITH_L1_RATE,
  UPF_MEASURE_N6_SEND_BANDWITH_PKG_RATE,
  UPF_MEASURE_N6_SEND_BANDWITH_L3_RATE,
  UPF_MEASURE_N6_SEND_BANDWITH_L2_RATE,
  UPF_MEASURE_N6_SEND_BANDWITH_L1_RATE,
  UPF_MEASURE_N6_RCVE_PKTS_IPV6,
  UPF_MEASURE_N6_RCVE_BTYES_IPV6,
  UPF_MEASURE_N6_SEND_PKTS_IPV6,
  UPF_MEASURE_N6_SEND_BTYES_IPV6,
  UPF_MEASURE_N6_DROP_PKTS_IPV6,
  UPF_MEASURE_N6_DROP_PERCENT_IPV6,
  UPF_MEASURE_N6_DROP_LIMIT_IPV6,
  UPF_MEASURE_N6_RECV_BANDWITH_PKG_RATE_IPV6,
  UPF_MEASURE_N6_RECV_BANDWITH_L3_RATE_IPV6,
  UPF_MEASURE_N6_RECV_BANDWITH_L2_RATE_IPV6,
  UPF_MEASURE_N6_RECV_BANDWITH_L1_RATE_IPV6,
  UPF_MEASURE_N6_SEND_BANDWITH_PKG_RATE_IPV6,
  UPF_MEASURE_N6_SEND_BANDWITH_L3_RATE_IPV6,
  UPF_MEASURE_N6_SEND_BANDWITH_L2_RATE_IPV6,
  UPF_MEASURE_N6_SEND_BANDWITH_L1_RATE_IPV6,
  UPF_MEASURE_NS_N6_RCVE_PKTS = 151,
  UPF_MEASURE_NS_N6_RCVE_BTYES,
  UPF_MEASURE_NS_N6_SEND_PKTS,
  UPF_MEASURE_NS_N6_SEND_BTYES,
  UPF_MEASURE_NS_N6_DROP_PKTS,
  UPF_MEASURE_DNN_N6_RCVE_PKTS,
  UPF_MEASURE_DNN_N6_RCVE_BTYES,
  UPF_MEASURE_DNN_N6_SEND_PKTS,
  UPF_MEASURE_DNN_N6_SEND_BTYES,
  UPF_MEASURE_DNN_N6_DROP_PKTS,

  //N9
  UPF_MEASURE_N9_RCVE_PKTS = 201,
  UPF_MEASURE_N9_RCVE_BTYES,
  UPF_MEASURE_N9_SEND_PKTS,
  UPF_MEASURE_N9_SEND_BTYES,
  UPF_MEASURE_N9_DROP_PKTS,
  UPF_MEASURE_N9_DROP_PERCENT,
  UPF_MEASURE_N9_DROP_LIMIT,
  UPF_MEASURE_N9_RECV_BANDWITH_PKG_RATE,
  UPF_MEASURE_N9_RECV_BANDWITH_L3_RATE,
  UPF_MEASURE_N9_RECV_BANDWITH_L2_RATE,
  UPF_MEASURE_N9_RECV_BANDWITH_L1_RATE,
  UPF_MEASURE_N9_SEND_BANDWITH_PKG_RATE,
  UPF_MEASURE_N9_SEND_BANDWITH_L3_RATE,
  UPF_MEASURE_N9_SEND_BANDWITH_L2_RATE,
  UPF_MEASURE_N9_SEND_BANDWITH_L1_RATE,
  UPF_MEASURE_NS_N9a_RCVE_PKTS = 231,
  UPF_MEASURE_NS_N9a_RCVE_BTYES,
  UPF_MEASURE_NS_N9a_SEND_PKTS,
  UPF_MEASURE_NS_N9a_SEND_BTYES,
  UPF_MEASURE_NS_N9a_DROP_PKTS,
  UPF_MEASURE_DNN_N9a_RCVE_PKTS,
  UPF_MEASURE_DNN_N9a_RCVE_BTYES,
  UPF_MEASURE_DNN_N9a_SEND_PKTS,
  UPF_MEASURE_DNN_N9a_SEND_BTYES,
  UPF_MEASURE_DNN_N9a_DROP_PKTS,
  UPF_MEASURE_N9c_RCVE_PKTS = 251,
  UPF_MEASURE_N9c_RCVE_BTYES,
  UPF_MEASURE_N9c_SEND_PKTS,
  UPF_MEASURE_N9c_SEND_BTYES,
  UPF_MEASURE_N9c_DROP_PKTS,
  UPF_MEASURE_NS_N9c_RCVE_PKTS = 281,
  UPF_MEASURE_NS_N9c_RCVE_BTYES,
  UPF_MEASURE_NS_N9c_SEND_PKTS,
  UPF_MEASURE_NS_N9c_SEND_BTYES,
  UPF_MEASURE_NS_N9c_DROP_PKTS,
  UPF_MEASURE_DNN_N9c_RCVE_PKTS,
  UPF_MEASURE_DNN_N9c_RCVE_BTYES,
  UPF_MEASURE_DNN_N9c_SEND_PKTS,
  UPF_MEASURE_DNN_N9c_SEND_BTYES,
  UPF_MEASURE_DNN_N9c_DROP_PKTS,

  //N4 sess establish
  UPF_MEASURE_N4_SESS_ESTAB_REQ = 301,
  UPF_MEASURE_N4_SESS_ESTAB_RSP,
  UPF_MEASURE_N4_SESS_ESTAB_HANDLE_SUCC,
  UPF_MEASURE_N4_SESS_ESTAB_HANDLE_FAIL,
  UPF_MEASURE_N4_SESS_ESTAB_HANDLE_FAIL_CAUSE,  //305
  UPF_MEASURE_NS_N4_SESS_ESTAB_REQ = 321,
  UPF_MEASURE_NS_N4_SESS_ESTAB_RSP,
  UPF_MEASURE_NS_N4_SESS_ESTAB_HANDLE_SUCC,
  UPF_MEASURE_NS_N4_SESS_ESTAB_HANDLE_FAIL,
  UPF_MEASURE_DNN_N4_SESS_ESTAB_REQ = 341,
  UPF_MEASURE_DNN_N4_SESS_ESTAB_RSP,
  UPF_MEASURE_DNN_N4_SESS_ESTAB_HANDLE_SUCC,
  UPF_MEASURE_DNN_N4_SESS_ESTAB_HANDLE_FAIL,
  //N4 sess modify
  UPF_MEASURE_N4_SESS_MODIFY_REQ = 401,
  UPF_MEASURE_N4_SESS_MODIFY_RSP,
  UPF_MEASURE_N4_SESS_MODIFY_HANDLE_SUCC,
  UPF_MEASURE_N4_SESS_MODIFY_HANDLE_FAIL,
  UPF_MEASURE_N4_SESS_MODIFY_HANDLE_FAIL_CAUSE,  //405
  UPF_MEASURE_NS_N4_SESS_MODIFY_REQ = 421,
  UPF_MEASURE_NS_N4_SESS_MODIFY_RSP,
  UPF_MEASURE_NS_N4_SESS_MODIFY_HANDLE_SUCC,
  UPF_MEASURE_NS_N4_SESS_MODIFY_HANDLE_FAIL,
  UPF_MEASURE_DNN_N4_SESS_MODIFY_REQ = 441,
  UPF_MEASURE_DNN_N4_SESS_MODIFY_RSP,
  UPF_MEASURE_DNN_N4_SESS_MODIFY_HANDLE_SUCC,
  UPF_MEASURE_DNN_N4_SESS_MODIFY_HANDLE_FAIL,
  //N4 sess delete
  UPF_MEASURE_N4_SESS_DELETE_REQ = 501,
  UPF_MEASURE_N4_SESS_DELETE_RSP,
  UPF_MEASURE_N4_SESS_DELETE_HANDLE_SUCC,
  UPF_MEASURE_N4_SESS_DELETE_HANDLE_FAIL,
  UPF_MEASURE_NS_N4_SESS_DELETE_REQ,
  UPF_MEASURE_NS_N4_SESS_DELETE_RSP,
  UPF_MEASURE_NS_N4_SESS_DELETE_HANDLE_SUCC,
  UPF_MEASURE_NS_N4_SESS_DELETE_HANDLE_FAIL,
  UPF_MEASURE_DNN_N4_SESS_DELETE_REQ,
  UPF_MEASURE_DNN_N4_SESS_DELETE_RSP,
  UPF_MEASURE_DNN_N4_SESS_DELETE_HANDLE_SUCC,
  UPF_MEASURE_DNN_N4_SESS_DELETE_HANDLE_FAIL,

  //N4 sess report, 29244 8.2.21
  UPF_MEASURE_N4_SESS_REPORT_REQ = 521,
  UPF_MEASURE_N4_SESS_REPORT_RSP,
  UPF_MEASURE_N4_SESS_REPORT_HANDLE_SUCC,
  UPF_MEASURE_N4_SESS_REPORT_HANDLE_FAIL,
  UPF_MEASURE_N4_SESS_REPORT_DLDR,
  UPF_MEASURE_N4_SESS_REPORT_USAR,
  UPF_MEASURE_N4_SESS_REPORT_ERIR,
  UPF_MEASURE_N4_SESS_REPORT_UPIR,
  UPF_MEASURE_N4_SESS_REPORT_TMIR,
  UPF_MEASURE_N4_SESS_REPORT_SESR,
  UPF_MEASURE_N4_SESS_REPORT_UISR,
  UPF_MEASURE_N4_SESS_REPORT_SPARE,
  UPF_MEASURE_NS_N4_SESS_REPORT_REQ,
  UPF_MEASURE_NS_N4_SESS_REPORT_RSP,
  UPF_MEASURE_NS_N4_SESS_REPORT_HANDLE_SUCC,
  UPF_MEASURE_NS_N4_SESS_REPORT_HANDLE_FAIL,
  UPF_MEASURE_DNN_N4_SESS_REPORT_REQ,
  UPF_MEASURE_DNN_N4_SESS_REPORT_RSP,
  UPF_MEASURE_DNN_N4_SESS_REPORT_HANDLE_SUCC,
  UPF_MEASURE_DNN_N4_SESS_REPORT_HANDLE_FAIL,

  //N4 pfd management
  UPF_MEASURE_N4_PFD_MANAGE_REQ = 601,
  UPF_MEASURE_N4_PFD_MANAGE_RSP,
  UPF_MEASURE_N4_PFD_MANAGE_HANDLE_SUCC,
  UPF_MEASURE_N4_PFD_MANAGE_HANDLE_FAIL,
  //N4 association setup
  UPF_MEASURE_N4_ASSO_SETUP_REQ = 701,
  UPF_MEASURE_N4_ASSO_SETUP_RSP,
  UPF_MEASURE_N4_ASSO_SETUP_HANDLE_SUCC,
  UPF_MEASURE_N4_ASSO_SETUP_HANDLE_FAIL,
  //N4 association update
  UPF_MEASURE_N4_ASSO_UPDATE_REQ = 801,
  UPF_MEASURE_N4_ASSO_UPDATE_RSP,
  UPF_MEASURE_N4_ASSO_UPDATE_HANDLE_SUCC,
  UPF_MEASURE_N4_ASSO_UPDATE_HANDLE_FAIL,
  //N4 association release
  UPF_MEASURE_N4_ASSO_RELEASE_REQ =901,
  UPF_MEASURE_N4_ASSO_RELEASE_RSP,
  UPF_MEASURE_N4_ASSO_RELEASE_HANDLE_SUCC,
  UPF_MEASURE_N4_ASSO_RELEASE_HANDLE_FAIL,
  //N4 revice heartbeat
  UPF_MEASURE_N4_RECV_HEARTBEAT_REQ = 1001,
  UPF_MEASURE_N4_RECV_HEARTBEAT_RSP,
  UPF_MEASURE_N4_RECV_HEARTBEAT_HANDLE_SUCC,
  UPF_MEASURE_N4_RECV_HEARTBEAT_HANDLE_FAIL,
  //N4 send heartbeat
  UPF_MEASURE_N4_SEND_HEARTBEAT_REQ = 1101,
  UPF_MEASURE_N4_SEND_HEARTBEAT_RSP,
  UPF_MEASURE_N4_SEND_HEARTBEAT_HANDLE_SUCC,
  UPF_MEASURE_N4_SEND_HEARTBEAT_HANDLE_FAIL,
  //N4 node report
  UPF_MEASURE_N4_NODE_REPORT_REQ,
  UPF_MEASURE_N4_NODE_REPORT_RSP,
  UPF_MEASURE_N4_NODE_REPORT_HANDLE_SUCC,
  UPF_MEASURE_N4_NODE_REPORT_HANDLE_FAIL,

  UPF_MEASURE_UL_RCVE_PKTS = 1201,
  UPF_MEASURE_UL_RCVE_BTYES,
  UPF_MEASURE_UL_SEND_PKTS,
  UPF_MEASURE_UL_SEND_BTYES,
  UPF_MEASURE_UL_DROP_PKTS,
  UPF_MEASURE_DL_RCVE_PKTS,
  UPF_MEASURE_DL_RCVE_BTYES,
  UPF_MEASURE_DL_SEND_PKTS,
  UPF_MEASURE_DL_SEND_BTYES,
  UPF_MEASURE_DL_DROP_PKTS,

  //Qos
  UPF_MEASURE_QOSFLOW_AVG = 1301,
  UPF_MEASURE_NS_QOSFLOW_AVG,
  UPF_MEASURE_DNN_QOSFLOW_AVG,
  UPF_MEASURE_QOSFLOW_MAX,
  UPF_MEASURE_NS_QOSFLOW_MAX,
  UPF_MEASURE_DNN_QOSFLOW_MAX,

  //Cpu Load
  UPF_MEASURE_SYSTEM_LOAD_AVE = 1401,

  //Qos Monitor
  UPF_MEASURE_PSA_UE_PER_SNSSAI_RP_AVERAGE_DELAY = 1501, /* RP: round trip, per S-NSSAI */
  UPF_MEASURE_PSA_UE_PER_SNSSAI_RP_MAX_DELAY,
  UPF_MEASURE_PSA_UE_PER_SNSSAI_UL_AVERAGE_DELAY,
  UPF_MEASURE_PSA_UE_PER_SNSSAI_UL_MAX_DELAY,
  UPF_MEASURE_PSA_UE_PER_SNSSAI_DL_AVERAGE_DELAY,
  UPF_MEASURE_PSA_UE_PER_SNSSAI_DL_MAX_DELAY,

    //5glan
    UPF_MEASURE_5GLAN_PKTS = 1601,
    UPF_MEASURE_5GLAN_BYTES,
    UPF_MEASURE_5GLAN_SESS,
    UPF_MEASURE_5GLAN_TOTAL_PKTS,
    UPF_MEASURE_5GLAN_TOTAL_BYTES,
    UPF_MEASURE_5GLAN_TOTAL_SESS,
};

enum
{
  UPF_GRAB_MSG_ACTION_ADD = 0,
  UPF_GRAB_MSG_ACTION_STOP,
  UPF_GRAB_MSG_ACTION_START,
  UPF_GRAB_MSG_ACTION_DEL,
  UPF_GRAB_MSG_ACTION_INVALID = ~0,
};

enum
{
  UPF_USER_ID_IMEI = 0,
  UPF_USER_ID_IMSI,      //IMSI/SUPI-1,
  UPF_USER_ID_MSISDN,    //MSISDN/GPSI-2
  UPF_USER_ID_IMPU,      //IMPU-3
  UPF_USER_ID_INVALID = (u8)~0,
};

enum
{
  UPF_GRAB_MSG_INTERFACE_N4 = 0,
  UPF_GRAB_MSG_INTERFACE_N3,
  UPF_GRAB_MSG_INTERFACE_N6,
  UPF_GRAB_MSG_INTERFACE_N9,
  UPF_GRAB_MSG_INTERFACE_INVALID = (u8)~0,
};

typedef struct
{
  u8 is_used;
  u8 action;
  u8 user_id_type;
  u8 user_id[UPF_STRING_LEN16];
  u8 interface_type;
  u32 ne_instance_id;
  u8 is_ip4;
  ip46_address_t src_addr;
  ip46_address_t dst_addr;
  u32 src_ip;
  u32 dst_ip;
  u16 src_port;
  u16 dst_port;
  u16 protocol;
} upf_grab_msg_t;

typedef struct
{
  u32 is_used;
  u32 urr_id;
  u8 name[UPF_STRING_LEN64]; //name of traffic group corresponding to urr
  urr_measure_t volume;
} upf_urr_flow_statics_t;

typedef struct
{
  u8 thread_name[UPF_STRING_LEN16];
  u8 active;                         //the core is used for vpp
  u32 thread_id;
  u32 cpu_id;
  f64 node_vector_rates;
  f64 cpu_usage;
} upf_cpu_usage_t;

extern upf_grab_msg_t g_upf_single_trace[UPF_U16_MAX_SIZE];
extern u32 g_single_trace_flag;
extern u32 g_local_ne_id;
extern upf_urr_flow_statics_t g_upf_urr_flow_statistics[UPF_U8_MAX_SIZE];
extern u32 g_upf_urr_flow_statistics_switch;
extern u32 g_upf_redirect_type;
extern upf_cpu_usage_t g_upf_cpu_usage[UPF_VPP_CORE_MAX];
extern u32 g_upf_cpu_usage_threshold;

typedef struct
{
  u64 utc_time;
  u32 subtle_time;
  u16 task_id;
  u16 protocol;
  ip46_address_t src_addr;
  ip46_address_t dst_addr;
  u16 src_port;
  u16 dst_port;
  u16 msg_type;
  u16 raw_data_len;
  u8 *raw_data;
  u32 pdr_id;
  u32 far_id;
  u32 ne_instance_id;
  u8 is_ip4;
  u8 direction;
  u8 interface_type;
  u8 result;
} upf_single_trace_push_t;
/* auther:by sff; data:2020.11.11; end */

/* deleted by liuyu LICENSE_SCRAP_CODE 2022-02-17 */

/*begin add by huqingyang 2021.4.30 for dns Sniffer  */
#define UPF_DNS_SNIFFER_NAME_LEN 64
#define UPF_DNS_SNIFFER_IP_LIST_MAX_NUM 4


#define UPF_DNS_SNIFFER_NOT_STATIC 0
#define UPF_DNS_SNIFFER_STATIC 1


typedef struct
{
    char strKey[UPF_DNS_SNIFFER_NAME_LEN];
    char strHostName[UPF_DNS_SNIFFER_NAME_LEN];
    u32  uiIp;
    u32  uiTimeSec;
    u32  uiStaticFlag;
} UPF_DNS_SNIFFER_TYPE;

typedef struct{
  u16 type;     /**< record type */
  u16 class;    /**< class, 1 = internet */
  u32 ttl;      /**< time to live, in seconds */
  u16 rdlength; /**< length of r */
  u8  rdata[0]; /*addr*/
} UPF_DNS_BODY_WITHOUT_NAME_T;

typedef struct
{
    u8 rule_name[50];
	u32 old_far_id;
	u32 new_far_id;
}upf_predef_alarm_t;

typedef struct
{
    u8 rule_name[50];
	u32 old_app_id[50];
	u32 new_app_id[50];
}upf_predef_appid_alarm_t;


#define DSCP_MAX   63
#define GROUP_NUM  40
#define GRANULARITY  0.05
#define OPPOSITE_GRANU 20  /* 1/GRANULARITY */
#define GRANU_DEVIATION 0.01
typedef struct
{
    u32 has_init;
    u32 resv;
    u32 percent[DSCP_MAX + 1];
} UPF_RATIO_GROUP_T;


#define UPF_MIN_PKTS        (1000)
/* n(bytes) * 8(byte->bit) / 1000(bits to kbits) * 1000000(microsecond to second) */
#define UPF_BTYE_TO_KBIT_PER_SECOND  (8000)
#define UPF_MIN_DURATION    (1000) /* 1 millisencond */
typedef struct
{
    u64 stamp;  /* microsecond, 1e-6 second */
    u64 pkts;
    u64 bytes;
    u64 kbps;   /* k bps */
}upf_bps_calc_t;

typedef struct
{
    upf_bps_calc_t prev;
    upf_bps_calc_t curr;
}upf_bps_calc_grp_t;

typedef struct upf_gw_tunnel_backup_tag
{
    char tunnel1[32];
    char tunnel2[32];
    char intface[64];
    ip46_address_t src_ip;
    ip46_address_t gw1_a;
    ip46_address_t gw2_a;
    pfcp_time_t timer;
    u8 tunnel1_added;
    u8 tunnel2_added;
    ip46_address_t dn_ser;
}upf_gw_tunnel_backup_t;

typedef struct pfcp_f_seid_ha
{
  u8 flags;
  u64 seid;
  ip4_address_t ip4;
  ip6_address_t ip6;
}pfcp_f_seid_ha_t;

typedef struct pfcp_node_id_ha
{
  u8 type;
  ip46_address_t ip;
  //u8 fqdn[128];
}pfcp_node_id_ha_t;

typedef struct upf_pfcp_session_ha
{
  u8 pdn_type;
  u32 is_add;
  u32 fib_index;
  u32 s_nssai;
  u64 up_seid;
  u64 create_session_time;
  u64 update_session_time;
  u64 session_report_time;
  pfcp_f_seid_ha_t f_seid;
  ip46_address_t up_address;
  pfcp_node_id_ha_t node_id;
}upf_pfcp_session_ha_t;

typedef struct pfcp_f_teid_ha
{
  u32 flags;
  u32 teid;
  ip4_address_t ip4;
  ip6_address_t ip6;
  u32 choose_id;
}pfcp_f_teid_ha_t;

typedef struct pfcp_sdf_filter_ha
{
  u32 flags;
  u8  flow[64];
  u32 tos_traffic_class;
  u32 spi;
  u32 flow_label;
  u32 sdf_filter_id;
  u32 app_index;
}pfcp_sdf_filter_ha_t;

typedef struct upf_ue_address
{
  u32 flags;
  ip4_address_t ip4;
  ip6_address_t ip6;
}upf_ue_address_t;

typedef struct pfcp_pdi_ha {
  u32 fields;
  u32 source_interface;
  pfcp_f_teid_ha_t f_teid;
  u32 network_instance;
  upf_ue_address_t ue_ip_address;
  u32 n_sdf_filters;
  pfcp_sdf_filter_ha_t sdf_filter[16];
  u8 application_id[32];
  u32 qfi;
  u32 source_interface_type;
}pfcp_pdi_ha_t;

typedef struct upf_pfcp_pdr_ha
{
  u32 pdr_id;
  u32 precedence;
  u8 is_active;
  pfcp_pdi_ha_t pdi;
  u32 outer_header_removal;
  u32 far_id;
  u32 urr_ids[32];
  u32 qer_ids[32];
  u8 activate_predefined_rules[32];
}upf_pfcp_pdr_ha_t;

typedef struct pfcp_redirect_information_ha_t
{
  u32 type;
  ip46_address_t ip;
  u8 uri[64];
}pfcp_redirect_information_ha_t;

typedef struct pfcp_outer_header_creation_ha
{
  u32 description;
  u32 teid;
  ip46_address_t ip;
  u32 port;
}pfcp_outer_header_creation_ha_t;

typedef struct pfcp_forwarding_policy_ha
{
  u8 identifier[64];
}pfcp_forwarding_policy_ha_t;

typedef struct pfcp_header_enrichment_ha
{
  u32 type;
  u8 name[32];
  u8 value[32];
}pfcp_header_enrichment_ha_t;

typedef struct pfcp_proxying_ha
{
  u32 flags;
}pfcp_proxying_ha_t;

typedef struct pfcp_forwarding_parameters_ha
{
  u32 flags;
  u32 destination_interface;
  u8 network_instance[32];
  pfcp_redirect_information_ha_t redirect_information;
  pfcp_outer_header_creation_ha_t outer_header_creation;
  u32 transport_level_marking;
  pfcp_forwarding_policy_ha_t forwarding_policy;
  pfcp_header_enrichment_ha_t header_enrichment;
  u32 linked_traffic_endpoint_id;
  pfcp_proxying_ha_t proxying;
  u32 destination_interface_type;
}pfcp_forwarding_parameters_ha_t;

typedef struct pfcp_duplicating_parameters_ha
{
  u32 destination_interface;
  pfcp_outer_header_creation_ha_t outer_header_creation;
  u32 transport_level_marking;
  u8 forwarding_policy[32];
}pfcp_duplicating_parameters_ha_t;

typedef struct upf_pfcp_far_ha
{
  u32 far_id;
  u32 apply_action;
  pfcp_forwarding_parameters_ha_t forwarding_parameters;
  pfcp_duplicating_parameters_ha_t duplicating_parameters;
  u32 bar_id;
}upf_pfcp_far_ha_t;

typedef struct pfcp_packet_rate_ha
{
  packet_rate_t unit;
  packet_rate_t max;
}pfcp_packet_rate_ha_t;

typedef struct pfcp_dl_flow_level_marking_ha
{
  u32 flags;

  u32 tos_traffic_class;
  u32 service_class_indicator;
}pfcp_dl_flow_level_marking_ha_t;

typedef struct upf_pfcp_qer_ha {
  u32 qer_id;
  u32 flags;
  u32 qer_correlation_id;
  pfcp_gate_status_t gate_status;
  pfcp_bit_rate_t mbr;
  pfcp_bit_rate_t gbr;
  pfcp_packet_rate_ha_t packet_rate;
  pfcp_dl_flow_level_marking_ha_t dl_flow_level_marking;
  u32 qos_flow_identifier;
  u32 reflective_qos;
  u32 paging_policy_indicator;
  u32 averaging_window;
}upf_pfcp_qer_ha_t;

typedef struct vl_api_pfcp_volume_ie
{
  urr_counter_t packets;
  urr_counter_t bytes;
  urr_counter_t consumed;
}pfcp_measure_ie_t;

typedef struct pfcp_aggregated_urrs_ha
{
  u32 aggregated_urr_id;
  pfcp_multiplier_t multiplier;
}pfcp_aggregated_urrs_ha_t;

typedef struct upf_pfcp_urr_ha
{
  u32 urr_id;
  u32 measurement_method;
  u32 reporting_triggers;
  u32 measurement_period;
  pfcp_measure_ie_t volume_measure;
  urr_counter_t volume_threshold;
  urr_counter_t volume_quota;
  u32 time_threshold;
  u32 time_quota;
  u32 quota_holding_time;
  u32 dropped_dl_traffic_threshold;
  u32 monitoring_time;
  u32 subsequent_volume_threshold;
  u32 subsequent_time_threshold;
  u32 inactivity_detection_time;
  u32 linked_urr_id;
  pfcp_measurement_information_t measurement_information;
  pfcp_time_quota_mechanism_t time_quota_mechanism;
  pfcp_aggregated_urrs_ha_t aggregated_urrs;
  u32 far_id_for_quota_action;
  u32 ethernet_inactivity_timer;
}upf_pfcp_urr_ha_t;

typedef struct upf_pfcp_bar_ha
{
  u32 bar_id;
  u32 downlink_data_notification_delay;
  u32 dl_buffering_duration;
  u32 dl_buffering_suggested_packet_count;
  u32 suggested_buffering_packets_count;
}upf_pfcp_bar_ha_t;

typedef struct upf_pfcp_dnn_ha {
  u8 dnn[128];
}upf_pfcp_dnn_ha_t;

typedef struct upf_pfcp_userid_ha
{
  u32 flags;
  u32 imei_len;
  u8 imei[8];
  u8 imei_str[16];
  u32 imsi_len;
  u8 imsi[8];
  u8 imsi_str[16];
  u32 msisdn_len;
  u8 msisdn[8];
  u8 msisdn_str[16];
}upf_pfcp_userid_ha_t;

typedef struct upf_pfcp_ha_sync_sess {
    u32 n_pdrs;
    u32 n_fars;
    u32 n_qers;
    u32 n_urrs;
    u32 n_bars;
    upf_pfcp_session_ha_t session;
    upf_pfcp_pdr_ha_t pdrs[30];
    upf_pfcp_far_ha_t fars[30];
    upf_pfcp_qer_ha_t qers[10];
    upf_pfcp_urr_ha_t urrs[30];
    upf_pfcp_bar_ha_t bars[10];
    upf_pfcp_dnn_ha_t dnn;
    upf_pfcp_userid_ha_t userid;
}upf_pfcp_ha_sync_sess_t;

typedef struct pfcp_ha_node_id {
  u32 type;
  ip46_address_t ip;
  u8 fqdn[128];
}pfcp_ha_node_id_t;

typedef struct up_ip_resource_info_global {
  u32 source_intf;
  ip4_address_t ip4;
  ip6_address_t ip6;
  u8 network_instance[64];
  u64 teid_range_acct;
  u64 teid_range_status_low;
  u64 teid_range_status_high;
}up_ip_resource_info_global_t;

typedef struct up_ip_resource_info_per_intf {
  u32 flags;
  u32 teid_range_indication;
  u32 teid_range;
  u32 source_intf;
  ip4_address_t ip4;
  ip6_address_t ip6;
  u8 network_instance[64];
}up_ip_resource_info_per_intf_t;

typedef struct pfcp_up_ip_resource_info {
  u32 up_ip_resource_info_global_count;
  up_ip_resource_info_global_t up_ip_resource_info_global[4];
  u32 up_ip_resource_info_per_intf_count;
  up_ip_resource_info_per_intf_t up_ip_resource_info_per_intf[4];
}pfcp_up_ip_resource_info_t;

typedef struct upf_pfcp_ha_association {
  u32 client_index;
  u32 context;
  u32 is_add;
  u32 fib_index;
  ip46_address_t rmt_addr;
  ip46_address_t lcl_addr;
  pfcp_ha_node_id_t node_id;
  u32 recovery_time_stamp;
  u32 up_recovery_time_stamp;
  u32 cp_function_features;
  u32 up_function_features;
  pfcp_up_ip_resource_info_t up_ip_resource_info;
}upf_pfcp_ha_association_t;

typedef struct upf_ha_pfd_content {
  u8 flags;
  u8 flow_description[128];
  u8 url[32];
  u8 domain[16];
  u8 custom[8];
}upf_ha_pfd_content_t;

typedef struct upf_redis_get_fail_list {
  u8 flags;
  u8 cnt;
  char key[128];
}upf_redis_get_fail_list_t;

typedef struct upf_pfcp_ha_pfd_management {
  u8 appid[32];
  u32 n_pfd_contents;
  upf_ha_pfd_content_t pfd_contents[8];
}upf_pfcp_ha_pfd_management_t;

// add for gtp-u filter by liupeng on 2022-10-31 below
typedef struct
{
    u32 if_index;
    u8 if_index_flag;
    char str_msgtype[32];
    u8 msgtype;
    #define GTPU_ECHO_REQ BIT (0)
    #define GTPU_ERR_INDICATION BIT (1)
    #define GTPU_ENDMARK BIT (2)
    #define GTPU_T_PDU BIT (3)

    ip46_address_t srcAddr;
    u8 srcaddr_flag;
    u32 countnum[5];
    u8 filter_type;
    #define IF_INDEX_TYPE BIT (0)
    #define SRC_ADDR_TYPE BIT (1)
} upf_gtpu_filter_t;

typedef enum
{
    GTPU_RULE_ACTION_ALLOW = 0,
    GTPU_RULE_ACTION_DROP,
} upf_gtpu_filter_action_t;

typedef union
{
    uword p;
    struct
    {
        u32 sess_idx2;
        u32 sess_idx1;
    };
}upf_imsi_to_sess_idxs_t;

typedef enum
{
    UPF_PROCESS_IP_NEXT_DROP,
    UPF_PROCESS_IP_NEXT_GTP_IP4_ENCAP,
    UPF_PROCESS_IP_NEXT_GTP_IP6_ENCAP,
    UPF_PROCESS_IP_NEXT_IP4_INPUT,
    UPF_PROCESS_IP_NEXT_IP6_INPUT,
    UPF_PROCESS_IP_NEXT_IP4_LOOKUP,
    UPF_PROCESS_IP_NEXT_IP6_LOOKUP,
    UPF_PROCESS_IP_NEXT_IP4_LOCAL,
    UPF_PROCESS_IP_NEXT_IP6_LOCAL,
    UPF_PROCESS_IP_NEXT_IP4_FRAG,
    UPF_PROCESS_IP_NEXT_IP6_FRAG,
    UPF_PROCESS_IP_NEXT_VXLAN4_ENCAP,
    UPF_PROCESS_IP_NEXT_VXLAN6_ENCAP,
    UPF_PROCESS_IP_NEXT_UPF_5GLAN_IP4,
    UPF_PROCESS_IP_NEXT_UPF_5GLAN_IP6,
    UPF_PROCESS_IP_NEXT_N6_ENCAP,
    UPF_PROCESS_IP_NEXT_L2_INPUT,
    UPF_PROCESS_IP_NEXT_LAST,
}upf_process_next_t;

#define GTPU_TYPE_ALL 255
// add for gtp-u filter by liupeng on 2022-10-31 above

//add upf sys event
#define EVENT_STR_LEN   64
typedef enum {
    EVENT_INVALID,
    INTERFACE_N4_STATUS_DOWN_PRODEUCE,
    INTERFACE_N4_STATUS_UP_RECOVER,
    INTERFACE_N3_STATUS_DOWN_PRODEUCE,
    INTERFACE_N3_STATUS_UP_RECOVER,
    INTERFACE_N6_STATUS_DOWN_PRODEUCE,
    INTERFACE_N6_STATUS_UP_RECOVER,
    HA_STATUS_HANDOVER,
    UPF_START,
    UPF_RESTART,
    UPF_ABORT,
    BUFFERS_NO_AVALIABLE,

    EVENT_MAX,
}UPF_SYS_EVENT;
typedef struct upf_sys_event_s {
    u32 event_id;
    u8 event_str[EVENT_STR_LEN];
}upf_sys_event_t;

u64  iupf_bps_calc(u32 dir, u32 pkt_len);
int iupf_overload_dscp_shape(f64 ratio, u8 dscp);
int iupf_overload_pfcp_shape(u8 prior);
void iupf_bps_refreash();


int upf_dns_sniffer_hand_msg(vlib_buffer_t *vpbuff, u8 is_ip4);
u32 upf_dns_sniffer_add(UPF_DNS_SNIFFER_TYPE *vpstDnsSniffer);
u32 upf_dns_sniffer_del_by_key(char *vstrKey);
u32 upf_dns_sniffer_get_by_key(char *vstrKey, UPF_DNS_SNIFFER_TYPE *vpstDnsSniffer);
u32 upf_dns_sniffer_if_catch_by_name_addr(char *vstrName, u32 vIpAddr, u32 *vpFlag);
u32 upf_dns_sniffer_aging_by_key(char *vstrKey);
u32 upf_dns_sniffer_get_name_by_ip(u32 vuiIp, char *vpstrName);
u32 upf_dns_sniffer_get_name_by_url(char *vpstrUrl, char *vpstrName);
u32 upf_ip6_address_is_equal (const ip6_address_t * a, const ip6_address_t * b, u8 len);



extern uword *gHasUpfDnsSnifferBuff;
extern u32    gHasUpfDnsSnifferTtl ; //12 h = 43200 sec
extern u32    gHasUpfDnsSnifferMaxNumb;
extern u32    gHasUpfDnsSnifferCount;



/*end add by huqingyang 2021.4.30 for dns Sniffe */


void iupf_update_ha_status (u32 status);
int iupf_enable_disable (upf_main_t *sm, int enable_disable);
u8 *format_upf_encap_trace (u8 *s, va_list *args);
void upf_gtpu_send_end_marker (upf_far_t *far, struct rules *active);
u32 upf_gtpu_send_qmp_dummy_gtp (pfcp_qfi_t qfi, u32 sx_idx);
void upf_gtpu_send_echo_req (u32 id);
void upf_pfcp_resource_cache_fill (void);
void pfcp_send_rpc_to_thread (u32 pfcp_thread_index, void *fp, void *rpc_args);
clib_error_t *upf_http_server_main_init (vlib_main_t *vm);
int vnet_iupf_pfcp_endpoint_add_del (ip46_address_t *ip, u32 fib_index, u8 add);
int vnet_iupf_ue_stat_add_del(u8 *imsi, bool is_create, u64 up_seid, u64 create_time, u64 delete_time);
int vnet_iupf_upip_add_del (u8 *name, upf_upip_res_t *res, u8 add);
u8 *iupf_name_to_labels (u8 *name);
int vnet_iupf_nwi_add_del (u8 *name, u32 vrf, nwi_volume_t volume, u8 add, u32 sw_if_idx, u8 vxlan_idx, u8 is_vxlan4, u32 s_nssai, u32 vlan_id);
void upf_buffer_hex_print (void *buffer, int length);

void upf_interface_traffic_statistic (upf_session_t *sess, u32 pfcp_thread_index, upf_pdr_t *pdr, upf_far_t *far, u32 packet_length, u32 current_time, u8 is_ip4);
void upf_traffic_bandwidth_calculate(upf_bandwith_t *bandwidth, u32 used_time);
void upf_interface_drop_statistic (upf_session_t *sess, u32 pfcp_thread_index, upf_pdr_t *pdr, upf_far_t *far, u32 packet_length, u32 is_upf_process, u8 is_ip4);
void upf_alarm_notify(u32 alarm_id, u32 alarm_type, u32 alarm_level, void *data);
u32 upf_alarm_search (u32 alarm_type, void *data);
u32 upf_add_del_alarm_key (u32 alarm_type, void *data, u8 is_add);
f64 upf_get_total_cpu_usage();
void upf_alarm_init();
void upf_cpu_usage_init();
void iupf_get_total_statistics_data(upf_performance_measurement_t *upf_stat);
void upf_dp_grab_msg_push(vlib_buffer_t *buffer, upf_session_t *sess, upf_single_trace_push_t *grab, u8 is_ip4);
void upf_dp_grab_msg_update(upf_session_t *sess, upf_single_trace_push_t *grab, u8 is_ip4);

// Add for eth single trace by liupeng on 2022-06-27 below
void upf_dp_grab_eth_msg_push(vlib_buffer_t *buffer, upf_session_t *sess, upf_single_trace_push_t *grab);
// Add for eth single trace by liupeng on 2022-06-27 above

/* begin mod by liuyu 2021-08-05 */
int iupf_whitelist_upf_ip_add_del (vlib_main_t *vm, ip46_address_t *ip, u32 flags, u8 *name, u8 *url, u8 *numseg, u8 isopen, u8 *action, u8 fraud_check_flag, u8 sni_check_flag, u8 encryp_flag, u8 algorithm_flag, u8 *encryp_key, u8 encryp_key_flag, u16 sub_extension_flag, u8 extension_flag, u32 predefind_id, u8 *rat_type, u8 *uli, u16 encryp_extension_flag, u8 *msisdn_enr_header_str, u8 *user_ip_enr_header_str, u8 *imsi_enr_header_str, u8 *imei_enr_header_str, u8 *uli_enr_header_str, u8 *sgwc_ip_enr_header_str, u8 *smf_ip_enr_header_str, u8 *timestamp_enr_header_str, u8 *rat_type_enr_header_str, u8 *dnn_enr_header_str, u16 enr_header_flag, u8 tcp_number_modify_flag, u8 add);
/* end mod by liuyu 2021-08-05 */
u8 get_3gpp_interface_type(pfcp_tgpp_interface_type_t intfc_type);
void upf_nwi_traffic_statistic (upf_pdr_t *pdr, upf_far_t *far, u16 packet_length, u8 is_drop, u8 is_recv);
void upf_dnn_traffic_statistic (vlib_buffer_t *b, upf_pdr_t *pdr, upf_far_t *far, u16 packet_length, u8 is_drop, u8 is_recv);

void upf_pfcp_statistic       (u32 msg_type, pfcp_cause_t cause, u8 rx_tx_flag);
void upf_nwi_pfcp_statistic       (u32 s_nssai, u32 msg_type, pfcp_cause_t cause, u8 rx_tx_flag);
void upf_dnn_pfcp_statistic       (u8 *dnn, u32 msg_type, pfcp_cause_t cause, u8 rx_tx_flag);

upf_nwi_t *upf_lookup_nwi_by_s_nssai (u32 s_nssai);




u32 upf_nwi_mbr_process (upf_pdr_t *pdr, upf_far_t *far, u32 len);
int upf_check_session_same_ueip(upf_session_t *sess);
int upf_ip46_address_fib_cmp (const void *a0, const void *b0);

u32 upf_process_get_sess_pdr_far(vlib_buffer_t *b, upf_session_t **sess, struct rules **active, upf_pdr_t **pdr, upf_far_t **far);

// Add for Bw rule by liupeng on 2021-08-13 below
void upf_bw_rule_traffic_statistic (upf_bw_rule_measurement_t *upf_bw_rule_stat, u32 direction, u32 packet_length, u32 current_time);
// Add for Bw rule by liupeng on 2021-08-13 above
extern u8 g_upf_nwi_stat_flag;
extern u8 g_upf_nwi_flow_control_flag;

extern u8 g_upf_dnn_stat_flag;
extern upf_sys_event_t __clib_unused upf_sys_event[EVENT_MAX - EVENT_INVALID + 1];

u32 upf_ip_session_instance_alloc ();
void upf_ip_session_instance_free (u32 index);

enum
{
  N3_INTTEFACE = 1,
  N6_INTTEFACE,
  N9_INTTEFACE,
  BUFF_INTERFACE,
};

typedef struct
{
    char *name;
} upf_switch_str_t;

typedef enum
{
    KL_SWITCH_UEIP_DUPLICATE,
    KL_DUPLICATE_BUF,
    KL_SWITCH_DST_NAT66,     /* neil.fan@******** add NAT N6 DN IP */
    KL_SWITCH_KEEP_GTP_PEER, /* neil.fan@******** add preconfig gtp peer for delay measurement by gtp echo/reply */
    KL_SWITCH_GTPU_PATH_QOS_REPORT,

    KL_SWITCH_PDR_DETECT_DNS,
    KL_SWITCH_PDR_DETECT_HTTP,
    KL_SWITCH_PDR_DETECT_APPID,
    KL_SWITCH_EXACT_ROUTING,
    KL_SWITCH_REASS_GTP_DECAP_INNER,
    KL_SWITCH_UL_SW_IDX_LOCAL0,
    KL_SWITCH_TEST06,
    KL_SWITCH_TEST07,
    KL_SWITCH_TEST08,
    KL_SWITCH_TEST09,
    KL_SWITCH_TEST10,

    KL_SWITCH_NUM,
} upf_key_log_switch_type_t;
#define KEY_LOG_SWITCH(x) ISSET_BIT (g_upf_main.key_log_switch, x)
void iupf_key_log_switch_init (void);

always_inline upf_per_pfcp_thread_t *
upf_get_per_pfcp ()
{
  upf_main_t *um = &g_upf_main;
  uword thread_index = vlib_get_thread_index ();
  u32 pfcp_thread_index = thread_index - um->first_pfcp_thread_index;
  if (pfcp_thread_index == ~0)
  {
      pfcp_thread_index = 0;
  }

  return um->per_pfcp_thread[pfcp_thread_index];
}

always_inline upf_per_oam_thread_t *
upf_get_per_oam ()
{
  upf_main_t *um = &g_upf_main;
  uword thread_index = vlib_get_thread_index ();

  u32 oam_thread_index = thread_index - um->first_oam_thread_index;

  //main thread
  if (thread_index == 0 )
  {
      oam_thread_index = 0;
  }
  else
  {
    //per oam thread
  }
   
  return um->per_oam_thread[oam_thread_index];
}


#define is_v4_packet(_h) ((*(u8 *)_h) & 0xF0) == 0x40
#define is_v6_packet(_h) ((*(u8 *)_h) & 0xF0) == 0x60

#include "flowtable.h"

#define APPEND_U_INDEX(_point_idx, _index) do {                       \
        vec_alloc (_point_idx, 1);                                    \
        *vec_end (_point_idx) = _index;                               \
        _vec_len (_point_idx)++;                                      \
    } while (0)

#define APPEND_NEW_MEMBER(_point_idx, _new) do {                      \
        vec_alloc (_point_idx, 1);                                    \
        _new = vec_end (_point_idx);                                  \
        memset(_new, 0, sizeof(*_new));                               \
        _vec_len (_point_idx)++;                                      \
    } while (0)

#define APPEND_NEW_MEMBER_WITHOUT_CLEAR(_point_idx, _new) do {        \
        vec_alloc (_point_idx, 1);                                    \
        _new = vec_end (_point_idx);                                  \
        _vec_len (_point_idx)++;                                      \
    } while (0)

#define IS_BROADCAST(ip46, is_ip4) ((is_ip4) ? \
    (ip4_address_is_global_broadcast(&(ip46)->ip4)) : 0)

#define IS_MULTICAST(ip46, is_ip4) ((is_ip4) ? \
    (ip4_address_is_multicast(&(ip46)->ip4)) : (ip6_address_is_multicast(&(ip46)->ip6)))

/* neil.fan@20220310 fix issue "compare with mask not exact division(eg:mask = 27)", and move here as inline function */
static_always_inline int ip4_gateway_is_equal (const ip4_address_t *ip_a, const ip4_address_t *ip_b, u16 mask_n)
{
#define MASK_32 0xFFFFFFFF
    if (PREDICT_FALSE(!ip_a || !ip_b))
        return 0;

    if (PREDICT_FALSE(!mask_n))
        return 1; /* equals */

    if (PREDICT_FALSE(mask_n > 32))
        mask_n = 32;

    return (((clib_host_to_net_u32(ip_a->as_u32)) & (MASK_32 << (32 - mask_n))) \
        == ((clib_host_to_net_u32(ip_b->as_u32)) & (MASK_32 << (32 - mask_n))));
}

/* neil.fan@20220310 fix issue "compare with mask not exact division(eg:mask = 27)", and move here as inline function */
static_always_inline int ip6_gateway_is_equal (const ip6_address_t *ip_a, const ip6_address_t *ip_b, u16 mask_n)
{
#define MASK_64 0xFFFFFFFFFFFFFFFF
    if (PREDICT_FALSE(!ip_a || !ip_b))
        return 0;

    if (PREDICT_FALSE(!mask_n))
        return 1; /* equals */

    if (PREDICT_FALSE(mask_n > 128))
        mask_n = 128;

    if (mask_n <= 64)
    {
        return (((clib_host_to_net_u64(ip_a->as_u64[0])) & (MASK_64 << (64 - mask_n))) \
            == ((clib_host_to_net_u64(ip_b->as_u64[0])) & (MASK_64 << (64 - mask_n))));
    }
    else
    {
        return ((ip_a->as_u64[0] == ip_b->as_u64[0]) ? \
            (((clib_host_to_net_u64(ip_a->as_u64[1])) & (MASK_64 << (128 - mask_n))) \
                == ((clib_host_to_net_u64(ip_b->as_u64[1])) & (MASK_64 << (128 - mask_n)))) : 0);
    }
}

static_always_inline upf_session_t *sx_get_by_index(u32 sx_index)
{
    upf_session_t *sx = NULL;

    if ((~0 != sx_index) && (!pool_is_free_index(g_upf_main.sessions, sx_index)))
    {
        sx = pool_elt_at_index (g_upf_main.sessions, sx_index);
        if (PREDICT_FALSE(sx && (sx->flags & SX_DELETING)))
        {
            return NULL;
        }
    }

    return sx;
}

static_always_inline upf_vnip_sessions_t *vnip_sx_get_by_index(u32 sx_index)
{
    upf_vnip_sessions_t *sx = NULL;

    if ((~0 != sx_index) && (!pool_is_free_index(g_upf_main.vnip_sessions, sx_index)))
        sx = pool_elt_at_index (g_upf_main.vnip_sessions, sx_index);

    return sx;
}

static_always_inline upf_nwi_t *nwi_get_by_index(u32 nwi_idx)
{
    upf_nwi_t *nwi = NULL;

    if ((~0 != nwi_idx) && (!pool_is_free_index(g_upf_main.nwis, nwi_idx)))
        nwi = pool_elt_at_index (g_upf_main.nwis, nwi_idx);

    return nwi;
}

static_always_inline void obtain_ip_from_buffer(vlib_buffer_t *b, u32 is_v6, ip46_sd_t *ip46)
{
    if (is_v6)
    {
        ip6_header_t *ip6 = vlib_buffer_get_current (b);
        ip46_address_set_ip6(&ip46->src, &ip6->src_address);
        ip46_address_set_ip6(&ip46->dst, &ip6->dst_address);
    }
    else
    {
        ip4_header_t *ip4 = vlib_buffer_get_current (b);
        ip46_address_set_ip4(&ip46->src, &ip4->src_address);
        ip46_address_set_ip4(&ip46->dst, &ip4->dst_address);
    }
}
u8 *format_ip46_sd (u8 * s, va_list * args);

void *upf_eth_next_header(void *data, u16 *eth_type);

static_always_inline void upf_vlan_encap(u8 *l2_hdr, ethernet_vlan_header_tv_t *tv)
{
    if (PREDICT_FALSE(!l2_hdr || !tv))
        return;

    memmove(l2_hdr - 4, l2_hdr, 12); /* dst mac + src mac len is 12, vlan header len is 4 */

    u16 *p = (u16 *)(l2_hdr - 4 + 12);
    p[0] = clib_net_to_host_u16(tv->type);
    p[1] = clib_net_to_host_u16(tv->priority_cfi_and_id);
}

static_always_inline void upf_vlan_buffer_encap(vlib_buffer_t *b, ethernet_vlan_header_tv_t *tv)
{
    if (PREDICT_FALSE(!b || !tv))
        return;

    upf_vlan_encap(vlib_buffer_get_current(b), tv);
    vlib_buffer_advance (b, -4);
}

static_always_inline void upf_vlan_decap(u8 *l2_hdr)
{
    if (l2_hdr)
    {
        u8 mac[12]; /* dst mac + src mac len is 12, vlan header len is 4 */
        clib_memcpy_fast(mac, l2_hdr, sizeof(mac));
        clib_memcpy_fast(l2_hdr + 4, mac, sizeof(mac));
    }
}
static_always_inline void upf_vlan_decap_twice(u8 *l2_hdr)
{
    if (l2_hdr)
    {
        u8 mac[12]; /* dst mac + src mac len is 12, vlan header len is 4 */
        clib_memcpy_fast(mac, l2_hdr, sizeof(mac));
        clib_memcpy_fast(l2_hdr + 8, mac, sizeof(mac));
    }
}

static_always_inline void upf_vlan_buffer_decap(vlib_buffer_t *b)
{
    if (b)
    {
        upf_vlan_decap(vlib_buffer_get_current(b));
        vlib_buffer_advance (b, 4);
    }
}
static_always_inline void upf_vlan_buffer_decap_twice(vlib_buffer_t *b)
{
    if (b)
    {
        upf_vlan_decap_twice(vlib_buffer_get_current(b));
        vlib_buffer_advance (b, 8);
    }
}

static_always_inline void upf_time_now_nsec_fraction(upf_64bit_timestamp_t *t)
{
    unix_time_now_nsec_fraction (&t->sec, &t->psec);

    /* refer to rfc5905 section 6: a 32-bit fraction field resolving 232 picoseconds. */
#if 0
    u64 val = t->psec;
    t->psec = (u32)(val * 1000 / 232);
#else
    t->psec = t->psec << 2; /* (1000 / 232) nearly 4, i.e. << 2 */
#endif
}

static_always_inline u32 upf_time_psec_to_milli_sec (u32 psec)
{
    return ((psec >> 2) / 1000000); /* picosecond -> nanosecond -> microsecond -> milisecond */
}

static_always_inline void upf_64bit_timestamp_endian (upf_64bit_timestamp_t *t)
{
    t->sec = clib_host_to_net_u32(t->sec);
    t->psec = clib_host_to_net_u32(t->psec);
}

void getRatType(unsigned char *rat_type_str, pfcp_cmcc_rat_type_t rat_type);

int upf_https_client_hello_is_segment(vlib_main_t *vm,vlib_buffer_t *b);
int upf_is_https_client_hello (u8 **payload, word *len);
int upf_is_http_request (u8 **payload, word *len);
u16 upf_get_eth_type(u8 *data);
void upf_get_eth_vlan_id(u8 *l2_hdr, u16 *c_vid, u16 *s_vid);
void upf_get_eth_info(u8 *l2_hdr, u16 *c_vid, u16 *s_vid, u16 *eth_type);
void upf_l2_key_get(u8 *l2_hdr, upf_l2_key_t *key, u32 is_dst, u32 nwi_idx);
u32 upf_get_n6_nwi_by_sess_idx(u32 sess_idx);
u32 upf_get_spec_far(upf_session_t *sx, u8 dst_intf, upf_far_t **p);
u32 upf_get_spec_pdr_far(upf_session_t *sx, u8 src_intf, u32 *pdr_index, u8 dst_intf, upf_far_t **far0);
u8 *format_duplicated_ueip_switch (u8 *s);
u32 upf_get_encap_qos_info (struct rules *rule, u32 *qer_ids, pdu_sess_info_qos_t *pdu_qos);
u32 upf_pdu_session_container_ul_decode(pdu_sess_container_ex_t *container, ul_pdu_sess_info_t *info);
u8 upf_pdu_session_container_dl_encode (gtpu_flags_t *gtp_flags, pdu_sess_container_ex_t *container, pdu_sess_info_qos_t *pdu_qos);

upf_s_nssai_t *upf_s_nssai_lookup(u32 s_nssai);
void upf_s_nssai_add_del (upf_s_nssai_t *t, u32 is_add);
void upf_psa_delay_timer_init (void);
void upf_psa_delay_per_s_nssai_record (upf_psa_delay_per_snssai_t *n, pfcp_qos_monitoring_measurement_t *measure);
u8 *upf_format_delay_per_s_nssai (u8 *s, va_list *args);
u8 *format_rfc5905_u64_timestamp(u8 *s, va_list *args);
u8 *format_rfc5905_64bit_timestamp(u8 *s, va_list *args);

u32 upf_get_current_total_pps(vlib_main_t *vm);
void iupf_show_s_nssai_statistics_command_fn (vlib_main_t *vm);

u32 upf_dst_nat66_map (ip6_address_t *dst_ip);
u32 upf_dst_nat66_revert (ip6_address_t *src_ip);
void upf_nat_update_checksums(vlib_main_t * vm, vlib_buffer_t *b, int is_ip4);

// Add for 5G TSN function-cmd managment by liupeng on 2024-02-22 below
typedef struct
{
    u32 sx_if_index;
    mac_address_t *ue_macs;
} uemac_nwtt_relation_t;

void ptp_transfer_encap_gtp_header(vlib_buffer_t *b0, upf_far_t *far, pdu_sess_info_qos_t *pdu_qos, u32 *next);

void send_sx_msg_to_redis_thread (upf_send_msg_t *msg);

// Add for 5G TSN function-cmd managment by liupeng on 2024-02-22 above

void upf_statistics_update_timer (vlib_main_t *vm);
void upf_statistics_timer_init();
void upf_global_statistics_timer_init();
void upf_status_report_timer_init();
void get_total_ue_stats(upf_ue_stat_t *ue_stat, upf_pdu_sess_stat_t *upf_pdu_sess_stat);
void upf_set_report_OMC_default_node_hash();

#endif /* __included_upf_h__ */

/*
 * fd.io coding-style-patch-verification: ON
 *
 * Local Variables:
 * eval: (c-set-style "gnu")
 * End:
 */
