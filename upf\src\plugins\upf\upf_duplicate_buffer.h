#include <vppinfra/error.h>
#include <vppinfra/hash.h>
#include <vnet/vnet.h>
#include <vnet/ip/ip.h>
#include <vnet/fib/ip4_fib.h>
#include <vnet/fib/ip6_fib.h>
#include <vnet/ethernet/ethernet.h>

#include <upf/upf.h>
#include "flowtable.h"


typedef struct
{
    u64 up_seid;
    u64 cp_seid;
    u32 pdr_id;
    u32 far_id;
    u32 duplicate_num;
    u8 packet_data[64 - 1 * sizeof (u32)];
} upf_duplicate_trace_t;

typedef struct
{
  flow_key_t key;
  u32 flow_idx;
  u32 sw_if_index;
  u32 next_index;
  u32 dupl_num;
  u32 pdr_index_0;
  u32 pdr_index_1;
  u8 packet_data[64 - 1 * sizeof (u32)];
} flow_trace_t;

typedef struct
{
  u64 up_seid;
  u64 cp_seid;
  u32 pdr_id;
  u32 far_id;
  u8 packet_data[64 - 1 * sizeof (u32)];
  u8 cached;
  u32 dupl_num; // 0 is original flow
  u32 next;
} upf_dup_trace_t;

int handle_far_dupli_para_policy( pfcp_forwarding_policy_t forwarding_policy, upf_far_duplicate_t *duplicating);
                       
int handle_far_duplicating_para(upf_session_t *sx, upf_far_t *far, pfcp_create_far_t *create);

int handle_far_update_duplicating_para(upf_session_t *sx, upf_far_t *far, pfcp_update_far_t *update);

u8 *format_pre_duplicate_far (u8 *s, va_list *args);

void ip_udp_gtpu_duplicate_rewrite (upf_far_duplicate_t *fd, int is_ip4);
void process_duplicate_buffer(vlib_main_t *vm, vlib_node_runtime_t *node,  
                       u32 is_ip4, vlib_buffer_t *b, upf_pdr_t *pdr0,
                       upf_far_t *far0, u32 is_flow_cache, clib_bihash_kv_64_8_t *kv, u32 flow_idx);

