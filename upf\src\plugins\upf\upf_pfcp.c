/*
 * Copyright (c) 2017 Travelping GmbH
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <inttypes.h>
#include <netinet/ip.h>
#include <search.h>
#include <setjmp.h>
#include <signal.h>
#include <stdio.h>
#include <vlib/vlib.h>
#include <vnet/fib/fib_entry.h>
#include <vnet/fib/fib_table.h>
#include <vnet/fib/ip4_fib.h>
#include <vnet/fib/ip6_fib.h>
#include <vnet/ip/ip6_hop_by_hop.h>

#include <vnet/ip/format.h>
#include <vnet/ip/ip.h>
#include <vppinfra/clib.h>
#include <vppinfra/mem.h>
#include <vppinfra/pool.h>

#include "upf_https_client_hello.h"

#include "rcu.h"
#include "pfcp.h"
#include "upf.h"
#include "upf_pfcp.h"
#include "upf_pfcp_api.h"
#include <upf/upf_5glan.h>
#include <upf/upf_5glan_eth.h>
#include <upf/upf_eth_broadcast.h>
#include <upf/upf_eth_multicast.h>
#include <upf/flowtable_eth_node.h>
#include "upf_frer.h"

#define PFCP_CACHE_FILL_SZ 256
#define CLEAN_1BIT(x, n) ((x) = ((x) & (~(0x1 << (n)))))

upf_main_t g_upf_main;

/* only modified by pfcp thread */
upf_alarm_state_t g_upf_alarm_state[UPF_ALARM_MAX - UPF_ALARM_BASE];
u32 g_upf_alarm_switch;
u32 g_upf_alarm_id_switch[UPF_ALARM_MAX - UPF_ALARM_BASE];
upf_alarm_main_t g_upf_alarm_db;

u32 g_upf_alarm_threshold[UPF_ALARM_MAX - UPF_ALARM_BASE];

u32 g_upf_dpi_switch = 1;

u32 g_upf_l2tp_switch;
u32 g_upf_l2tp_authen_type;  //0 chap  1 pap   liukang add 2022/08/17

//apndnn check switch
u32 g_upf_apndnn_check_switch = SWITCH_OFF;

// dnn func switch. default off
u32 g_upf_dnn_switch = SWITCH_OFF;
// ue communication based on dnn. default off
u32 g_upf_dnn_ue_ping_switch = SWITCH_OFF;

u32 g_upf_s_nssai_switch;

//padding pkts statistics control switch; default is on;
u32 g_upf_padding_switch = SWITCH_ON;

// duplicated ueip delete switch
u32 g_upf_duplicated_ueip_delete = DUPLICATE_REPLACE;

//auxiliary command switch, off during performance test and on during function test
u32 g_upf_auxiliary_switch = UPF_ALL_OFF_SWITCH;
// upf global rule group switch
u32 g_upf_rg_switch = 0;

//The number of repetitions at the same node when the data message appears in the loop
u8 g_upf_message_repeat_times = 30;

/* global flow overload protection function switch */
upf_flow_overload_protect_t g_upf_flow_overload_protect;

/* base interface flow overload protect */
upf_int_flow_ovlp_t g_upf_int_flow_ovlp;

/* global flow overload alarm function switch */
upf_flow_overload_alarm_t g_upf_flow_overload_alarm;

/* prevent ue dos attacks (TCP SYN packet) */
upf_dos_syn_t g_upf_dos_syn;

// signaling trace
upf_grab_msg_t g_upf_single_trace[UPF_U16_MAX_SIZE];
u32 g_single_trace_flag;
u32 g_local_ne_id;   // ID assigned by the oam to the NE

upf_urr_flow_statics_t g_upf_urr_flow_statistics[UPF_U8_MAX_SIZE];
// switch : DPI identifies packets based on URRID statistics (UE is not distinguished)
u32 g_upf_urr_flow_statistics_switch = 1; /* statistics baosed on dpi recognition */

u32 g_upf_redirect_type = 0; /* 0:permanent; 1:once */

upf_cpu_usage_t g_upf_cpu_usage[UPF_VPP_CORE_MAX];
u32 g_upf_cpu_usage_threshold = 70;

u32 g_upf_qer_mbr_offset = 0;

uword* g_dns_sniffer_rule_hash = NULL;
u32 g_dns_ageing_ttl = 1440;
dns_rule_rr_t *g_dns_sniffer_rule = NULL;

upf_plmn_t g_home_plmn;

extern u64 g_upf_seid_ha_id;
u32 g_alloc_fixed_seid_switch = SWITCH_OFF;

u32 g_upf_anonymization_switch = SWITCH_OFF;

/* neil.fan@20211220 add increment value for allocate unique seid */
#define PFCP_THREAD_MAX 8
static u64 g_pfcp_thread_inc[PFCP_THREAD_MAX];
extern u32 g_upf_dscp_shape_switch;

extern u8 upf_graylist_num;

u32 g_upf_qmp_sync_mode = UPF_QMP_NOT_TIME_SYNCHRONIZED;

extern u8 g_http_header_enrichment_whitelist_flag;
extern u8 g_https_header_enrichment_whitelist_flag;
extern u8 g_http_header_enrichment_graylist_flag;
extern u8 g_https_header_enrichment_graylist_flag;


static void upf_vrf_ip_add_del (const void *vrf_ip, void *si, int is_add);
static void upf_v4_teid_add_del (const void *teid, void *si, int is_add);
static void upf_v6_teid_add_del (const void *teid, void *si, int is_add);

#define combine_volume_type(Dst, Src, T, D) \
  (Dst)->measure.T.D += (Src)->measure.T.D
#define combine_volume(Dst, Src, T)                 \
  do                                                \
    {                                               \
      combine_volume_type ((Dst), (Src), T, ul);    \
      combine_volume_type ((Dst), (Src), T, dl);    \
      combine_volume_type ((Dst), (Src), T, total); \
    }                                               \
  while (0)

#define SWITCH_AND_CLEAR(_p1, _p2) do { _p1 = _p2; _p2 = NULL; } while (0)

void upf_pfcp_resource_cache_fill ()
{
  u32 i;
  upf_main_t *um = &g_upf_main;
  sx_server_main_t *sm = &sx_server_main;
  upf_per_pfcp_thread_t *per_pfcp;
  upf_session_t *sx;
  upf_peer_t *peer;
  upf_qer_policer_t *qer_policer;
  sx_msg_t *msg;
  upf_vnip_sessions_t *vnip_sx;

  per_pfcp = upf_get_per_pfcp ();

  if (vec_len (per_pfcp->session_indices) == 0)
    {
      for (i = 0; i < PFCP_CACHE_FILL_SZ; i++)
        {
          clib_spinlock_lock (&um->sessions_lock);
          pool_get_aligned (um->sessions, sx, CLIB_CACHE_LINE_BYTES);
          vec_add1 (per_pfcp->session_indices, sx - um->sessions);
          clib_spinlock_unlock (&um->sessions_lock);
        }
    }

  if (vec_len (per_pfcp->peer_indices) == 0)
    {
      for (i = 0; i < PFCP_CACHE_FILL_SZ; i++)
        {
          clib_spinlock_lock (&um->peers_lock);
          pool_get_aligned (um->peers, peer, CLIB_CACHE_LINE_BYTES);
          vec_add1 (per_pfcp->peer_indices, peer - um->peers);
          clib_spinlock_unlock (&um->peers_lock);
        }
    }

  if (vec_len (per_pfcp->policer_indices) == 0)
    {
      for (i = 0; i < PFCP_CACHE_FILL_SZ; i++)
        {
          clib_spinlock_lock (&um->qer_lock);
          pool_get_aligned (um->qer_policers, qer_policer,
                            CLIB_CACHE_LINE_BYTES);
          vec_add1 (per_pfcp->policer_indices, qer_policer - um->qer_policers);
          clib_spinlock_unlock (&um->qer_lock);
        }
    }

  if (vec_len (per_pfcp->msg_indices) == 0)
    {
      for (i = 0; i < PFCP_CACHE_FILL_SZ; i++)
        {
          clib_spinlock_lock (&sm->lock);
          pool_get_aligned (sm->msg_pool, msg, CLIB_CACHE_LINE_BYTES);
          vec_add1 (per_pfcp->msg_indices, msg - sm->msg_pool);
          clib_spinlock_unlock (&sm->lock);
        }
    }

  if (vec_len (per_pfcp->vnip_sxs_indices) == 0)
    {
      for (i = 0; i < PFCP_CACHE_FILL_SZ; i++)
        {
          clib_spinlock_lock (&um->vnip_sxs_lock);
          pool_get_aligned (um->vnip_sessions, vnip_sx, CLIB_CACHE_LINE_BYTES);
          vec_add1 (per_pfcp->vnip_sxs_indices, vnip_sx - um->vnip_sessions);
          clib_spinlock_unlock (&um->vnip_sxs_lock);
        }
    }
}

u32 upf_session_instance_alloc ()
{
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  if (vec_len (per_pfcp->session_indices) == 0)
    {
      upf_pfcp_resource_cache_fill ();
    }

  return vec_pop (per_pfcp->session_indices);
}

void upf_session_instance_free (u32 index)
{
  upf_main_t *um = &g_upf_main;
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  ASSERT (!pool_is_free_index (um->sessions, index));

  if (vec_len (per_pfcp->session_indices) < 8192)
    {
      vec_add1 (per_pfcp->session_indices, index);
    }
  else
    {
      clib_spinlock_lock (&um->sessions_lock);
      pool_put_index (um->sessions, index);
      clib_spinlock_unlock (&um->sessions_lock);
    }
}

u32 upf_peer_instance_alloc ()
{
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  if (vec_len (per_pfcp->peer_indices) == 0)
    {
      upf_pfcp_resource_cache_fill ();
    }

  return vec_pop (per_pfcp->peer_indices);
}

u32 upf_qer_policer_instance_alloc ()
{
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  if (vec_len (per_pfcp->policer_indices) == 0)
    {
      upf_pfcp_resource_cache_fill ();
    }

  return vec_pop (per_pfcp->policer_indices);
}

void upf_qer_policer_instance_free (u32 index)
{
  upf_main_t *um = &g_upf_main;
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  ASSERT (!pool_is_free_index (um->qer_policers, index));

  if (vec_len (per_pfcp->policer_indices) < 8192)
    {
      vec_add1 (per_pfcp->policer_indices, index);
    }
  else
    {
      clib_spinlock_lock (&um->qer_lock);
      pool_put_index (um->qer_policers, index);
      clib_spinlock_unlock (&um->qer_lock);
    }
}

u32 upf_msg_pool_instance_alloc ()
{
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  if (vec_len (per_pfcp->msg_indices) == 0)
    {
      upf_pfcp_resource_cache_fill ();
    }

  return vec_pop (per_pfcp->msg_indices);
}

void upf_msg_pool_instance_free (u32 index)
{
  sx_server_main_t *sm = &sx_server_main;
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  ASSERT (!pool_is_free_index (sm->msg_pool, index));

  if (vec_len (per_pfcp->msg_indices) < 8192)
    {
      vec_add1 (per_pfcp->msg_indices, index);
    }
  else
    {
      clib_spinlock_lock (&sm->lock);
      pool_put_index (sm->msg_pool, index);
      clib_spinlock_unlock (&sm->lock);
    }
}

u32 upf_ip_session_instance_alloc ()
{
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  if (vec_len (per_pfcp->vnip_sxs_indices) == 0)
    {
      upf_pfcp_resource_cache_fill ();
    }

  return vec_pop (per_pfcp->vnip_sxs_indices);
}

void upf_ip_session_instance_free (u32 index)
{
  upf_main_t *um = &g_upf_main;
  upf_per_pfcp_thread_t *per_pfcp = upf_get_per_pfcp ();

  ASSERT (!pool_is_free_index (um->vnip_sessions, index));

  if (vec_len (per_pfcp->vnip_sxs_indices) < 8192)
    {
      vec_add1 (per_pfcp->vnip_sxs_indices, index);
    }
  else
    {
      clib_spinlock_lock (&um->vnip_sxs_lock);
      pool_put_index (um->vnip_sessions, index);
      clib_spinlock_unlock (&um->vnip_sxs_lock);
    }
}

int upf_pfcp_pdr_id_compare (const void *p1, const void *p2)
{
  const upf_pdr_t *a = (upf_pdr_t *)p1;
  const upf_pdr_t *b = (upf_pdr_t *)p2;
  return intcmp (a->id, b->id);
}

int upf_pfcp_url_len_get (const upf_pdr_t *pdr)
{
  upf_app_id_pfd_ctx_t *pfd_list = NULL;
  upf_pfd_ctx_t *pfd = NULL;

  CHECK_POOL_IS_VALID_RET(g_upf_main.pfd_list, pdr->pdi.app_index, 0);
  pfd_list = pool_elt_at_index (g_upf_main.pfd_list, pdr->pdi.app_index);
  if (pfd_list->pfd_contents)
    {
      pool_foreach (pfd, pfd_list->pfd_contents, ({
                      if (pfd->flags & F_PFD_C_URL)
                        return vec_len (pfd->url);
                    }));
    }
  return 0;
}

int upf_pfcp_pdr_priority_and_id_compare (const void *p1, const void *p2)
{
    const upf_pdr_t *a = (upf_pdr_t *)p1;
    const upf_pdr_t *b = (upf_pdr_t *)p2;

    /* higher priority if the pdr has longer url, for cmcc*/
    if (a->precedence == b->precedence)
    {
        if ((a->pdi.app_index != ~0) && (b->pdi.app_index != ~0))
            return intcmp (upf_pfcp_url_len_get (b), upf_pfcp_url_len_get (a));
    }

    return intcmp (a->precedence, b->precedence);
}

static int upf_sdf_id_compare (const void *p1, const void *p2)
{
    const upf_sdf_t *a = (upf_sdf_t *)p1;
    const upf_sdf_t *b = (upf_sdf_t *)p2;
    return intcmp (a->id, b->id);
}

static int upf_eth_id_compare (const void *p1, const void *p2)
{
    return intcmp (((const upf_eth_t *)p1)->id, ((const upf_eth_t *)p2)->id);
}

#define upf_vec_diff(new, old, compar, add_del, user)                    \
  do                                                                 \
    {                                                                \
      size_t _i = 0, _j = 0;                                         \
                                                                     \
      if (new)                                                       \
        vec_sort_with_function (new, compar);                        \
      if (old)                                                       \
        vec_sort_with_function (old, compar);                        \
      if (new &&old)                                                 \
        while (_i < vec_len (new) && _j < vec_len (old))             \
          {                                                          \
            int r = compar (&vec_elt (new, _i), &vec_elt (old, _j)); \
            if (r == 0)                                              \
              {                                                      \
                _i++;                                                \
                ;                                                    \
                _j++;                                                \
              }                                                      \
            else if (r < 0)                                          \
              {                                                      \
                /* insert new entry */                               \
                add_del (&vec_elt (new, _i), user, 1);               \
                _i++;                                                \
              }                                                      \
            else                                                     \
              {                                                      \
                /* remove old entry */                               \
                /*add_del(&vec_elt(old, _j), user, 0);*/             \
                _j++;                                                \
              }                                                      \
          }                                                          \
                                                                     \
      if (new)                                                       \
        for (; _i < vec_len (new); _i++) /* insert new entry */      \
          add_del (&vec_elt (new, _i), user, 1);                     \
      /*if (old) */                                                  \
      /*for (; _j < vec_len(old); _j++) */ /* remove old entry */    \
      /*add_del(&vec_elt(old, _j), user, 0); */                      \
    }                                                                \
  while (0)

#define vec_diff_ex(new, old, compar, add_del, user)                    \
    do                                                                 \
      {                                                                \
        size_t _i = 0, _j = 0;                                         \
                                                                       \
        if (new)                                                       \
          vec_sort_with_function (new, compar);                        \
        if (old)                                                       \
          vec_sort_with_function (old, compar);                        \
        if (new &&old)                                                 \
          while (_i < vec_len (new) && _j < vec_len (old))             \
            {                                                          \
              int r = compar (&vec_elt (new, _i), &vec_elt (old, _j)); \
              if (r == 0)                                              \
                {                                                      \
                  _i++;                                                \
                  ;                                                    \
                  _j++;                                                \
                }                                                      \
              else if (r < 0)                                          \
                {                                                      \
                  /* insert new entry */                               \
                  add_del (&vec_elt (new, _i), user, 1);               \
                  _i++;                                                \
                }                                                      \
              else                                                     \
                {                                                      \
                  /* remove old entry */                               \
                  add_del(&vec_elt(old, _j), user, 0);                 \
                  _j++;                                                \
                }                                                      \
            }                                                          \
                                                                       \
        if (new)                                                       \
          for (; _i < vec_len (new); _i++) /* insert new entry */      \
            add_del (&vec_elt (new, _i), user, 1);                     \
        if (old)                                                       \
          for (; _j < vec_len(old); _j++)  /* remove old entry */      \
            add_del(&vec_elt(old, _j), user, 0);                       \
      }                                                                \
    while (0)

#define vec_diff_without_sort(new, old, compar, add_del, user)         \
    do {                                                               \
        size_t _i = 0, _j = 0;                                         \
                                                                       \
        if (new && old)                                                \
          while (_i < vec_len (new) && _j < vec_len (old))             \
            {                                                          \
              int r = compar (&vec_elt (new, _i), &vec_elt (old, _j)); \
              if (r)                                                   \
                {                                                      \
                  /* remove old entry */                               \
                  add_del(&vec_elt(old, _j), user, 0);                 \
                  /* insert new entry */                               \
                  add_del (&vec_elt (new, _i), user, 1);               \
                }                                                      \
                _i++;                                                  \
                _j++;                                                  \
            }                                                          \
                                                                       \
        if (new)                                                       \
          for (; _i < vec_len (new); _i++) /* insert new entry */      \
            add_del (&vec_elt (new, _i), user, 1);                     \
        if (old)                                                       \
          for (; _j < vec_len(old); _j++)  /* remove old entry */      \
            add_del(&vec_elt(old, _j), user, 0);                       \
      } while (0)

int upf_far_id_compare (const void *p1, const void *p2)
{
  const upf_far_t *a = (upf_far_t *)p1;
  const upf_far_t *b = (upf_far_t *)p2;

  return intcmp (a->id, b->id);
}

int upf_urr_id_compare (const void *p1, const void *p2)
{
  const upf_urr_t *a = (upf_urr_t *)p1;
  const upf_urr_t *b = (upf_urr_t *)p2;

  return intcmp (a->id, b->id);
}

int upf_qer_id_compare (const void *p1, const void *p2)
{
  const upf_qer_t *a = (upf_qer_t *)p1;
  const upf_qer_t *b = (upf_qer_t *)p2;

  return intcmp (a->id, b->id);
}

int upf_bar_id_compare (const void *p1, const void *p2)
{
  const upf_bar_t *a = (upf_bar_t *)p1;
  const upf_bar_t *b = (upf_bar_t *)p2;

  return intcmp (a->id, b->id);
}

int upf_srr_id_compare (const void *p1, const void *p2)
{
  return intcmp (((const upf_srr_t *)p1)->id, ((const upf_srr_t *)p2)->id);
}

upf_node_assoc_t *upf_get_association (pfcp_node_id_t *node_id)
{
  upf_main_t *gtm = &g_upf_main;
  uword *p = NULL;

  switch (node_id->type)
    {
    case NID_IPv4:
    case NID_IPv6:
      p = hash_get_mem (gtm->node_index_by_ip, &node_id->ip);
      break;

    case NID_FQDN:
      p = hash_get_mem (gtm->node_index_by_fqdn, node_id->fqdn);
      break;
    }

  if (!p)
    return 0;

  CHECK_POOL_IS_VALID_RET(gtm->nodes, p[0], 0);
  return pool_elt_at_index (gtm->nodes, p[0]);
}

upf_node_assoc_t *
upf_new_association (u32 fib_index, ip46_address_t *lcl_addr,
                    ip46_address_t *rmt_addr, pfcp_node_id_t *node_id)
{
  upf_main_t *gtm = &g_upf_main;
  upf_node_assoc_t *n;

  clib_spinlock_lock (&gtm->nodes_lock);
  pool_get_aligned (gtm->nodes, n, CLIB_CACHE_LINE_BYTES);
  clib_spinlock_unlock (&gtm->nodes_lock);

  memset (n, 0, sizeof (*n));
  n->sessions = ~0;
  n->node_id = *node_id;
  n->fib_index = fib_index;
  n->lcl_addr = *lcl_addr;
  n->rmt_addr = *rmt_addr;
  n->user_plane_ip_resource_information = NULL;
  gtm->upf_status = UPF_STATUS_UP;

  // Initialize heartbeat timer for N4 link monitoring
  // added by caozhongwei 2025-07-11
  f64 now = unix_time_now();
  n->HB_timer.base = now;
  n->HB_timer.period = PFCP_HB_INTERVAL;
  n->HB_timer.last_received = now;  // Initialize last reception timestamp

  // Start heartbeat timer
  upf_pfcp_server_timer_start(PFCP_SERVER_HB_TIMER, n - gtm->nodes, &n->HB_timer);

  switch (node_id->type)
    {
    case NID_IPv4:
    case NID_IPv6:
      hash_set_mem_alloc (&gtm->node_index_by_ip, &node_id->ip,
                          n - gtm->nodes);
      break;

    case NID_FQDN:
      n->node_id.fqdn = vec_dup (node_id->fqdn);
      hash_set_mem (gtm->node_index_by_fqdn, n->node_id.fqdn, n - gtm->nodes);
      break;
    }

  return n;
}

void upf_release_all_association (void)
{
  upf_main_t *um = &g_upf_main;
  upf_node_assoc_t *n;

  pool_foreach (n, um->nodes, ({
                  upf_pfcp_release_association (n);
                }));

  upf_err ("all pfcp association released!");
}

void upf_pfcp_ip_teid_range_map_free (upf_node_assoc_t *n)
{
  upf_main_t *gtm = &g_upf_main;
  pfcp_user_plane_ip_resource_information_t *tmp_ip_resource;
  upf_upip_res_t *res;

  if (!n)
    return;

  vec_foreach (tmp_ip_resource, n->user_plane_ip_resource_information)
  {
    /* *INDENT-OFF* */
    pool_foreach (res, gtm->upip_res, ({
                    if (tmp_ip_resource->teid_range < 64)
                      {
                        CLEAN_1BIT (res->teid_range_status_low,
                                    tmp_ip_resource->teid_range);
                      }
                    else
                      {
                        CLEAN_1BIT (res->teid_range_status_high,
                                    tmp_ip_resource->teid_range - 64);
                      }
                  }));
    vec_free (tmp_ip_resource->network_instance);
    /* *INDENT-ON* */
  }
  vec_free (n->user_plane_ip_resource_information);
}

static void upf_release_one_node_sessions_pfcp_thread (void *arg)
{
  u32 idx;
  u64 node_id = (u64)arg;
  u32 *sx_idx, *sxs = NULL;
  upf_node_assoc_t *n;
  upf_session_t *sx;
  upf_main_t *um = &g_upf_main;
  vlib_main_t *vm = vlib_get_main ();

  upf_debug ("received notification for sx release for node %lu\n", node_id);

  CHECK_POOL_IS_VALID_NORET(um->nodes, node_id);
  n = pool_elt_at_index (um->nodes, node_id);

  clib_spinlock_lock (&n->lock);
  idx = n->sessions;
  while (idx != ~0)
    {
      CHECK_POOL_IS_VALID_CONTINUE(um->sessions, idx);
      sx = pool_elt_at_index (um->sessions, idx);

      upf_debug ("upf session %u located on %u\n", idx, sx->thread_index);

      ASSERT (sx->assoc.node == node_id);
      if (sx->thread_index == vm->thread_index)
        {
          vec_add1 (sxs, idx);
        }

      idx = sx->assoc.next;
    }
  clib_spinlock_unlock (&n->lock);

  vec_foreach (sx_idx, sxs)
  {
    upf_debug ("disable session %u\n", *sx_idx);

    CHECK_POOL_IS_VALID_CONTINUE(um->sessions, *sx_idx);
    sx = pool_elt_at_index (um->sessions, *sx_idx);
    if (upf_pfcp_session_disable (sx, false) != 0)
      upf_err ("failed to remove UPF session 0x%016" PRIx64, sx->cp_seid);
    upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_DEL_SESS, sx, NULL);
    upf_pfcp_session_free (sx);
    if (um->session_num)
        um->session_num--;
  }
  vec_free (sxs);
}

void upf_pfcp_release_one_node_sessions (upf_node_assoc_t *n)
{
  upf_main_t *gtm = &g_upf_main;
  u64 node_id = n - gtm->nodes;
  u32 i;

  //upf_pfcp_ip_teid_range_map_free (n);

  upf_debug ("upf_pfcp_release_one_node_sessions idx: %u");

  for (i = 0; i < gtm->num_pfcp_threads; i++)
  {
      upf_debug ("notify pfcp thread %u to release sessions %u\n", i, node_id);
      pfcp_send_rpc_to_thread (i, upf_release_one_node_sessions_pfcp_thread,
                               (void *)node_id);
  }
}

static clib_error_t * upf_release_all_upf_session (vlib_main_t *vm,
                            unformat_input_t *input, vlib_cli_command_t *cmd)
{
  upf_main_t *um = &g_upf_main;
  upf_node_assoc_t *n = NULL;

  pool_foreach (n, um->nodes, ({
                  upf_pfcp_release_one_node_sessions (n);
                }));
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_release_sessions_command, static) = {
    .path = "upf clear sessions",
    .short_help = "upf clear sessions",
    .function = upf_release_all_upf_session,
};
/* *INDENT-ON* */

static void upf_release_association_pfcp_thread (void *arg)
{
  u32 idx;
  u64 node_id = (u64)arg;
  u32 *sx_idx, *sxs = NULL;
  upf_node_assoc_t *n;
  upf_session_t *sx;
  upf_main_t *um = &g_upf_main;
  vlib_main_t *vm = vlib_get_main ();

  upf_debug ("received notification for sx release for node %lu\n", node_id);

  CHECK_POOL_IS_VALID_NORET(um->nodes, node_id);
  n = pool_elt_at_index (um->nodes, node_id);

  clib_spinlock_lock (&n->lock);
  idx = n->sessions;
  while (idx != ~0)
    {
      CHECK_POOL_IS_VALID_CONTINUE(um->sessions, idx);
      sx = pool_elt_at_index (um->sessions, idx);

      upf_debug ("upf session %u located on %u\n", idx, sx->thread_index);

      ASSERT (sx->assoc.node == node_id);
      if (sx->thread_index == vm->thread_index)
        {
          vec_add1 (sxs, idx);
        }

      idx = sx->assoc.next;
    }
  clib_spinlock_unlock (&n->lock);

  vec_foreach (sx_idx, sxs)
  {
    upf_debug ("disable session %u\n", *sx_idx);

    CHECK_POOL_IS_VALID_CONTINUE(um->sessions, *sx_idx);
    sx = pool_elt_at_index (um->sessions, *sx_idx);
    if (upf_pfcp_session_disable (sx, false) != 0)
      upf_err ("failed to remove UPF session 0x%016" PRIx64, sx->cp_seid);
    upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_DEL_SESS, sx, NULL);
    upf_pfcp_session_free (sx);
    if (um->session_num)
        um->session_num--;
  }
  vec_free (sxs);

  clib_atomic_add_fetch (&n->release_ref, 1);
  if (n->release_ref == um->num_pfcp_threads)
    {
      ASSERT (n->sessions == ~0);

      upf_debug ("release association %lu\n", node_id);

      clib_spinlock_lock (&um->nodes_lock);
      pool_put (um->nodes, n);
      clib_spinlock_unlock (&um->nodes_lock);

      u32 asso_num = 0;
      pool_foreach (n, um->nodes, ({
            ++asso_num;
          }));
      if (!asso_num)
      {
          um->upf_status = UPF_STATUS_DOWN;
      }
    }
}

u32 upf_pfcp_release_association (upf_node_assoc_t *n)
{
  upf_main_t *gtm = &g_upf_main;
  u64 node_id = n - gtm->nodes;
  u32 i;
  clib_bihash_kv_8_8_t kv;
  clib_bihash_kv_40_8_t kv40;

  if (1 == gtm->context_release_disable)
    {
      upf_info ("UPF context release disable!");
      return 1;
    }
  clib_memset (&kv, 0, sizeof (kv));
  clib_memset (&kv40, 0, sizeof (kv40));
  switch (n->node_id.type)
    {
    case NID_IPv4:
    case NID_IPv6:
      hash_unset_mem_free (&gtm->node_index_by_ip, &n->node_id.ip);
      break;

    case NID_FQDN:
      hash_unset_mem (gtm->node_index_by_fqdn, n->node_id.fqdn);
      vec_free (n->node_id.fqdn);
      break;
    }

  upf_pfcp_ip_teid_range_map_free (n);

#if 0
  while (idx != ~0) {
    upf_session_t *sx = pool_elt_at_index(gtm->sessions, idx);

    ASSERT(sx->assoc.node == node_id);

    idx = sx->assoc.next;

    if (upf_pfcp_session_disable(sx, true) != 0)
      upf_err ("failed to remove UPF session 0x%016" PRIx64, sx->cp_seid);
    upf_pfcp_session_free(sx);
  }

  ASSERT(n->sessions == ~0);

  /* *INDENT-OFF* */
  pool_foreach(msg, sxsm->msg_pool, ({
    if (msg->node == node_id)
      vec_add1(msgs, msg - sxsm->msg_pool);
  }));
  /* *INDENT-ON* */

  vec_foreach(m, msgs) {
    msg = pool_elt_at_index(sxsm->msg_pool, *m);
    kv.key = msg->seq_no;
    clib_bihash_add_del_8_8(&sxsm->request_q, &kv, 0 /* is_del*/);

    clib_memcpy(kv40.key, msg->request_key, 32);
    clib_bihash_add_del_40_8(&sxsm->response_q, &kv40, 0 /* is_del */);
    if (msg->timer)
      upf_pfcp_server_timer_stop(msg->timer);
    sx_msg_free(sxsm, msg);
  }
  vec_free (msgs);
#else
  for (i = 0; i < gtm->num_pfcp_threads; i++)
    {
      upf_debug ("notify pfcp thread %u to release asso:%U (%lu)", i, upf_format_node_id, &n->node_id, node_id);
      pfcp_send_rpc_to_thread (i, upf_release_association_pfcp_thread,
                               (void *)node_id);
    }
#endif
  upf_pfcp_server_timer_stop (n->HB_timer.handle);

  return 0; /* release ok */
}

static void upf_pfcp_node_assoc_attach_session (upf_node_assoc_t *n, upf_session_t *sx)
{
  upf_main_t *gtm = &g_upf_main;
  u32 sx_idx = sx - gtm->sessions;

  sx->assoc.node = n - gtm->nodes;
  sx->assoc.prev = ~0;

  if (n->sessions != ~0)
    {
      if (PREDICT_TRUE(!pool_is_free_index(gtm->sessions, n->sessions)))
      {
          upf_session_t *prev = pool_elt_at_index (gtm->sessions, n->sessions);

          ASSERT (prev->assoc.prev == ~0);
          ASSERT (prev->assoc.node == sx->assoc.node);
          ASSERT (!pool_is_free_index (gtm->sessions, n->sessions));

          prev->assoc.prev = sx_idx;
      }
    }

  sx->assoc.next = n->sessions;
  n->sessions = sx_idx;
}

static void upf_pfcp_node_assoc_detach_session (upf_session_t *sx)
{
  upf_main_t *gtm = &g_upf_main;
  upf_node_assoc_t *n;

  ASSERT (sx->assoc.node != ~0);
  ASSERT (!pool_is_free_index (gtm->nodes, sx->assoc.node));

  if (sx->assoc.prev != ~0)
    {
      if (PREDICT_TRUE(!pool_is_free_index(gtm->sessions, sx->assoc.prev)))
      {
          upf_session_t *prev = pool_elt_at_index (gtm->sessions, sx->assoc.prev);

          ASSERT (prev->assoc.node == sx->assoc.node);

          prev->assoc.next = sx->assoc.next;
      }
    }
  else
    {
      if (PREDICT_TRUE(!pool_is_free_index(gtm->nodes, sx->assoc.node)))
      {
          n = pool_elt_at_index (gtm->nodes, sx->assoc.node);
          ASSERT (n->sessions != ~0);

          n->sessions = sx->assoc.next;
      }
    }

  if (sx->assoc.next != ~0)
    {
      if (PREDICT_TRUE(!pool_is_free_index(gtm->nodes, sx->assoc.node)))
      {
          upf_session_t *next = pool_elt_at_index (gtm->sessions, sx->assoc.next);

          ASSERT (next->assoc.node == sx->assoc.node);

          next->assoc.prev = sx->assoc.prev;
      }
    }

  sx->assoc.node = sx->assoc.prev = sx->assoc.next = ~0;
}

upf_session_t *upf_pfcp_session_create (upf_node_assoc_t *assoc, int sx_fib_index, session_id_t *sxid, u32 seq_no)
{
    sx_server_main_t *sxsm = &sx_server_main;
    upf_main_t *gtm = &g_upf_main;
    u32 sx_index = ~0;
    upf_session_t *sx;
    upf_session_t *old_sx = NULL;
    clib_bihash_kv_8_8_t kv;
    clib_bihash_kv_24_8_t kv_24_8;
    u64 time_ref = 0; /* timestamp reference */
    u64 pfcp_thread_id = 0;

    memset (&kv, 0, sizeof (kv));
    memset (&kv_24_8, 0, sizeof (kv_24_8));

    upf_debug ("CP F-SEID: 0x%016" PRIx64 " @ %U"
               "UP F-SEID: 0x%016" PRIx64 " @ %U",
               sxid->cp.seid, format_ip46_address, &sxid->cp.address, IP46_TYPE_ANY,
               sxid->up.seid, format_ip46_address, &sxid->up.address, IP46_TYPE_ANY);

    /* neil.fan@20211216 modify for distinguish retry msg and residual session resource */
    old_sx = upf_session_lookup_by_fseid (&sxid->cp.address, sxid->cp.seid);
    if (old_sx)
    {
        /* excluding pfcp retry msg */
        if (seq_no == old_sx->pfcp_seq_no)
        {
            upf_warn ("session has established, ignore retry msg, cp_seid:0x%lx seq_no:%u", sxid->cp.seid, seq_no);
            return NULL;
        }
        else
        {
            upf_trace ("session reestablish but the cp_seid:0x%lx has exist, seq_no:%u", sxid->cp.seid, seq_no);
            if (upf_pfcp_session_disable (old_sx, false) != 0)
                upf_err ("residual session delete failed, cp_seid:0x%lx seq_no:%u", sxid->cp.seid, seq_no);
            upf_pfcp_session_free (old_sx);
        }
        old_sx = NULL;
    }

    sx_index = upf_session_instance_alloc ();
    CHECK_POOL_IS_VALID_RET(gtm->sessions, sx_index, NULL);
    sx = pool_elt_at_index (gtm->sessions, sx_index);

    memset (sx, 0, sizeof (*sx));

    sx->fib_index = sx_fib_index;
    sx->thread_index = vlib_get_thread_index ();
    sx->up_address = sxid->up.address;
    sx->cp_seid = sxid->cp.seid;
    sx->cp_address = sxid->cp.address;
    sx->msgs_id = NULL;
    sx->dnn = NULL;
    sx->key_list = NULL;
    sx->eth_n6_nwi = ~0;

    /* neil.fan@20211216 add: always destroy the old session found by cp-fseid except for pfcp establishment retry msg,
     * make sure the pfcp_seq_no of session is not equals to seq_no.
     */
    if (PFCP_SEQNO_DEFAULT != seq_no)
        sx->pfcp_seq_no = seq_no;
    else
        sx->pfcp_seq_no = 0;

    sx->unix_time_start = sxsm->now;

    clib_spinlock_init (&sx->lock);

    /* neil.fan@20211216 modify: distinguish the seid allocated by different ha board (master and slave) */
    if (PFCP_SEID_DEFAULT == sxid->up.seid)
    {
        pfcp_thread_id = (sx->thread_index - gtm->first_pfcp_thread_index) & (PFCP_THREAD_MAX - 1);
        /* neil.fan@20211220 modify for allocate unique seid
         * 1) 13 bits[12:0]  8192 IDs;
         * 2) 3  bits[15:13] 8 pfcp thread IDs;
         * 3) 4  bits[19:16] 1/16 second
         * 4) 32 bits[51:20] more than 100 years not repeat
         */
        if (g_alloc_fixed_seid_switch == SWITCH_OFF)
            time_ref = (unix_time_now_nsec() >> 10) & UPF_SEID_HA_TIME_MASK;
        else
            time_ref = 0;
        sx->up_seid = g_upf_seid_ha_id | time_ref | ((pfcp_thread_id << 13) & UPF_SEID_HA_THREAD_MASK)
                      | (g_pfcp_thread_inc[pfcp_thread_id] & UPF_SEID_HA_INC_MASK);
        g_pfcp_thread_inc[pfcp_thread_id]++;
        old_sx = upf_session_lookup(sx->up_seid);
        if (NULL != old_sx)
        {
            upf_err ("alloc unique upseid(0x%lx) failed, cp_seid:0x%lx, conflict with old cp_sied 0x%lx!",
                sx->up_seid, sxid->cp.seid, old_sx->cp_seid);
            return NULL;
        }
    }
    else
        sx->up_seid = sxid->up.seid;

    clib_spinlock_lock (&assoc->lock);
    upf_pfcp_node_assoc_attach_session (assoc, sx);
    clib_spinlock_unlock (&assoc->lock);

    kv.key = sx->up_seid;
    kv.value = sx - gtm->sessions;

    clib_bihash_add_del_8_8 (&gtm->session_by_id, &kv, 1 /* is_add */);
    kv_24_8.key[0] = sxid->cp.address.as_u64[0];
    kv_24_8.key[1] = sxid->cp.address.as_u64[1];
    kv_24_8.key[2] = sxid->cp.seid;
    kv_24_8.value = sx - gtm->sessions;
    clib_bihash_add_del_24_8 (&gtm->session_by_fseid, &kv_24_8, 1 /* is_add */);
    sx->hash_teid_by_chooseid = hash_create (0, sizeof (u32));
    hash_set_flags (sx->hash_teid_by_chooseid, HASH_FLAG_NO_AUTO_SHRINK | HASH_FLAG_NO_AUTO_GROW);
    sx->hash_upip4_by_pool = hash_create (0, sizeof (u32));
    sx->hash_upip6_by_pool = hash_create (0, sizeof (u32));
    sx->hash_pdr_id_by_flow_id[FT_ORIGIN] = hash_create (0, sizeof (u32));
    sx->hash_pdr_id_by_flow_id[FT_REVERSE] = hash_create (0, sizeof (u32));
    memset(sx->single_trace_list, ~0, sizeof(sx->single_trace_list));

    sx->rat_type = 0xFF;
    sx->user_location_information.geographic_location_type = 0xFF;
    sx->user_location_information.geographic_location = NULL;
    return sx;
}

void upf_pfcp_session_update (upf_session_t *sx)
{
  struct rules *pending;
  pending = upf_get_rules (sx, SX_PENDING);
  ASSERT ((sx->flags & SX_UPDATING) == 0);
  ASSERT (pending->pdr == NULL);
  ASSERT (pending->far == NULL);
  ASSERT (pending->qer == NULL);
  ASSERT (pending->urr == NULL);

  sx->flags |= SX_UPDATING;
}

void upf_pfcp_peer_restack_dpo (upf_peer_t *t)
{
  dpo_id_t dpo = DPO_INVALID;

  fib_entry_contribute_forwarding (t->fib_entry_index, t->forw_type, &dpo);
  while (DPO_LOAD_BALANCE == dpo.dpoi_type)
    {
      load_balance_t *lb = load_balance_get (dpo.dpoi_index);
      if (lb->lb_n_buckets > 1)
        break;

      dpo_copy (&dpo, load_balance_get_bucket_i (lb, 0));
    }

  dpo_stack_from_node (t->encap_index, &t->next_dpo, &dpo);
  dpo_reset (&dpo);
}

static upf_peer_t *upf_pfcp_peer_from_fib_node (fib_node_t *node)
{
  // ASSERT (FIB_NODE_TYPE_UPF_TUNNEL == node->fn_type);
  return (
      (upf_peer_t *)(((char *)node) - STRUCT_OFFSET_OF (upf_peer_t, node)));
}

/**
 * Function definition to backwalk a FIB node -
 * Here we will restack the new dpo of GTPU DIP to encap node.
 */
static fib_node_back_walk_rc_t
upf_pfcp_peer_back_walk (fib_node_t *node, fib_node_back_walk_ctx_t *ctx)
{
  upf_pfcp_peer_restack_dpo (upf_pfcp_peer_from_fib_node (node));
  return (FIB_NODE_BACK_WALK_CONTINUE);
}

/**
 * Function definition to get a FIB node from its index
 */
static fib_node_t *
upf_pfcp_peer_fib_node_get (fib_node_index_t index)
{
  upf_peer_t *t;
  upf_main_t *um = &g_upf_main;

  CHECK_POOL_IS_VALID_RET(um->peers, index, NULL);
  t = pool_elt_at_index (um->peers, index);

  return (&t->node);
}

/**
 * Function definition to inform the FIB node that its last lock has gone.
 */
static void
upf_pfcp_peer_last_lock_gone (fib_node_t *node)
{
  /*
   * The GTP peer is a root of the graph. As such
   * it never has children and thus is never locked.
   */
  ASSERT (0);
}

/*
 * Virtual function table registered by GTPU tunnels
 * for participation in the FIB object graph.
 */
const fib_node_vft_t upf_pfcp_fib_vft = {
    .fnv_get = upf_pfcp_peer_fib_node_get,
    .fnv_last_lock = upf_pfcp_peer_last_lock_gone,
    .fnv_back_walk = upf_pfcp_peer_back_walk,
};

const static char *const upf_pfcp_ip4_nodes[] = {
    "upf-ip4-input",
    NULL,
};

const static char *const upf_pfcp_ip6_nodes[] = {
    "upf-ip6-input",
    NULL,
};

const char *const *const g_upf_pfcp_nodes[DPO_PROTO_NUM] = {
    [DPO_PROTO_IP4] = upf_pfcp_ip4_nodes,
    [DPO_PROTO_IP6] = upf_pfcp_ip6_nodes,
    [DPO_PROTO_MPLS] = NULL,
};

static void
upf_pfcp_dpo_lock (dpo_id_t *dpo)
{
}

static void
upf_pfcp_dpo_unlock (dpo_id_t *dpo)
{
}

static u32
upf_pfcp_dpo_get_urpf (const dpo_id_t *dpo)
{
  return 0;
}

void
upf_pfcp_dpo_create (dpo_proto_t dproto, u32 index, dpo_id_t *dpo)
{
  dpo_set (dpo, g_upf_main.dpo_type, dproto, index);
}

u8 *
format_upf_pfcp_dpo (u8 *s, va_list *args)
{
  index_t index = va_arg (*args, index_t);
  CLIB_UNUSED (u32 indent) = va_arg (*args, u32);

  return (format (s, "UPF: %d", index));
}

const dpo_vft_t upf_pfcp_dpo_vft = {
    .dv_lock = upf_pfcp_dpo_lock,
    .dv_unlock = upf_pfcp_dpo_unlock,
    .dv_format = format_upf_pfcp_dpo,
    .dv_get_urpf = upf_pfcp_dpo_get_urpf,
};

uword upf_pfcp_peer_addr_ref (upf_far_forward_t *fwd)
{
  u8 is_ip4 = !!(fwd->outer_header_creation.description &
                 OUTER_HEADER_CREATION_ANY_IP4);
  upf_main_t *gtm = &g_upf_main;
  u32 peer_index, fib_index = 0;
  upf_peer_t *p;
  clib_bihash_kv_24_8_t kv;
  pfcp_rpc_msg_t *msg;
  sx_add_del_peer_ip_t *sx_add_del_peer_ip;

  clib_memset (&kv, 0, sizeof (kv));

  clib_memcpy (kv.key, &fwd->outer_header_creation.ip,
               sizeof (ip46_address_t));
  if (fwd->flags & FAR_F_NETWORK_INSTANCE)
    {
      if (fwd->nwi != ~0)
        {
          if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, fwd->nwi)))
          {
              upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, fwd->nwi);
              upf_debug ("fwd network instance:%U", upf_format_network_instance,
                         nwi ? nwi->name : NULL);
              if (~0 == (fib_index =
                             fib_table_find (fib_ip_proto (!ip46_address_is_ip4 (
                                                 &fwd->outer_header_creation.ip)),
                                             nwi->vrf)))
                {
                  upf_warn ("vrf(table_id) %d in not (yet) defined for IPv4",
                            nwi->vrf);
                  return ~0;
                }
          }
        }
    }
  kv.key[2] = fib_index;

  if (!clib_bihash_search_inline_24_8 (&gtm->peer_index_by_ip, &kv))
    {
      CHECK_POOL_IS_VALID_RET(gtm->peers, kv.value, ~0);
      p = pool_elt_at_index (gtm->peers, kv.value);
      p->ref_cnt++;
      return kv.value;
    }

  peer_index = upf_peer_instance_alloc ();
  CHECK_POOL_IS_VALID_RET(gtm->peers, peer_index, ~0);
  p = pool_elt_at_index (gtm->peers, peer_index);

  memset (p, 0, sizeof (*p));
  p->ref_cnt = 1;
  // p->encap_fib_index = *(u32 *)(&kv.key[2]);
  p->encap_fib_index = (u32)kv.key[2];

  if (is_ip4)
    {
      p->encap_index = upf4_encap_node.index;
      p->forw_type = FIB_FORW_CHAIN_TYPE_UNICAST_IP4;
    }
  else
    {
      p->encap_index = upf6_encap_node.index;
      p->forw_type = FIB_FORW_CHAIN_TYPE_UNICAST_IP6;
    }

  kv.value = p - gtm->peers;

  clib_bihash_add_del_24_8 (&gtm->peer_index_by_ip, &kv, 1 /*is_add*/);

  u32 msg_size = CLIB_CACHE_LINE_ROUND(sizeof(sx_add_del_peer_ip_t) + PFCP_RPC_BASIC);
  if (g_upf_auxiliary_switch & UPF_LINUX_MALLOC_SWITCH)
  {
      msg = malloc(msg_size);
  }
  else
  {
      msg = clib_mem_alloc_aligned_no_fail (msg_size, CLIB_CACHE_LINE_BYTES);
  }
  memset (msg, 0, msg_size);

  msg->magic = PFCP_RPC_MAGIC;
  msg->type = PFCP_RPC_SX_ADD_DEL_PEER_IP;

  sx_add_del_peer_ip = &msg->data.sx_add_del_peer_ip;
  sx_add_del_peer_ip->is_add = 1;
  sx_add_del_peer_ip->peer_index = p - gtm->peers;
  sx_add_del_peer_ip->fib_index = p->encap_fib_index;
  sx_add_del_peer_ip->encap_index = p->encap_index;

  fib_prefix_from_ip46_addr (&fwd->outer_header_creation.ip,
                             &sx_add_del_peer_ip->pfx);

  pfcp_thread_call_main_thread (msg);

  u8 is_gtpu = !!(fwd->outer_header_creation.description &
                  OUTER_HEADER_CREATION_GTP_ANY);
  if (is_gtpu)
    {
      clib_memcpy (&p->ohc_ip, &fwd->outer_header_creation.ip,
                   sizeof (ip46_address_t));
      vec_validate_aligned (p->fwd_rewrite, vec_len (fwd->rewrite),
                            CLIB_CACHE_LINE_BYTES);
      clib_memcpy (p->fwd_rewrite, fwd->rewrite, vec_len (fwd->rewrite));

      do
        {
          if ((fwd->dst_intf == DST_INTF_CP) && (g_upf_main.gtp_n4_switch == SWITCH_OFF))
            break;

          upf_server_send_gtpu_echo_req(p - gtm->peers);
        } while (0);
    }
  return p - gtm->peers;
}

uword upf_pfcp_peer_addr_unref (const upf_far_forward_t *fwd)
{
  upf_main_t *gtm = &g_upf_main;
  clib_bihash_kv_24_8_t kv, value;
  upf_peer_t *p;
  pfcp_rpc_msg_t *msg;
  sx_add_del_peer_ip_t *sx_add_del_peer_ip;

  clib_memset (&kv, 0, sizeof (kv));

  clib_memcpy (kv.key, &fwd->outer_header_creation.ip,
               sizeof (ip46_address_t));
  kv.key[2] = fwd->fib_index;
  if (PREDICT_FALSE (
          clib_bihash_search_24_8 (&gtm->peer_index_by_ip, &kv, &value)))
    {
      upf_err ("No peer found! peer index:%x", value.value);
      return -1;
    }

  CHECK_POOL_IS_VALID_RET(gtm->peers, value.value, -1);
  p = pool_elt_at_index (gtm->peers, value.value);
  if (--(p->ref_cnt) != 0)
    return p->ref_cnt;
  upf_pfcp_server_timer_stop (p->retry_timer.handle);
  upf_pfcp_server_timer_stop (p->timer.handle);
  vec_free (p->fwd_rewrite);
  clib_bihash_add_del_24_8 (&gtm->peer_index_by_ip, &kv, 0 /*is_del*/);

  u32 msg_size = CLIB_CACHE_LINE_ROUND(sizeof(sx_add_del_peer_ip_t) + PFCP_RPC_BASIC);
  if (g_upf_auxiliary_switch & UPF_LINUX_MALLOC_SWITCH)
  {
      msg = malloc(msg_size);
  }
  else
  {
      msg = clib_mem_alloc_aligned_no_fail (msg_size, CLIB_CACHE_LINE_BYTES);
  }
  memset (msg, 0, msg_size);

  msg->magic = PFCP_RPC_MAGIC;
  msg->type = PFCP_RPC_SX_ADD_DEL_PEER_IP;

  sx_add_del_peer_ip = &msg->data.sx_add_del_peer_ip;
  sx_add_del_peer_ip->is_add = 0;
  sx_add_del_peer_ip->peer_index = p - gtm->peers;
  sx_add_del_peer_ip->fib_index = p->fib_entry_index;
  sx_add_del_peer_ip->sibling_index = p->sibling_index;
  sx_add_del_peer_ip->node = p->node;

  pfcp_thread_call_main_thread (msg);

  return 0;
}

static int upf_make_pending_pdr (upf_session_t *sx)
{
    struct rules *pending = upf_get_rules (sx, SX_PENDING);
    struct rules *active = upf_get_rules (sx, SX_ACTIVE);

    if (pending->pdr)
        return 0;

    if (active->pdr)
    {
        size_t i;
        clib_spinlock_lock (&sx->lock);
        memcpy(&pending->pdr_edge, &active->pdr_edge, sizeof(active->pdr_edge));
        pending->pdr = vec_dup (active->pdr);
        vec_foreach_index (i, pending->pdr)
        {
            upf_pdr_t *dst_pdr = vec_elt_at_index (pending->pdr, i);
            upf_pdr_t *src_pdr = vec_elt_at_index (active->pdr, i);

            dst_pdr->urr_ids = vec_dup (src_pdr->urr_ids);
            dst_pdr->qer_ids = vec_dup (src_pdr->qer_ids);
            dst_pdr->flow_table_ids = vec_dup (src_pdr->flow_table_ids);
            dst_pdr->activate_predefined_rules = vec_dup (src_pdr->activate_predefined_rules);
            dst_pdr->ip_multicast_addressing_info = vec_dup (src_pdr->ip_multicast_addressing_info);
            if (dst_pdr->ip_multicast_addressing_info)
            {
                int k;
                vec_foreach_index (k, dst_pdr->ip_multicast_addressing_info)
                {
                    vec_elt(dst_pdr->ip_multicast_addressing_info, k).source_ip_address
                        = vec_dup(vec_elt(src_pdr->ip_multicast_addressing_info, k).source_ip_address);
                }
            }

            upf_pdi_t *dst_pdi = &dst_pdr->pdi;
            upf_pdi_t *src_pdi = &src_pdr->pdi;
            dst_pdi->ip_multicast_addressing_info = vec_dup (src_pdi->ip_multicast_addressing_info);
            if (dst_pdi->ip_multicast_addressing_info)
            {
                int k;
                vec_foreach_index (k, dst_pdi->ip_multicast_addressing_info)
                {
                    vec_elt(dst_pdi->ip_multicast_addressing_info, k).source_ip_address
                        = vec_dup(vec_elt(src_pdi->ip_multicast_addressing_info, k).source_ip_address);
                }
            }

            dst_pdi->acl = vec_dup (src_pdi->acl);
            dst_pdi->app_name = vec_dup (src_pdi->app_name);
            dst_pdi->framed_route = vec_dup (src_pdi->framed_route);
            dst_pdi->framed_ipv6_route = vec_dup (src_pdi->framed_ipv6_route);

            dst_pdi->eth_rule = vec_dup(src_pdi->eth_rule);
            if (dst_pdi->eth_rule)
            {
                int k;
                vec_foreach_index (k, dst_pdi->eth_rule)
                {
                    vec_elt(dst_pdi->eth_rule, k).mac_address = vec_dup(vec_elt(src_pdi->eth_rule, k).mac_address);
                    vec_elt(dst_pdi->eth_rule, k).acl = vec_dup(vec_elt(src_pdi->eth_rule, k).acl);
                }
            }
        }
        clib_spinlock_unlock (&sx->lock);
    }

    return 0;
}

static int upf_make_pending_far (upf_session_t *sx)
{
  struct rules *pending = upf_get_rules (sx, SX_PENDING);
  struct rules *active = upf_get_rules (sx, SX_ACTIVE);

  if (pending->far)
    return 0;

  if (active->far)
    {
      size_t i, j;

	  clib_spinlock_lock (&sx->lock);
      pending->far = vec_dup (active->far);
      clib_spinlock_unlock (&sx->lock);
      vec_foreach_index (i, active->far)
      {
        upf_far_t *far = vec_elt_at_index (active->far, i);
        CHECK_VEC_VALUE_ISVALID_RET(active->far, far, 0);

        vec_elt (pending->far, i).forward.rewrite = NULL;

        // if (!(far->apply_action & FAR_FORWARD))
        // continue;

        if (far->forward.rewrite)
        {
          if (!(far->id & 0x80000000))
            vec_elt (pending->far, i).forward.rewrite =
              vec_dup (far->forward.rewrite);
        }

        if (far->forward.header_enrichment)
          {
            vec_elt (pending->far, i).forward.header_enrichment =
                vec_dup (far->forward.header_enrichment);

            vec_foreach_index (j, far->forward.header_enrichment)
            {
              vec_elt (pending->far, i).forward.header_enrichment[j].name =
                  vec_dup (vec_elt (far->forward.header_enrichment, j).name);
              vec_elt (pending->far, i).forward.header_enrichment[j].value =
                  vec_dup (vec_elt (far->forward.header_enrichment, j).value);
            }
          }

        if (far->forward.flags & FAR_F_NETWORK_INSTANCE)
          {
            vec_elt (pending->far, i).forward.table_id = far->forward.table_id;
            vec_elt (pending->far, i).forward.nwi = far->forward.nwi;
            vec_elt (pending->far, i).forward.fib_index =
                far->forward.fib_index;
          }
        if (far->forward.flags & FAR_F_TRANSPORT_LEVEL_MARKING)
          {
            vec_elt (pending->far, i).forward.transport_level_marking =
                far->forward.transport_level_marking;
          }
        vec_elt (pending->far, i).forward.dst_intf = far->forward.dst_intf;
      }
    }

  return 0;
}

static int upf_make_pending_urr (upf_session_t *sx)
{
  struct rules *pending = upf_get_rules (sx, SX_PENDING);
  struct rules *active = upf_get_rules (sx, SX_ACTIVE);
  upf_urr_t *urr;

  if (pending->urr)
    return 0;

  if (active->urr) /* Andy: when establishment workfolw, active->urr is null.
                    */
    {
      size_t i = 0;;
      upf_debug ("active->urr != null.");
      pending->urr = vec_dup (active->urr);
      vec_foreach (urr, pending->urr)
      {
        urr->linked_urr_ids =
            vec_dup (vec_elt (active->urr, i).linked_urr_ids);

        clib_spinlock_init (&urr->lock);
        memset (&urr->volume.measure, 0, sizeof (urr->volume.measure));
        i++;
      }
    }

  return 0;
}

static int upf_make_pending_qer (upf_session_t *sx)
{
  struct rules *pending = upf_get_rules (sx, SX_PENDING);
  struct rules *active = upf_get_rules (sx, SX_ACTIVE);
  upf_qer_t *qer;

  if (pending->qer)
    return 0;

  if (active->qer)
    {
      pending->qer = vec_dup (active->qer);
      vec_foreach (qer, pending->qer)
      {
        clib_spinlock_init (&qer->lock);
      }
    }

  return 0;
}

static int upf_make_pending_bar (upf_session_t *sx)
{
  struct rules *pending = upf_get_rules (sx, SX_PENDING);
  struct rules *active = upf_get_rules (sx, SX_ACTIVE);

  if (pending->bar)
    return 0;

  if (active->bar)
    {
      pending->bar = vec_dup (active->bar);
    }

  return 0;
}

static int upf_make_pending_srr (upf_session_t *sx)
{
  struct rules *pending = upf_get_rules (sx, SX_PENDING);
  struct rules *active = upf_get_rules (sx, SX_ACTIVE);

  if (pending->srr)
    return 0;

  if (active->srr)
    {
      pending->srr = vec_dup (active->srr);
      int i;
      vec_foreach_index (i, pending->srr)
        {
          upf_srr_t *dst_srr = vec_elt_at_index (pending->srr, i);
          upf_srr_t *src_srr = vec_elt_at_index (active->srr, i);

          dst_srr->per_qos_flow_ctrl = vec_dup(src_srr->per_qos_flow_ctrl);
          int j;
          vec_foreach_index (j, dst_srr->per_qos_flow_ctrl)
            {
              upf_per_qos_flow_control_t *dst_ctl = vec_elt_at_index (dst_srr->per_qos_flow_ctrl, j);
              upf_per_qos_flow_control_t *src_ctl = vec_elt_at_index (src_srr->per_qos_flow_ctrl, j);
              dst_ctl->flow_ctrl = vec_dup (src_ctl->flow_ctrl);
            }
        }
    }

  return 0;
}

static int upf_make_pending_sdf (upf_session_t *sx)
{
  struct rules *pending = upf_get_rules (sx, SX_PENDING);
  struct rules *active = upf_get_rules (sx, SX_ACTIVE);

  if (pending->sdf)
    return 0;

  if (active->sdf)
    {

      pending->sdf = vec_dup (active->sdf);
    }

  return 0;
}

static int upf_make_pending_eth (upf_session_t *sx)
{
  struct rules *pending = upf_get_rules (sx, SX_PENDING);
  struct rules *active = upf_get_rules (sx, SX_ACTIVE);

  if (pending->eth)
    return 0;

  if (active->eth)
    {
      pending->eth = vec_dup (active->eth);
      if (pending->eth) /* take effect when active->eth is not null and vec_dup successfully */
        {
          int k;
          vec_foreach_index (k, active->eth)
          {
              vec_elt(pending->eth, k).bidirectional_eth.acl = vec_dup(vec_elt(active->eth, k).bidirectional_eth.acl);
              vec_elt(pending->eth, k).bidirectional_eth.mac_address = vec_dup(vec_elt(active->eth, k).bidirectional_eth.mac_address);
          }
        }
    }

  return 0;
}

static upf_qer_policer_t *
upf_apply_qer_mbr_policer (upf_qer_t *qer, u32 pol_index)
{
  sse2_qos_pol_cfg_params_st cfg = {
      .rate_type = SSE2_QOS_RATE_KBPS,
      .rnd_type = SSE2_QOS_ROUND_TO_CLOSEST,
      .rfc = SSE2_QOS_POLICER_TYPE_1R2C,
      .color_aware = 0,
      .conform_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
      .exceed_action =
          {
              .action_type = SSE2_QOS_ACTION_DROP,
          },
      .violate_action =
          {
              .action_type = SSE2_QOS_ACTION_DROP,
          },
  };
  upf_main_t *gtm = &g_upf_main;
  upf_qer_policer_t *pol;

  const int BYTES_PER_KBIT = (1000 / 8);

  CHECK_POOL_IS_VALID_RET(gtm->qer_policers, pol_index, NULL);
  pol = pool_elt_at_index (gtm->qer_policers, pol_index);

  qer->mbr_policer.v = pol - gtm->qer_policers;
  if (qer->mbr.ul)
    {
      cfg.rb.kbps.cir_kbps = qer->mbr.ul + g_upf_qer_mbr_offset;
      cfg.rb.kbps.cb_bytes = (qer->mbr.ul  + g_upf_qer_mbr_offset) * BYTES_PER_KBIT;
    }
  else
    {
      cfg.rb.kbps.cir_kbps = ~0;
      cfg.rb.kbps.cb_bytes = (u64)~0;
    }

  sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_UL]);
  qer->pol_mbr[UPF_UL] = cfg.rb.kbps.cir_kbps;
  if (qer->flags & SX_QER_AVERAGING_WINDOW)
    pol->policer[UPF_UL].current_limit =
        (pol->policer[UPF_UL].current_limit * qer->averaging_window) / 1000;

  if (qer->mbr.dl)
    {
      cfg.rb.kbps.cir_kbps = qer->mbr.dl + g_upf_qer_mbr_offset;
      cfg.rb.kbps.cb_bytes = (qer->mbr.dl + g_upf_qer_mbr_offset) * BYTES_PER_KBIT;
    }
  else
    {
      cfg.rb.kbps.cir_kbps = ~0;
      cfg.rb.kbps.cb_bytes = (u64)~0;
    }
  sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_DL]);
  qer->pol_mbr[UPF_DL] = cfg.rb.kbps.cir_kbps;
  if (qer->flags & SX_QER_AVERAGING_WINDOW)
    pol->policer[UPF_DL].current_limit =
        (pol->policer[UPF_DL].current_limit * qer->averaging_window) / 1000;

  return pol;
}

static upf_qer_policer_t *
upf_apply_qer_gbr_policer (upf_qer_t *qer, u32 pol_index)
{
  sse2_qos_pol_cfg_params_st cfg = {
      .rate_type = SSE2_QOS_RATE_KBPS,
      .rnd_type = SSE2_QOS_ROUND_TO_CLOSEST,
      .rfc = SSE2_QOS_POLICER_TYPE_1R2C,
      .color_aware = 0,
      .conform_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
      .exceed_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
      .violate_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
  };
  upf_main_t *gtm = &g_upf_main;
  upf_qer_policer_t *pol;
  const int BYTES_PER_KBIT = (1000 / 8);

  CHECK_POOL_IS_VALID_RET(gtm->qer_policers, pol_index, NULL);
  pol = pool_elt_at_index (gtm->qer_policers, pol_index);

  qer->gbr_policer.v = pol - gtm->qer_policers;
  if (qer->gbr.ul)
    {
      cfg.rb.kbps.cir_kbps = qer->gbr.ul;
      cfg.rb.kbps.cb_bytes = qer->gbr.ul * BYTES_PER_KBIT;
    }
  else
    {
      cfg.rb.kbps.cir_kbps = ~0;
      cfg.rb.kbps.cb_bytes = (u64)~0;
    }

  sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_UL]);
  if (qer->flags & SX_QER_AVERAGING_WINDOW)
    pol->policer[UPF_UL].current_limit =
        (pol->policer[UPF_UL].current_limit * qer->averaging_window) / 1000;

  if (qer->gbr.dl)
    {
      cfg.rb.kbps.cir_kbps = qer->gbr.dl;
      cfg.rb.kbps.cb_bytes = qer->gbr.dl * BYTES_PER_KBIT;
    }
  else
    {
      cfg.rb.kbps.cir_kbps = ~0;
      cfg.rb.kbps.cb_bytes = (u64)~0;
    }
  sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_DL]);
  if (qer->flags & SX_QER_AVERAGING_WINDOW)
    pol->policer[UPF_DL].current_limit =
        (pol->policer[UPF_DL].current_limit * qer->averaging_window) / 1000;

  return pol;
}

static void
upf_attach_qer_policer (upf_qer_t *qer)
{
  upf_main_t *gtm = &g_upf_main;
  upf_qer_policer_t *pol;
  u32 pol_index;

  if (qer->flags & SX_QER_MBR)
    {
      if (clib_bihash_search_inline_8_8 (&gtm->qer_by_id,
                                         &qer->mbr_policer.kv))
        {
          pol_index = upf_qer_policer_instance_alloc ();
          pol = upf_apply_qer_mbr_policer (qer, pol_index);

          clib_bihash_add_del_8_8 (&gtm->qer_by_id, &qer->mbr_policer.kv,
                                   1 /* is_add */);
        }
      else
        {
          pol = upf_apply_qer_mbr_policer (qer, qer->mbr_policer.v);
        }
      clib_atomic_fetch_add (&pol->ref_cnt, 1);
    }

  if (qer->flags & SX_QER_GBR)
    {
      if (clib_bihash_search_inline_8_8 (&gtm->qer_by_id,
                                         &qer->gbr_policer.kv))
        {
          pol_index = upf_qer_policer_instance_alloc ();
          pol = upf_apply_qer_gbr_policer (qer, pol_index);
          clib_bihash_add_del_8_8 (&gtm->qer_by_id, &qer->gbr_policer.kv,
                                   1 /* is_add */);
        }
      else
        {
          pol = upf_apply_qer_gbr_policer (qer, qer->gbr_policer.v);
        }

      clib_atomic_fetch_add (&pol->ref_cnt, 1);
    }
}

void upf_attach_dnn_policer (upf_dnn_t *dnn)
{
  sse2_qos_pol_cfg_params_st cfg = {
      .rate_type = SSE2_QOS_RATE_KBPS,
      .rnd_type = SSE2_QOS_ROUND_TO_CLOSEST,
      .rfc = SSE2_QOS_POLICER_TYPE_1R2C,
      .color_aware = 0,
      .conform_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
      .exceed_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
      .violate_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
  };

  upf_dnn_policer_t *pol;
  const int BYTES_PER_KBIT = (1000 / 8);

  pol = &dnn->dnn_policer;

  if (dnn->volume.mbr.ul)
    {
      cfg.rb.kbps.cir_kbps = dnn->volume.mbr.ul;
      cfg.rb.kbps.cb_bytes = dnn->volume.mbr.ul * BYTES_PER_KBIT;
    }
  else
    {
      cfg.rb.kbps.cir_kbps = ~0;
      cfg.rb.kbps.cb_bytes = (u64)~0;
    }

  sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_UL]);

  if (dnn->volume.mbr.dl)
    {
      cfg.rb.kbps.cir_kbps = dnn->volume.mbr.dl;
      cfg.rb.kbps.cb_bytes = dnn->volume.mbr.dl * BYTES_PER_KBIT;
    }
  else
    {
      cfg.rb.kbps.cir_kbps = ~0;
      cfg.rb.kbps.cb_bytes = (u64)~0;
    }
  sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_DL]);

  return;
}

void upf_attach_s_nssai_policer (upf_s_nssai_t *nssai)
{
  sse2_qos_pol_cfg_params_st cfg = {
      .rate_type = SSE2_QOS_RATE_KBPS,
      .rnd_type = SSE2_QOS_ROUND_TO_CLOSEST,
      .rfc = SSE2_QOS_POLICER_TYPE_1R2C,
      .color_aware = 0,
      .conform_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
      .exceed_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
      .violate_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
  };

  upf_s_nssai_policer_t *pol;
  const int BYTES_PER_KBIT = (1000 / 8);

  pol = &nssai->s_nssai_policer;

  if (nssai->volume.mbr.ul)
    {
      cfg.rb.kbps.cir_kbps = nssai->volume.mbr.ul;
      cfg.rb.kbps.cb_bytes = nssai->volume.mbr.ul * BYTES_PER_KBIT;
    }
  else
    {
      cfg.rb.kbps.cir_kbps = ~0;
      cfg.rb.kbps.cb_bytes = (u64)~0;
    }

  sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_UL]);

  if (nssai->volume.mbr.dl)
    {
      cfg.rb.kbps.cir_kbps = nssai->volume.mbr.dl;
      cfg.rb.kbps.cb_bytes = nssai->volume.mbr.dl * BYTES_PER_KBIT;
    }
  else
    {
      cfg.rb.kbps.cir_kbps = ~0;
      cfg.rb.kbps.cb_bytes = (u64)~0;
    }
  sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_DL]);

  return;
}

void upf_attach_nwi_policer (upf_nwi_t *nwi)
{
    sse2_qos_pol_cfg_params_st cfg =
    {
        .rate_type = SSE2_QOS_RATE_KBPS,
        .rnd_type = SSE2_QOS_ROUND_TO_CLOSEST,
        .rfc = SSE2_QOS_POLICER_TYPE_1R2C,
        .color_aware = 0,
        .conform_action =
            {
                .action_type = SSE2_QOS_ACTION_TRANSMIT,
            },
        .exceed_action =
            {
                .action_type = SSE2_QOS_ACTION_DROP,
            },
        .violate_action =
            {
                .action_type = SSE2_QOS_ACTION_DROP,
            },
     };

     upf_nwi_policer_t *pol;
     const int BYTES_PER_KBIT = (1000 / 8);

     pol = &nwi->nwi_policer;

    if (nwi->volume.mbr.ul)
       {
         cfg.rb.kbps.cir_kbps = nwi->volume.mbr.ul;
         cfg.rb.kbps.cb_bytes = nwi->volume.mbr.ul * BYTES_PER_KBIT;
       }
    else
       {
         cfg.rb.kbps.cir_kbps = ~0;
         cfg.rb.kbps.cb_bytes = (u64)~0;
       }

     sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_UL]);

    if (nwi->volume.mbr.dl)
       {
         cfg.rb.kbps.cir_kbps = nwi->volume.mbr.dl;
         cfg.rb.kbps.cb_bytes = nwi->volume.mbr.dl * BYTES_PER_KBIT;
       }
    else
       {
         cfg.rb.kbps.cir_kbps = ~0;
         cfg.rb.kbps.cb_bytes = (u64)~0;
       }

     sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_DL]);

  return;
}

static void
upf_detach_qer_mbr_policer (upf_qer_t *qer)
{
  upf_main_t *gtm = &g_upf_main;
  upf_qer_policer_t *pol;

  if (qer->mbr_policer.v == ~0)
    return;

  CHECK_POOL_IS_VALID_NORET(gtm->qer_policers, qer->mbr_policer.v);
  pol = pool_elt_at_index (gtm->qer_policers, qer->mbr_policer.v);
  if (!clib_atomic_sub_fetch (&pol->ref_cnt, 1))
    {
      clib_bihash_add_del_8_8 (&gtm->qer_by_id, &qer->mbr_policer.kv,
                               0 /* is_add */);
      upf_qer_policer_instance_free (pol - gtm->qer_policers);
    }
}

static void
upf_detach_qer_gbr_policer (upf_qer_t *qer)
{
  upf_main_t *gtm = &g_upf_main;
  upf_qer_policer_t *pol;

  if (qer->gbr_policer.v == ~0)
    return;

  CHECK_POOL_IS_VALID_NORET(gtm->qer_policers, qer->gbr_policer.v);
  pol = pool_elt_at_index (gtm->qer_policers, qer->gbr_policer.v);
  if (!clib_atomic_sub_fetch (&pol->ref_cnt, 1))
    {
      clib_bihash_add_del_8_8 (&gtm->qer_by_id, &qer->gbr_policer.kv,
                               0 /* is_add */);
      upf_qer_policer_instance_free (pol - gtm->qer_policers);
    }
}

static void
upf_pfcp_rules_free (upf_session_t *sx, int rule)
{
  struct rules *rules = upf_get_rules (sx, rule);
  upf_pdr_t *pdr;
  upf_far_t *far;
  upf_qer_t *qer;
  upf_urr_t *urr;
  u32 i;

  vec_foreach (pdr, rules->pdr)
  {
    // upf_adf_put_adr_db (pdr->pdi.adr.db_id);
    vec_free (pdr->urr_ids);
    vec_free (pdr->qer_ids);
    vec_free (pdr->activate_predefined_rules);
    vec_free (pdr->pdi.app_name);
    vec_free (pdr->pdi.acl);
    vec_free (pdr->pdi.framed_route);
    vec_free (pdr->pdi.framed_ipv6_route);

    upf_ethernet_packet_filter_t *eth_r;
    vec_foreach(eth_r, pdr->pdi.eth_rule)
    {
        vec_free(eth_r->acl);
        vec_free(eth_r->mac_address);
    }
    vec_free(pdr->pdi.eth_rule);

    if (pdr->pdi.ip_multicast_addressing_info)
    {
        int k;
        vec_foreach_index (k, pdr->pdi.ip_multicast_addressing_info)
        {
            vec_free (vec_elt(pdr->pdi.ip_multicast_addressing_info, k).source_ip_address);
        }
        vec_free (pdr->pdi.ip_multicast_addressing_info);
    }
    if (pdr->ip_multicast_addressing_info)
    {
        int k;
        vec_foreach_index (k, pdr->ip_multicast_addressing_info)
        {
            vec_free (vec_elt(pdr->ip_multicast_addressing_info, k).source_ip_address);
        }
        vec_free (pdr->ip_multicast_addressing_info);
    }
  }
  vec_free (rules->pdr);

  vec_foreach (far, rules->far)
  {
    if (far->forward.outer_header_creation.description != 0)
      upf_pfcp_peer_addr_unref (&far->forward);

    if (far->forward.rewrite != NULL)
    {
        u32 len = _vec_len(far->forward.rewrite);
        if (len > 0)
        {
            if (!(far->id & 0x80000000))
            {
                vec_free (far->forward.rewrite);
                far->forward.rewrite = NULL;
            }
        }
    }
    for (i = 0; i < vec_len (far->forward.header_enrichment); i++)
      {
        vec_free (far->forward.header_enrichment[i].name);
        vec_free (far->forward.header_enrichment[i].value);
      }
    vec_free (far->forward.header_enrichment);
  }
  vec_free (rules->far);
  vec_foreach (urr, rules->urr)
  {
    vec_free (urr->linked_urr_ids); /* Andy added */
    clib_spinlock_free (&urr->lock);
  }
  vec_free (rules->urr);
  vec_foreach (qer, rules->qer)
  {
    upf_detach_qer_mbr_policer (qer);
    upf_detach_qer_gbr_policer (qer);
    clib_spinlock_free (&qer->lock);
  }
  vec_free (rules->qer);
  vec_free (rules->vrf_ip);
  vec_free (rules->local_v4_teid);
  vec_free (rules->local_v6_teid);
  vec_free (rules->peer_v4_teid);
  vec_free (rules->peer_v6_teid);
  vec_free (rules->sdf);

  upf_eth_t *t;
  vec_foreach(t, rules->eth)
    {
      vec_free (t->bidirectional_eth.acl);
      vec_free (t->bidirectional_eth.mac_address);
    }
  vec_free (rules->eth);

  upf_5glan_ip_pdrs_t *ip_pdr;
  vec_foreach (ip_pdr, rules->ip4_pdrs)
  {
    vec_free (ip_pdr->pdr_idx);
  }
  vec_free (rules->ip4_pdrs);

  vec_foreach (ip_pdr, rules->ip6_pdrs)
  {
    vec_free (ip_pdr->pdr_idx);
  }
  vec_free (rules->ip6_pdrs);

  vec_free (rules->l2_keys);

  //hs_free_scratch (rules->scratch);
  //hs_free_database (rules->database);
  upf_acl_free (rules->acl);
  upf_acl_free (rules->acl6);

  vec_free (rules->bar);

  upf_srr_t *srr;
  vec_foreach (srr, rules->srr)
    {
      upf_per_qos_flow_control_t *per_qos_ctrl;
      vec_foreach (per_qos_ctrl, srr->per_qos_flow_ctrl)
        {
          vec_free (per_qos_ctrl->flow_ctrl);
        }
      vec_free (srr->per_qos_flow_ctrl);
    }
  vec_free (rules->srr);

  memset (rules, 0, sizeof (*rules));
}

void
upf_pfcp_clean_far_buffering_list (upf_session_t *sx, upf_far_t *far)
{
  vlib_main_t *vm = vlib_get_main ();
  upf_main_t *um = &g_upf_main;

  clib_spinlock_lock (&sx->lock);
  if (far->buffer_bi[UPF_DL])
    {
      vlib_buffer_free (vm, far->buffer_bi[UPF_DL], far->downlink_buf_n);
      vec_free (far->buffer_bi[UPF_DL]);
    }

  if (far->buffer_bi[UPF_UL])
    {
      vlib_buffer_free (vm, far->buffer_bi[UPF_UL], far->uplink_buf_n);
      vec_free (far->buffer_bi[UPF_UL]);
    }
  if (um->far_buffering_n >= (far->uplink_buf_n + far->downlink_buf_n))
  {
    clib_atomic_fetch_sub (&um->far_buffering_n, far->uplink_buf_n + far->downlink_buf_n);
  }
  else
  {
    UPF_STATISTICS_ADD(UPF_ALL_BUFFERED_SIGNAL_FAR_BUFFERED_INCONSISTENT);
	upf_err("buffer count inconsistent, far_buffering_n %u uplink_buf_n %u downlink_buf_n %u\n",
		 g_upf_main.far_buffering_n, far->uplink_buf_n, far->downlink_buf_n);
	clib_atomic_fetch_and (&um->far_buffering_n, 0);
  }
    um->far_buffering_n = (um->far_buffering_n < 0) ? 0 :um->far_buffering_n;
  clib_atomic_fetch_and (&far->uplink_buf_n, 0);
  clib_atomic_fetch_and (&far->downlink_buf_n, 0);
  clib_spinlock_unlock (&sx->lock);
}

void upf_frer_mac_sess_del(upf_session_t *sx)
{
    upf_main_t *gtm = &g_upf_main;
    upf_frer_mac_t *p_key = NULL;
    upf_frer_mac_sess_t *p_value = NULL;
    uword *new = NULL;
    uword **hh = &gtm->hash_frer_sess_by_mac;
    uword *h = *hh;
    u32 sess_index = sx - gtm->sessions;

    hash_foreach_mem (p_key, p_value, gtm->hash_frer_sess_by_mac,
    ({
        if (p_value->sess_idx[0] == sess_index)
        {
            p_value->sess_idx[0] = ~0;
        }

        if (p_value->sess_idx[1] == sess_index)
        {
            p_value->sess_idx[1] = ~0;
        }

        if ((p_value->sess_idx[0] == ~0) && (p_value->sess_idx[1] == ~0))
        {
            new = hash_unset_mem(h, p_key);
            if (PREDICT_FALSE(h != new))
            {
                *hh = new;
            }
            clib_mem_free(p_key);
            clib_mem_free(p_value);
            break;
        }

    }));

}

int upf_pfcp_session_disable (upf_session_t *sx, int drop_msgs)
{
  struct rules *active = upf_get_rules (sx, SX_ACTIVE);
  sx_server_main_t *sxsm = &sx_server_main;
  upf_main_t *gtm = &g_upf_main;
  ip46_address_fib_t *vrf_ip;
  upf_gtpu4_tunnel_key_t *v4_teid;
  upf_gtpu6_tunnel_key_t *v6_teid;
  upf_far_t *far;
  upf_urr_t *urr;
  clib_bihash_kv_8_8_t kv;
  clib_bihash_kv_24_8_t kv_24_8;
  clib_bihash_kv_40_8_t kv40;

  if(sx)
    vnet_iupf_ue_stat_add_del(sx->user_id.imsi_str, 0, sx->up_seid, 0, unix_time_now_nsec());

  sx->flags |= SX_DELETING;

  clib_memset (&kv, 0, sizeof (kv));
  clib_memset (&kv_24_8, 0, sizeof (kv_24_8));
  clib_memset (&kv40, 0, sizeof (kv40));
  kv.key = sx->up_seid;
  clib_bihash_add_del_8_8 (&gtm->session_by_id, &kv, 0 /* is_del */);
  kv_24_8.key[0] = sx->cp_address.as_u64[0];
  kv_24_8.key[1] = sx->cp_address.as_u64[1];
  kv_24_8.key[2] = sx->cp_seid;
  clib_bihash_add_del_24_8 (&gtm->session_by_fseid, &kv_24_8, 0 /* is_del */);

  if (sx->flags & SX_USER_ID)
    {
      if (sx->user_id.flags & USER_ID_IMSI)
        {
          upf_imsi_to_sess_idxs_t sess_idxs = {0};
          uword *p = hash_get_mem (gtm->session_by_imsi, sx->user_id.imsi_str);
          if (p)
          {
              sess_idxs.p = *p;
              if (sess_idxs.sess_idx1 == sx - gtm->sessions)
                  sess_idxs.sess_idx1 = 0;
              else if (sess_idxs.sess_idx2 == sx - gtm->sessions)
                  sess_idxs.sess_idx2 = 0;

              if (sess_idxs.p == 0)
              {
                  hash_unset_mem (gtm->session_by_imsi, sx->user_id.imsi_str);
              }
              else
              {
                  hash_set_mem (gtm->session_by_imsi, sx->user_id.imsi_str, sess_idxs.p);
              }
          }
        }

      // Add for get ue identity info by liupeng on 2021-09-06 below
      if (sx->user_id.flags & USER_ID_MSISDN)
      {
          hash_unset_mem (gtm->session_by_msisdn, sx->user_id.msisdn_str);
      }
      if (sx->user_id.flags & USER_ID_IMEI)
      {
          hash_unset_mem (gtm->session_by_imei, sx->user_id.imei_str);
      }
    }

  if (sx->flags & SX_UEIP_SET)
      hash_unset_mem (gtm->session_by_ueip, &sx->ue_address);

  // Add for 5G TSN function by liupeng on 2024-02-21 below
  if (sx->pdn_type == PDN_TYPE_ETHERNET)
  {
      hash_unset_mem (gtm->session_by_uemac, &sx->ue_mac);
  }
  // Add for 5G TSN function by liupeng on 2024-02-21 above
  /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 below */
  u32 pkt_rate_sess_idx = vec_search(gtm->pkt_rate_sess_idxs, sx - gtm->sessions);
  if (pkt_rate_sess_idx != ~0)
  {
    vec_del1(gtm->pkt_rate_sess_idxs, pkt_rate_sess_idx);
  }

  /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 above */

  upf_frer_mac_sess_del(sx);

  vec_foreach (v4_teid, active->local_v4_teid)
  {
    upf_v4_teid_add_del (v4_teid, sx, 0);
  }
  vec_foreach (v4_teid, active->peer_v4_teid)
  {
    upf_v4_teid_add_del (v4_teid, sx, 0);
  }
  vec_foreach (v6_teid, active->local_v6_teid)
  {
    upf_v6_teid_add_del (v6_teid, sx, 0);
  }
  vec_foreach (v6_teid, active->peer_v6_teid)
  {
    upf_v6_teid_add_del (v6_teid, sx, 0);
  }
  vec_foreach (vrf_ip, active->vrf_ip)
  {
    upf_vrf_ip_add_del (vrf_ip, sx, 0);
  }

  upf_5glan_ip_pdrs_t *ip_pdr = NULL;
  vec_foreach (ip_pdr, active->ip4_pdrs)
  {
      sx_vn_ip_sx_vrf_add_del (ip_pdr, sx, 0);
  }
  vec_foreach (ip_pdr, active->ip6_pdrs)
  {
      sx_vn_ip_sx_vrf_add_del (ip_pdr, sx, 0);
  }

  upf_5glan_l2_keys_t *l2_key;
  vec_foreach (l2_key, active->l2_keys)
  {
      upf_l2_keys_add_del (l2_key, sx, 0);
  }

  upf_5glan_usr_leave_multicast (sx);

  vec_foreach (far, active->far)
  {
    upf_pfcp_clean_far_buffering_list (sx, far);
  }

  /* stop all timers */
  vec_foreach (urr, active->urr)
  {
    upf_pfcp_session_urr_time_stop (&urr->measurement_period);
    upf_pfcp_session_urr_time_stop (&urr->monitoring_time);
  }

  if (drop_msgs)
    {
      // u32 si = sx - gtm->sessions;
      // u32 *msgs = NULL;
      sx_msg_t *msg;
      u32 *msg_id;

      vec_foreach (msg_id, sx->msgs_id)
      {
        if (PREDICT_TRUE(!pool_is_free_index(sxsm->msg_pool, *msg_id)))
        {
            msg = pool_elt_at_index (sxsm->msg_pool, *msg_id);
            kv.key = msg->seq_no;
            clib_bihash_add_del_8_8 (&sxsm->request_q, &kv, 0 /* is_del*/);
            clib_memcpy (kv40.key, msg->request_key, 32);
            clib_bihash_add_del_40_8 (&sxsm->response_q, &kv40, 0 /* is_del */);
            upf_pfcp_server_timer_stop (msg->timer.handle);
            sx_msg_free (sxsm, msg);
        }
      }
      vec_free (sx->msgs_id);
    }
  upf_pfcp_server_timer_stop (sx->up_inactive_timer.timer.handle);
  if (!hash_validate(sx->hash_teid_by_chooseid))
  {
      hash_free (sx->hash_teid_by_chooseid);
  }
  hash_free (sx->hash_upip4_by_pool);
  hash_free (sx->hash_upip6_by_pool);

  if (sx->vn_nwi_name)
    upf_5glan_usr_leave_from_nwi(sx);

  // free frer bitmap for seq
  if (sx->bitmap_frer_dd[0])
    clib_bitmap_free(sx->bitmap_frer_dd[0]);
  if (sx->bitmap_frer_dd[1])
    clib_bitmap_free(sx->bitmap_frer_dd[1]);

  return 0;
}
#ifndef os_offsetof
#define os_offsetof(TYPE, MEMBER) ((size_t) & ((TYPE *)0)->MEMBER)
#endif
#ifndef container_of
#define container_of(ptr, type, member)                    \
  ({                                                       \
    const typeof(((type *)0)->member) *__mptr = (ptr);     \
    (type *)((char *)__mptr - os_offsetof (type, member)); \
  })
#endif

static void
upf_pfcp_rcu_free_sx_session_info (struct upf_rcu_entry_st *head)
{
  struct rcu_session_info *si =
      container_of (head, struct rcu_session_info, rcu_entry);
  upf_main_t *um = &g_upf_main;
  upf_session_t *sx;

  CHECK_POOL_IS_VALID_NORET(um->sessions, si->idx);
  sx = pool_elt_at_index (um->sessions, si->idx);

  for (size_t i = 0; i < ARRAY_LEN (sx->rules); i++)
    upf_pfcp_rules_free (sx, i);

  vec_free (sx->dnn);
  if (sx->flags & SX_USER_ID)
      upf_free_user_id(&sx->user_id);
  upf_free_node_id(&sx->node_id);
  upf_free_cmcc_user_location_information(&sx->user_location_information);

  clib_spinlock_free (&sx->lock);
  upf_session_instance_free (si->idx);

  upf_rcu_ip_upf_session_update_finish(sx);

  if (sx->vn_nwi_name)
      vec_free(sx->vn_nwi_name);

  if (sx->key_list)
      upf_del_member_from_broadcast_table(sx);
  if (sx->key_multicast_list)
      upf_del_member_from_multicast_table(sx);

  vec_free(sx->mac_address_tag_vec);

  upf_l2_forw_del_mem_by_sess(sx);

  if (sx->flags & SX_L2TP_SESSION_INFO)
      ; /* need to free sx->upf_l2tp_session_information according to upf_l2tp_session_info_init */

  if (sx->flags & SX_L2TP_TUNNEL_INFO)
      ; /* need to free sx->upf_l2tp_tunnel_information according to upf_l2tp_tunnel_info_init */

  upf_debug ("finish free session %u up_seid:0x%lx\n", si->idx, sx->up_seid);

  clib_mem_free (si);
}

void upf_pfcp_session_free (upf_session_t *sx)
{
  upf_main_t *gtm = &g_upf_main;
  struct rcu_session_info *si;

  upf_trace ("[IN] session->fib_index %u\n", sx->fib_index);

  CHECK_POOL_IS_VALID_NORET(gtm->nodes, sx->assoc.node);
  upf_node_assoc_t *n = pool_elt_at_index (gtm->nodes, sx->assoc.node);
  clib_spinlock_lock (&n->lock);
  upf_pfcp_node_assoc_detach_session (sx);
  clib_spinlock_unlock (&n->lock);

  for (size_t i = 0; i < ARRAY_LEN (sx->rules); i++)
    upf_session_cleaning_flow_table (sx, i);

  si = clib_mem_alloc_no_fail (sizeof (*si));
  si->idx = sx - gtm->sessions;

  upf_call_rcu_func (&si->rcu_entry, upf_pfcp_rcu_free_sx_session_info);

  upf_debug ("start free session %u\n", si->idx);
}

#define upf_pfcp_rule_vector_fns(t, REMOVE, CREATE)                       \
  upf_##t##_t *upf_get_##t##_by_id (struct rules *rules,                  \
                                   typeof(((upf_##t##_t *)0)->id) t##_id) \
  {                                                                       \
    upf_##t##_t r = {.id = t##_id};                                       \
    return vec_bsearch (&r, rules->t, upf_##t##_id_compare);              \
  }                                                                       \
                                                                          \
  upf_##t##_t *upf_get_##t (upf_session_t *sx, int rule,                  \
                           typeof(((upf_##t##_t *)0)->id) t##_id)         \
  {                                                                       \
    struct rules *rules = upf_get_rules (sx, rule);                       \
    upf_##t##_t r = {.id = t##_id};                                       \
                                                                          \
    if (rule == SX_PENDING)                                               \
      if (upf_make_pending_##t (sx) != 0)                                 \
        return NULL;                                                      \
    return vec_bsearch (&r, rules->t, upf_##t##_id_compare);              \
  }                                                                       \
                                                                          \
  int upf_create_##t (upf_session_t *sx, upf_##t##_t *t)                  \
  {                                                                       \
    struct rules *rules = upf_get_rules (sx, SX_PENDING);                 \
                                                                          \
    if (upf_make_pending_##t (sx) != 0)                                   \
      return -1;                                                          \
    if (!t)                                                               \
      return -1;                                                          \
    do                                                                    \
      {                                                                   \
        CREATE;                                                           \
      }                                                                   \
    while (0);                                                            \
    vec_add1 (rules->t, *t);                                              \
    vec_sort_with_function (rules->t, upf_##t##_id_compare);              \
    return 0;                                                             \
  }                                                                       \
                                                                          \
  int upf_delete_##t (upf_session_t *sx, u32 t##_id)                      \
  {                                                                       \
    struct rules *rules;                                                  \
    upf_##t##_t r = {.id = t##_id};                                       \
    upf_##t##_t *p;                                                       \
                                                                          \
    if (upf_make_pending_##t (sx) != 0)                                   \
      return -1;                                                          \
    rules = upf_get_rules (sx, SX_PENDING);                               \
    if (!(p = vec_bsearch (&r, rules->t, upf_##t##_id_compare)))          \
      return -1;                                                          \
                                                                          \
    do                                                                    \
      {                                                                   \
        REMOVE;                                                           \
      }                                                                   \
    while (0);                                                            \
                                                                          \
    vec_delete (rules->t, 1, p - rules->t);                               \
    return 0;                                                             \
  }

void upf_pfcp_send_end_marker (upf_session_t *sx, u32 id)
{
  struct rules *rules = upf_get_rules (sx, SX_PENDING);

  vec_add1 (rules->send_end_marker, id);
  upf_debug ("need send end marker for far id: %d\n", id);
}

/* neil.fan@20220328 modify: add "pdr block" concept, divided pdrs into blocks by source interface,
   sort pdr blocks by interface type, and sort pdrs of a block by precedence */
int upf_pdr_create_by_priority (upf_session_t *sx, upf_pdr_t *t)
{
    struct rules *rules = upf_get_rules (sx, SX_PENDING);

    if (upf_make_pending_pdr (sx) != 0)
        return -1;

    if (PREDICT_FALSE(vec_len(rules->pdr) >= UPF_PDR_MAX))
    {
        upf_debug ("pdr create more than max: %d\n", UPF_PDR_MAX);
        return -1;
    }

    ASSERT(t->pdi.src_intf < SRC_INTF_NUM);

    static const void *intf_label[SRC_INTF_NUM + 1] = {
        &&_ACCESS,
        &&_CORE,
        &&_SGI_LAN,
        &&_CP_FUNCTION,
        &&_VN_INTERNAL,
        &&_END};

    pdr_edge_t *block = &rules->pdr_edge[t->pdi.src_intf];
    if (PREDICT_FALSE(block->start == block->end))
    {
        vec_insert_elts(rules->pdr, t, 1, block->start);
    }
    else
    {
        u16 i;
        for (i = block->start; i < block->end; i++)
        {
            if (upf_pfcp_pdr_priority_and_id_compare(&rules->pdr[i], t) > 0)
                break;
        }
        vec_insert_elts(rules->pdr, t, 1, i);
    }
    block->end += 1;
    goto *(intf_label[t->pdi.src_intf + 1]);

#define UPDATE_INDEX(_rules, _index) do { \
        pdr_edge_t *edge = &_rules->pdr_edge[0]; \
        edge[_index].start += 1; \
        edge[_index].end += 1; \
    } while (0)

_ACCESS:/* invalid */
_CORE:
    UPDATE_INDEX(rules, SRC_INTF_CORE);
_SGI_LAN:
    UPDATE_INDEX(rules, SRC_INTF_SGI_LAN);
_CP_FUNCTION:
    UPDATE_INDEX(rules, SRC_INTF_CP);
_VN_INTERNAL:
    UPDATE_INDEX(rules, SRC_INTF_5G_VN);
_END:/* nothing */
  return 0;
#undef UPDATE_INDEX
}

int upf_pdr_delete (upf_session_t *sx, u32 pdr_id)
{
    struct rules *rules;
    upf_pdr_t r = {.id = pdr_id};
    upf_pdr_t *p;

    if (upf_make_pending_pdr (sx) != 0)
        return -1;
    rules = upf_get_rules (sx, SX_PENDING);
    size_t pdr_l = vec_len (rules->pdr);
    if (!(p = vec_lfind (&r, rules->pdr, &pdr_l, upf_pfcp_pdr_id_compare)))
        return -1;

    ASSERT(p->pdi.src_intf < SRC_INTF_NUM);

    static const void *intf_label[SRC_INTF_NUM + 1] = {
        &&_ACCESS,
        &&_CORE,
        &&_SGI_LAN,
        &&_CP_FUNCTION,
        &&_VN_INTERNAL,
        &&_END};

    u8 src_intf = p->pdi.src_intf;
    pdr_edge_t *block = &rules->pdr_edge[src_intf];
    if (PREDICT_FALSE(block->start >= block->end))
    {
        upf_err("src_intf:%u  start:%u is larger than or equals to end:%u", src_intf, block->start, block->end);
        return -1;
    }

    vec_delete (rules->pdr, 1, p - rules->pdr);

    block->end -= 1;
    goto *(intf_label[src_intf + 1]);

#define UPDATE_INDEX(_rules, _index) do { \
        pdr_edge_t *edge = &_rules->pdr_edge[0]; \
        edge[_index].start -= 1; \
        edge[_index].end -= 1; \
    } while (0)

_ACCESS:/* invalid */
_CORE:
    UPDATE_INDEX(rules, SRC_INTF_CORE);
_SGI_LAN:
    UPDATE_INDEX(rules, SRC_INTF_SGI_LAN);
_CP_FUNCTION:
    UPDATE_INDEX(rules, SRC_INTF_CP);
_VN_INTERNAL:
    UPDATE_INDEX(rules, SRC_INTF_5G_VN);
_END:/* nothing */

    return 0;
#undef UPDATE_INDEX
}

upf_pdr_t *upf_get_pdr_by_index (upf_session_t *sx, u32 pdr_index)
{
  if (!sx)
    return NULL;

  struct rules *active = upf_get_rules (sx, SX_ACTIVE);
  if (pdr_index < vec_len (active->pdr))
    {
      return (active->pdr + pdr_index);
    }
  return NULL;
}

upf_pdr_t *upf_get_pdr_by_index_r (struct rules *active, u32 pdr_index)
{
  if ((active) && (pdr_index < vec_len (active->pdr)))
    {
      return (active->pdr + pdr_index);
    }
  return NULL;
}

upf_pdr_t *upf_get_pdr_by_id (struct rules *rules, typeof(((upf_pdr_t *)0)->id) pdr_id)
{
  upf_pdr_t r = {.id = pdr_id};
  size_t pdr_l = vec_len (rules->pdr);
  return vec_lfind (&r, rules->pdr, &pdr_l, upf_pfcp_pdr_id_compare);
}

upf_pdr_t *upf_get_pdr (upf_session_t *sx, int rule, typeof(((upf_pdr_t *)0)->id) pdr_id)
{
  struct rules *rules = upf_get_rules (sx, rule);
  upf_pdr_t r = {.id = pdr_id};
  if (rule == SX_PENDING)
    if (upf_make_pending_pdr (sx) != 0)
      return NULL;
  size_t pdr_l = vec_len (rules->pdr);
  return vec_lfind (&r, rules->pdr, &pdr_l, upf_pfcp_pdr_id_compare);
}

int upf_ip46_address_fib_cmp (const void *a0, const void *b0)
{
  const ip46_address_fib_t *a = a0;
  const ip46_address_fib_t *b = b0;
  int r;

  if ((r = intcmp (a->fib_index, b->fib_index)) != 0)
    return r;

  if ((r = a->prefix - b->prefix) != 0)
    return r;

  return ip46_address_cmp (&a->addr, &b->addr);
}

static int upf_v4_teid_cmp (const void *a, const void *b)
{
  return memcmp (a, b, sizeof (upf_gtpu4_tunnel_key_t));
}

static int upf_v6_teid_cmp (const void *a, const void *b)
{
  return memcmp (a, b, sizeof (upf_gtpu6_tunnel_key_t));
}

static void upf_vrf_ip_add_del (const void *ip, void *si, int is_add)
{
  upf_main_t *um = &g_upf_main;
  const ip46_address_fib_t *vrf_ip = ip;
  upf_session_t *sess = si;
  pfcp_rpc_msg_t *msg;
  sx_add_del_vrf_ip_t *vrf;

  upf_debug ("upf_pfcp: is_add: %d, fib index: %u, IP:%U, "
             "Session_index:%d",
             is_add, vrf_ip->fib_index, format_ip46_address, &vrf_ip->addr,
             IP46_TYPE_ANY, sess - um->sessions);

  u32 msg_size = CLIB_CACHE_LINE_ROUND(sizeof(sx_add_del_vrf_ip_t) + PFCP_RPC_BASIC);
  if (g_upf_auxiliary_switch & UPF_LINUX_MALLOC_SWITCH)
  {
      msg = malloc(msg_size);
  }
  else
  {
      msg = clib_mem_alloc_aligned_no_fail (msg_size, CLIB_CACHE_LINE_BYTES);
  }
  memset (msg, 0, msg_size);

  msg->magic = PFCP_RPC_MAGIC;
  msg->type = PFCP_RPC_SX_ADD_DEL_VRF_IP;

  vrf = &msg->data.sx_add_del_vrf_ip;
  vrf->is_add = is_add;
  vrf->fib_index = vrf_ip->fib_index;
  vrf->session_index = sess - um->sessions;

  if ((um->upf_features & F_UPFF_UEIP) && (!is_add))
    {
      uword *p;
      p = hash_get (um->ip_pool_by_fib_indx, vrf_ip->fib_index);
      if (!p)
        {
          upf_info ("Can't find the ue ip pool by fib_index: %d",
                    vrf_ip->fib_index);
        }
      else
        {
          upf_ue_ip_pool_t *ue_ip_pool;
          uword bits;
          if (PREDICT_TRUE(!pool_is_free_index(um->ip_pools, p[0])))
          {
              ue_ip_pool = pool_elt_at_index (um->ip_pools, p[0]);
              if ((1 == ue_ip_pool->is_v4) &&
                  (ip46_address_is_ip4 (&vrf_ip->addr)))
                {
                  bits = clib_net_to_host_u32 (vrf_ip->addr.ip4.as_u32 -
                                               ue_ip_pool->ip4_low.as_u32);
                  clib_bitmap_set (ue_ip_pool->bitmap_ue_ip4_pool, bits, 0);

                  hash_unset (sess->hash_upip4_by_pool, p[0]);
                }
              if ((1 == ue_ip_pool->is_v6) &&
                  (!ip46_address_is_ip4 (&vrf_ip->addr)))
                {
                  bits = clib_net_to_host_u64 (vrf_ip->addr.ip6.as_u64 -
                                               ue_ip_pool->ip6_low.as_u64);
                  clib_bitmap_set (ue_ip_pool->bitmap_ue_ip6_pool, bits, 0);

                  hash_unset (sess->hash_upip6_by_pool, p[0]);
                }
            }
        }
    }
  if (ip46_address_is_ip4 (&vrf_ip->addr))
    {
      vrf->pfx.fp_addr.ip4.as_u32 = vrf_ip->addr.ip4.as_u32;
      vrf->pfx.fp_len = vrf_ip->prefix;
      vrf->pfx.fp_proto = FIB_PROTOCOL_IP4;
    }
  else
    {
      vrf->pfx.fp_addr.ip6.as_u64[0] = vrf_ip->addr.ip6.as_u64[0];
      vrf->pfx.fp_addr.ip6.as_u64[1] = vrf_ip->addr.ip6.as_u64[1];
      vrf->pfx.fp_len = vrf_ip->prefix;
      vrf->pfx.fp_proto = FIB_PROTOCOL_IP6;
    }
  pfcp_thread_call_main_thread (msg);
}

u32 upf_pfcp_session_is_valid (u32 sidx)
{
  upf_session_t *sx;

  if (pool_is_free_index (g_upf_main.sessions, sidx))
    return 0;

  sx = pool_elt_at_index (g_upf_main.sessions, sidx);
  return !(sx->flags & SX_DELETING);
}

u32 upf_get_valid_session (u32 sidx, upf_session_t **sx)
{
  if (!pool_is_free_index (g_upf_main.sessions, sidx))
  {
    *sx = pool_elt_at_index (g_upf_main.sessions, sidx);
    return !((*sx)->flags & SX_DELETING);
  }
  return 0;
}

gtpu4_tunnel_val_t upf_gtpu4_get_session_index_by_ip_teid (u32 fib_index, ip4_header_t *ip4, u32 teid)
{
  gtpu4_tunnel_kv_t kv = {0};

  kv.k.dst = ip4->dst_address.as_u32;
  kv.k.teid = clib_net_to_host_u32 (teid);
  kv.k.fib_index = fib_index;
  kv.v.as_u64 = ~0;

  upf_trace ("fib: %u TEID: 0x%08x, IP:%U", kv.k.fib_index, kv.k.teid,
             format_ip4_address, &kv.k.dst);

  clib_bihash_search_16_8 (&g_upf_main.v4_tunnel_by_key,
                           (clib_bihash_kv_16_8_t *)&kv,
                           (clib_bihash_kv_16_8_t *)&kv);

  upf_trace ("find src_intf is %u, session_index is %u", kv.v.src_intf, kv.v.session_index);
  return kv.v;
}

gtpu6_tunnel_val_t upf_gtpu6_get_session_index_by_ip_teid (u32 fib_index, ip6_header_t *ip6, u32 teid)
{
  gtpu6_tunnel_kv_t kv = {0};

  kv.k.dst = ip6->dst_address;
  kv.k.teid = clib_net_to_host_u32 (teid);
  kv.k.fib_index = fib_index;
  kv.v.as_u64 = ~0;

  upf_trace ("Key: 0x%lx 0x%lx 0x%lx", kv.k.as_u64[0], kv.k.as_u64[1], kv.k.as_u64[2]);

  clib_bihash_search_24_8 (&g_upf_main.v6_tunnel_by_key,
                           (clib_bihash_kv_24_8_t *)&kv,
                           (clib_bihash_kv_24_8_t *)&kv);

  return kv.v;
}

u32 upf_get_session_index_by_ip4 (u32 fib_index, ip4_address_t *ip4)
{
  u32 lb_index;
  const dpo_id_t *dpo;
  upf_main_t *um = &g_upf_main;

  lb_index = ip4_fib_forwarding_lookup (fib_index, ip4);
  dpo = load_balance_get_bucket_i (load_balance_get (lb_index), 0);
  if (dpo->dpoi_type != um->dpo_type)
    return ~0;
  else
    return dpo->dpoi_index;
}

u32 upf_get_session_index_by_ip6 (u32 fib_index, ip6_address_t *ip6)
{
  u32 lb_index;
  const dpo_id_t *dpo;
  upf_main_t *um = &g_upf_main;

  lb_index = ip6_fib_table_fwding_lookup (fib_index, ip6);
  dpo = load_balance_get_bucket_i (load_balance_get (lb_index), 0);
  if (dpo->dpoi_type != um->dpo_type)
    return ~0;
  else
    return dpo->dpoi_index;
}

static void upf_v4_teid_add_del (const void *teid, void *si, int is_add)
{
  upf_main_t *gtm = &g_upf_main;
  upf_session_t *sess = si;
  const upf_gtpu4_tunnel_key_t *v4_teid = teid;
  gtpu4_tunnel_kv_t kv = {0};
  struct rules *rules = upf_get_rules (sess, SX_PENDING);
  upf_pdr_t *pdr;
  u16       teid_pdr_cnt = 0;
  
  kv.k = v4_teid->gtpu4_tunnel_key;
  kv.v.session_index = sess - gtm->sessions;
  kv.v.src_intf = v4_teid->inf;

  upf_debug ("upf_pfcp: is_add: %d, fib index: %u TEID: 0x%08x, IP:%U, "
             "Session:%p, idx: %u src intf %u.",
             is_add, kv.k.fib_index, kv.k.teid, format_ip4_address, &kv.k.dst,
             sess, kv.v.session_index, kv.v.src_intf);

  clib_bihash_add_del_16_8 (&gtm->v4_tunnel_by_key,
                            (clib_bihash_kv_16_8_t *)&kv, is_add);
  // far's teid don't delete from bitmap_teid
  if (v4_teid->is_far)
    return;

  if(!is_add)
  {
     vec_pdr_foreach(pdr, &rules->pdr_edge[SRC_INTF_ACCESS], rules->pdr)
     {
        if(pdr->pdi.teid.teid       == kv.k.teid  &&
           pdr->pdi.teid.choose_id  == v4_teid->choose_id)
        {
            upf_debug("upf_pfcp: find teid =%d, choose_id=%d", pdr->pdi.teid.teid,pdr->pdi.teid.choose_id);
              
            teid_pdr_cnt++;
        }
     }
  }

  upf_debug("upf_pfcp: teid_pdr_cnt =%d", teid_pdr_cnt);
  
  clib_spinlock_lock (&sx_server_main.lock);
  
  if (gtm->teid_alloc_option == TEID_ALLOC_BY_UP && !is_add && teid_pdr_cnt<1)
  {
      clib_bitmap_set (sx_server_main.bitmap_teid,
                     v4_teid->gtpu4_tunnel_key.teid, 0);

      hash_unset (sess->hash_teid_by_chooseid, v4_teid->choose_id);

      upf_debug("upf_pfcp: delete teid =%d, choose_id=%d", kv.k.teid, v4_teid->choose_id);
  }
  
  clib_spinlock_unlock (&sx_server_main.lock);
}

static void upf_v6_teid_add_del (const void *teid, void *si, int is_add)
{
  upf_main_t *gtm = &g_upf_main;
  upf_session_t *sess = si;
  const upf_gtpu6_tunnel_key_t *v6_teid = teid;
  gtpu6_tunnel_kv_t kv = {0};
  struct rules *rules = upf_get_rules (sess, SX_PENDING);
  upf_pdr_t *pdr;
  u16        teid_pdr_cnt = 0;
  
  kv.k = v6_teid->gtpu6_tunnel_key;
  kv.v.session_index = sess - gtm->sessions;
  kv.v.src_intf = v6_teid->inf;

  upf_debug ("upf_pfcp: is_add: %d, TEID: 0x%08x, IP:%U, Session:%p, idx: %u "
             "src intf %u.",
             is_add, kv.k.teid, format_ip6_address, &kv.k.dst, sess,
             kv.v.session_index, kv.v.src_intf);

  clib_bihash_add_del_24_8 (&gtm->v6_tunnel_by_key,
                            (clib_bihash_kv_24_8_t *)&kv, is_add);
  // far's teid don't delete from bitmap_teid
  if (v6_teid->is_far)
    return;

  if(!is_add)
  {
     vec_pdr_foreach(pdr, &rules->pdr_edge[SRC_INTF_ACCESS], rules->pdr)
     {
        if(pdr->pdi.teid.teid       == kv.k.teid  &&
           pdr->pdi.teid.choose_id  == v6_teid->choose_id)
        {
            upf_debug("upf_pfcp: find teid =%d, choose_id=%d", pdr->pdi.teid.teid,pdr->pdi.teid.choose_id);
              
            teid_pdr_cnt++;
        }
     }
  }

  upf_debug("upf_pfcp: teid_pdr_cnt =%d", teid_pdr_cnt);
  
  clib_spinlock_lock (&sx_server_main.lock);

  if (gtm->teid_alloc_option == TEID_ALLOC_BY_UP && !is_add && teid_pdr_cnt<1)
  {
     clib_bitmap_set (sx_server_main.bitmap_teid,
                     v6_teid->gtpu6_tunnel_key.teid, 0);

     hash_unset (sess->hash_teid_by_chooseid, v6_teid->choose_id);

     upf_debug("upf_pfcp: delete teid =%d, choose_id=%d", kv.k.teid, v6_teid->choose_id);
  }
  
  clib_spinlock_unlock (&sx_server_main.lock);
}

static int upf_rules_add_v4_teid (struct rules *r, const ip4_address_t *addr, u32 teid, u8 choose_id, u32 fib_index, u8 inf, u8 is_far)
{
  upf_gtpu4_tunnel_key_t key = {0};
  upf_gtpu4_tunnel_key_t *tmp_key = NULL;
  upf_gtpu4_tunnel_key_t *rule = NULL;
  u16 exist = 0;

  key.gtpu4_tunnel_key.teid = teid;
  key.gtpu4_tunnel_key.dst = addr->as_u32;
  key.gtpu4_tunnel_key.fib_index = fib_index;
  key.inf = inf;
  key.is_far    = is_far;
  key.choose_id = choose_id;
  
  if (is_far)
      rule = r->peer_v4_teid;
  else
      rule = r->local_v4_teid;
  vec_foreach (tmp_key, rule)
  {
    if (!memcmp (tmp_key, &key, sizeof (upf_gtpu4_tunnel_key_t)))
      {
        exist = 1;
        break;
      }
  }
  if (!exist)
  {
    if (is_far)
        vec_add1 (r->peer_v4_teid, key);
    else
        vec_add1 (r->local_v4_teid, key);

    /* upf_warn ("+++++pending:fib index: %u TEID: 0x%08x, IP:%U, unused:%u, is_far: %u src intf %u.",
           key.gtpu4_tunnel_key.fib_index, key.gtpu4_tunnel_key.teid, format_ip4_address, &key.gtpu4_tunnel_key.dst,
           key.gtpu4_tunnel_key.unused, key.is_far, key.inf); */
  }
  else
    return 0;

  return 1;
}

static int upf_rules_add_v6_teid (struct rules *r, const ip6_address_t *addr, u32 teid, u8 choose_id, u32 fib_index, u8 inf, u8 is_far)
{
  upf_gtpu6_tunnel_key_t key = {0};
  upf_gtpu6_tunnel_key_t *tmp_key = NULL;
  upf_gtpu6_tunnel_key_t *rule = NULL;
  u16 exist = 0;

  key.gtpu6_tunnel_key.dst = *addr;
  key.gtpu6_tunnel_key.teid = teid;
  key.gtpu6_tunnel_key.fib_index = fib_index;
  key.inf = inf;
  key.is_far = is_far;
  key.choose_id = choose_id;
  
  if (is_far)
      rule = r->peer_v6_teid;
  else
      rule = r->local_v6_teid;
  vec_foreach (tmp_key, rule)
  {
    if (!memcmp (tmp_key, &key, sizeof (upf_gtpu6_tunnel_key_t)))
      {
        exist = 1;
        break;
      }
  }
  if (!exist)
    {
      if (is_far)
        vec_add1 (r->peer_v6_teid, key);
      else
        vec_add1 (r->local_v6_teid, key);
    }
  else
    return 0;

  return 1;
}

int ip46_address_5glan_fib_cmp (const void *a0, const void *b0)
{
   const ip46_address_fib_t *a = &((upf_5glan_ip_pdrs_t *)a0)->vrf;
   const ip46_address_fib_t *b = &((upf_5glan_ip_pdrs_t *)b0)->vrf;
   int r;

   /* if ((r = intcmp (a->fib_index, b->fib_index)) != 0)
   return r; */

   r = a->prefix - b->prefix;
   if (r != 0)
       return r;

   return ip46_address_cmp (&a->addr, &b->addr);
}

int upf_5glan_l2_keys_cmp (const void *a0, const void *b0)
{
    return memcmp(a0, b0, sizeof(upf_l2_key_t));
}

int upf_broadcast_keys_cmp (const void *a0, const void *b0)
{
    return memcmp(a0, b0, sizeof(upf_broadcast_key_t));
}

void bidirection_acl_learning(struct rules *pending, acl_rule_t *v_acl, u16 *has_bi)
{
    acl_rule_t *tmp_acl_rule;
    vec_foreach (tmp_acl_rule, v_acl)
    {
        /*only sdf with acl_out has flow description */
        if (tmp_acl_rule->id && (tmp_acl_rule->direction == ACL_OUT) && tmp_acl_rule->flag)
        {
            vec_alloc (pending->sdf, 1);
            upf_sdf_t *sdf = vec_end (pending->sdf);
            clib_memcpy (&sdf->bidirectional_sdf, tmp_acl_rule, sizeof (acl_rule_t));
            sdf->id = tmp_acl_rule->id;
            _vec_len (pending->sdf)++;
            vec_sort_with_function(pending->sdf, upf_sdf_id_compare);
            *has_bi = 1;
        }
    }
}

int bidirection_acl_query(upf_session_t *sx, struct rules *pending, acl_rule_t *v_acl)
{
    acl_rule_t *tmp_acl_rule;
    vec_foreach (tmp_acl_rule, v_acl)
    {
        if (tmp_acl_rule->id && !tmp_acl_rule->flag)
        {
            upf_sdf_t *sdf = upf_get_sdf (sx, SX_PENDING, tmp_acl_rule->id);
            if (sdf != NULL)
            {
                clib_memcpy (tmp_acl_rule, &sdf->bidirectional_sdf, sizeof (acl_rule_t));
                tmp_acl_rule->flag = 1;
            }
            else
            {
                upf_warn ("sdf id:%d not found", tmp_acl_rule->id);
                return -1;
            }
        }
    }
    return 0;
}

void bidirection_eth_learning(struct rules *pending, upf_ethernet_packet_filter_t *v_eth, u16 *has_bi)
{
    upf_ethernet_packet_filter_t *f;
    vec_foreach (f, v_eth)
    {
        if (ISSET_BIT(f->grp.fields, ETHERNET_PACKET_FILTER_ETHERNET_FILTER_ID)
            && ISSET_BIT(f->grp.fields, ETHERNET_PACKET_FILTER_ETHERNET_FILTER_PROPERTIES))
        {
            upf_eth_t *n_eth;
            APPEND_NEW_MEMBER_WITHOUT_CLEAR(pending->eth, n_eth);
            clib_memcpy (&n_eth->bidirectional_eth, f, sizeof (*f));
            n_eth->bidirectional_eth.acl = vec_dup(f->acl);
            n_eth->bidirectional_eth.mac_address = vec_dup(f->mac_address);
            n_eth->id = f->ethernet_filter_id;
            vec_sort_with_function(pending->eth, upf_eth_id_compare);
            *has_bi = 1;
        }
    }
}

int bidirection_eth_query(upf_session_t *sx, struct rules *pending, upf_ethernet_packet_filter_t *v_eth)
{
    upf_ethernet_packet_filter_t *f;
    vec_foreach (f, v_eth)
    {
        if (ISSET_BIT(f->grp.fields, ETHERNET_PACKET_FILTER_ETHERNET_FILTER_ID)
            && !ISSET_BIT(f->grp.fields, ETHERNET_PACKET_FILTER_ETHERNET_FILTER_PROPERTIES))
        {
            upf_eth_t *o_eth = upf_get_eth (sx, SX_PENDING, f->ethernet_filter_id);
            if (o_eth != NULL)
            {
                clib_memcpy (f, &o_eth->bidirectional_eth, sizeof (*o_eth));
                f->acl = vec_dup(o_eth->bidirectional_eth.acl);
                f->mac_address = vec_dup(o_eth->bidirectional_eth.mac_address);
                RESET_BIT(f->grp.fields, ETHERNET_PACKET_FILTER_ETHERNET_FILTER_PROPERTIES);
            }
            else
            {
                upf_warn ("ethernet_filter_id id:%d not found", f->ethernet_filter_id);
                return -1;
            }
        }
    }
    return 0;
}

int upf_session_ueip_handle(pfcp_ue_ip_address_t *p, upf_session_t *sx)
{
    if (p->flags & IE_UE_IP_ADDRESS_V4)
    {
        sx->ue_address.ip4.as_u32 = p->ip4.as_u32;
    }
    else if (p->flags & IE_UE_IP_ADDRESS_V6)
    {
        sx->ue_address.ip6.as_u64[0] = p->ip6.as_u64[0];
        sx->ue_address.ip6.as_u64[1] = p->ip6.as_u64[1];
    }
    else
    {
        return 0;
    }

    if (-1 == upf_check_session_same_ueip(sx))
    {
        upf_err("Reject the same ueip: %U", format_ip46_address, &sx->ue_address, IP46_TYPE_ANY);
        return -1;
    }

    return 0;
}

static int upf_build_pdr_rules (upf_session_t *sx)
{
    upf_main_t *gtm = &g_upf_main;
    u16 has_v4 = 0;
    u16 exist = 0;
    u16 has_v6 = 0;
    u16 has_bi = 0; /* birectional SDF or Ethernet packet filter */
    struct rules *pending = upf_get_rules (sx, SX_PENDING);
    struct rules *active = upf_get_rules (sx, SX_ACTIVE);
    upf_pdr_t *pdr;
    u8 eth_pdu_flag = 0;

    if (pool_elts (gtm->nwis) == 0)
    {
        upf_warn ("not configured any nwis!");
        return -1;
    }

    vec_outer_pdr_foreach(pdr, pending->pdr_edge, pending->pdr)
    {
        if ((~0 != pdr->far_id) && (!(upf_get_far_by_id (pending, pdr->far_id) || upf_get_far_by_id (active, pdr->far_id))))
        {
            upf_warn ("far_id:%d is not found.", pdr->far_id);
            return -1;
        }

        u32 fib_index = 0;

        if (0 != upf_session_ueip_handle(&pdr->pdi.ue_addr, sx))
            return -1;

        /*
         * From 3GPP TS 29.244 version 14.3.0, Table *******-2
         * NOTE 2: When a Local F-TEID is provisioned in the PDI, the Network Instance shall relate to the IP
         *         address of the F-TEID. Otherwise, the Network Instance shall relate to the UE IP address.
         */
        /* create UE IP route from SGi Network Instance into Session */
        if (!(pdr->pdi.fields & F_PDI_LOCAL_F_TEID) &&
            pdr->pdi.fields & (F_PDI_UE_IP_ADDR | F_PDI_FRAMED_ROUTE | F_PDI_FRAMED_IPV6_ROUTE))
        {
            ip46_address_fib_t *vrf_ip;
            exist = 0;
            if (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_V4)
            {
                vec_foreach (vrf_ip, pending->vrf_ip)
                {
                    if (ip4_address_is_equal (&vrf_ip->addr.ip4, &pdr->pdi.ue_addr.ip4))
                    {
                        exist = 1;
                        break;
                    }
                }

                if (!exist)
                {
                    vec_alloc (pending->vrf_ip, 1);
                    vrf_ip = vec_end (pending->vrf_ip);
                    ip46_address_set_ip4 (&vrf_ip->addr, &pdr->pdi.ue_addr.ip4);
                    vrf_ip->prefix = 32;

                    if ((pdr->pdi.fields & F_PDI_NWI) && (pdr->pdi.nwi != ~0))
                    {
                        if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, pdr->pdi.nwi)))
                        {
                            upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, pdr->pdi.nwi);
                            if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP4, nwi->vrf)))
                            {
                                upf_warn ("vrf(table_id) %d in not (yet) defined for IPv4", nwi->vrf);
                                return -1;
                            }
                        }
                    }

                    vrf_ip->fib_index = fib_index;
                    vrf_ip->src_inf = pdr->pdi.src_intf;
                    _vec_len (pending->vrf_ip)++;
                }
        }

        if (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_V6)
        {
            exist = 0;
            vec_foreach (vrf_ip, pending->vrf_ip)
            {
                if (ip6_address_is_equal (&vrf_ip->addr.ip6, &pdr->pdi.ue_addr.ip6))
                {
                    exist = 1;
                    break;
                }
            }
            if (!exist)
            {
                vec_alloc (pending->vrf_ip, 1);
                vrf_ip = vec_end (pending->vrf_ip);
                vrf_ip->addr.ip6 = pdr->pdi.ue_addr.ip6;
                if (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_IPv6D)
                    vrf_ip->prefix = 64 - pdr->pdi.ue_addr.prefix_delegation_length;
                else if (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_IP6PL)
                    vrf_ip->prefix = pdr->pdi.ue_addr.prefix_length;
                else
                    vrf_ip->prefix = 64;

                if ((pdr->pdi.fields & F_PDI_NWI) && (pdr->pdi.nwi != ~0))
                {
                    if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, pdr->pdi.nwi)))
                    {
                        upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, pdr->pdi.nwi);
                        if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP6, nwi->vrf)))
                        {
                            upf_warn ("vrf(table_id) %d in not (yet) defined for IPv6", nwi->vrf);
                            return -1;
                        }
                    }
                }

                vrf_ip->fib_index = fib_index;
                vrf_ip->src_inf = pdr->pdi.src_intf;
                _vec_len (pending->vrf_ip)++;
            }
        }

        if (pdr->pdi.fields & F_PDI_FRAMED_ROUTE)
        {
            fib_prefix_t *framed_route;
            vec_foreach (framed_route, pdr->pdi.framed_route)
            {
                vec_alloc (pending->vrf_ip, 1);
                vrf_ip = vec_end (pending->vrf_ip);
                ip46_address_set_ip4 (&vrf_ip->addr, &framed_route->fp_addr.ip4);
                vrf_ip->prefix = framed_route->fp_len;

                if ((pdr->pdi.fields & F_PDI_NWI) && (pdr->pdi.nwi != ~0))
                {
                    if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, pdr->pdi.nwi)))
                    {
                        upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, pdr->pdi.nwi);
                        if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP4, nwi->vrf)))
                        {
                            upf_warn ("vrf(table_id) %d in not (yet) defined for IPv4", nwi->vrf);
                            return -1;
                        }
                    }
                }

                vrf_ip->fib_index = fib_index;
                vrf_ip->src_inf = pdr->pdi.src_intf;
                _vec_len (pending->vrf_ip)++;
            }
        }

        if (pdr->pdi.fields & F_PDI_FRAMED_IPV6_ROUTE)
        {
            fib_prefix_t *framed_ipv6_route;
            vec_foreach (framed_ipv6_route, pdr->pdi.framed_ipv6_route)
            {
                vec_alloc (pending->vrf_ip, 1);
                vrf_ip = vec_end (pending->vrf_ip);
                ip6_address_copy (&vrf_ip->addr.ip6, &framed_ipv6_route->fp_addr.ip6);
                vrf_ip->prefix = framed_ipv6_route->fp_len;

                if ((pdr->pdi.fields & F_PDI_NWI) && (pdr->pdi.nwi != ~0))
                {
                    if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, pdr->pdi.nwi)))
                    {
                        upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, pdr->pdi.nwi);
                        if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP6, nwi->vrf)))
                        {
                            upf_warn ("vrf(table_id) %d in not (yet) defined for IPv6", nwi->vrf);
                            return -1;
                        }
                    }
                }

                vrf_ip->fib_index = fib_index;
                vrf_ip->src_inf = pdr->pdi.src_intf;
                _vec_len (pending->vrf_ip)++;
                }
            }
        }

        /* register Local F-TEIDs */
        if (pdr->pdi.fields & F_PDI_LOCAL_F_TEID)
        {
            if (pdr->pdi.teid.flags & F_TEID_V4)
            {
                has_v4 = 1;

                if ((pdr->pdi.fields & F_PDI_NWI) && (pdr->pdi.nwi != ~0))
                {
                    if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, pdr->pdi.nwi)))
                    {
                        upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, pdr->pdi.nwi);
                        if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP4, nwi->vrf)))
                        {
                            upf_warn ("vrf(table_id) %d in not (yet) defined for IPv4", nwi->vrf);
                            return -1;
                        }
                    }
                }
                upf_rules_add_v4_teid (pending, &pdr->pdi.teid.ip4, pdr->pdi.teid.teid, pdr->pdi.teid.choose_id, fib_index, pdr->pdi.src_intf, 0);
            }

            if (pdr->pdi.teid.flags & F_TEID_V6)
            {
                has_v6 = 1;
                if ((pdr->pdi.fields & F_PDI_NWI) && (pdr->pdi.nwi != ~0))
                {
                    if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, pdr->pdi.nwi)))
                    {
                        upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, pdr->pdi.nwi);
                        if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP6, nwi->vrf)))
                        {
                            upf_warn ("vrf(table_id) %d in not (yet) defined for IPv6", nwi->vrf);
                            return -1;
                        }
                    }
                }

                upf_rules_add_v6_teid (pending, &pdr->pdi.teid.ip6, pdr->pdi.teid.teid, pdr->pdi.teid.choose_id, fib_index, pdr->pdi.src_intf, 0);
            }
        }

        if (pdr->pdi.fields & F_PDI_SDF_FILTER)
            bidirection_acl_learning(pending, pdr->pdi.acl, &has_bi);

        if (pdr->pdi.fields & F_PDI_ETHERNET_PACKET_FILTER)
            bidirection_eth_learning(pending, pdr->pdi.eth_rule, &has_bi);

        if (pdr->pdi.fields & F_PDI_ETH_PDU_SESSION_INFO)
            eth_pdu_flag = pdr->pdi.eth_pdu_flag;

        //if ((pdr->pdi.fields & F_PDI_APPLICATION_ID))
        {
            pending->flags |= SX_APP;
        }
    }

    /* neil.fan@20220530 add: leaning bi-sdf or bi-ethernet from 5G VN PDR and reference by all PDRs by this session,
     * to fully support Bidirectional SDF filters and Bidirectional Ethernet Filter.
     */
    vec_pdr_foreach(pdr, &pending->pdr_edge[SRC_INTF_5G_VN], pending->pdr)
    {
        if (pdr->pdi.fields & F_PDI_SDF_FILTER)
            bidirection_acl_learning(pending, pdr->pdi.acl, &has_bi);
        if (pdr->pdi.fields & F_PDI_ETHERNET_PACKET_FILTER)
            bidirection_eth_learning(pending, pdr->pdi.eth_rule, &has_bi);

        if (pdr->pdi.fields & F_PDI_LOCAL_F_TEID)
        {
            upf_warn ("error for vn pdr[%d] has teid", pdr->id);
            return -1;
        }

        if (~0 != pdr->far_id)
        {
            upf_far_t *far_p = upf_get_far_by_id (pending, pdr->far_id);
            upf_far_t *far_a = upf_get_far_by_id (active, pdr->far_id);
            if (far_p)
                pdr->is_to_vn_group = (far_p->forward.dst_intf == DST_INTF_CORE);
            else if (far_a)
                pdr->is_to_vn_group = (far_a->forward.dst_intf == DST_INTF_CORE);
            else
            {
                upf_warn ("far_id:%d is not found.", pdr->far_id);
                return -1;
            }
        }

        if (pdr->pdi.fields & F_PDI_ETH_PDU_SESSION_INFO)
            eth_pdu_flag = pdr->pdi.eth_pdu_flag;
    }

    if (sx->eth_pdu_flag != eth_pdu_flag)
        sx->eth_pdu_flag = eth_pdu_flag;

    if (has_bi)
    {
        vec_foreach(pdr, pending->pdr)
        {
            if ((pdr->pdi.fields & F_PDI_SDF_FILTER) && (-1 == bidirection_acl_query(sx, pending, pdr->pdi.acl)))
                return -1;

            if ((pdr->pdi.fields & F_PDI_ETHERNET_PACKET_FILTER)
                && (-1 == bidirection_eth_query(sx, pending, pdr->pdi.eth_rule)))
                return -1;
        }
    }

    if (gtm->pdr_search_perf)
    {
        if (has_v4)
            upf_bdt_database_update (sx);
        if (has_v6)
            upf_bdt6_database_update (sx);
    }

    return 0;
}

int upf_build_far_rules(struct rules *pending)
{
    upf_main_t *gtm = &g_upf_main;
    upf_far_t *far;

    vec_foreach (far, pending->far)
    {
        if (far->forward.outer_header_creation.description == 0)
            continue;

        if (far->forward.outer_header_creation.description & OUTER_HEADER_CREATION_GTP_IP4)
        {
            u32 fib_index = 0;
            g_upf_relate_IpAddr[0].dstIp4Addr = far->forward.outer_header_creation.ip.ip4;
            if (far->forward.flags & FAR_F_NETWORK_INSTANCE)
            {
                if (far->forward.table_id != ~0)
                {
                    if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP4, far->forward.table_id)))
                    {
                        upf_warn("vrf(table_id) %d in not (yet) defined for IPv4", far->forward.table_id);
                        return -1;
                    }
                }
            }
            upf_rules_add_v4_teid (pending, &far->forward.outer_header_creation.ip.ip4,
                               far->forward.outer_header_creation.teid, 0, fib_index, far->forward.dst_intf, 1);
            ip4_header_t *ip = (ip4_header_t *)far->forward.rewrite;
            if(!ip)
                continue;

            upf_pdr_t *pdr;
            vec_outer_pdr_foreach(pdr, pending->pdr_edge, pending->pdr)
            {
                /* neil.fan@20230111 add conditions: nwi should be equal, because 4G/5G has different nwi, and Access IP(N3/P58) */
                if ((pdr->pdi.fields & F_PDI_LOCAL_F_TEID) && (pdr->pdi.src_intf == far->forward.dst_intf)
                    && (pdr->pdi.teid.flags & F_TEID_V4))
                {
                    u32 fib_index = ~0;
                    if ((pdr->pdi.fields & F_PDI_NWI) && (far->forward.flags & FAR_F_NETWORK_INSTANCE)
                        && (pdr->pdi.nwi == far->forward.nwi))
                    {
                        if (pdr->pdi.nwi != ~0)
                        {
                            if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, pdr->pdi.nwi)))
                            {
                                upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, pdr->pdi.nwi);
                                if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP4, nwi->vrf)))
                                {
                                    upf_warn ("vrf(table_id) %d in not (yet) defined for IPv4", nwi->vrf);
                                    return -1;
                                }
                            }
                        }
                    }

                    if (fib_index == far->forward.fib_index)
                    {
                        ip->src_address = pdr->pdi.teid.ip4;
                        ip->checksum = ip4_header_checksum (ip);
                        g_upf_relate_IpAddr[0].srcIp4Addr = ip->src_address;
                        upf_trace ("n3 use IP4 src: %U", format_ip4_address, &ip->src_address);
                        break;
                    }
                    else
                        upf_debug("n3 use IP4 src set failed!");
                }
            }
        }
        else if (far->forward.outer_header_creation.description & OUTER_HEADER_CREATION_GTP_IP6)
        {
            u32 fib_index = 0;
            g_upf_relate_IpAddr[0].dstIp6Addr = far->forward.outer_header_creation.ip.ip6;
            if (far->forward.flags & FAR_F_NETWORK_INSTANCE)
            {
                if (far->forward.table_id != ~0)
                {
                    if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP6, far->forward.table_id)))
                    {
                        upf_warn("vrf(table_id) %d in not (yet) defined for IPv6", far->forward.table_id);
                        return -1;
                    }
                }
            }

            upf_rules_add_v6_teid (pending, &far->forward.outer_header_creation.ip.ip6,
                far->forward.outer_header_creation.teid, 0, fib_index, far->forward.dst_intf, 1);
            ip6_header_t *ip = (ip6_header_t *)far->forward.rewrite;
            upf_pdr_t *pdr;
            vec_outer_pdr_foreach(pdr, pending->pdr_edge, pending->pdr)
            {
                u32 fib_index = ~0;
                if ((pdr->pdi.fields & F_PDI_LOCAL_F_TEID) && (pdr->pdi.src_intf == far->forward.dst_intf)
                    && (pdr->pdi.teid.flags & F_TEID_V6))
                {
                    if ((pdr->pdi.fields & F_PDI_NWI) && (far->forward.flags & FAR_F_NETWORK_INSTANCE)
                        && (pdr->pdi.nwi == far->forward.nwi))
                    {
                        if (pdr->pdi.nwi != ~0)
                        {
                            if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, pdr->pdi.nwi)))
                            {
                                upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, pdr->pdi.nwi);
                                if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP6, nwi->vrf)))
                                {
                                    upf_warn ("vrf(table_id) %d in not (yet) defined for IPv6", nwi->vrf);
                                    return -1;
                                }
                            }
                        }
                    }

                    if (fib_index == far->forward.fib_index)
                    {
                        ip->src_address = pdr->pdi.teid.ip6;
                        g_upf_relate_IpAddr[0].srcIp6Addr = ip->src_address;
                        upf_trace ("n3 use IPv6 as src: %U", format_ip6_address, &ip->src_address);
                        break;
                    }
                }
            }
        }

        u32 peer_idx = upf_pfcp_peer_addr_ref (&far->forward);
        if (~0 == peer_idx)
            return -1;
        far->forward.peer_idx = peer_idx;
    }

    return 0;
}


void upf_check_handover_onging(upf_session_t *sx)
{
    sx->handover_ongoing = false;
    struct rules *active = upf_get_rules (sx, SX_ACTIVE);
    upf_pdr_t *pdr;
    upf_far_t *far;
    vec_pdr_foreach(pdr, &active->pdr_edge[SRC_INTF_ACCESS], active->pdr)
    {
        far = upf_get_far_by_id (active, pdr->far_id);
        if(far && far->forward.dst_intf == SRC_INTF_ACCESS)
        {
            sx->handover_ongoing = true;
            return ;
        }
    }
    return ;
}

int upf_session_update_apply (upf_session_t *sx)
{
    struct rules *pending = upf_get_rules (sx, SX_PENDING);
    struct rules *active = upf_get_rules (sx, SX_ACTIVE);
    int pending_pdr, pending_far, pending_urr, pending_qer, pending_bar, pending_srr;
    sx_server_main_t *sxsm = &sx_server_main;
    u32 si = sx - g_upf_main.sessions;
    u32 is_vn_upate = 0;
    upf_urr_t *urr;
    upf_qer_t *qer;
    upf_far_t *far;

    if (!pending->pdr && !pending->far && !pending->urr && !pending->qer && !pending->bar && !pending->srr)
        return 0;

    pending_pdr = !!pending->pdr;
    pending_far = !!pending->far;
    pending_urr = !!pending->urr;
    pending_qer = !!pending->qer;
    pending_bar = !!pending->bar;
    pending_srr = !!pending->srr;

    if (pending_pdr)
    {
        if (upf_build_pdr_rules (sx) != 0)
            return -1;
        is_vn_upate = (has_edge_pdr(&pending->pdr_edge[SRC_INTF_5G_VN]) || has_edge_pdr(&active->pdr_edge[SRC_INTF_5G_VN]));
        if (is_vn_upate)
        {
            if (sx->pdn_type != PDN_TYPE_ETHERNET)
            {
                if (build_sx_5glan_ip_sx(sx) != 0)
                    return -1;
            }
            else
            {
                if (upf_build_5glan_eth_pdr(sx) != 0)
                    return -1;
            }
        }
    }
    else
    {
        clib_spinlock_lock (&sx->lock);
        memcpy(&pending->pdr_edge, &active->pdr_edge, sizeof(active->pdr_edge));
        pending->pdr = active->pdr;
        pending->ip4_pdrs =  active->ip4_pdrs;
        pending->ip6_pdrs =  active->ip6_pdrs;

        /* clear active members immediately because these members used by this function only.(not used for pkt process) */
        SWITCH_AND_CLEAR(pending->vrf_ip, active->vrf_ip);
        SWITCH_AND_CLEAR(pending->local_v4_teid, active->local_v4_teid);
        SWITCH_AND_CLEAR(pending->local_v6_teid, active->local_v6_teid);
        SWITCH_AND_CLEAR(pending->sdf, active->sdf);
        SWITCH_AND_CLEAR(pending->eth, active->eth);
        SWITCH_AND_CLEAR(pending->l2_keys, active->l2_keys);

        //pending->database = active->database;
        //pending->scratch = active->scratch;
        pending->acl = active->acl;
        pending->acl6 = active->acl6;
        pending->flags = active->flags;
        clib_spinlock_unlock (&sx->lock);
    }

    if (pending_far)
    {
        if (upf_build_far_rules(pending))
            return -1;
    }
    else
    {
        pending->far = active->far;
        SWITCH_AND_CLEAR(pending->peer_v4_teid, active->peer_v4_teid);
        SWITCH_AND_CLEAR(pending->peer_v6_teid, active->peer_v6_teid);
    }

    if (!pending_urr)
        pending->urr = active->urr;

    if (pending_qer)
    {
        vec_foreach (qer, pending->qer)
        {
            upf_attach_qer_policer (qer);
        }
    }
    else
        pending->qer = active->qer;

    if (!pending_bar)
        pending->bar = active->bar;

    if (!pending_srr)
        pending->srr = active->srr;

    if (pending_pdr)
    {
        upf_vec_diff (pending->vrf_ip, active->vrf_ip, upf_ip46_address_fib_cmp, upf_vrf_ip_add_del, sx);

        vec_diff_ex (pending->local_v4_teid, active->local_v4_teid, upf_v4_teid_cmp, upf_v4_teid_add_del, sx);
        vec_diff_ex (pending->local_v6_teid, active->local_v6_teid, upf_v6_teid_cmp, upf_v6_teid_add_del, sx);
    }

    if (pending_far)
    {
        vec_diff_ex (pending->peer_v4_teid, active->peer_v4_teid, upf_v4_teid_cmp, upf_v4_teid_add_del, sx);
        vec_diff_ex (pending->peer_v6_teid, active->peer_v6_teid, upf_v6_teid_cmp, upf_v6_teid_add_del, sx);
    }

    if (is_vn_upate)
        vec_diff_ex(pending->l2_keys, active->l2_keys, upf_5glan_l2_keys_cmp, upf_l2_keys_add_del, sx);

    /* flip the switch */
    clib_atomic_fetch_xor (&sx->active, SX_PENDING);

    /* sx->active has changed, but the pending and active pointers not updating accordanyly */
    if (is_vn_upate)
    {
        vec_diff_without_sort(pending->ip4_pdrs, active->ip4_pdrs, ip46_address_5glan_fib_cmp, sx_vn_ip_sx_vrf_add_del, sx);
        vec_diff_without_sort(pending->ip6_pdrs, active->ip6_pdrs, ip46_address_5glan_fib_cmp, sx_vn_ip_sx_vrf_add_del, sx);
    }

    pending = upf_get_rules (sx, SX_PENDING);
    active = upf_get_rules (sx, SX_ACTIVE);

    if (active->send_end_marker)
    {
        u32 *send_em;
        vec_foreach (send_em, active->send_end_marker)
        {
            upf_far_t r = {.id = *send_em};
            if (!(far = vec_bsearch (&r, pending->far, upf_far_id_compare)))
                continue;

            upf_debug ("TODO: send_end_marker for FAR %d", far->id);
            upf_gtpu_send_end_marker (far, active);
        }
        vec_free (active->send_end_marker);
    }

    vec_foreach (far, active->far)
    {
        if (far->downlink_buf_n && (far->apply_action & F_APPLY_FORW))
        {
            upf_debug(" modify buffering pkt to farword and related ueip: %U", format_ip46_address, &sx->ue_address, IP46_TYPE_ANY);
            upf_downlink_forward_request (sx, far->id);
            UPF_PDU_SESS_STATISTICS_ADD(sx, CP_TO_UP_FWD_BUFFER);
        }
    }

    vec_foreach (urr, active->urr)
    {
        if (urr->update_flags & SX_URR_UPDATE_MEASUREMENT_PERIOD)
        {
            upf_pfcp_session_urr_time_start_stop (si, &urr->measurement_period,
                !!(urr->triggers & REPORTING_TRIGGER_PERIODIC_REPORTING), 1);
        }

        if (urr->update_flags & SX_URR_UPDATE_MONITORING_TIME)
        {
            upf_pfcp_session_urr_time_abs_start_stop (si, &urr->monitoring_time);
        }

        if (urr->update_flags & SX_URR_UPDATE_IDT)
        {
            urr->idt.base = sxsm->now;
            upf_pfcp_session_urr_time_start_stop (si, &urr->idt, 1, 1);
        }
        else
        {
            if (urr->update_flags & SX_URR_UPDATE_TIME_THRESHOLD)
            {
                urr->time_threshold.base = sxsm->now;
                urr->time_threshold.period = urr->time_threshold.total_time;
                upf_pfcp_session_urr_time_start_stop (si, &urr->time_threshold, 1, 1);
            }
            if (urr->update_flags & SX_URR_UPDATE_TIME_QUOTA)
            {
                urr->time_quota.base = sxsm->now;
                urr->time_quota.period = urr->time_quota.total_time;
                upf_pfcp_session_urr_time_start_stop (si, &urr->time_quota, 1, 1);
            }
        }
        if (urr->update_flags & SX_URR_UPDATE_STOP_TRAFFIC)
        {
            /* neil.fan@20230107 fix timestamp base too old within g_upf_main.pre_urr */
            if ((urr->stop_of_traffic.base) && (sxsm->now - urr->stop_of_traffic.base <= urr->stop_of_traffic.period))
                urr->stop_of_traffic.base = urr->stop_of_traffic.base;
            else
                urr->stop_of_traffic.base = sxsm->now;
            upf_pfcp_session_urr_time_start_stop (si, &urr->stop_of_traffic, 1, 1);
        }

        if (urr->update_flags & SX_URR_UPDATE_ETH_INACT_TIMER)
        {
            /* continue the previous time base if it is valid, or reset it */
            urr->mac_detect.base = (urr->mac_detect.base != 0) ? urr->mac_detect.base : sxsm->now;
            upf_pfcp_session_urr_time_start_stop (si, &urr->mac_detect, 1, 1);
        }

        if (urr->update_flags & SX_URR_UPDATE_TRAFFIC_INACT_TIMER)
        {
            /* continue the previous time base if it is valid, or reset it */
            urr->traf_inact_detect_timer.base = (urr->traf_inact_detect_timer.base != 0) ? urr->traf_inact_detect_timer.base : sxsm->now;
            upf_pfcp_session_urr_time_start_stop (si, &urr->traf_inact_detect_timer, 1, 1);
        }
    }

    if (!pending_pdr)
    {
        memset(&pending->pdr_edge, 0, sizeof(active->pdr_edge));
        pending->pdr = NULL;
        pending->ip4_pdrs = NULL;
        pending->ip6_pdrs = NULL;
        pending->acl = NULL;
        pending->acl6 = NULL;
        //pending->database = NULL;
        //pending->scratch = NULL;
    }

    if (!pending_far)
        pending->far = NULL;

    if (pending_urr)
    {
        /* copy rest traffic from old active (now pending) to current
         * new URR was initialized with zero, simply add the old values */
        vec_foreach (urr, pending->urr)
        {
            upf_urr_t *new_urr = upf_get_urr_by_id (active, urr->id);
            if (!new_urr)
            {
                /* stop all timers */
                upf_pfcp_session_urr_time_stop (&urr->measurement_period);
                upf_pfcp_session_urr_time_stop (&urr->monitoring_time);
                upf_pfcp_session_urr_time_stop (&urr->idt);
                upf_pfcp_session_urr_time_stop (&urr->stop_of_traffic);
                upf_pfcp_session_urr_time_stop (&urr->time_quota);
                upf_pfcp_session_urr_time_stop (&urr->time_threshold);
                upf_pfcp_session_urr_time_stop (&urr->mac_detect);
                upf_pfcp_session_urr_time_stop (&urr->traf_inact_detect_timer);
                continue;
            }

            if ((new_urr->methods & SX_URR_VOLUME))
            {
                urr_volume_t *old_volume = &urr->volume;
                urr_volume_t *new_volume = &new_urr->volume;

                urr_pfcp_lock (urr);
                combine_volume (new_volume, old_volume, packets);
                combine_volume (new_volume, old_volume, bytes);
                combine_volume (new_volume, old_volume, consumed);
                urr_pfcp_unlock (urr);
            }
        }
    }
    else
        pending->urr = NULL;

    if (!pending_qer)
        pending->qer = NULL;

    if (!pending_bar)
        pending->bar = NULL;

    upf_srr_t *srr;
    vec_foreach (srr, active->srr)
    {
        upf_per_qos_flow_control_t *per_qos_flow_ctrl;
        vec_foreach (per_qos_flow_ctrl, srr->per_qos_flow_ctrl)
        {
            upf_per_qos_monitor_flow_control_t *flow_ctrl;
            vec_foreach (flow_ctrl, per_qos_flow_ctrl->flow_ctrl)
            {
                if (flow_ctrl->qmp_trigger_flag & QMP_TRIG_PERIOD)
                    upf_pfcp_monitor_send_timer_reset (&flow_ctrl->periodic_report, sxsm->now, si, TIMER_RESET);
                if (flow_ctrl->qmp_trigger_flag & QMP_TRIG_DEFAULT)
                    upf_pfcp_monitor_send_timer_reset (&flow_ctrl->default_report, sxsm->now, si, TIMER_RESET);
                upf_debug ("timer start: qmp_trigger_flag:0x%x", flow_ctrl->qmp_trigger_flag);
            }
        }
    }

    if (pending_srr)
    {
        vec_foreach (srr, pending->srr)
        {
            upf_srr_t *new_srr = upf_get_srr_by_id (active, srr->id);
            if (!new_srr)
            {
                /* stop all timers */
                upf_per_qos_flow_control_t *per_qos_flow_ctrl;
                vec_foreach (per_qos_flow_ctrl, srr->per_qos_flow_ctrl)
                {
                    upf_per_qos_monitor_flow_control_t *flow_ctrl;
                    vec_foreach (flow_ctrl, per_qos_flow_ctrl->flow_ctrl)
                    {
                        if (flow_ctrl->qmp_trigger_flag & QMP_TRIG_PERIOD)
                            upf_pfcp_monitor_send_timer_stop (&flow_ctrl->periodic_report);
                        if (flow_ctrl->qmp_trigger_flag & QMP_TRIG_DEFAULT)
                            upf_pfcp_monitor_send_timer_stop (&flow_ctrl->default_report);
                    }
                }
                continue;
            }
        }
    }
    else
        pending->srr = NULL;

    upf_pfcp_server_timer_stop (sx->up_inactive_timer.timer.handle);
    if (sx->up_inactive_timer.period)
        upf_pfcp_server_timer_start (PFCP_SX_DATA_ACTIVE, si, &sx->up_inactive_timer.timer);

    upf_check_handover_onging(sx);
    upf_trace("upf handover_ongoing is %u",sx->handover_ongoing);
  return 0;
}

void upf_rcu_ip_upf_session_update_finish(upf_session_t *sx)
{
    upf_main_t *um = &g_upf_main;
    if (!sx)
        return;

    if (sx->ip_sessions_idx)
    {
        u32 *idx;
        vec_foreach(idx, sx->ip_sessions_idx)
        {
            CHECK_POOL_IS_VALID_CONTINUE(um->vnip_sessions, *idx);
            upf_vnip_sessions_t *p = pool_elt_at_index (um->vnip_sessions, *idx);
            if (p->flags & VN_UPDATING)
            {
                ip_sessions_free (p, SX_PENDING);
                p->flags &= ~VN_UPDATING;
            }
        }

        vec_free(sx->ip_sessions_idx);
        sx->ip_sessions_idx = NULL;
    }
}

static void upf_rcu_upf_session_update_finish (struct upf_rcu_entry_st *head)
{
  struct rcu_session_info *si =
      container_of (head, struct rcu_session_info, rcu_entry);
  upf_main_t *um = &g_upf_main;
  upf_session_t *sx;

  CHECK_POOL_IS_VALID_NORET(um->sessions, si->idx);
  sx = pool_elt_at_index (um->sessions, si->idx);

  upf_rcu_ip_upf_session_update_finish(sx);

  upf_pfcp_rules_free (sx, SX_PENDING);
  sx->flags &= ~SX_UPDATING;

  upf_debug ("finish free session pending %u\n", si->idx);

  clib_mem_free (si);
}

void upf_session_update_finish (upf_session_t *sx)
{
  upf_main_t *gtm = &g_upf_main;
  struct rules *pending;
  struct rules dummy = {0};
  struct rcu_session_info *si;

  pending = upf_get_rules (sx, SX_PENDING);
  if (clib_memcmp (&dummy.pdr, &pending->pdr, sizeof (struct rules) - 8) == 0)
    {
      /* initial state vn_sx->pending is empty */
      upf_rcu_ip_upf_session_update_finish(sx);

      sx->flags &= ~SX_UPDATING;
      return;
    }

  si = clib_mem_alloc_no_fail (sizeof (*si));
  si->idx = sx - gtm->sessions;

  if ((g_upf_auxiliary_switch & UPF_FLOWTABLE_OLD_SWITCH) || gtm->ha_status == HA_STATUS_BACKUP)
  {
      upf_debug ("ha status is back up, fix updating!");
      upf_rcu_upf_session_update_finish(&si->rcu_entry);
  }
  else
  {
      upf_call_rcu_func (&si->rcu_entry, upf_rcu_upf_session_update_finish);
  }

  upf_debug ("start free session pending %u\n", sx - gtm->sessions);
}

/******************** Sx Session functions **********************/

/**
 * @brief Function to return session info entry address.
 *
 */
upf_session_t *
upf_session_lookup (uint64_t up_seid)
{
  upf_main_t *gtm = &g_upf_main;
  clib_bihash_kv_8_8_t kv;

  kv.key = up_seid;
  if (clib_bihash_search_inline_8_8 (&gtm->session_by_id, &kv))
    return NULL;
  else
  {
    CHECK_POOL_IS_VALID_RET(gtm->sessions, kv.value, NULL);
    return pool_elt_at_index (gtm->sessions, kv.value);
  }
}
upf_session_t *
upf_session_lookup_by_fseid (const ip46_address_t *cp_address, uint64_t cp_seid)
{
  upf_main_t *gtm = &g_upf_main;
  clib_bihash_kv_24_8_t kv_24_8;
  u32 rv = 0;

  kv_24_8.key[0] = cp_address->as_u64[0];
  kv_24_8.key[1] = cp_address->as_u64[1];
  kv_24_8.key[2] = cp_seid;

  rv = clib_bihash_search_24_8 (&gtm->session_by_fseid, &kv_24_8, &kv_24_8);
  if (rv)
    {
      return NULL;
    }
  else
    {
      CHECK_POOL_IS_VALID_RET(gtm->sessions, kv_24_8.value, NULL);
      return pool_elt_at_index (gtm->sessions, kv_24_8.value);
    }
}

upf_session_t *upf_session_lookup_by_imsi (u8 *imsi)
{
    uword *p = hash_get_mem (g_upf_main.session_by_imsi, imsi);
    if (!p)
      return NULL;
    else
    {
        upf_imsi_to_sess_idxs_t sess_idxs = {0};
        sess_idxs.p = *p;
        if (sess_idxs.sess_idx1)
            return sx_get_by_index(sess_idxs.sess_idx1);
        else if (sess_idxs.sess_idx2)
            return sx_get_by_index(sess_idxs.sess_idx2);
        else
            return NULL;
    }
}

/*get sx info by imsi and dnn by libingxing*/
upf_session_t *upf_session_lookup_by_imsi_and_dnn (u8 *imsi, u8 *dnn)
{
    upf_session_t *sx = NULL;

    uword *p = hash_get_mem (g_upf_main.session_by_imsi, imsi);
    if (!p)
      return NULL;
    else
    {
        upf_imsi_to_sess_idxs_t sess_idxs = {0};
        sess_idxs.p = *p;
        if (sess_idxs.sess_idx1)
        {
            sx = sx_get_by_index(sess_idxs.sess_idx1);
            upf_trace("upf_session_lookup_by_imsi_and_dnn sess_idx1:%u", sess_idxs.sess_idx1);
            if (strcmp ((char *)dnn, (char *)sx->dnn) == 0)
            {
                return sx;
            }
        }
        if (sess_idxs.sess_idx2)
        {
            sx = sx_get_by_index(sess_idxs.sess_idx2);
            upf_trace("upf_session_lookup_by_imsi_and_dnn sess_idx2:%u", sess_idxs.sess_idx2);
            if (strcmp ((char *)dnn, (char *)sx->dnn) == 0)
            {
                return sx;
            }
        }
    }
    return NULL;
}

static int upf_urr_increment_and_check_counter (u64 *packets, u64 *bytes, u64 *consumed,
                                 u64 threshold, u64 quota, u64 n_bytes,
                                 upf_urr_t *urr)
{
  int r = URR_OK;

  if (quota != 0 &&
      (PREDICT_FALSE (*consumed < quota && *consumed + n_bytes >= quota) ||
       PREDICT_FALSE (*consumed > quota && !(urr->status & URR_OVER_QUOTA))))
    r |= URR_QUOTA_EXHAUSTED;

  if (threshold != 0 &&
      (PREDICT_FALSE (*bytes < threshold && *bytes + n_bytes >= threshold) ||
       PREDICT_FALSE (*bytes > threshold && !(urr->status & URR_REACHED_THRESHOLD))))
    r |= URR_THRESHOLD_REACHED;

  *consumed += n_bytes;
  *bytes += n_bytes;

  *packets += 1;

  return r;
}

void upf_mac_address_detected_report(vlib_buffer_t *b, upf_session_t *sess,upf_urr_t *urr)
{
    upf_mac_info_t mac_info;
    ethernet_header_t *eth_hdr = (ethernet_header_t *)vlib_buffer_get_current (b);
    upf_get_eth_vlan_id((u8 *)eth_hdr, &mac_info.c_tag, &mac_info.s_tag);
    mac_address_from_bytes(&mac_info.mac, eth_hdr->src_address);

    u32 found = 0;
    upf_mac_addr_detect_t *t;
    vec_foreach (t, sess->mac_address_tag_vec)
    {
        if (0 == memcmp(&t->mac_info, &mac_info, sizeof(mac_info)))
        {
            if (t->detect_is_reported)
            {
                if (!t->is_active)
                    t->is_active = 1; /* update to active if it has been reported */
                return;
            }
            else
            {
                /* reactive report (i.e. had report detected, removed) */
                found = 1;
                t->detect_is_reported = 1;
                break;
            }
        }
    }

  if (!found)
  {
      APPEND_NEW_MEMBER(sess->mac_address_tag_vec, t);
      t->mac_info = mac_info;
      t->detect_is_reported = 1;
  }
  t->is_active = 1;
  t->pdr_index = upf_buffer_opaque (b)->upf.pdr_index;

  upf_report_mac_information_t report_info = {0};

  pfcp_mac_addresses_t *addr;
  APPEND_NEW_MEMBER(report_info.addr, addr);
  addr->c_tag.tci = mac_info.c_tag;
  addr->c_tag.mask = VLAN_MASK_VID;
  addr->s_tag.tci = mac_info.s_tag;
  addr->s_tag.mask = VLAN_MASK_VID;
  vec_add1(addr->macs, mac_info.mac);

  report_info.is_detect = 1;
  report_info.sess_idx = upf_buffer_opaque (b)->upf.session_index;
  report_info.pdr_idx = t->pdr_index;

  upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
  memset (msg, 0, sizeof (*msg));
  msg->data = clib_mem_alloc_aligned_no_fail (sizeof(report_info), CLIB_CACHE_LINE_BYTES);
  msg->msg_id = PFCP_RPC_PUBLISH_REPORT_MAC_ADDRESS_REPORT;
  memcpy(msg->data, &(report_info), sizeof((report_info)));

  for (int i = 0; i < g_upf_main.num_pfcp_threads; i++)
  {
      upf_trace ("PFCP_RPC_PUBLISH_REPORT_IP_MULTICAST DETECTED");
      send_sx_msg_to_pfcp_thread(i, msg);
  }
  return;
}

int upf_single_urr_process (upf_urr_t *urr, u32 dir, u32 len)
{
  int r = URR_OK;

  if (!urr)
    return r;

  if ((urr->methods & SX_URR_VOLUME))
    {
      urr_lock (urr);

      if (dir == UPF_UL)
        r |= urr_incr_and_check (urr->volume, ul, len, urr);
      else
        r |= urr_incr_and_check (urr->volume, dl, len, urr);

      r |= urr_incr_and_check (urr->volume, total, len, urr);
      urr_unlock (urr);
    }

  if ((urr->methods & SX_URR_TIME))
    {
      if (PREDICT_FALSE (!(urr->status & URR_ACTIVE_TIME)))
        clib_atomic_fetch_or (&urr->status, URR_ACTIVE_TIME);
    }

  if (PREDICT_FALSE (!(urr->status & URR_TRAFFIC)))
    {
      clib_atomic_fetch_or (&urr->status, URR_TRAFFIC);
      r |= URR_START_TRAFFIC;
    }

  if (PREDICT_FALSE(urr->update_flags & SX_URR_UPDATE_TRAFFIC_INACT_TIMER))
    {
      if (!urr->traf_inact_detect.is_active)
        urr->traf_inact_detect.is_active = 1;
    }

  return r;
}

u32 upf_urrs_process (vlib_main_t *vm, upf_session_t *sess, struct rules *active,
              upf_pdr_t *pdr, vlib_buffer_t *b, u8 direction, u8 src_intf)
{
  int r = URR_OK;
  u32 *urr_id;
  u32 len = 0;
  upf_far_t *far = NULL;
  flow_entry_t *flow = NULL;

  len = vlib_buffer_length_in_chain (vm, b);

  //ip6 not need handle(as ip6 payload more than 46 bytes)
  if ((sess->pdn_type != PDN_TYPE_ETHERNET) && (is_v4_packet (vlib_buffer_get_current (b))) && !g_upf_padding_switch)
  {
      ip4_header_t *ip4 = vlib_buffer_get_current (b);
      len = clib_net_to_host_u16(ip4->length);
  }
  upf_trace ("DIR:%d", direction);

  //if (PREDICT_FALSE (!(sess->up_inactive_timer.status & DATA_ACTIVE)))
  //  clib_atomic_fetch_or (&sess->up_inactive_timer.status, DATA_ACTIVE);

  if(!(g_upf_auxiliary_switch & UPF_RULE_GROUP_SWITCH))
  {
      if(upf_buffer_opaque (b)->upf.flow_index != ~0)
      {
          flowtable_main_t *fm = NULL;
          if (upf_buffer_node_ttl(b)->ttl.is_eth == IP_TRAFFIC)
          {
              fm = &g_flowtable_main;
          }
          else
          {
              fm = &eth_flowtable_main;
          }
          if (PREDICT_TRUE(!pool_is_free_index(fm->flows, upf_buffer_opaque (b)->upf.flow_index)))
          {
            flow = pool_elt_at_index (fm->flows, upf_buffer_opaque (b)->upf.flow_index);
          }
      }
  }
  vec_foreach (urr_id, pdr->urr_ids)
  {
    upf_urr_t *urr = upf_get_urr_by_id (active, *urr_id);
    if (!urr)
      {
        upf_info (
            "drop packet for no urr found by id, up_seid:0x%lx, urr_id:0x%x\n",
            sess->up_seid, *urr_id);
        return 1;
      }
    if (PREDICT_FALSE (urr->far_id))
      {
        far = upf_get_far_by_id (active, urr->far_id);
        if (far && (far->apply_action & F_APPLY_DROP))
          {
            upf_trace (
                "drop packet for far_id_for_quota_action, urr->far_id:\n",
                urr->far_id);

            return 1;
          }
      }

    if (PREDICT_FALSE (urr->update_flags & SX_URR_UPDATE_TRAFFIC_INACT_TIMER))
      {
        if (!urr->traf_inact_detect.has_pdr)
          {
            urr->traf_inact_detect.has_pdr = 1;
            urr->traf_inact_detect.pdr_id = (u16)pdr->id;
          }
      }

    if (PREDICT_FALSE (urr->status & URR_OVER_QUOTA))
      {
        upf_trace ("drop packet for URR OVER QUOTA\n");

        return 1;
      }

    if (sess->pdn_type == PDN_TYPE_ETHERNET)
    {
        if ((urr->triggers & REPORTING_TRIGGER_MAC_ADDRESSES_REPORTING) && (src_intf == SRC_INTF_ACCESS))
        {
            upf_mac_address_detected_report(b, sess, urr);
        }

        /* neil.fan@20220815 add: both UE and DN can trigger this report (source interface is access or core),
         *  refers to the chapter ******** "broadcast communications between the members of Ethernet 5G VN group"
         * of test specification of china mobile.
         */
        if ((src_intf <= SRC_INTF_CORE)
            && (urr->triggers & REPORTING_TRIGGER_IP_MULTICAST_JOIN_LEAVE))
        {
            u16 type = 0;
            void *ip = upf_eth_next_header(vlib_buffer_get_current (b), &type);
            if ((type == ETHERNET_TYPE_IP4) || (type == ETHERNET_TYPE_IP6))
            {
                upf_multicast_addr_t m_addr = {0};
                u32 r = upf_parse_multicast_packet(ip, &m_addr);
                if (!r)
                    upf_multicast_address_report(&m_addr, upf_buffer_opaque (b)->upf.session_index, pdr - active->pdr);

                upf_free_multicast_group (&m_addr.group);
            }
        }
    }

    if(!(g_upf_auxiliary_switch & UPF_RULE_GROUP_SWITCH))
    {
        if (flow && flow->urr_id != ~0)
        {
            if (flow->urr_id == urr->id)
            {
                r |= upf_single_urr_process (urr, (u32)direction, len);
            }
        }
        else
        {
            r |= upf_single_urr_process (urr, (u32)direction, len);
        }
    }
    else
    {
        r |= upf_single_urr_process (urr, (u32)direction, len);
    }

    flowcache_upf_urr_t *data = upf_flowcache_add_action (
          b, FLOWCACHE_ACTION_UPF_URR, sizeof (flowcache_upf_urr_t));
    data->session_index = upf_buffer_opaque (b)->upf.session_index;
    data->sess = sess;
    data->pdr_index = pdr - active->pdr;
    data->urr_index = urr - active->urr;
    data->urr = urr;
    data->far_apply_action = far ? far->apply_action:0;
    data->direction = direction;
  }

  if (PREDICT_FALSE (r != URR_OK))
    upf_pfcp_server_session_report_usage (sess, pdr - active->pdr);

  return 0;
}

/* 0:succ; 1:fail */
u32 upf_qmp_delay_calc (ul_pdu_sess_info_t *qmp_info, upf_64bit_timestamp_t *t4, u32 *ul_delay, u32 *dl_delay, u32 *rp_delay)
{
    ul_pdu_sess_flags_t *ul_flag = &qmp_info->flags.ul_flag;
    upf_64bit_timestamp_t *t1 = &qmp_info->snp_content.dl_sending_ts_repeated;
    upf_64bit_timestamp_t *t2 = &qmp_info->snp_content.dl_received_ts;
    upf_64bit_timestamp_t *t3 = &qmp_info->snp_content.ul_sending_ts;

    upf_trace ("Per Qos flow per UE Qos Monitor:\n T1:%U\n T2:%U\n T3:%U\n T4:%U\n dl_delay:%u ul_delay:%u",
        format_rfc5905_u64_timestamp, t1, format_rfc5905_u64_timestamp, t2,
        format_rfc5905_u64_timestamp, t3, format_rfc5905_u64_timestamp, t4,
        qmp_info->dl_delay_result, qmp_info->ul_delay_result);

    if (g_upf_qmp_sync_mode == UPF_QMP_NOT_TIME_SYNCHRONIZED)
    {
        if (ul_flag->dl_delay_ind && ul_flag->ul_delay_ind)
        {
            /* (T2 - T1 + T4 - T3) / 2 */
#if 0
            u32 sec = t2->sec + t4->sec - t1->sec - t3->sec;
            u32 psec = t2->psec + t4->psec - t1->psec - t3->psec;

            /* second to millisecond */
            u32 msec = sec * 1000 + upf_time_psec_to_milli_sec (psec);
            upf_trace ("syn mode: total seconds:%u picoseconds:%u ==> milliseconds:%u", sec, psec, msec);
#else /* neil.fan@20230118 fix calculation error issue when the T1.sec = T2.sec = T3.sec = T4.sec - 1  */
            u64 msec_a = ((u64)t1->sec + (u64)t3->sec) * 1000 + upf_time_psec_to_milli_sec(t1->psec) + upf_time_psec_to_milli_sec(t3->psec);
            u64 msec_b = ((u64)t2->sec + (u64)t4->sec) * 1000 + upf_time_psec_to_milli_sec(t2->psec) + upf_time_psec_to_milli_sec(t4->psec);
            if (msec_a > msec_b)
            {
                upf_debug ("syn mode time error, msec_a:%u larger than msec_b:%u", msec_a, msec_b);
                return 1;
            }
            u32 msec = (u32)(msec_b - msec_a);
            upf_trace ("syn mode: msec_b:%u - msec_a:%u = milliseconds:%u", msec_b, msec_a, msec);
#endif

            u32 ran_delay = qmp_info->dl_delay_result + qmp_info->ul_delay_result;
            *rp_delay = (msec + ran_delay);
            *ul_delay = *rp_delay / 2;
            *dl_delay = *ul_delay;
            return 0;
        }
        return 1;
    }

    if (g_upf_qmp_sync_mode == UPF_QMP_TIME_SYNCHRONIZED)
    {
        /* not support now */
        return 1;
    }
    return 1;
}

u32 upf_per_qos_monitor_report_build(upf_per_qos_flow_control_t *ctrl, upf_per_qos_monitor_flow_control_t *flow_ctrl, vlib_buffer_t *b0,
                                     upf_monitor_send_timer_t *timer, u32 sx_index, u8 srr_id, u8 trigger_flag)
{
    upf_main_t *um = &g_upf_main;

    if (!timer->measuring || !timer->send_cnt || timer->is_report || timer->recv_cnt > 3)
        return 0;
    timer->recv_cnt++;

    gtpu_flags_t *gtp_flags = &(upf_buffer_opaque (b0)->upf.gtp_flags);
    pdu_sess_container_ex_t *container = vlib_buffer_get_current(b0) + upf_buffer_opaque (b0)->upf.data_offset
                                         - gtp_flags->container_len;

    if (*((u8 *)container - 1) != GTP_EX_TYPE_PDU_SESS)
        return 0;

    upf_64bit_timestamp_t t4;
    upf_time_now_nsec_fraction (&t4);
    ul_pdu_sess_info_t qmp_info;
    upf_pdu_session_container_ul_decode (container, &qmp_info);

    u8 is_report = 0;
    pfcp_qos_monitoring_measurement_t measure = {0};
    u32 r = upf_qmp_delay_calc (&qmp_info, &t4, &measure.ul_pkt_delay, &measure.dl_pkt_delay, &measure.rp_pkt_delay);
    if (!r)
        upf_trace ("delay calc result: qfi:%u, measure UL:%ums, DL:%ums, RP:%ums", flow_ctrl->qfi, measure.ul_pkt_delay, measure.dl_pkt_delay, measure.rp_pkt_delay);

    if ((ctrl->reporting_frequency.flags & IE_REPORTING_FREQUENCYE_PERIOD) && (trigger_flag == QMP_TRIG_PERIOD))
    {
        upf_trace ("period report, qfi:%u", flow_ctrl->qfi);
        is_report = 1;
    }
    else if ((ctrl->reporting_frequency.flags & IE_REPORTING_FREQUENCYE_EVENT) && (trigger_flag == QMP_TRIG_DEFAULT))
    {
        if (t4.sec <= ctrl->minimum_wait_time.min_wait_time + flow_ctrl->default_report.wait_timestamp)
        {
            upf_trace ("measure not fulfill minimum_wait_time:%u, wait_timestamp:%u, current:%u",
                ctrl->minimum_wait_time.min_wait_time, flow_ctrl->default_report.wait_timestamp, t4.sec);
            return 0;
        }

        pfcp_packet_delay_thresholds_t *threshold = &ctrl->packet_delay_threshold;
        if (((threshold->flags & IE_PACKET_DELAY_THRESHOLD_RP) && (threshold->rp_pkt_delay_threshold < measure.rp_pkt_delay))
            || ((threshold->flags & IE_PACKET_DELAY_THRESHOLD_DL) && (threshold->dl_pkt_delay_threshold < measure.dl_pkt_delay))
            || ((threshold->flags & IE_PACKET_DELAY_THRESHOLD_UL) && (threshold->ul_pkt_delay_threshold < measure.ul_pkt_delay)))
        {
            is_report = 1;
            flow_ctrl->default_report.wait_timestamp = t4.sec;
            upf_trace ("threshold report, record wait_timestamp:%u qfi:%u", flow_ctrl->default_report.wait_timestamp, flow_ctrl->qfi);
        }
        else
        {
            upf_trace ("flow qfi:%u, not fulfill threshold condition UL:%ums, DL:%ums, RP:%ums",
                flow_ctrl->qfi, threshold->ul_pkt_delay_threshold, threshold->dl_pkt_delay_threshold, threshold->rp_pkt_delay_threshold);
            return 0;
        }
    }

    if (!r)
    {
        if (ctrl->requested_qos_monitor.flags & IE_REQUESTED_QOS_MONITORING_DL)
            measure.flags |= IE_QOS_MONITOR_MEASURE_DL;
        if (ctrl->requested_qos_monitor.flags & IE_REQUESTED_QOS_MONITORING_UL)
            measure.flags |= IE_QOS_MONITOR_MEASURE_UL;
        if (ctrl->requested_qos_monitor.flags & IE_REQUESTED_QOS_MONITORING_RP)
            measure.flags |= IE_QOS_MONITOR_MEASURE_RP;
        timer->is_report = 1;

        flow_ctrl->last_measurement = measure;
        flow_ctrl->last_measure_start_time = timer->start_time;

        upf_session_t *sx = sx_get_by_index(sx_index);
        if (sx)
        {
            upf_s_nssai_t *n = upf_s_nssai_lookup (sx->s_nssai);
            if (n)
            {
                upf_psa_delay_per_s_nssai_record (&n->psa_delay, &measure);
            }
        }
    }
    else
    {
        measure.flags = IE_QOS_MONITOR_MEASURE_PLMF;
    }

    if (is_report)
    {
        upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
        msg->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_inner_report_info_t), CLIB_CACHE_LINE_BYTES);
        memset (msg->data, 0, sizeof(upf_inner_report_info_t));
        msg->msg_id = PFCP_RPC_PUBLISH_PER_QOS_MONITOR_REPORT;

        upf_inner_report_info_t *info = msg->data;
        info->sess_idx = sx_index;
        info->has_srrid = 1;
        info->srr_id = srr_id;

        u8 reporting_frequency = IE_REPORTING_FREQUENCYE_EVENT;
        if (trigger_flag == QMP_TRIG_PERIOD)
            reporting_frequency = IE_REPORTING_FREQUENCYE_PERIOD;
        pfcp_build_one_qos_monitor_report (&info->per_qos_monitor_report, flow_ctrl, reporting_frequency, t4.sec, &measure);

        for (int i = 0; i < um->num_pfcp_threads; i++)
        {
            upf_trace ("PFCP_RPC_PUBLISH_PER_QOS_MONITOR_REPORT,\n");
            send_sx_msg_to_pfcp_thread(i, msg);
        }
    }

    return 0;
}

u32 upf_srr_per_qos_monitor_proc (upf_session_t *sx, upf_srr_t *srr, upf_pdr_t *pdr0,
                                  upf_far_t *far0, struct rules *active, vlib_buffer_t *b0)
{
    gtpu_flags_t *gtp_flags = &(upf_buffer_opaque (b0)->upf.gtp_flags);
    u32 sx_idx = sx - g_upf_main.sessions;

    /* To get QFI only */
    pdu_sess_info_qos_t pdu_qos = {0};
    if (!upf_get_encap_qos_info(active, pdr0->qer_ids, &pdu_qos))
        return 0;

    upf_per_qos_flow_control_t *ctrl;
    vec_foreach (ctrl, srr->per_qos_flow_ctrl)
    {
        upf_per_qos_monitor_flow_control_t *flow_ctrl;
        vec_foreach (flow_ctrl, ctrl->flow_ctrl)
        {
            if (flow_ctrl->qfi != pdu_qos.flags.qfi)
                continue;

            if (!flow_ctrl->qmp_trigger_flag)
                continue;

            if (gtp_flags->qmp)
            {
                /* PSA handle UL QMP packet */
                if ((gtp_flags->pdu_type == UPF_UL)
                    && (pdr0->pdi.src_intf == SRC_INTF_ACCESS)
                    && (far0->forward.dst_intf == DST_INTF_CORE)
                    && !(far0->forward.flags & FAR_F_OUTER_HEADER_CREATION))
                {
                    u32 r = 0;
                    if (flow_ctrl->qmp_trigger_flag & QMP_TRIG_PERIOD)
                        r |= upf_per_qos_monitor_report_build (ctrl, flow_ctrl, b0, &flow_ctrl->periodic_report, sx_idx, srr->id, QMP_TRIG_PERIOD);
                    if (flow_ctrl->qmp_trigger_flag & QMP_TRIG_DEFAULT)
                        r |= upf_per_qos_monitor_report_build (ctrl, flow_ctrl, b0, &flow_ctrl->default_report, sx_idx, srr->id, QMP_TRIG_DEFAULT);
                    if (r)
                    {
                        upf_debug ("drop ul dummy-gtp packet, r: 0x%x", r);
                        return 1; /* drop ul dummy-gtp in PSA-UPF after Qos monitor handled */
                    }
                }
            }
            else
            {
                /* Trigger Per Qos flow Per UE measurement only, not handle I-UPF forwarding QMP packet */
                if ((pdr0->pdi.src_intf == SRC_INTF_CORE)
                    && (far0->forward.dst_intf == DST_INTF_ACCESS)
                    && (far0->forward.flags & FAR_F_OUTER_HEADER_CREATION))
                {
                    if (flow_ctrl->qmp_trigger_flag & QMP_TRIG_PERIOD)
                    {
                        if (!flow_ctrl->periodic_report.measuring || flow_ctrl->periodic_report.send_cnt > 1)
                            continue;
                        flow_ctrl->periodic_report.send_cnt++;
                    }
                    else
                    {
                        if (!flow_ctrl->default_report.measuring || flow_ctrl->default_report.send_cnt > 1)
                            continue;
                        flow_ctrl->default_report.send_cnt++;
                    }
                    upf_debug ("psa trigger, qmp_trigger_flag:0x%x", flow_ctrl->qmp_trigger_flag);
                    gtp_flags->psa_trigger = 1;
                    return 0;
                }
            }
        }
    }
    return 0;
}

int upf_single_srr_process (upf_session_t *sx, struct rules *active, upf_srr_t *srr, upf_pdr_t *pdr0, upf_far_t *far0, vlib_buffer_t *b0)
{
    if (upf_srr_per_qos_monitor_proc (sx, srr, pdr0, far0, active, b0))
        return 1; /* drop dummy gtp packet */

    return 0;
}

u32 upf_srrs_process (upf_session_t *sx, struct rules *active, upf_pdr_t *pdr0, upf_far_t *far0, vlib_buffer_t *b0)
{
    upf_srr_t *srr;
    vec_foreach (srr, active->srr)
    {
        u32 r = upf_single_srr_process(sx, active, srr, pdr0, far0, b0);

        flowcache_upf_srr_t *data = upf_flowcache_add_action (b0, FLOWCACHE_ACTION_UPF_SRR, sizeof (flowcache_upf_srr_t));
        data->session_index = upf_buffer_opaque (b0)->upf.session_index;
        data->srr_index = srr - active->srr;
        data->srr = srr;
        data->pdr = pdr0;
        data->far = far0;

        if (!r)
            return r;
    }

    return 0;
}

/* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 below */
void upf_pkt_rate_min_timer_start()
{
    upf_main_t *gtm = &g_upf_main;
    time_t now = time(NULL);
    struct tm curr_time;
    localtime_r(&now, &curr_time);

    struct tm tm_target = curr_time;
    tm_target.tm_min++;
    tm_target.tm_sec = 0;

    gtm->pkt_rate_minute_timer.base = unix_time_now();
    gtm->pkt_rate_minute_timer.period = mktime(&tm_target) - mktime(&curr_time);
    upf_pfcp_server_timer_start(UPF_PKT_RATE_MIN_TIMER, UPF_PKT_RATE_MIN_TIMER, &gtm->pkt_rate_minute_timer);
    upf_trace("pkt rate minute timer started");
}

void upf_pkt_rate_tenth_hour_timer_start()
{
    upf_main_t *gtm = &g_upf_main;
    time_t now = time(NULL);
    struct tm curr_time;
    localtime_r(&now, &curr_time);

    struct tm tm_target = curr_time;
    tm_target.tm_min = (curr_time.tm_min / 6 + 1) * 6;  /*0,6,12,18...*/
    tm_target.tm_sec = 0;

    gtm->pkt_rate_tenth_of_hour_timer.base = unix_time_now();
    gtm->pkt_rate_tenth_of_hour_timer.period = mktime(&tm_target) - mktime(&curr_time);
    upf_pfcp_server_timer_start(UPF_PKT_RATE_TENTH_HOUR_TIMER, UPF_PKT_RATE_TENTH_HOUR_TIMER, &gtm->pkt_rate_tenth_of_hour_timer);
    upf_trace("pkt rate tenth of hour timer started");
}

void upf_pkt_rate_hour_timer_start()
{
    upf_main_t *gtm = &g_upf_main;
    time_t now = time(NULL);
    struct tm curr_time;
    localtime_r(&now, &curr_time);

    struct tm tm_target = curr_time;
    tm_target.tm_hour++;
    tm_target.tm_min = 0;
    tm_target.tm_sec = 0;

    gtm->pkt_rate_hour_timer.base = unix_time_now();
    gtm->pkt_rate_hour_timer.period = mktime(&tm_target) - mktime(&curr_time);
    upf_pfcp_server_timer_start(UPF_PKT_RATE_HOUR_TIMER, UPF_PKT_RATE_HOUR_TIMER, &gtm->pkt_rate_hour_timer);
    upf_trace("pkt rate hour timer started");
}

void upf_pkt_rate_day_timer_start()
{
    upf_main_t *gtm = &g_upf_main;
    time_t now = time(NULL);
    struct tm curr_time;
    localtime_r(&now, &curr_time);

    struct tm tm_target = curr_time;
    tm_target.tm_mday++;
    tm_target.tm_hour = 0;
    tm_target.tm_min = 0;
    tm_target.tm_sec = 0;

    gtm->pkt_rate_day_timer.base = unix_time_now();
    gtm->pkt_rate_day_timer.period = mktime(&tm_target) - mktime(&curr_time);
    upf_pfcp_server_timer_start(UPF_PKT_RATE_DAY_TIMER, UPF_PKT_RATE_DAY_TIMER, &gtm->pkt_rate_day_timer);
    upf_trace("pkt rate day timer started");
}

void upf_pkt_rate_week_timer_start()
{
    upf_main_t *gtm = &g_upf_main;
    time_t now = time(NULL);
    struct tm curr_time;
    localtime_r(&now, &curr_time);

    int next_monday = (7 - curr_time.tm_wday + 1) % 7;
    next_monday = next_monday == 0 ? 7 : next_monday;

    struct tm tm_target = curr_time;
    tm_target.tm_mday += next_monday;
    tm_target.tm_hour = 0;
    tm_target.tm_min = 0;
    tm_target.tm_sec = 0;

    gtm->pkt_rate_week_timer.base = unix_time_now();
    gtm->pkt_rate_week_timer.period = mktime(&tm_target) - mktime(&curr_time);
    upf_pfcp_server_timer_start(UPF_PKT_RATE_WEEK_TIMER, UPF_PKT_RATE_WEEK_TIMER, &gtm->pkt_rate_week_timer);
    upf_trace("pkt rate week timer started");
}

void upf_pkt_rate_init()
{
    upf_pkt_rate_min_timer_start();
    upf_pkt_rate_tenth_hour_timer_start();
    upf_pkt_rate_hour_timer_start();
    upf_pkt_rate_day_timer_start();
    upf_pkt_rate_week_timer_start();
}

int upf_pkt_rate_exceed(u16 max, u8 has_a, u16 a_max, u16 *pkts, u16 *a_pkts)
{
    (*pkts)++;
    if (*pkts > max)
    {
        if (has_a)
        {
            (*a_pkts)++;
            if (*a_pkts > a_max)
            {
                upf_trace ("exceed additional max: max: %u, has_a: %u, a_max: %u, pkts: %u, a_pkts: %u\n",
                    max, has_a, a_max, *pkts, *a_pkts);
                return QER_DROP_CAUSE_PACKET_RATE;
            }
        }
        else
        {
            upf_trace ("exceed max: max: %u, has_a: %u, a_max: %u, pkts: %u, a_pkts: %u\n",
                max, has_a, a_max, *pkts, *a_pkts);
            return QER_DROP_CAUSE_PACKET_RATE;
        }
    }
    return 0;
}
void upf_pkt_rate_time_unit_refresh(u8 unit)
{
    upf_main_t *umt = &g_upf_main;
    u32 *idx;
    vec_foreach (idx, umt->pkt_rate_sess_idxs)
    {
        upf_trace("idx: %u", *idx);
        upf_session_t *sx = pool_elt_at_index (umt->sessions, *idx);
        if (sx)
        {
            upf_trace("sess index: %u", sx - umt->sessions);
            struct rules *active = upf_get_rules (sx, SX_ACTIVE);
            upf_pdr_t *pdr;
            vec_foreach (pdr, active->pdr)
            {
                u32 *qer_ids;
                vec_foreach(qer_ids, pdr->qer_ids)
                {
                    upf_qer_t *qer = upf_get_qer_by_id (active, *qer_ids);
                    if (qer && (qer->flags & SX_QER_PACKET_RATE))
                    {
                        if ((qer->packet_rate.flags & PACKET_RATE_ULPR) && (qer->packet_rate.ul.unit == unit))
                        {
                            qer->ul_pkts = 0;
                            upf_trace("clear ul_pkts");
                        }
                        if ((qer->packet_rate.flags & PACKET_RATE_DLPR) && (qer->packet_rate.dl.unit == unit))
                        {
                            qer->dl_pkts = 0;
                            upf_trace("clear dl_pkts");
                        }
                        if ((qer->packet_rate.flags & PACKET_RATE_ULPR) &&
                            (qer->packet_rate.flags & PACKET_RATE_APRC) &&
                            (qer->packet_rate.a_ul.unit == unit))
                        {
                            qer->a_ul_pkts = 0;
                            upf_trace("clear a_ul_pkts");
                        }
                        if ((qer->packet_rate.flags & PACKET_RATE_DLPR) &&
                            (qer->packet_rate.flags & PACKET_RATE_APRC) &&
                            (qer->packet_rate.a_dl.unit == unit))
                        {
                            qer->a_dl_pkts = 0;
                            upf_trace("clear a_dl_pkts");
                        }
                        upf_trace("SX_QER_PACKET_RATE end");
                    }
                }
            }
        }
    }
    upf_trace("update pkg rate sess end");
}

void upf_pkt_rate_timer_handle(u32 timer_id)
{
    upf_trace("timer_id: %u", timer_id);

    switch (timer_id)
    {
        case UPF_PKT_RATE_MIN_TIMER:
            upf_pkt_rate_time_unit_refresh(UPF_PKT_RATE_TIME_UNIT_MIN);
            upf_pkt_rate_min_timer_start();
            break;
        case UPF_PKT_RATE_TENTH_HOUR_TIMER:
            upf_pkt_rate_time_unit_refresh(UPF_PKT_RATE_TIME_UNIT_TENTH_HOUR);
            upf_pkt_rate_tenth_hour_timer_start();
            break;
        case UPF_PKT_RATE_HOUR_TIMER:
            upf_pkt_rate_time_unit_refresh(UPF_PKT_RATE_TIME_UNIT_HOUR);
            upf_pkt_rate_hour_timer_start();
            break;
        case UPF_PKT_RATE_DAY_TIMER:
            upf_pkt_rate_time_unit_refresh(UPF_PKT_RATE_TIME_UNIT_DAY);
            upf_pkt_rate_day_timer_start();
            break;
        case UPF_PKT_RATE_WEEK_TIMER:
            upf_pkt_rate_time_unit_refresh(UPF_PKT_RATE_TIME_UNIT_WEEK);
            upf_pkt_rate_week_timer_start();
            break;
        default:
            upf_err("Error timer id: %u\n", timer_id);
            return;
    }
}
/* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 above */

int upf_single_qer_process (upf_session_t *sess, upf_qer_t *qer, vlib_buffer_t *b, u8 direction, u32 len)
{
  upf_qer_policer_t *pol;
  u32 col __attribute__ ((unused));
  upf_main_t *gtm = &g_upf_main;
  u64 time_in_policer_periods;

  if (!qer)
  {
    upf_debug ("qer is NULL!");
    return 0;
  }
  if (qer->gate_status[direction])
    {
      upf_trace ("qer gate close, drop packet\n");
      UPF_STATISTICS_ADD(QER_FAILED_FOR_GATE_CLOSED);
      UPF_PDU_SESS_STATISTICS_ADD(sess, QER_FAILED_FOR_GATE_CLOSED);
      return QER_DROP_CAUSE_GATE;
    }
  time_in_policer_periods =
      clib_cpu_time_now () >> POLICER_TICKS_PER_PERIOD_SHIFT;
  if (qer->flags & SX_QER_MBR)
    {
      if ((qer->mbr.dl == 0) && (qer->mbr.ul == 0))
        return 0;
      if (pool_is_free_index (gtm->qer_policers, qer->mbr_policer.v))
        return 0;
      CHECK_POOL_IS_VALID_RET(gtm->qer_policers, qer->mbr_policer.v, 0);
      pol = pool_elt_at_index (gtm->qer_policers, qer->mbr_policer.v);
      //qer_lock (qer);
      col = vnet_police_packet (&pol->policer[direction], len, POLICE_CONFORM,
                                time_in_policer_periods);
      //qer_unlock (qer);
      if (col != 0)
        {
          upf_trace ("drop packet for MBR\n");
          UPF_STATISTICS_ADD(QER_FAILED_FOR_MBR);
          UPF_PDU_SESS_STATISTICS_ADD(sess, QER_FAILED_FOR_MBR);
          return QER_DROP_CAUSE_MBR;
        }
    }

  if (qer->flags & SX_QER_GBR)
    {
      if ((qer->gbr.dl == 0) && (qer->gbr.ul == 0))
        return 0;
      if (pool_is_free_index (gtm->qer_policers, qer->gbr_policer.v))
        return 0;

      CHECK_POOL_IS_VALID_RET(gtm->qer_policers, qer->gbr_policer.v, 0);
      pol = pool_elt_at_index (gtm->qer_policers, qer->gbr_policer.v);
      //qer_lock (qer);
      col = vnet_police_packet (&pol->policer[direction], len, POLICE_CONFORM,
                                time_in_policer_periods);
      //qer_unlock (qer);
      if (col == POLICE_CONFORM)
        {
          vnet_buffer2 (b)->qos.source = 0xFF;
        }
      else
        {
            UPF_STATISTICS_ADD(QER_FAILED_FOR_GBR);
            UPF_PDU_SESS_STATISTICS_ADD(sess, QER_FAILED_FOR_GBR);
        }
    }

    /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 below */
    if (qer->flags & SX_QER_PACKET_RATE)
    {
        if ((direction == UPF_UL) && (qer->packet_rate.flags & PACKET_RATE_ULPR))
        {
            col = upf_pkt_rate_exceed(qer->packet_rate.ul.max,
                                         qer->packet_rate.flags & PACKET_RATE_APRC,
                                         qer->packet_rate.a_ul.max,
                                         &qer->ul_pkts,
                                         &qer->a_ul_pkts);
            if (col != 0)
            {
                upf_trace("drop pkt for UL Packet Rate\n");
                return QER_DROP_CAUSE_PACKET_RATE;
            }
        }
        else if ((direction == UPF_DL) && (qer->packet_rate.flags & PACKET_RATE_DLPR))
        {
            col = upf_pkt_rate_exceed(qer->packet_rate.dl.max,
                                         qer->packet_rate.flags & PACKET_RATE_APRC,
                                         qer->packet_rate.a_dl.max,
                                         &qer->dl_pkts,
                                         &qer->a_dl_pkts);
            if (col != 0)
            {
                upf_trace("drop pkt for DL Packet Rate\n");
                return QER_DROP_CAUSE_PACKET_RATE;
            }
        }
    }
    /* Add for NB-IoT Packet Rate by lixiaogang on 2024-04-28 above */
  return 0;
}

u32 upf_qers_process (upf_session_t *sess, vlib_main_t *vm, struct rules *r, upf_pdr_t *pdr, vlib_buffer_t *b, u8 direction)
{

  u32 *qer_id;
  u32 len, drop;

  upf_trace ("DIR:%d", direction);

  len = vlib_buffer_length_in_chain (vm, b);

  vec_foreach (qer_id, pdr->qer_ids)
  {
    upf_qer_t *qer = upf_get_qer_by_id (r, *qer_id);

    if (!qer)
      continue;

    drop = upf_single_qer_process (sess, qer, b, direction, len);
    if (drop)
      return drop;

    flowcache_upf_qer_t *data = upf_flowcache_add_action (
        b, FLOWCACHE_ACTION_UPF_QER, sizeof (flowcache_upf_qer_t));
    data->session_index = upf_buffer_opaque (b)->upf.session_index;
    data->qer_index = qer - r->qer;
    data->qer = qer;
    data->direction = direction;
  }

  return 0;
}

// Add for dnn/s-nssai timeval by liupeng on 2021-11-16 below
void upf_send_dnn_usage()
{
    upf_dnn_t *dnn;
    upf_main_t *um = &g_upf_main;

    vec_foreach (dnn, um->dnn)
    {
        upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
        memset (msg, 0, sizeof (*msg));
        msg->data = clib_mem_alloc_aligned_no_fail (sizeof(dnn_volume_t), CLIB_CACHE_LINE_BYTES);
        memset (msg->data, 0, sizeof(dnn_volume_t));
        msg->msg_id = PFCP_RPC_PUBLISH_ADD_DNN_USAGE;
        memcpy(dnn->volume.dnn_name, dnn->name, sizeof(dnn->volume.dnn_name));
        memcpy(msg->data, &dnn->volume, sizeof(dnn_volume_t));

        for (int i = 0; i < um->num_pfcp_threads; i++)
        {
            upf_trace ("PFCP_RPC_PUBLISH_DNN_USAGE_PUSH,\n");
            send_sx_msg_to_pfcp_thread(i, msg);
        }
    }
    um->dnn_timer.base = unix_time_now ();
    um->dnn_timer.period = um->dnn_timeval;
    start_first_thread_timer (DNN_TIMER, DNN_TIMER, &um->dnn_timer);
}

void upf_send_s_nssai_usage()
{
    upf_s_nssai_t *nssai;
    upf_main_t *um = &g_upf_main;
    vec_foreach (nssai, um->nssai)
    {
        upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
        memset (msg, 0, sizeof (*msg));
        msg->data = clib_mem_alloc_aligned_no_fail (sizeof(s_nssai_volume_t), CLIB_CACHE_LINE_BYTES);
        memset (msg->data, 0, sizeof(s_nssai_volume_t));
        msg->msg_id = PFCP_RPC_PUBLISH_ADD_S_NSSAI_USAGE;
        nssai->volume.s_nssai = nssai->s_nssai;

        memcpy(msg->data, &nssai->volume, sizeof(s_nssai_volume_t));

        for (int i = 0; i < um->num_pfcp_threads; i++)
        {
            upf_trace ("PFCP_RPC_PUBLISH_S_NSSAI_USAGE_PUSH,\n");
            send_s_nssai_msg_to_pfcp_thread(i, msg);
        }
    }
    um->s_nssai_timer.base = unix_time_now ();
    um->s_nssai_timer.period = um->s_nssai_timeval;
    start_first_thread_timer (S_NSSAI_TIMER, S_NSSAI_TIMER, &um->s_nssai_timer);
}
// Add for dnn/s-nssai timeval by liupeng on 2021-11-16 above

u32 upf_pkt_is_tcp_syn (vlib_buffer_t *b)
{
    u32 offs = upf_buffer_opaque (b)->upf.data_offset;
    ip4_header_t *ip4 = NULL;
    ip6_header_t *ip6 = NULL;
    tcp_header_t *tcp = NULL;

    u8 is_ip4 = is_v4_packet (vlib_buffer_get_current (b));

    if (is_ip4)
    {
        ip4 = (ip4_header_t *)(vlib_buffer_get_current (b) + offs);
        if (ip4->protocol == IP_PROTOCOL_TCP)
        {
            tcp = ip4_next_header (ip4);
            if (tcp && tcp->flags & TCP_FLAG_SYN)
            {
                return 1;
            }
        }
    }
    else
    {
        ip6 = (ip6_header_t *)(vlib_buffer_get_current (b) + offs);
        if (ip6->protocol == IP_PROTOCOL_TCP)
        {
            tcp = ip6_next_header (ip6);
            if (tcp && tcp->flags & TCP_FLAG_SYN)
            {
                return 1;
            }
        }
    }

    return 0;
}

void upf_attach_dos_syn_policer (upf_dos_syn_t *dos_syn)
{
  sse2_qos_pol_cfg_params_st cfg = {
      .rate_type = SSE2_QOS_RATE_KBPS,
      .rnd_type = SSE2_QOS_ROUND_TO_CLOSEST,
      .rfc = SSE2_QOS_POLICER_TYPE_1R2C,
      .color_aware = 0,
      .conform_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
      .exceed_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
      .violate_action =
          {
              .action_type = SSE2_QOS_ACTION_TRANSMIT,
          },
  };

  upf_flow_policer_t *pol;
  const int BYTES_PER_KBIT = (1000 / 8);

  pol = &dos_syn->flow_policer;

  if (dos_syn->mbr.ul)
    {
      cfg.rb.kbps.cir_kbps = dos_syn->mbr.ul;
      cfg.rb.kbps.cb_bytes = dos_syn->mbr.ul * BYTES_PER_KBIT;
    }
  else
    {
      cfg.rb.kbps.cir_kbps = ~0;
      cfg.rb.kbps.cb_bytes = (u64)~0;
    }

  sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_UL]);

  if (dos_syn->mbr.dl)
    {
      cfg.rb.kbps.cir_kbps = dos_syn->mbr.dl;
      cfg.rb.kbps.cb_bytes = dos_syn->mbr.dl * BYTES_PER_KBIT;
    }
  else
    {
      cfg.rb.kbps.cir_kbps = ~0;
      cfg.rb.kbps.cb_bytes = (u64)~0;
    }
  sse2_pol_logical_2_physical (&cfg, &pol->policer[UPF_DL]);

  return;
}

u32 upf_single_dos_syn_process (upf_session_t *sess, u32 len, u32 dir)
{
    u64 time_in_policer_periods;
    u32 col __attribute__ ((unused));
    upf_flow_policer_t *pol;

    if (NULL == sess || (!g_upf_dos_syn.mbr.ul && !g_upf_dos_syn.mbr.dl))
    {
        return 0;
    }

    upf_dos_syn_t *dos_syn = &sess->dos_syn;
    time_in_policer_periods = clib_cpu_time_now () >> POLICER_TICKS_PER_PERIOD_SHIFT;

    if (dos_syn->ref_cnt != g_upf_dos_syn.ref_cnt)
    {
        dos_syn->mbr = g_upf_dos_syn.mbr;
        upf_attach_dos_syn_policer(dos_syn);
        dos_syn->ref_cnt = g_upf_dos_syn.ref_cnt;
    }

    if (dos_syn->mbr.ul || dos_syn->mbr.dl)
    {
        pol = &dos_syn->flow_policer;
        col = vnet_police_packet (&pol->policer[dir], len, POLICE_CONFORM,
                                  time_in_policer_periods);
        if (col != 0)
          {
            upf_trace ("drop packet for bandwidth limitation!\n");
            return 1;
          }
    }

    return 0;
}

u32 upf_dos_syn_process (vlib_main_t *vm, upf_session_t *sess, vlib_buffer_t *b, u8 direction)
{

  u32 len, drop;

  upf_trace ("DIR:%d", direction);

  len = vlib_buffer_length_in_chain (vm, b);

  drop = upf_single_dos_syn_process (sess, len, direction);
  if (drop)
    return drop;

  {
    flowcache_upf_dos_syn_t *data = upf_flowcache_add_action (
        b, FLOWCACHE_ACTION_UPF_DOS_SYN, sizeof (flowcache_upf_dos_syn_t));
    data->session_index = upf_buffer_opaque (b)->upf.session_index;
    data->direction = direction;
  }

  return 0;
}

u32 upf_single_dnn_process (u8 *dnn_name, u32 len, u32 dir)
{
    upf_dnn_t *dnn;
    upf_main_t *um = &g_upf_main;
    int hit = 0;
    u64 time_in_policer_periods;
    u32 col __attribute__ ((unused));
    upf_dnn_policer_t *pol;
    f64 current_time = 0;
    u32 ul_used_time = 0, dl_used_time = 0;

    if (NULL == dnn_name)
    {
        return 0;
    }

    time_in_policer_periods = clib_cpu_time_now () >> POLICER_TICKS_PER_PERIOD_SHIFT;

    vec_foreach (dnn, um->dnn)
    {
      if (vec_is_equal (dnn->name, dnn_name))
        {
          hit = 1;
          break;
        }
    }

    if (!hit)
    {
        upf_trace("DNN name[%s] not find!", dnn_name);
        return 0;
    }

    if (dnn->dnn_policer.ref_cnt != dnn->ref_cnt)
    {
        upf_attach_dnn_policer(dnn);
        dnn->dnn_policer.ref_cnt = dnn->ref_cnt;
    }

    if (dnn->volume.mbr.ul || dnn->volume.mbr.dl)
    {
        pol = &dnn->dnn_policer;
        col = vnet_police_packet (&pol->policer[dir], len, POLICE_CONFORM,
                                  time_in_policer_periods);
        if (col != 0)
          {
            upf_trace ("drop packet for bandwidth limitation!\n");
            return DNN_THRESHOLD_REACHED;
          }
    }

    dnn_lock (dnn);

    current_time = unix_time_now ();
    ul_used_time = current_time - dnn->ul_old_time;
    dl_used_time = current_time - dnn->dl_old_time;

    if (dir == UPF_UL)
    {
        dnn->volume.measure.bytes.ul += len;
        dnn->volume.measure.consumed.ul += len;
        dnn->volume.measure.packets.ul++;

        dnn->volume.ul_bandwidth.packets++;
        dnn->volume.ul_bandwidth.l3_bytes += len;
        dnn->volume.ul_bandwidth.l2_bytes += (len+UPF_L2_EXTEND_SIZE);
        dnn->volume.ul_bandwidth.l1_bytes += (len+UPF_L2_EXTEND_SIZE+UPF_L1_EXTEND_SIZE);
        if (ul_used_time >= UPF_INTERVAL_TIME)
        {
            upf_traffic_bandwidth_calculate(&dnn->volume.ul_bandwidth, ul_used_time);
            dnn->ul_old_time = current_time;
        }
    }
    else
    {
        dnn->volume.measure.bytes.dl += len;
        dnn->volume.measure.consumed.dl += len;
        dnn->volume.measure.packets.dl++;

        dnn->volume.dl_bandwidth.packets++;
        dnn->volume.dl_bandwidth.l3_bytes += len;
        dnn->volume.dl_bandwidth.l2_bytes += (len+UPF_L2_EXTEND_SIZE);
        dnn->volume.dl_bandwidth.l1_bytes += (len+UPF_L2_EXTEND_SIZE+UPF_L1_EXTEND_SIZE);
        if (dl_used_time >= UPF_INTERVAL_TIME)
        {
            upf_traffic_bandwidth_calculate(&dnn->volume.dl_bandwidth, dl_used_time);
            dnn->dl_old_time = current_time;
        }
    }

    dnn->volume.measure.bytes.total += len;
    dnn->volume.measure.consumed.total += len;
    dnn->volume.measure.packets.total++;

    dnn_unlock (dnn);

    if ((dnn->volume.threshold.ul && dnn->volume.measure.consumed.ul > dnn->volume.threshold.ul) ||
        (dnn->volume.threshold.dl && dnn->volume.measure.consumed.dl > dnn->volume.threshold.dl) ||
        (dnn->volume.threshold.total && dnn->volume.measure.consumed.total > dnn->volume.threshold.total))
    {
        upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
        memset (msg, 0, sizeof (*msg));
        msg->data = clib_mem_alloc_aligned_no_fail (sizeof(dnn_volume_t), CLIB_CACHE_LINE_BYTES);
        memset (msg->data, 0, sizeof(dnn_volume_t));
        msg->msg_id = PFCP_RPC_PUBLISH_ADD_DNN_USAGE;
        memcpy(dnn->volume.dnn_name, dnn->name, sizeof(dnn->volume.dnn_name));
        memcpy(msg->data, &dnn->volume, sizeof(dnn_volume_t));

        for (int i = 0; i < um->num_pfcp_threads; i++)
        {
            upf_trace ("PFCP_RPC_PUBLISH_DNN_USAGE_PUSH,\n");
            send_sx_msg_to_pfcp_thread(i, msg);
        }
        memset(&dnn->volume.measure.consumed, 0, sizeof(dnn->volume.measure.consumed));
    }

    return 0;
}

u32 upf_dnn_process (vlib_main_t *vm, upf_session_t *sess, vlib_buffer_t *b, u8 direction)
{

  u32 len, drop;

  upf_trace ("DIR:%d", direction);

  len = vlib_buffer_length_in_chain (vm, b);

  drop = upf_single_dnn_process (sess->dnn, len, direction);
  if (drop)
    return drop;

  {
    flowcache_upf_dnn_t *data = upf_flowcache_add_action (
        b, FLOWCACHE_ACTION_UPF_DNN, sizeof (flowcache_upf_dnn_t));
    data->session_index = upf_buffer_opaque (b)->upf.session_index;
    data->direction = direction;
  }

  return 0;
}

u32 upf_single_s_nssai_process (u32 nssai_idx, u32 len, u32 dir)
{
    upf_main_t *um = &g_upf_main;
    u64 time_in_policer_periods;
    u32 col __attribute__ ((unused));
    upf_s_nssai_policer_t *pol;
    f64 current_time = 0;
    u32 ul_used_time = 0, dl_used_time = 0;

    upf_s_nssai_t *nssai = upf_s_nssai_lookup(nssai_idx);
    if (!nssai)
    {
        upf_trace("S-NSSAI[%u] not find!", nssai_idx);
        return 0;
    }

    time_in_policer_periods = clib_cpu_time_now () >> POLICER_TICKS_PER_PERIOD_SHIFT;
    if (nssai->s_nssai_policer.ref_cnt != nssai->ref_cnt)
    {
        upf_attach_s_nssai_policer(nssai);
        nssai->s_nssai_policer.ref_cnt = nssai->ref_cnt;
    }

    if (nssai->volume.mbr.ul || nssai->volume.mbr.dl)
    {
        pol = &nssai->s_nssai_policer;
        col = vnet_police_packet (&pol->policer[dir], len, POLICE_CONFORM,
                                  time_in_policer_periods);
        if (col != 0)
          {
            upf_trace ("drop packet for bandwidth limitation!\n");
            return DNN_THRESHOLD_REACHED;
          }
    }

    nssai_lock (nssai);

    current_time = unix_time_now ();
    ul_used_time = current_time - nssai->ul_old_time;
    dl_used_time = current_time - nssai->dl_old_time;

    if (dir == UPF_UL)
    {
        nssai->volume.measure.bytes.ul += len;
        nssai->volume.measure.consumed.ul += len;
        nssai->volume.measure.packets.ul++;

        nssai->volume.ul_bandwidth.packets++;
        nssai->volume.ul_bandwidth.l3_bytes += len;
        nssai->volume.ul_bandwidth.l2_bytes += (len+UPF_L2_EXTEND_SIZE);
        nssai->volume.ul_bandwidth.l1_bytes += (len+UPF_L2_EXTEND_SIZE+UPF_L1_EXTEND_SIZE);
        if (ul_used_time >= UPF_INTERVAL_TIME)
        {
            upf_traffic_bandwidth_calculate(&nssai->volume.ul_bandwidth, ul_used_time);
            nssai->ul_old_time = current_time;
        }
    }
    else
    {
        nssai->volume.measure.bytes.dl += len;
        nssai->volume.measure.consumed.dl += len;
        nssai->volume.measure.packets.dl++;

        nssai->volume.dl_bandwidth.packets++;
        nssai->volume.dl_bandwidth.l3_bytes += len;
        nssai->volume.dl_bandwidth.l2_bytes += (len+UPF_L2_EXTEND_SIZE);
        nssai->volume.dl_bandwidth.l1_bytes += (len+UPF_L2_EXTEND_SIZE+UPF_L1_EXTEND_SIZE);
        if (dl_used_time >= UPF_INTERVAL_TIME)
        {
            upf_traffic_bandwidth_calculate(&nssai->volume.dl_bandwidth, dl_used_time);
            nssai->dl_old_time = current_time;
        }
    }

    nssai->volume.measure.bytes.total += len;
    nssai->volume.measure.consumed.total += len;
    nssai->volume.measure.packets.total++;

    nssai_unlock (nssai);

    if ((nssai->volume.threshold.ul && nssai->volume.measure.consumed.ul > nssai->volume.threshold.ul) ||
        (nssai->volume.threshold.dl && nssai->volume.measure.consumed.dl > nssai->volume.threshold.dl) ||
        (nssai->volume.threshold.total && nssai->volume.measure.consumed.total > nssai->volume.threshold.total))
    {
        upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
        memset (msg, 0, sizeof (*msg));
        msg->data = clib_mem_alloc_aligned_no_fail (sizeof(s_nssai_volume_t), CLIB_CACHE_LINE_BYTES);
        memset (msg->data, 0, sizeof(s_nssai_volume_t));
        msg->msg_id = PFCP_RPC_PUBLISH_ADD_S_NSSAI_USAGE;
        nssai->volume.s_nssai = nssai->s_nssai;
        memcpy(msg->data, &nssai->volume, sizeof(s_nssai_volume_t));

        for (int i = 0; i < um->num_pfcp_threads; i++)
        {
            upf_trace ("PFCP_RPC_PUBLISH_S_NSSAI_USAGE_PUSH,\n");
            send_s_nssai_msg_to_pfcp_thread(i, msg);
        }
        memset(&nssai->volume.measure.consumed, 0, sizeof(nssai->volume.measure.consumed));
    }

    return 0;
}

u32 upf_s_nssai_process (vlib_main_t *vm, upf_session_t *sess, vlib_buffer_t *b, u8 direction)
{

  u32 len, drop;

  upf_trace ("DIR:%d", direction);

  len = vlib_buffer_length_in_chain (vm, b);

  drop = upf_single_s_nssai_process (sess->s_nssai, len, direction);
  if (drop)
    return drop;

  {
    flowcache_upf_s_nssai_t *data = upf_flowcache_add_action (
        b, FLOWCACHE_ACTION_UPF_S_NSSAI, sizeof (flowcache_upf_s_nssai_t));
    data->session_index = upf_buffer_opaque (b)->upf.session_index;
    data->direction = direction;
  }

  return 0;
}

u32 upf_nwi_mbr_process (upf_pdr_t *pdr, upf_far_t *far, u32 len)
{
    if (!g_upf_nwi_flow_control_flag)
    {
        return 0;
    }

    if (NULL == pdr)
    {
        upf_err("pdr is null!\n");
        return 0;
    }

    upf_nwi_t *nwi_node = NULL;
    upf_nwi_t *nwi = NULL;
    upf_main_t *um = &g_upf_main;
    int hit = 0;
    u64 time_in_policer_periods;
    u32 col __attribute__ ((unused));
    upf_nwi_policer_t *pol;
    u8 dir = UPF_DIRECTION_MAX;

    switch (pdr->pdi.src_intf)
    {
        case SRC_INTF_ACCESS:
            dir = UPF_UL;
            break;

       case SRC_INTF_CORE:
           dir = UPF_DL;
           break;

       default:
           break;
    }

    switch (far->forward.dst_intf)
    {
        case DST_INTF_ACCESS:
            dir = UPF_DL;
            break;

       case DST_INTF_CORE:
           dir = UPF_UL;
           break;

       default:
           break;
    }

    if (!pool_is_free_index (um->nwis, pdr->pdi.nwi))
    {
        nwi = pool_elt_at_index (um->nwis, pdr->pdi.nwi);
    }

    if (NULL == nwi)
    {
        upf_err("nwi is not find, pdi.nwi is %u!\n", pdr->pdi.nwi);
        return 0;
    }

    time_in_policer_periods = clib_cpu_time_now () >> POLICER_TICKS_PER_PERIOD_SHIFT;

    vec_foreach (nwi_node, um->nwis)
    {
      if (vec_is_equal (nwi_node->name, nwi->name))
      {
        hit = 1;
        break;
      }
    }

    if (!hit)
    {
        upf_err("NWI name[%s] not find!\n", nwi->name);
        return 0;
    }

    if (nwi->nwi_policer.ref_cnt != nwi->ref_cnt)
    {
        upf_attach_nwi_policer(nwi);
        nwi->nwi_policer.ref_cnt = nwi->ref_cnt;
    }

    if (nwi->volume.mbr.ul || nwi->volume.mbr.dl)
    {
        pol = &nwi->nwi_policer;
        col = vnet_police_packet (&pol->policer[dir], len, POLICE_CONFORM,
                                  time_in_policer_periods);
        if (col != 0)
          {
            upf_trace ("drop packet for bandwidth limitation!\n");
            return 1;
          }
    }

    return 0;
}

int upf_get_url(struct upf_phr_header_t *headers, int header_num, u8 *url)
{
    int i;

    for (i = 0; i < header_num; i++)
    {
        if (NULL != headers[i].name)
        {
            if (0 == strncmp(headers[i].name, "Host", headers[i].name_len))
            {
                //vec_validate (*url, headers[i].value_len);
                memcpy(url, headers[i].value, headers[i].value_len);
                //clib_memcpy_fast (*url, headers[i].value, headers[i].value_len);

                upf_debug ("url find \n");

                return 1;
            }
        }
    }

    upf_debug ("url not find \n");

    return 0;
}

extern u8 upf_whitelist_num;

u32 upf_bcd_to_str(u8 *str, u8 *bcd, u32 bcd_len)
{
    u16 i, j;
    u8 bcd_chg;

    if ((bcd == NULL) || (bcd_len == 0))
    {
        return 0;
    }

    j = 0;
    for (i = 0; i < bcd_len; i++)
    {
        bcd_chg = bcd[i]>>4;
        if (bcd_chg == 0xF)
        {
            break;
        }
        str[j] = (bcd_chg > 9) ? (bcd_chg - 10 + 'A') : (bcd_chg + '0');
        j++;

        bcd_chg = bcd[i] & 0x0F;
        if (bcd_chg == 0xF)
        {
            break;
        }
        str[j] = (bcd_chg > 9) ? (bcd_chg - 10 + 'A') : (bcd_chg + '0');
        j++;
    }

    str[j] = '\0';

    return 1;
}

u32 tupf_bcd_to_str(u8 *str, u8 *bcd, u32 bcd_len)
{
    u16 i, j;
    u8 bcd_chg;

    if ((bcd == NULL) || (bcd_len == 0))
    {
        return 0;
    }

    j = 0;
    for (i = 0; i < bcd_len; i++)
    {
        bcd_chg = bcd[i] & 0x0F;
        if (bcd_chg == 0xF)
        {
            break;
        }
        str[j] = (bcd_chg > 9) ? (bcd_chg - 10 + 'A') : (bcd_chg + '0');
        j++;

        bcd_chg = bcd[i]>>4;
        if (bcd_chg == 0xF)
        {
            break;
        }
        str[j] = (bcd_chg > 9) ? (bcd_chg - 10 + 'A') : (bcd_chg + '0');
        j++;
    }

    str[j] = '\0';

    return 1;
}

u32 upf_flow_overload_protect_handle(upf_flow_overload_protect_t *overload_protect, u32 len, u32 dir, u8 dscp)
{
    u32 col __attribute__ ((unused));
    upf_flow_policer_t *pol;
    u64 time_in_policer_periods;

    overload_protect_lock (overload_protect);
    if (g_upf_dscp_shape_switch)
    {
        f64 kbps = (f64)iupf_bps_calc(dir, len);
        f64 ratio = 0;
        if ((dir == UPF_UL) && (overload_protect->mbr.ul))
        {
            ratio = kbps / overload_protect->mbr.ul;
        }
        else if ((dir == UPF_DL) && (overload_protect->mbr.dl))
        {
            ratio = kbps / overload_protect->mbr.dl;
        }

        if (iupf_overload_dscp_shape(ratio, dscp))
        {
            overload_protect_unlock (overload_protect);
            return 1;
        }
    }

    if (overload_protect->mbr.ul || overload_protect->mbr.dl)
    {
        pol = &overload_protect->flow_policer;
        time_in_policer_periods = clib_cpu_time_now () >> POLICER_TICKS_PER_PERIOD_SHIFT;
        col = vnet_police_packet (&pol->policer[dir], len, POLICE_CONFORM,
                                  time_in_policer_periods);
        if (col != 0)
        {
            overload_protect_unlock (overload_protect);
            return 1;
        }
    }
    overload_protect_unlock (overload_protect);

    return 0;
}

u32 upf_single_flow_overload_protect (upf_session_t *sess, upf_pdr_t *pdr, upf_far_t *far, u32 len, u32 dir, u8 dscp)
{
    upf_main_t *gtm = &g_upf_main;
    u64 time_in_policer_periods;
    f64 current_time;
    static f64 alarm_produce_time;
    u32 col __attribute__ ((unused));
    upf_flow_policer_t *pol;

    current_time = unix_time_now ();

    //overload alarm
    overload_alarm_lock (&g_upf_flow_overload_alarm);
    upf_flow_overload_alarm_t *overload_alarm = &g_upf_flow_overload_alarm;
    if (overload_alarm->mbr.ul || overload_alarm->mbr.dl)
    {
        pol = &overload_alarm->flow_policer;
        time_in_policer_periods = clib_cpu_time_now () >> POLICER_TICKS_PER_PERIOD_SHIFT;
        col = vnet_police_packet (&pol->policer[dir], len, POLICE_CONFORM,
                                  time_in_policer_periods);
        if (col != 0)
        {
          alarm_produce_time = current_time;
          if (g_upf_alarm_state[UPF_ALARM_FLOW_OVERLOAD_PROTECT - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_RECOVER)
          {
              g_upf_alarm_state[UPF_ALARM_FLOW_OVERLOAD_PROTECT - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_PRODUCE;
              upf_alarm_msg_t *upf_alarm= clib_mem_alloc_no_fail (sizeof (*upf_alarm));
              memset (upf_alarm, 0, sizeof (*upf_alarm));

              upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_flow_overload_protect_t), CLIB_CACHE_LINE_BYTES);
              memset (upf_alarm->data, 0, sizeof (upf_flow_overload_protect_t));
              upf_alarm->alarm_id = UPF_ALARM_FLOW_OVERLOAD_PROTECT;
              upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
              upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
              memcpy(upf_alarm->data, overload_alarm, sizeof(upf_flow_overload_protect_t));
              for (int i = 0; i < gtm->num_pfcp_threads; i++)
              {
                  pfcp_send_sx_alarm_to_thread(i, upf_alarm);
              }
          }
        }
        else
        {
          if ((g_upf_alarm_state[UPF_ALARM_FLOW_OVERLOAD_PROTECT - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_PRODUCE) &&
            (current_time - alarm_produce_time > 2))
          {
              g_upf_alarm_state[UPF_ALARM_FLOW_OVERLOAD_PROTECT - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_RECOVER;
              upf_alarm_msg_t *upf_alarm= clib_mem_alloc_no_fail (sizeof (*upf_alarm));
              memset (upf_alarm, 0, sizeof (*upf_alarm));

              upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_flow_overload_protect_t), CLIB_CACHE_LINE_BYTES);
              memset (upf_alarm->data, 0, sizeof (*upf_alarm->data));
              upf_alarm->alarm_id = UPF_ALARM_FLOW_OVERLOAD_PROTECT;
              upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
              upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
              memcpy(upf_alarm->data, overload_alarm, sizeof(upf_flow_overload_protect_t));
              for (int i = 0; i < gtm->num_pfcp_threads; i++)
              {
                  pfcp_send_sx_alarm_to_thread(i, upf_alarm);
              }
          }
        }
    }
    overload_alarm_unlock (&g_upf_flow_overload_alarm);

    if (upf_flow_overload_protect_handle(&g_upf_flow_overload_protect, len, dir, dscp))
    {
        upf_trace("global overload protect drop!");
        return 1;
    }

    if (get_3gpp_interface_type(far->forward.dest_interface_type) == N3_INTTEFACE ||
        get_3gpp_interface_type(pdr->pdi.source_interface_type) == N3_INTTEFACE)
    {
        if (upf_flow_overload_protect_handle(&g_upf_int_flow_ovlp.n3_int, len, dir, dscp))
        {
            upf_trace("N3 interface overload protect drop!");
            return 1;
        }
    }

    if (get_3gpp_interface_type(far->forward.dest_interface_type) == N6_INTTEFACE ||
        get_3gpp_interface_type(pdr->pdi.source_interface_type) == N6_INTTEFACE)
    {
        if (upf_flow_overload_protect_handle(&g_upf_int_flow_ovlp.n6_int, len, dir, dscp))
        {
            upf_trace("N6 interface overload protect drop!");
            return 1;
        }
    }

    if (get_3gpp_interface_type(far->forward.dest_interface_type) == N9_INTTEFACE ||
        get_3gpp_interface_type(pdr->pdi.source_interface_type) == N9_INTTEFACE)
    {
        if (upf_flow_overload_protect_handle(&g_upf_int_flow_ovlp.n9_int, len, dir, dscp))
        {
            upf_trace("N9 interface overload protect drop!");
            return 1;
        }
    }

    return 0;
}

u32 upf_flow_overload_protect_proc (vlib_main_t *vm, upf_session_t *sess, vlib_buffer_t *b, upf_pdr_t *pdr, upf_far_t *far, u8 direction)
{
  u32 len = 0, drop = 0;

  len = vlib_buffer_length_in_chain (vm, b);

  if (g_upf_flow_overload_alarm.mbr.ul || g_upf_flow_overload_alarm.mbr.dl ||
     g_upf_flow_overload_protect.mbr.ul || g_upf_flow_overload_protect.mbr.dl ||
      g_upf_int_flow_ovlp.n3_int.mbr.ul || g_upf_int_flow_ovlp.n3_int.mbr.dl ||
      g_upf_int_flow_ovlp.n6_int.mbr.ul || g_upf_int_flow_ovlp.n6_int.mbr.dl ||
      g_upf_int_flow_ovlp.n9_int.mbr.ul || g_upf_int_flow_ovlp.n9_int.mbr.dl)
  {
      drop = upf_single_flow_overload_protect (sess, pdr, far, len, direction, upf_buffer_node_ttl(b)->ttl.dscp);
      {
        flowcache_upf_overload_protect_t *data = upf_flowcache_add_action (
            b, FLOWCACHE_ACTION_UPF_OVERLOAD_PROTECT, sizeof (flowcache_upf_overload_protect_t));
        data->session_index = upf_buffer_opaque (b)->upf.session_index;
        data->pdr = pdr;
        data->far = far;
        data->direction = direction;
      }
  }

  return drop;
}

static u32 upf_cpu_overload_protect_handle(upf_flow_control_t *overload_protect, u32 len)
{
    u32 col __attribute__ ((unused));
    upf_flow_policer_t *pol;
    u64 time_in_policer_periods;

    clib_spinlock_lock (&overload_protect->lock);
    if (overload_protect->mbr.total)
    {
        pol = &overload_protect->flow_policer;
        time_in_policer_periods = clib_cpu_time_now () >> POLICER_TICKS_PER_PERIOD_SHIFT;
        col = vnet_police_packet (&pol->policer[UPF_TOTAL], len, POLICE_CONFORM, time_in_policer_periods);
        if (col != 0)
        {
            clib_spinlock_unlock (&overload_protect->lock);
            return 1;
        }
    }
    clib_spinlock_unlock (&overload_protect->lock);

    return 0;
}

u32 upf_single_cpu_overload_protect (u32 len)
{
    upf_main_t *gtm = &g_upf_main;
    u64 time_in_policer_periods;
    f64 current_time;
    static f64 alarm_produce_time;
    u32 col __attribute__ ((unused));
    upf_flow_policer_t *pol;

    current_time = unix_time_now ();

    //overload alarm
    clib_spinlock_lock (&g_upf_cpu_overload.overload_alarm.lock);
    upf_flow_control_t *overload_alarm = &g_upf_cpu_overload.overload_alarm;
    if (overload_alarm->mbr.total)
    {
        pol = &overload_alarm->flow_policer;
        time_in_policer_periods = clib_cpu_time_now () >> POLICER_TICKS_PER_PERIOD_SHIFT;
        col = vnet_police_packet (&pol->policer[UPF_TOTAL], len, POLICE_CONFORM, time_in_policer_periods);
        if (col != 0)
        {
          alarm_produce_time = current_time;
          if (g_upf_alarm_state[UPF_ALARM_CPU_OVERLOAD_PRIVATE - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_RECOVER)
          {
              g_upf_alarm_state[UPF_ALARM_CPU_OVERLOAD_PRIVATE - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_PRODUCE;
              upf_alarm_msg_t *upf_alarm= clib_mem_alloc_no_fail (sizeof (*upf_alarm));
              memset (upf_alarm, 0, sizeof (*upf_alarm));

              upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_flow_control_t), CLIB_CACHE_LINE_BYTES);
              memset (upf_alarm->data, 0, sizeof (upf_flow_control_t));
              upf_alarm->alarm_id = UPF_ALARM_CPU_OVERLOAD_PRIVATE;
              upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
              upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
              memcpy(upf_alarm->data, overload_alarm, sizeof(upf_flow_control_t));
              for (int i = 0; i < gtm->num_pfcp_threads; i++)
              {
                  pfcp_send_sx_alarm_to_thread(i, upf_alarm);
              }
          }
        }
        else
        {
          if ((g_upf_alarm_state[UPF_ALARM_CPU_OVERLOAD_PRIVATE - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_PRODUCE) &&
            (current_time - alarm_produce_time > 5))
          {
              g_upf_alarm_state[UPF_ALARM_CPU_OVERLOAD_PRIVATE - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_RECOVER;
              upf_alarm_msg_t *upf_alarm= clib_mem_alloc_no_fail (sizeof (*upf_alarm));
              memset (upf_alarm, 0, sizeof (*upf_alarm));

              upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_flow_control_t), CLIB_CACHE_LINE_BYTES);
              memset (upf_alarm->data, 0, sizeof (upf_flow_control_t));
              upf_alarm->alarm_id = UPF_ALARM_CPU_OVERLOAD_PRIVATE;
              upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
              upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
              memcpy(upf_alarm->data, overload_alarm, sizeof(upf_flow_control_t));
              for (int i = 0; i < gtm->num_pfcp_threads; i++)
              {
                  pfcp_send_sx_alarm_to_thread(i, upf_alarm);
              }
          }
        }
    }
    clib_spinlock_unlock (&g_upf_cpu_overload.overload_alarm.lock);

    if (upf_cpu_overload_protect_handle(&g_upf_cpu_overload.overload_protect, len))
    {
        upf_trace("cpu overload protect drop!");
        return 1;
    }

    return 0;
}

u32 upf_cpu_overload_protect_proc (vlib_main_t *vm, vlib_buffer_t *b)
{
  u32 len = 0, drop = 0;

  len = vlib_buffer_length_in_chain (vm, b);

  if (g_upf_cpu_overload.overload_alarm.mbr.total || g_upf_cpu_overload.overload_protect.mbr.total)
  {
      drop = upf_single_cpu_overload_protect (len);
      {
        flowcache_upf_overload_protect_t *data = upf_flowcache_add_action (
            b, FLOWCACHE_ACTION_CPU_OVERLOAD_PROTECT, sizeof (flowcache_upf_overload_protect_t));
        data->session_index = upf_buffer_opaque (b)->upf.session_index;
      }
  }

  return drop;
}

// Add for Bw rule by liupeng on 2021-08-13 below
u32 upf_single_flow_bw_protect(upf_flow_overload_protect_t *bw_rule_protect, u32 len, u32 dir)
{
    u64 time_in_policer_periods;
    u32 col __attribute__ ((unused));
    upf_flow_policer_t *pol;

    time_in_policer_periods = clib_cpu_time_now () >> POLICER_TICKS_PER_PERIOD_SHIFT;

    if (bw_rule_protect->mbr.ul || bw_rule_protect->mbr.dl)
    {
        pol = &bw_rule_protect->flow_policer;
        col = vnet_police_packet (&pol->policer[dir], len, POLICE_CONFORM, time_in_policer_periods);
        if (col != 0)
        {
            return 1;
        }
    }

    return 0;
}

#if 0
u32 upf_single_flow_bw_sess_rule_protect (upf_single_trace_push_t *grab, u32 len, u32 dir, u32 current_time)
{
    upf_bw_rule_sess key;
    const uword *p = NULL;
    u32 ret = 0;

    memset(&key, 0, sizeof(upf_bw_rule_sess));
    memcpy_s(&key.source_ip, sizeof(ip46_address_t), &grab->src_addr, sizeof(ip46_address_t));
    key.source_port = grab->src_port;
    memcpy_s(&key.dest_ip, sizeof(ip46_address_t), &grab->dst_addr, sizeof(ip46_address_t));
    key.dest_port = grab->dst_port;
    key.protocol = grab->protocol;

    p = hash_get_mem (g_BwRulesessKey, &key);
    if (p == NULL)
    {
        upf_debug ("  ##not exist\n");
        return 0;
    }

    upf_bw_rule_value_t *p_bw_rule = (upf_bw_rule_value_t*)p[0];
    ret = upf_single_flow_bw_protect(&p_bw_rule->flow_overload_protect, len, dir);
    if (!ret)
    {
        upf_bw_rule_traffic_statistic (&p_bw_rule->upf_bw_rule_stat, dir, len, current_time);
    }
    return ret;
}
#endif



static inline int
upf_hs_bw_handler_global (unsigned int id, unsigned long long from,
                      unsigned long long to, unsigned int flags, void *ctx)
{
    *((unsigned int *)ctx) = id;
    return 1;
}

u32 upf_single_flow_bw_rule_protect (upf_bw_rule_value_t *p_bw_rule, u32 len, u32 dir, u32 current_time)
{
    if (p_bw_rule == NULL)
    {
        return 0;
    }
    u32 ret = 0;
    ret = upf_single_flow_bw_protect(&p_bw_rule->flow_overload_protect, len, dir);
    if (!ret)
    {
        upf_bw_rule_traffic_statistic (&p_bw_rule->upf_bw_rule_stat, dir, len, current_time);
    }
    return ret;
}

u8 *upf_get_http_header_from_buffer(vlib_buffer_t *b)
{
    flowtable_main_t *fm = &g_flowtable_main;
    flow_entry_t *flow = NULL;
    u8 *headers = NULL;
    u8 *str = NULL;

    if(upf_buffer_opaque (b)->upf.flow_index != ~0)
    {
        CHECK_POOL_IS_VALID_RET(fm->flows, upf_buffer_opaque (b)->upf.flow_index, NULL);
        flow = pool_elt_at_index (fm->flows, upf_buffer_opaque (b)->upf.flow_index);
        if(flow == NULL)
        {
            upf_err ("flow is null!");
        }
    }
    else
    {
        upf_err ("upf.flow_index is invalid!");
    }
    ASSERT (flow != NULL);
    headers = upf_inflate_hd_func (flow, b);
    if (headers == NULL)
    {
        str = vlib_buffer_get_current (b);
    }
    else
    {
        str = headers;
    }
    return str;
}

upf_bw_rule_t* upf_url_match (u32 len, u8 *str)
{
    unsigned int id = ~0;
    upf_bw_rule_t *bwrule = NULL;

    if ((g_upf_main.database_bw) == NULL || (str == NULL))
    {
        upf_trace ("g_upf_main.database or httpheaders is NULL");
        return 0;
    }

    if (hs_scan (g_upf_main.database_bw, (const char *)str, len, 0,
                 g_upf_main.scratch_bw[vlib_get_thread_index () - g_upf_main.first_worker_thread_index],
                 upf_hs_bw_handler_global, &id) != HS_SCAN_TERMINATED)
    {
        return 0;
    }

    CHECK_POOL_IS_VALID_RET(g_upf_main.bwrule_list, id, NULL);
    bwrule = pool_elt_at_index (g_upf_main.bwrule_list, id);
    if (bwrule == NULL)
    {
        return NULL;
    }
    upf_debug ("url match: %s\n", bwrule->url);

    return bwrule;
}


extern int upf_https_client_hello_is_segment(vlib_main_t *vm,vlib_buffer_t *b);

int upf_https_header_enrichment (vlib_main_t *vm, vlib_buffer_t *b, upf_far_t *far, upf_session_t *sess,
                            u32 is_ip4)
{
    ip4_header_t *ip4 = NULL;
    ip6_header_t *ip6 = NULL;
    tcp_header_t *tcp;
    const unsigned char *https;
    u32 header_length;
    u32 https_length;
    int j;
    pfcp_header_enrichment_t *header_enrichment;
    u8 action[64] = "https_header_enrichment";
    u8 usrid[17] = {0};
    //u8 is_find_whitelist = 0;
    //u8 sni[64] = {0};
    //u8 msisdn[64] = {0};
    //u8 user_ip[64] = {0};
    //u8 supi[64] = {0};
    //u8 pei[64] = {0};
    //u8 uri[64] = {0};
    //u8 upf_ip[64] = {0};
    //u8 time_stamp[64] = {0};
    //u8 rat_type[64] = {0};
    //u8 dnn[64] = {0};
    upf_whitelist_t *upf_whitelist = NULL;
    RAW_EXTENSION upf_ext_headers[100] = {0};
    UPF_EXTENSION upf_sub_ext_headers[100] = {0};
    int ext_num = 0;
    int sub_ext_num = 0;
    //int sub_ext_length;
    u8 *sub_ext_msisdn = NULL;
    int sub_ext_msisdn_len = 0;
    u8 *sub_ext_user_ip = NULL;
    int sub_ext_user_ip_len = 0;
    u8 *sub_ext_supi = NULL;
    int sub_ext_supi_len = 0;
    u8 *sub_ext_pei = NULL;
    int sub_ext_pei_len = 0;
    //u8 *sub_ext_uri = NULL;
    //int sub_ext_uri_len = 0;
    //u8 *sub_ext_sgwc_ip = NULL;
    //int sub_ext_sgwc_ip_len = 0;
    u8 *sub_ext_smf_ip = NULL;
    int sub_ext_smf_ip_len = 0;
    //u8 *sub_ext_timestamp = NULL;
    //int sub_ext_timestamp_len = 0;
    //u8 *sub_ext_rat_type = NULL;
    //int sub_ext_rat_type_len = 0;
    /*u8 *sub_ext_dnn = NULL;
    int sub_ext_dnn_len = 0;*/
    SUB_EXTENSION https_info = {0};
    unsigned char insert_buff[0x1000];
    unsigned int insert_length;
    u8 is_find_graylist = 0;

    if (!g_https_header_enrichment_whitelist_flag && !g_https_header_enrichment_graylist_flag)
    {
        upf_trace ("https header enrichment whitelist flag and https header enrichment graylist flag is close");
        return 0;
    }

    if (!vlib_buffer_chain_linearize (vm, b))
    {
        upf_err ("vlib_buffer_chain_linearize %p failed\n", b);
        return -1;
    }

    int ret = 0;
    ret = upf_https_client_hello_is_segment(vm,b);
    if (ret == 1)
    {
        upf_debug("segment pkt just return!\n");
        return 0; //segment pkt just return
    }

    if (is_ip4)
    {
        ip4 = (ip4_header_t *)vlib_buffer_get_current (b);
        tcp = ip4_next_header (ip4);
        https = (const unsigned char *)tcp + tcp_header_bytes (tcp);
        header_length = ip4_header_bytes (ip4) + tcp_header_bytes (tcp);
        https_length = vlib_buffer_length_in_chain (vm, b) - header_length;
    }
    else
    {
        ip6 = (ip6_header_t *)vlib_buffer_get_current (b);
        tcp = ip6_next_header (ip6);
        https = (const unsigned char *)tcp + tcp_header_bytes (tcp);
        header_length = sizeof (*ip6) + tcp_header_bytes (tcp);
        https_length = vlib_buffer_length_in_chain (vm, b) - header_length;
    }

    if (parse_upf_extensions(https, https_length, upf_ext_headers, upf_sub_ext_headers, &ext_num, &sub_ext_num) != 0)
    {
        upf_err ("parse upf extensions failed\n");
        return -1;
    }

    parse_swap_upf_extensions(upf_sub_ext_headers, sub_ext_num);

    /*for (i = 0; i < sub_ext_num; i++)
    {
        for (j = 0; j < vec_len (far->forward.header_enrichment); j++)
        {
            header_enrichment = &far->forward.header_enrichment[j];

            if (upf_sub_ext_headers[i].type != header_enrichment->type)
            {
                continue;
            }

            if (strncmp (upf_sub_ext_headers[i].buf, (char *)header_enrichment->value,
                     upf_sub_ext_headers[i].length) != 0)
            {
                return -1;
            }
        }
    }*/

    /*begin add by huqingyang 2021.5.15 for https graylist  */
    if (g_https_header_enrichment_graylist_flag && upf_graylist_num)
    {
        if (sess->user_id.flags & USER_ID_IMSI)
        {
            tupf_bcd_to_str(usrid, sess->user_id.imsi, sizeof(sess->user_id.imsi));
            if (upf_lookup_upf_ip_graylist(NULL, (u8 *)upf_ext_headers->hostname, sess->dnn, usrid, action))
            {
                is_find_graylist = 1;
            }
        }

        if (sess->user_id.flags & USER_ID_MSISDN)
        {
            tupf_bcd_to_str(usrid, sess->user_id.msisdn, sizeof(sess->user_id.msisdn));

            if (upf_lookup_upf_ip_graylist(NULL, (u8 *)upf_ext_headers->hostname, sess->dnn, usrid, action))
            {
                is_find_graylist = 1;
            }
        }

        if (sess->user_id.flags & USER_ID_IMEI)
        {
            upf_bcd_to_str(usrid, sess->user_id.imei, sizeof(sess->user_id.imei));

            if (upf_lookup_upf_ip_graylist(NULL, (u8 *)upf_ext_headers->hostname, sess->dnn, usrid, action))
            {
                is_find_graylist = 1;
            }
        }

        if (is_find_graylist && (!sub_ext_num))
        {
            upf_debug("https in graylist and no  sub ext hand");
            return 0;
        }
    }
    /*end add by huqingyang 2021.5.15 for https graylist  */

    if (g_https_header_enrichment_whitelist_flag && upf_whitelist_num)
    {
        if (strlen(upf_ext_headers->hostname) > 0)
        {
              upf_whitelist = upf_lookup_upf_ip_whitelist(NULL, (u8 *)upf_ext_headers->hostname, NULL, action);

              if (upf_whitelist)
              {
                  if (upf_whitelist->key.ip.ip4.data_u32 != 0 && upf_whitelist->key.ip.ip4.data_u32 != ip4->dst_address.data_u32)
                  {
                      upf_err ("whitelist ip check failed\n");
                      return 0;
                  }
              }
              else
              {
                  ip46_address_t ip;

                  ip.ip4 = ip4->dst_address;

                  upf_whitelist = upf_lookup_upf_ip_whitelist(&ip, NULL, NULL, action);

                  if (upf_whitelist)
                  {
                      if (upf_whitelist->key.url[0] != 0)
                      {
                          upf_err ("whitelist url check failed\n");
                          return 0;
                      }
                  }
                  else
                  {
                      upf_whitelist = upf_lookup_upf_ip_whitelist(&ip, (u8 *)upf_ext_headers->hostname, NULL, action);

                      if (!upf_whitelist)
                      {
                          upf_err ("whitelist ip url check failed\n");
                          return 0;
                      }
                  }
              }
        }
        else
        {
            ip46_address_t ip;

            ip.ip4 = ip4->dst_address;

            upf_whitelist = upf_lookup_upf_ip_whitelist(&ip, NULL, NULL, action);

            if (!upf_whitelist)
            {
                  upf_err ("whitelist ip check failed\n");
                  return 0;
            }

            if (upf_whitelist->key.url[0] != 0)
            {
                upf_err ("whitelist url check failed\n");
                return 0;
            }
        }
    }

    if ((g_https_header_enrichment_graylist_flag && upf_graylist_num) || (g_https_header_enrichment_whitelist_flag && upf_whitelist_num))
    {
        if (upf_whitelist)
        {
            for (j = 0; j < vec_len (far->forward.header_enrichment); j++)
            {
                header_enrichment = &far->forward.header_enrichment[j];

                if (header_enrichment->name == NULL ||  header_enrichment->value == NULL)
                {
                    upf_err ("header_enrichment name or header_enrichment value is NULL\n");
                    continue;
                }

                if (strncmp ((const char *)upf_whitelist->msisdn_enr_header_str, (char *)header_enrichment->name,
                         strlen((const char *)header_enrichment->name)) == 0)
                {
                    memcpy(sess->user_id.msisdn_str, header_enrichment->value, strlen((const char *)header_enrichment->value));
                }

                if (strncmp ((const char *)upf_whitelist->user_ip_enr_header_str, (char *)header_enrichment->name,
                             strlen((const char *)header_enrichment->name)) == 0)
                {
                      memcpy(https_info.user_ip, header_enrichment->value, strlen((const char *)header_enrichment->value));
                }

                if (strncmp ((const char *)upf_whitelist->imsi_enr_header_str, (char *)header_enrichment->name,
                               strlen((const char *)header_enrichment->name)) == 0)
                {
                    memcpy(sess->user_id.imsi_str, header_enrichment->value, strlen((const char *)header_enrichment->value));
                }

                if (strncmp ((const char *)upf_whitelist->imei_enr_header_str, (char *)header_enrichment->name,
                               strlen((const char *)header_enrichment->name)) == 0)
                {
                    memcpy(sess->user_id.imei_str, header_enrichment->value, strlen((const char *)header_enrichment->value));
                }

                if (strncmp ((const char *)upf_whitelist->uli_enr_header_str, (char *)header_enrichment->name,
                               strlen((const char *)header_enrichment->name)) == 0)
                {
                    memcpy(upf_whitelist->uli, header_enrichment->value, strlen((const char *)header_enrichment->value));
                }

                if (strncmp ((const char *)upf_whitelist->sgwc_ip_enr_header_str, (char *)header_enrichment->name,
                             strlen((const char *)header_enrichment->name)) == 0)
                {
                      memcpy(https_info.sgwc_ip, header_enrichment->value, strlen((const char *)header_enrichment->value));
                }

                if (strncmp ((const char *)upf_whitelist->smf_ip_enr_header_str, (char *)header_enrichment->name,
                             strlen((const char *)header_enrichment->name)) == 0)
                {
                      memcpy(https_info.smf_ip, header_enrichment->value, strlen((const char *)header_enrichment->value));
                }

                if (strncmp ((const char *)upf_whitelist->timestamp_enr_header_str, (char *)header_enrichment->name,
                             strlen((const char *)header_enrichment->name)) == 0)
                {
                    https_info.timestamp = atoll((char*)header_enrichment->value);
                  //https_info.timestamp = htonl64(https_info.timestamp);
                }

                if (strncmp ((const char *)upf_whitelist->rat_type_enr_header_str, (char *)header_enrichment->name,
                               strlen((const char *)header_enrichment->name)) == 0)
                {
                    memcpy(upf_whitelist->rat_type, header_enrichment->value, strlen((const char *)header_enrichment->value));
                }

                if (strncmp ((const char *)upf_whitelist->dnn_enr_header_str, (char *)header_enrichment->name,
                               strlen((const char *)header_enrichment->name)) == 0)
                {
                  if (!sess->dnn)
                  {
                      sess->dnn = header_enrichment->value;
                  }
                    //memcpy(sess->dnn, header_enrichment->value, strlen((const char *)header_enrichment->value));
                }
            }
        }

        sub_ext_msisdn = (unsigned char *)get_upf_sub_extensions(upf_sub_ext_headers, UPF_EXTENSION_MSISDN_TYPE, &sub_ext_msisdn_len);
        if (sub_ext_msisdn_len > 0)
        {
            if (memcmp(sub_ext_msisdn+7, sess->user_id.msisdn_str, sub_ext_msisdn_len) != 0)
            {
                upf_err ("msisdn check failed\n");
                return -1;
            }
        }

        sub_ext_user_ip = (unsigned char *)get_upf_sub_extensions(upf_sub_ext_headers, UPF_EXTENSION_SRCIP_TYPE, &sub_ext_user_ip_len);
        if (sub_ext_user_ip_len > 0)
        {
            u8 *buff = NULL;
            buff = format(buff, "%U", format_ip46_address, &sess->ue_address, IP46_TYPE_ANY);

            if (memcmp(sub_ext_user_ip, buff, sub_ext_user_ip_len) != 0)
            {
                upf_err ("user ip check failed\n");
                return -1;
            }
        }

        sub_ext_supi = (unsigned char *)get_upf_sub_extensions(upf_sub_ext_headers, UPF_EXTENSION_SUPI_TYPE, &sub_ext_supi_len);
        if (sub_ext_supi_len > 0)
        {
            if (memcmp(sub_ext_supi+5, sess->user_id.imsi_str, sub_ext_supi_len) != 0)
            {
                upf_err ("supi check failed\n");
                return -1;
            }
        }

        sub_ext_pei = (unsigned char *)get_upf_sub_extensions(upf_sub_ext_headers, UPF_EXTENSION_PEI_TYPE, &sub_ext_pei_len);
        if (sub_ext_pei_len > 0)
        {
            if (memcmp(sub_ext_pei, sess->user_id.imei_str, sub_ext_pei_len) != 0)
            {
                upf_err ("pei check failed\n");
                return -1;
            }
        }

        /*sub_ext_sgwc_ip = (unsigned char *)get_upf_sub_extensions(upf_sub_ext_headers, UPF_EXTENSION_SGWCIP_TYPE, &sub_ext_sgwc_ip_len);
        if (sub_ext_sgwc_ip_len > 0)
        {
            u8 *buff = NULL;
            buff = format(buff, "%U", format_ip46_address, &sess->sgwc_address, IP46_TYPE_ANY);

            if (memcmp(sub_ext_sgwc_ip, buff, sub_ext_sgwc_ip_len) != 0)
            {
                upf_err ("sgwc ip check failed\n");
                return -1;
            }
        }*/

        sub_ext_smf_ip = (unsigned char *)get_upf_sub_extensions(upf_sub_ext_headers, UPF_EXTENSION_SMFIP_TYPE, &sub_ext_smf_ip_len);
        if (sub_ext_smf_ip_len > 0)
        {
            u8 *buff = NULL;
            buff = format(buff, "%U", format_ip46_address, &sess->cp_address, IP46_TYPE_ANY);

            if (memcmp(sub_ext_smf_ip, buff, sub_ext_smf_ip_len) != 0)
            {
                upf_err ("smf ip check failed\n");
                return -1;
            }
        }

        /*sub_ext_dnn = (unsigned char  *)get_upf_sub_extensions(upf_sub_ext_headers, F_SUB_EXTENSTION_DNN, &sub_ext_dnn_len);
        if (memcmp(sub_ext_dnn, sess->dnn, sub_ext_dnn_len) != 0)
        {
            upf_err ("dnn check failed\n");
            return -1;
        }*/
    }

    if (g_https_header_enrichment_whitelist_flag && upf_whitelist_num)
    {
        if (upf_whitelist->sni_check_flag)
        {
            if (ip46_address_is_ip4 (&upf_whitelist->key.ip))
            {
                u32 flag = 0;
                upf_dns_sniffer_if_catch_by_name_addr(upf_ext_headers->hostname, ip4->dst_address.data_u32, &flag);

                if (!flag)
                {
                    return 0;
                }
            }
        }

        /*if (is_tcp_segment(ip4))
        {
            upf_err ("https tcp layer segment\n");
            return 0;
        }*/


        if (!(upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_OPEN))
        {
            return 0;
        }

        //if (upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_MSISDN)
        {
             memcpy(https_info.msisdn, sess->user_id.msisdn_str, sizeof(sess->user_id.msisdn_str));
             https_info.msisdn_flag = 1;

             if (upf_whitelist->encryp_extension_flag & F_SUB_EXTENSTION_MSISDN)
             {
                 https_info.msisdn_flag = 2;
             }
        }

        if (upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_USERIP)
        {
             if (strlen((const char *)https_info.user_ip) == 0)
             {
                 u8 *buff = NULL;
                 buff = format(buff, "%U", format_ip46_address, &sess->ue_address, IP46_TYPE_ANY);

                 memcpy(&https_info.user_ip, buff, strlen((const char *)buff));
                 https_info.user_ip[strlen((const char *)buff)] = '\0';
             }

             https_info.user_ip_flag = 1;

             if (upf_whitelist->encryp_extension_flag & F_SUB_EXTENSTION_USERIP)
             {
                 https_info.user_ip_flag = 2;
             }
        }

        if (upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_IMSI)
        {
             memcpy(https_info.imsi, sess->user_id.imsi_str, sizeof(sess->user_id.imsi_str));
             https_info.imsi_flag = 1;

             if (upf_whitelist->encryp_extension_flag & F_SUB_EXTENSTION_IMSI)
             {
                 https_info.imsi_flag = 2;
             }
        }

        if (upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_IMEI)
        {
             memcpy(https_info.imei, sess->user_id.imei_str, sizeof(sess->user_id.imei_str));
             https_info.imei_flag = 1;

             if (upf_whitelist->encryp_extension_flag & F_SUB_EXTENSTION_IMEI)
             {
                 https_info.imei_flag = 2;
             }
        }

        if (upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_ULI)
        {
             if (upf_whitelist->uli[0] == 0)
             {
                 pfcp_cmcc_user_location_information_t *uli = (pfcp_cmcc_user_location_information_t *)&upf_whitelist->uli;

               uli->geographic_location_type = sess->user_location_information.geographic_location_type;
               memcpy(uli->geographic_location, sess->user_location_information.geographic_location, strlen((const char *)sess->user_location_information.geographic_location));
             }

             memcpy(https_info.uli, upf_whitelist->uli, sizeof(https_info.uli));
             https_info.uli_flag = 1;

             if (upf_whitelist->encryp_extension_flag & F_SUB_EXTENSTION_ULI)
             {
                 https_info.uli_flag = 2;
             }
        }

        if (upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_SGWCIP)
        {
             if (strlen((const char *)https_info.sgwc_ip) == 0)
             {
                 //u8 *buff = NULL;
                 //buff = format(buff, "%U", format_ip46_address, &sess->up_address, IP46_TYPE_ANY);

                 //memcpy(&https_info.sgwc_ip, buff, strlen((const char *)buff));
                 //https_info.sgwc_ip[strlen((const char *)buff)] = '\0';
                 https_info.sgwc_ip[0] = '\0';
             }

             https_info.sgwc_ip_flag = 1;

             if (upf_whitelist->encryp_extension_flag & F_SUB_EXTENSTION_SGWCIP)
             {
                 https_info.sgwc_ip_flag = 2;
             }
        }

        if (upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_SMFIP)
        {
             if (strlen((const char *)https_info.smf_ip) == 0)
             {
                 u8 *buff = NULL;
                 buff = format(buff, "%U", format_ip46_address, &sess->cp_address, IP46_TYPE_ANY);

                 memcpy(&https_info.smf_ip, buff, strlen((const char *)buff));
                 https_info.smf_ip[strlen((const char *)buff)] = '\0';
             }

             https_info.smf_ip_flag = 1;

             if (upf_whitelist->encryp_extension_flag & F_SUB_EXTENSTION_SMFIP)
             {
                 https_info.smf_ip_flag = 2;
             }
        }

        if (upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_TIMESTAMP)
        {
             if (https_info.timestamp == 0)
             {
                 https_info.timestamp = time(NULL);
             }

             https_info.timestamp_flag = 1;

             if (upf_whitelist->encryp_extension_flag & F_SUB_EXTENSTION_TIMESTAMP)
             {
                 https_info.timestamp_flag = 2;
             }
        }

        if (upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_RATTYPE)
        {
             if (upf_whitelist->rat_type[0] == 0)
             {
                 upf_whitelist->rat_type[0] = sess->rat_type;
             }

             memcpy(https_info.rat_type, upf_whitelist->rat_type, sizeof(https_info.rat_type));
             https_info.rat_type_flag = 1;

             if (upf_whitelist->encryp_extension_flag & F_SUB_EXTENSTION_RATTYPE)
             {
                 https_info.rat_type_flag = 2;
             }
        }

        if (upf_whitelist->sub_extension_flag & F_SUB_EXTENSTION_DNN)
        {
             memcpy(https_info.dnn, sess->dnn, strlen((const char *)sess->dnn));
             https_info.dnn_flag = 1;

             if (upf_whitelist->encryp_extension_flag & F_SUB_EXTENSTION_DNN)
             {
                 https_info.dnn_flag = 2;
             }
        }

        memset(insert_buff, 0, sizeof(insert_buff));
        insert_length = 0;

        construct_upf_extensions((upf_whitelist->extension_flag ? 17516 : 18888), &https_info, insert_buff, &insert_length, upf_whitelist->algorithm_flag, upf_whitelist->encryp_key);

        if (insert_length)
        {
             vlib_buffer_advance (b, -(word)insert_length);
             memmove (vlib_buffer_get_current (b),
                     vlib_buffer_get_current (b) + insert_length,
                     header_length + https_length);

             if (is_ip4)
             {
                 ip4 = (ip4_header_t *)((char *)ip4 - insert_length);
                 ip4->length = clib_host_to_net_u16 (vlib_buffer_length_in_chain (vm, b));
             }
             else
             {
                 ip6 = (ip6_header_t *)((char *)ip6 - insert_length);
                 ip6->payload_length = clib_host_to_net_u16 (b->current_length);
             }

             https = https- insert_length;

             memcpy ((void *)https+https_length, insert_buff, insert_length);

             u16 length = clib_net_to_host_u16(*((u16 *)(https + 3)));
                  u16 *p = (u16 *)(https + 3);
             *p = clib_net_to_host_u16(length + insert_length);

             length = clib_net_to_host_u16(*((u16 *)(https + 7)));
             p = (u16 *)(https + 7);
             *p = clib_net_to_host_u16(length + insert_length);

             u8 sesson_id_length = (*((u8 *)(https + 9 + 2 + 32)));
             u16 cipher_suites_length = clib_net_to_host_u16(*((u16 *)(https + 9 + 2 + 32 + 1 + sesson_id_length)));
             u8 compression_length = (*((u8 *)(https + 9 + 2 + 32 + 1 + sesson_id_length + 2 + cipher_suites_length)));
             length = clib_net_to_host_u16(*((u16 *)(https + 9 + 2 + 32 + 1 + sesson_id_length + 2 + cipher_suites_length + 1 + compression_length)));
             p = (u16 *)(https + 9 + 2 + 32 + 1 + sesson_id_length + 2 + cipher_suites_length + 1 + compression_length);
             *p = clib_net_to_host_u16(length + insert_length);

             if (is_ip4)
             {
                ip4->checksum = ip4_header_checksum (ip4);
             }

             tcp = (tcp_header_t *)((char *)tcp - insert_length);
             tcp->checksum = 0;
             //tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);
             if (is_ip4)
             {
                 tcp->checksum = 0;
                 tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);
             }
             else
             {
                 int bogus_length = 0;
                 tcp->checksum = ip6_tcp_udp_icmp_compute_checksum (vm, b, ip6, &bogus_length);
             }

             if (upf_whitelist->tcp_number_modify_flag)
             {
                 flowtable_main_t *fm = &g_flowtable_main;
                 flow_entry_t *flow = pool_elt_at_index (fm->flows, upf_buffer_opaque (b)->upf.flow_index);
                 if (NULL != flow)
                 {
                     flow->https_header_enhancement_flag = 1;
                     flow->https_header_insert_length += insert_length;
                 }
             }
        }
    }

  return 0;
}

int upf_pfcp_http_header_enrichment (vlib_main_t *vm, vlib_buffer_t *b, upf_far_t *far, upf_session_t *sess,
                            u32 is_ip4)
{
  int i, j, k, ret;
  ip4_header_t *ip4 = NULL;
  ip6_header_t *ip6 = NULL;
  tcp_header_t *tcp;
  char *first_header;
  const char *http;
  const char *method;
  size_t method_len;
  const char *path;
  size_t path_len;
  int minor_version;
  u8 skip[32] = {0};
  struct upf_phr_header_t headers[32];
  size_t num_headers = sizeof (headers) / sizeof (headers[0]);
  i32 insert_length;
  i32 total_insert_length = 0;
  u32 header_length;
  u32 http_length;
  pfcp_header_enrichment_t *header_enrichment;
  char new_header[256];
  //u8 *url = NULL;
  //u8 *action = NULL;
  u8 url[64] = {0};
  u8 action[64] = {0};
  //ip46_address_t ip = {0};
  u8 usrid[17] = {0};
  u8 is_find_whitelist = 0;
  u8 is_find_graylist = 0;
  upf_whitelist_t *upf_whitelist = NULL;

  if (!g_http_header_enrichment_whitelist_flag && !g_http_header_enrichment_graylist_flag)
    {
        upf_trace ("http header enrichment whitelist flag and http header enrichment graylist flag is close");
        return 0;
    }

  if (!vlib_buffer_chain_linearize (vm, b))
    {
      upf_debug ("vlib_buffer_chain_linearize %p failed\n", b);
      return -1;
    }

  if (is_ip4)
    {
      ip4 = (ip4_header_t *)vlib_buffer_get_current (b);
      tcp = ip4_next_header (ip4);
      http = (const char *)tcp + tcp_header_bytes (tcp);
      header_length = ip4_header_bytes (ip4) + tcp_header_bytes (tcp);
      http_length = vlib_buffer_length_in_chain (vm, b) - header_length;
    }
  else
    {
      ip6 = (ip6_header_t *)vlib_buffer_get_current (b);
      tcp = ip6_next_header (ip6);
      http = (const char *)tcp + tcp_header_bytes (tcp);
      header_length = sizeof (*ip6) + tcp_header_bytes (tcp);
      http_length = vlib_buffer_length_in_chain (vm, b) - header_length;
    }

  ret =
      upf_phr_parse_request_func (http, http_length, &method, &method_len, &path,
                         &path_len, &minor_version, headers, &num_headers, 0);
  if (ret < 0)
    {
      upf_debug ("parse http header failed, ret %d\n", ret);
      return ret;
    }

  if (g_http_header_enrichment_whitelist_flag && upf_whitelist_num)
  {
    //action = vec_dup("http_header_enrichment");
    //vec_validate (action, sizeof("http_header_enrichment"));
    //clib_memcpy_fast (action, "http_header_enrichment", sizeof("http_header_enrichment"));
    strcpy((char *)action, "http_header_enrichment");

    /*sess->user_id.imsi[0] = 0x31;
    sess->user_id.imsi[1] = 0x88;
    sess->user_id.imsi[2] = 0x88;
    sess->user_id.imsi[3] = 0x88;
    sess->user_id.imsi[4] = 0x88;
    sess->user_id.imsi[5] = 0x88;
    sess->user_id.imsi[6] = 0xF8;*/

    if (!upf_get_url(headers, 32, url))
    {
        upf_debug ("get url fail \n", url);
        return 0;
    }

    if (sess->user_id.flags & USER_ID_IMSI)
    {
        tupf_bcd_to_str(usrid, sess->user_id.imsi, sizeof(sess->user_id.imsi));

        upf_whitelist = upf_lookup_upf_num_whitelist(NULL, url, usrid, action);

        if (upf_whitelist)
        {
            is_find_whitelist = 1;
            goto next;
        }
    }

    if (sess->user_id.flags & USER_ID_MSISDN)
    {
        tupf_bcd_to_str(usrid, sess->user_id.msisdn, sizeof(sess->user_id.msisdn));

        upf_whitelist = upf_lookup_upf_num_whitelist(NULL, url, usrid, action);

        if (upf_whitelist)
        {
            is_find_whitelist = 1;
            goto next;
        }
    }

    if (sess->user_id.flags & USER_ID_IMEI)
    {
        upf_bcd_to_str(usrid, sess->user_id.imei, sizeof(sess->user_id.imei));

        upf_whitelist = upf_lookup_upf_num_whitelist(NULL, url, usrid, action);

        if (upf_whitelist)
        {
            is_find_whitelist = 1;
            goto next;
        }
    }

    if (!sess->user_id.flags)
    {
        upf_whitelist = upf_lookup_upf_ip_whitelist(NULL, url, NULL, action);

        if (upf_whitelist)
        {
            is_find_whitelist = 1;
            goto next;
        }
    }

    if (!is_find_whitelist)
    {
        upf_debug ("lookup upf ip whitelist fail \n");
        //return 0;
    }
  }

  if (g_http_header_enrichment_graylist_flag && upf_graylist_num && !is_find_whitelist)
  {

    strcpy((char *)action, "http_header_enrichment");


    if (!upf_get_url(headers, 32, url))
    {
        upf_debug ("get url fail \n", url);
        return 0;
    }

    if (sess->user_id.flags & USER_ID_IMSI)
    {
        tupf_bcd_to_str(usrid, sess->user_id.imsi, sizeof(sess->user_id.imsi));
        if (upf_lookup_upf_ip_graylist(NULL, url, sess->dnn, usrid, action))
        {
            is_find_graylist = 1;
            goto next;
        }
    }

    if (sess->user_id.flags & USER_ID_MSISDN)
    {
        tupf_bcd_to_str(usrid, sess->user_id.msisdn, sizeof(sess->user_id.msisdn));

        if (upf_lookup_upf_ip_graylist(NULL, url, sess->dnn, usrid, action))
        {
            is_find_graylist = 1;
            goto next;
        }
    }

    if (sess->user_id.flags & USER_ID_IMEI)
    {
        upf_bcd_to_str(usrid, sess->user_id.imei, sizeof(sess->user_id.imei));

        if (upf_lookup_upf_ip_graylist(NULL, url, sess->dnn, usrid, action))
        {
            is_find_graylist = 1;
            goto next;
        }
    }

    if (!is_find_graylist)
    {
        upf_debug ("lookup upf ip graylist fail \n");
        //return 0;
    }
  }

  if(!is_find_whitelist && !is_find_graylist)
  {
     return 0;
  }


next:

  //begin liukang add for upf http detect 2021/06/24
  if(is_find_whitelist)
  {
    for (i = 0; i < num_headers; i++)
    {

          for (j = 0; j < vec_len (far->forward.header_enrichment); j++)
        {
          header_enrichment = &far->forward.header_enrichment[j];
          if (strncmp (headers[i].name, "x-up-calling-line-id",headers[i].name_len) == 0)
          {

              if (strncmp (headers[i].name, (char *)header_enrichment->name,
                           headers[i].name_len) == 0)
              {
                      char *num = (char *)header_enrichment->value;
                    int num_len = 0;
                    while(1)
                    {
                        if(isdigit(*num))
                        {
                            num_len++;
                            num++;
                        }
                        else
                        {
                            break;
                        }
                    }

                    if(headers[i].value_len != num_len)
                    {
                        return -2;
                    }

                    if(strncmp (headers[i].value, (char *)header_enrichment->value,
                           headers[i].value_len)==0)
                    {
                        ;
                        //goto next;
                    }
                    else
                    {
                        return -2;
                    }
              }
          }

        }
    }
  }
  //end liukang add for upf http detect 2021/06/24

  for (i = 0; i < num_headers; i++)
    {
      for (j = 0; j < vec_len (far->forward.header_enrichment); j++)
        {
          header_enrichment = &far->forward.header_enrichment[j];

          if (strncmp (headers[i].name, (char *)header_enrichment->name,
                       headers[i].name_len))
            {
              continue;
            }

          skip[j] = 1;

          if (strncmp (headers[i].value, (char *)header_enrichment->value,
                       headers[i].value_len) == 0)
            {
              break;
            }


           if(is_find_graylist)
           {
                return -2; //block tcp link         liukang add 2021/05/10
           }

          insert_length =
              strlen ((char *)header_enrichment->value) - headers[i].value_len;
          if (insert_length)
            {
              vlib_buffer_advance (b, -insert_length);

              memmove (vlib_buffer_get_current (b),
                       vlib_buffer_get_current (b) + insert_length,
                       header_length + (headers[i].value - http));
              if (is_ip4)
                {
                  ip4 = (ip4_header_t *)((char *)ip4 - insert_length);
                  ip4->length = clib_host_to_net_u16 (
                      vlib_buffer_length_in_chain (vm, b));
                  ip4->checksum = ip4_header_checksum (ip4);
                }
              else
                {
                  ip6 = (ip6_header_t *)((char *)ip6 - insert_length);
                  ip6->payload_length =
                      clib_host_to_net_u16 (b->current_length);
                }

              tcp = (tcp_header_t *)((char *)tcp - insert_length);
              http -= insert_length;
              headers[i].name -= insert_length;
              headers[i].value -= insert_length;
              headers[i].value_len = strlen ((char *)header_enrichment->value);

              for (k = i + 1; k < num_headers; k++)
                {
                  headers[k].name -= insert_length;
                  headers[i].value -= insert_length;
                }

              total_insert_length += insert_length;
            }

          memcpy ((char *)headers[i].name, header_enrichment->name,
                  strlen ((char *)header_enrichment->name));
          memcpy ((char *)headers[i].name +
                      strlen ((char *)header_enrichment->name),
                  ":", 1);
          memcpy ((char *)headers[i].value, header_enrichment->value,
                  strlen ((char *)header_enrichment->value));
        }
    }
  if(!is_find_graylist && is_find_whitelist)
  {
      first_header = (char *)headers[0].name;
      for (i = 0; i < vec_len (far->forward.header_enrichment); i++)
        {
          if (skip[i])
            continue;

          header_enrichment = &far->forward.header_enrichment[i];
          insert_length = strlen ((const char *)header_enrichment->name) +
                          vec_len (header_enrichment->value) + 4; /* neil.fan@20221230 fix 0x0 content in value field */
          if (0 == strncmp ((const char *)header_enrichment->name, "ULI", 3))
          {
              insert_length += 3; /* 29061-h30 3GPP-User-Location-Info, insert 3 field: the 3GPP type(22), 3GPP Length, Geographic Location Type */
          }

          vlib_buffer_advance (b, -insert_length);
          memmove (vlib_buffer_get_current (b),
                   vlib_buffer_get_current (b) + insert_length,
                   header_length + (first_header - http));

          if (is_ip4)
          {
            ip4 = (ip4_header_t *)((char *)ip4 - insert_length);
            ip4->length =
                clib_host_to_net_u16 (vlib_buffer_length_in_chain (vm, b));
            ip4->checksum = ip4_header_checksum (ip4);
          }
           else
          {
            ip6 = (ip6_header_t *)((char *)ip6 - insert_length);
            ip6->payload_length = clib_host_to_net_u16 (b->current_length);
          }

          //tcp = (tcp_header_t *)((char *)tcp - insert_length);
          //tcp->checksum = 0;
          //tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);

          //http -= insert_length;
          //first_header -= insert_length;
          //total_insert_length += insert_length;
          //sprintf (new_header, "%s: %s\r\n", header_enrichment->name,


          http -= insert_length;
          first_header -= insert_length;
          total_insert_length += insert_length;
          if (0 == strncmp ((const char *)header_enrichment->name, "ULI", 3))
          {
              sprintf (new_header, "%s: ", header_enrichment->name);
              u8 offset = vec_len (header_enrichment->name) + 2; /* 2 means ": " */

              u8 tmp[20] = {0x16, 0x09, 0x88}; /* 29061-h30 3GPP-User-Location-Info, 0x88 means "136 5GS TAI" */
              tmp[1] = vec_len(header_enrichment->value) + 3; /* 3: octet1-3, i.e. 0x16, length, Geographic Location Type */
              clib_memcpy (tmp + 3, header_enrichment->value, vec_len(header_enrichment->value));
              tmp[tmp[1]] = '\r';
              tmp[tmp[1] + 1] = '\n';
              clib_memcpy (new_header + offset, tmp, tmp[1] + 2); /* 2: "\r\n" */
          }
          else
          {
              sprintf (new_header, "%s: %s\r\n", header_enrichment->name,
                     header_enrichment->value);
          }
          memcpy (first_header, new_header, insert_length);

          tcp = (tcp_header_t *)((char *)tcp - insert_length);
          tcp->checksum = 0;
          //tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);
          if (is_ip4)
          {
              tcp->checksum = 0;
              tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);
          }
          else
          {
              int bogus_length = 0;
              tcp->checksum = ip6_tcp_udp_icmp_compute_checksum (vm, b, ip6, &bogus_length);
          }

          if (upf_whitelist->tcp_number_modify_flag)
          {
              flowtable_main_t *fm = &g_flowtable_main;
              flow_entry_t *flow = pool_elt_at_index (fm->flows, upf_buffer_opaque (b)->upf.flow_index);
              if (NULL != flow)
              {
                  flow->http_header_enhancement_flag = 1;
                  flow->http_header_insert_length += total_insert_length;
              }
          }



          memcpy (first_header, new_header, insert_length);
        }
      }

  if (total_insert_length)
    {
        for (i = 0; i < num_headers; i++)
        {
          if (strncmp (headers[i].name, "Content-Length",
                       headers[i].name_len) == 0)
            {
              j = strtoul (headers[i].value, NULL, 10);
              j += total_insert_length;
              k = sprintf (new_header, "%d", j);
              insert_length = k - headers[i].value_len;

              if (insert_length)
                {
                  vlib_buffer_advance (b, -insert_length);
                  memmove (vlib_buffer_get_current (b),
                           vlib_buffer_get_current (b) + insert_length,
                           header_length + (headers[i].value - http));
                  if (is_ip4)
                    {
                      ip4 = (ip4_header_t *)((char *)ip4 - insert_length);
                      ip4->length = clib_host_to_net_u16 (
                          vlib_buffer_length_in_chain (vm, b));
                      ip4->checksum = ip4_header_checksum (ip4);
                    }
                  else
                    {
                      ip6 = (ip6_header_t *)((char *)ip6 - insert_length);
                      ip6->payload_length =
                          clib_host_to_net_u16 (b->current_length);
                    }

                  tcp = (tcp_header_t *)((char *)tcp - insert_length);
                  tcp->checksum = 0;
                  tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);

                  http -= insert_length;
                  first_header -= insert_length;
                  headers[i].value -= insert_length;
                }

              memcpy ((char *)headers[i].value, new_header, k);

              if (is_ip4)
              {
                ip4->checksum = ip4_header_checksum (ip4);
              }

              tcp->checksum = 0;
              //tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);
              if (is_ip4)
              {
                  tcp->checksum = 0;
                  tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);
              }
              else
              {
                  int bogus_length = 0;
                  tcp->checksum = ip6_tcp_udp_icmp_compute_checksum (vm, b, ip6, &bogus_length);
              }


              if (upf_whitelist->tcp_number_modify_flag)
              {
                  flowtable_main_t *fm = &g_flowtable_main;
                  flow_entry_t *flow = pool_elt_at_index (fm->flows, upf_buffer_opaque (b)->upf.flow_index);
                  if (NULL != flow)
                  {
                      flow->http_header_enhancement_flag = 1;
                      flow->http_header_insert_length += total_insert_length;
                  }
              }

              flowtable_main_t *fm = &g_flowtable_main;
              flow_entry_t *flow = pool_elt_at_index (fm->flows, upf_buffer_opaque (b)->upf.flow_index);

              if (NULL != flow)
              {
                  flow->http_header_enhancement_flag = 1;
                  flow->http_header_insert_length = total_insert_length;
              }



              break;
            }
        }
    }

  return 0;
}

static const char *g_apply_action_flags[] = {
    "DROP", "FORWARD", "BUFFER", "NOTIFY_CP", "DUPLICATE", "",
    "",     "",        "",       "",          "DDPN",      NULL};

static const char *g_urr_method_flags[] = {"TIME", "VOLUME", "EVENT", NULL};

static const char *g_urr_trigger_flags[] = {"PERIODIC REPORTING",
                                          "VOLUME THRESHOLD",
                                          "TIME THRESHOLD",
                                          "QUOTA HOLDING TIME",
                                          "START OF TRAFFIC",
                                          "STOP OF TRAFFIC",
                                          "DROPPED DL TRAFFIC THRESHOLD",
                                          "LINKED USAGE REPORTING",
                                          "VOLUME QUOTA",
                                          "TIME QUOTA",
                                          "ENVELOPE CLOSURE",
                                          "MAC ADDRESSES REPORTING",
                                          "EVENT_THRESHOLD",
                                          "EVENT_QUOTA",
                                          "IP_MULTICAST_JOIN_LEAVE",
                                          NULL};

static const char *g_urr_status_flags[] = {
    "OVER QUOTA", "THRESHOLD REACHED", "AFTER MONITORING TIME",
    "ACTIVE",     "START_TRAFFIC",     NULL};

static const char *g_outer_header_removal_str[] = {
    "GTP-U/UDP/IPv4", "GTP-U/UDP/IPv6", "UDP/IPv4",   "UDP/IPv6",
    "IPv4",           "IPv6",           "GTPU/UDP/IP"};

static const char *g_qer_gate_status_flags[] = {"OPEN", "CLOSED", NULL};

static u8 *upf_format_time_stamp_msec (u8 *s, va_list *args)
{
  u64 *v = va_arg (*args, u64 *);
  struct timeval tv;
  tv.tv_sec = *v*1e-9;
  tv.tv_usec = (u32)*v*1e-4;

  return format (s, "%U", format_timeval, 0, &tv);
}

static u8 *upf_format_urr_counter (u8 *s, va_list *args)
{
  void *m = va_arg (*args, void *);
  void *t = va_arg (*args, void *);
  off_t offs = va_arg (*args, off_t);

  return format (
      s, "Measured: %20" PRIu64 ", Threshold: %20" PRIu64 ", Pkts: %10" PRIu64,
      *(u64 *)(m + offsetof (urr_measure_t, bytes) + offs), *(u64 *)(t + offs),
      *(u64 *)(m + offsetof (urr_measure_t, packets) + offs));
}

static u8 *upf_format_urr_quota (u8 *s, va_list *args)
{
  void *m = va_arg (*args, void *);
  void *q = va_arg (*args, void *);
  off_t offs = va_arg (*args, off_t);

  return format (s, "Consumed: %20" PRIu64 ", Quota:    %20" PRIu64,
                 *(u64 *)(m + offsetof (urr_measure_t, consumed) + offs),
                 *(u64 *)(q + offs));
}

static u8 *upf_format_urr_time (u8 *s, va_list *args)
{
  urr_time_t *t = va_arg (*args, urr_time_t *);
  f64 now = unix_time_now ();

  return format (s, "%20" PRIu64 " secs @ %U, in %9.3f secs, handle 0x%08x",
                 t->period,
                 /* VPP does not support ISO dates... */
                 format_time_float, 0, t->base + (f64)t->period,
                 ((f64)t->period) - (now - t->base), t->handle);
}

static u8 *upf_format_urr_time_idt (u8 *s, va_list *args)
{
  urr_time_t *t = va_arg (*args, urr_time_t *);
  return format (s, "%u secs , consumed %u secs, idt %u secs, handle 0x%x",
                 t->total_time, t->consumed, (t->base == 0) ? t->period : 0,
                 t->handle);
}

static u8 *upf_format_urr_time_abs (u8 *s, va_list *args)
{
  urr_time_t *t = va_arg (*args, urr_time_t *);
  f64 now = unix_time_now ();

  return format (s, "%U, in %9.3f secs, handle 0x%08x",
                 /* VPP does not support ISO dates... */
                 format_time_float, 0, t->base, t->base - now, t->handle);
}

u32 upf_table_id_get_by_sx_rules (upf_session_t *sx, struct rules *rules)
{
    u32 table_id = ~0;
    if (rules->vrf_ip)
    {
        ip46_address_fib_t vrf_ip = vec_elt (rules->vrf_ip, 0);
        if (ip46_address_is_ip4 (&sx->ue_address))
            table_id = fib_table_get_table_id (vrf_ip.fib_index, FIB_PROTOCOL_IP4);
        else
            table_id = fib_table_get_table_id (vrf_ip.fib_index, FIB_PROTOCOL_IP6);
    }
    else if (rules->ip4_pdrs)
    {
        upf_5glan_ip_pdrs_t *ip_pdr = vec_elt_at_index(rules->ip4_pdrs, 0);
        table_id = ip_pdr->table_id;
    }
    else if (rules->ip6_pdrs)
    {
        upf_5glan_ip_pdrs_t *ip_pdr = vec_elt_at_index(rules->ip6_pdrs, 0);
        table_id = ip_pdr->table_id;
    }
    return table_id;
}

u32 upf_buffers_get_by_sx_rules (struct rules *rules)
{
    u32 buf_n = 0;
    upf_far_t *far0;

    vec_foreach (far0, rules->far)
    {
        buf_n += far0->uplink_buf_n + far0->downlink_buf_n;
    }
    return buf_n;
}

u8 *upf_format_simple_sx_session (u8 *s, va_list *args)
{
    upf_session_t *sx = va_arg (*args, upf_session_t *);
    int rule = va_arg (*args, int);
    struct rules *rules = upf_get_rules (sx, rule);
    s = format (s,
                "----------------------------------------------------\n"
                "CP F-SEID: 0x%016lx@%U\n"
                "UP F-SEID: 0x%016lx@%U\n",
                sx->cp_seid, format_ip46_address, &sx->cp_address, IP46_TYPE_ANY,
                sx->up_seid, format_ip46_address, &sx->up_address, IP46_TYPE_ANY);
    s = format (s, "Create   : %U\n", upf_format_time_stamp_msec, &sx->create_session_time);
    s = format (s, "Update   : %U\n", upf_format_time_stamp_msec, &sx->update_session_time);
    s = format (s, "Report   : %U\n", upf_format_time_stamp_msec, &sx->session_report_time);
    s = format (s, "IMSI     : %s\n", sx->user_id.imsi_str);
    s = format (s, "PDN Type : %U\n", upf_format_pdn_type, &sx->pdn_type);

    if(sx->rat_type == RAT_TYPE_UTRAN)
        s = format (s, "RAT Type : %s\n", "UTRAN");

    if(sx->rat_type == RAT_TYPE_LTE_NR)
        s = format (s, "RAT Type : %s\n", "NR");

    if ((sx->pdn_type < PDN_TYPE_NON_IP) && (!ip46_address_is_zero(&sx->ue_address)))
    {
        u32 table_id = upf_table_id_get_by_sx_rules (sx, rules);
        if (table_id != ~0)
        {
            s = format (s, "table_idx %d ", table_id);
        }
        s = format (s, "ue_ipaddr %U\n", format_ip46_address, &sx->ue_address, IP46_TYPE_ANY);
    }

    u32 buf_n = upf_buffers_get_by_sx_rules (rules);
    if (buf_n)
        s = format (s,"Cached   : %u\n", buf_n);

    return s;
}

u8 *upf_format_simple_sx_session_line (u8 *s, va_list *args)
{
    upf_session_t *sx = va_arg (*args, upf_session_t *);
    int rule = va_arg (*args, int);
    struct rules *rules = upf_get_rules (sx, rule);
    s = format (s, "cp[0x%016lx@%U] up[0x%016lx@%U] ",
                sx->cp_seid, format_ip46_address, &sx->cp_address, IP46_TYPE_ANY,
                sx->up_seid, format_ip46_address, &sx->up_address, IP46_TYPE_ANY);
    s = format (s, "create time[%U] ", upf_format_time_stamp_msec, &sx->create_session_time);
    s = format (s, "update time[%U] ", upf_format_time_stamp_msec, &sx->update_session_time);
    s = format (s, "report time[%U] ", upf_format_time_stamp_msec, &sx->session_report_time);
    s = format (s, "imsi[%s] ", sx->user_id.imsi_str);
    s = format (s, "pdn[%U]", upf_format_pdn_type, &sx->pdn_type);

    if ((sx->pdn_type < PDN_TYPE_NON_IP) && (!ip46_address_is_zero(&sx->ue_address)))
    {
        u32 table_id = upf_table_id_get_by_sx_rules (sx, rules);
        if (table_id != ~0)
        {
            s = format (s, "table_idx[%d] ", table_id);
        }
        s = format (s, "ue_ipaddr[%U] ", format_ip46_address, &sx->ue_address, IP46_TYPE_ANY);
    }

    u32 buf_n = upf_buffers_get_by_sx_rules(rules);
    if (buf_n)
        s = format (s, "cached[%u]\n", buf_n);

    return s;
}

u8 *upf_format_simple_dnn_session (u8 *s, va_list *args)
{
  upf_urr_t *urr;
  upf_session_t *sx = va_arg (*args, upf_session_t *);
  s = format (s,
              "\t\t CP F-SEID: 0x%016" PRIx64 " (%" PRIu64 ") @ %U\n"
              "\t\t UP F-SEID: 0x%016" PRIx64 " (%" PRIu64 ") @ %U\n",
              sx->cp_seid, sx->cp_seid, format_ip46_address, &sx->cp_address,
              IP46_TYPE_ANY, sx->up_seid, sx->up_seid, format_ip46_address,
              &sx->up_address, IP46_TYPE_ANY, sx);
  s = format (s, "\t\t cp_ip %U ue_ip %U\n",
              format_ip46_address, &sx->cp_address, IP46_TYPE_ANY,
              format_ip46_address, &sx->ue_address, IP46_TYPE_ANY);

  struct rules *rules = upf_get_rules (sx, SX_ACTIVE);

  vec_foreach (urr, rules->urr)
  {
    /* *INDENT-OFF* */
    s = format (s, "\t\t URR[%u] reported times:%u\n", urr->id, urr->reported_times);
  }

  return s;
}

u8 *upf_format_simple_s_nssai_session (u8 *s, va_list *args)
{
  upf_urr_t *urr;
  upf_session_t *sx = va_arg (*args, upf_session_t *);
  s = format (s,
              "\t\t CP F-SEID: 0x%016" PRIx64 " (%" PRIu64 ") @ %U\n"
              "\t\t UP F-SEID: 0x%016" PRIx64 " (%" PRIu64 ") @ %U\n",
              sx->cp_seid, sx->cp_seid, format_ip46_address, &sx->cp_address,
              IP46_TYPE_ANY, sx->up_seid, sx->up_seid, format_ip46_address,
              &sx->up_address, IP46_TYPE_ANY, sx);
  s = format (s, "\t\t cp_ip %U ue_ip %U\n",
              format_ip46_address, &sx->cp_address, IP46_TYPE_ANY,
              format_ip46_address, &sx->ue_address, IP46_TYPE_ANY);

  struct rules *rules = upf_get_rules (sx, SX_ACTIVE);

  vec_foreach (urr, rules->urr)
  {
    /* *INDENT-OFF* */
    s = format (s, "\t\t URR[%u] reported times:%u\n", urr->id, urr->reported_times);
  }

  return s;
}

u8 *upf_format_pre_qer (u8 *s, va_list *args)
{
  upf_qer_t *qer;
  /* begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
  s = format (s, "----------------------------------------------------\n"
                 "Pre Def QER Info:\n");

  vec_foreach (qer, g_upf_main.pre_qer)
  {
    s = format (s, "----------------------------------------------------\n");
    /* *INDENT-OFF* */

    s = format (s,
                "Predef QER: %u\n"
                "  Gate Uplink: %d == %U\n"
                "  Gate Downlink: %d == %U\n"
                "  QFI: %u\n"
                "  RQI: %u\n"
                "  MBR Uplink: %ukbps\n"
                "  MBR Downlink: %ukbps\n"
                "  police Uplink: %ukbps\n"
                "  police Downlink: %ukbps\n",
                qer->id, qer->gate_status[UPF_UL], format_flags,
                (u64)qer->gate_status[UPF_UL] + 1, g_qer_gate_status_flags,
                qer->gate_status[UPF_DL], format_flags,
                (u64)qer->gate_status[UPF_DL] + 1, g_qer_gate_status_flags,
                qer->qos_flow_identifier, qer->reflective_qos, qer->mbr.ul,
                qer->mbr.dl, qer->pol_mbr[UPF_UL], qer->pol_mbr[UPF_DL]);
    /* *INDENT-ON* */
  }
  /* end modified by liuyu UPF_CMD_MOD 2022-08-22 */
  return s;
}

u8 *upf_format_pre_far (u8 *s, va_list *args)
{
  upf_far_t *far;
  upf_far_duplicate_t *dupl_far = NULL;
  u8 index = 1;
  upf_main_t *gtm = &g_upf_main;
  /* begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
  s = format (s, "----------------------------------------------------\n"
                 "Pre Def FAR Info:\n");

  vec_foreach (far, g_upf_main.pre_far)
  {
    upf_nwi_t *nwi = NULL;

    if (!pool_is_free_index (gtm->nwis, far->forward.nwi))
      nwi = pool_elt_at_index (gtm->nwis, far->forward.nwi);

    s = format (s, "----------------------------------------------------\n");

    s = format (s,
                "Predef FAR: %u\n"
                "  FAR Action: %08x == %U\n",
                far->id, far->apply_action, format_flags, far->apply_action,
                g_apply_action_flags);

    s = format (s,
                "  Forwardding:\n"
                "    Dest Interface: %U\n", upf_format_destination_interface, &far->forward.dst_intf);
    if (far->forward.flags & FAR_F_NETWORK_INSTANCE)
      {
        if (far->forward.dst_intf == DST_INTF_5G_VN)
        {
            upf_5glan_nwi_t *nwi_inst = upf_5glan_index_to_nwi(far->forward.nwi);
            s = format (s, "    Network Instance: %U\n", upf_format_network_instance,
                        nwi_inst ? nwi_inst->name : NULL);
        }
        else
        {
            s = format (s, "    Network Instance: %U\n", upf_format_network_instance,
                        nwi ? nwi->name : NULL);
        }
      }

    if (far->forward.flags & FAR_F_REDIRECT_INFORMATION)
      s = format (s, "    FAR Redirect Info: %U\n",
                  upf_format_redirect_information,
                  &far->forward.redirect_information);
    if (far->forward.flags & FAR_F_OUTER_HEADER_CREATION)
      s = format (s, "    Outer Header Create: %U\n",
                  upf_format_outer_header_creation,
                  &far->forward.outer_header_creation);
    //}
    /* end modified by liuyu UPF_CMD_MOD 2022-08-22 */

    vec_foreach(dupl_far, far->duplicates)
    {
        s = format (s,
        "  Duplicate: %u\n"
        "    Destination Interface: %U\n", index, upf_format_destination_interface, &dupl_far->dst_intf);

        if (dupl_far->flags & FAR_D_OUTER_HEADER_CREATION)
        {
            s = format (s, "    Outer Header Creation: %U\n",
                upf_format_outer_header_creation,
                &dupl_far->outer_header_creation);
        }
    }
  }
  return s;
}

u8 *upf_format_pre_rules (u8 *s, va_list *args)
{
  predef_rule_t *rule;
  predef_appid_t *appid;
  u8 *rule_name = va_arg (*args, u8 *);
  /* begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
  s = format (s, "----------------------------------------------------\n"
                 "Pre Def Rule Info:\n");
  pool_foreach (
      rule, g_upf_main.pre_rule, ({
        if (rule_name && !vec_is_equal (rule->rule_id, rule_name))
          continue;
        int j = 0, i = 0;
        s = format (s,
                    "----------------------------------------------------\n");
        s = format (s, "name:%v\n", rule->rule_id);
        pool_foreach(appid, rule->app_ids, ({
            s = format (s, "  Rule APPid: %v\t", appid->app_id);
            s = format (s, "  Rule FARid: %u\t", appid->far_id);
            s = format (s, "  Rule URRids: [");
            vec_foreach_index (i, appid->urr_id)
            {
              if (vec_elt (appid->urr_id, i))
                s = format (s, "%s%u", i != 0 ? "," : "",
                            vec_elt (appid->urr_id, i));
            }
            s = format (s, "]");
            s = format (s, "  Rule QERids: [");
            vec_foreach_index (j, appid->qer_id)
            {
              if (vec_elt (appid->qer_id, j))
                s = format (s, "%s%u", j != 0 ? "," : "",
                            vec_elt (appid->qer_id, j));
            }
            s = format (s, "]\n");
            }));
      }));
      /* end modified by liuyu UPF_CMD_MOD 2022-08-22 */

  return s;
}

u8 *upf_format_pre_rule_groups (u8 *s, va_list *args)
{
  predef_rule_name_t *rule;
  predef_rule_group_t *rule_group;
  u32 flag = 0;
  u8 *rule_group_name = va_arg (*args, u8 *);
  s = format (s, "----------------------------------------------------\n"
                 "Pre_Defined Rule Group Info:\n");
  pool_foreach (
      rule_group, g_upf_main.pre_rule_group, ({
        if (rule_group_name && !vec_is_equal (rule_group->rule_group, rule_group_name))
          continue;
        flag = 0;
        s = format (s,
                    "----------------------------------------------------\n");
        s = format (s, "rule_group:%v\n", rule_group->rule_group);
        s = format (s, "  rule_name: [");
        pool_foreach(rule, rule_group->rule_names, ({
            if (flag == 0)
            {
                s = format (s, "%v", rule->rule_name);
                flag = 1;
            }
            else
            {
                s = format (s, ",%v", rule->rule_name);
            }
        }));
      s = format (s, "]\n");
      }));

  return s;
}

u8 *upf_format_pre_urr (u8 *s, va_list *args)
{
  upf_urr_t *urr;
  int i = 0;
  u32 *id = va_arg (*args, u32 *);
  /* begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
  s = format (s, "----------------------------------------------------\n"
                 "Pre Def urr Info:\n");

  vec_foreach (urr, g_upf_main.pre_urr)
  {

    if (id && (urr->id != *id))
      continue;
    s = format (s, "----------------------------------------------------\n");
    /* *INDENT-OFF* */
    s = format (s,
                "URR Info: %u\n"
                "  method: %04x == %U\n"
                "  trigger: %04x == %U\n",
                urr->id, urr->methods, format_flags, (u64)urr->methods,
                g_urr_method_flags, urr->triggers, format_flags,
                (u64)urr->triggers, g_urr_trigger_flags, urr->status,
                format_flags);

    if (urr->measurement_period.period != 0)
      {
        s = format (s, "  Measurement Period: %U\n", upf_format_urr_time,
                    &urr->measurement_period);
      }
    if (urr->methods & SX_URR_VOLUME)
      {
        urr_volume_t *v = &urr->volume;

        /* *INDENT-OFF* */
        s = format (s,
                    "  URR volume\n"
                    "    uplink:    %U\n           %U\n"
                    "    downlink:  %U\n           %U\n"
                    "    total: %U\n           %U\n",
                    upf_format_urr_counter, &v->measure, &v->threshold,
                    offsetof (urr_counter_t, ul), upf_format_urr_quota,
                    &v->measure, &v->quota, offsetof (urr_counter_t, ul),
                    upf_format_urr_counter, &v->measure, &v->threshold,
                    offsetof (urr_counter_t, dl), upf_format_urr_quota,
                    &v->measure, &v->quota, offsetof (urr_counter_t, dl),
                    upf_format_urr_counter, &v->measure, &v->threshold,
                    offsetof (urr_counter_t, total), upf_format_urr_quota,
                    &v->measure, &v->quota, offsetof (urr_counter_t, total));
        /* *INDENT-ON* */
      }

    if (urr->methods & SX_URR_TIME)
      {
        s = format (s, "  urr_time\n    urr_quota:     %U\n    urr_threshold: %U\n",
                    upf_format_urr_time_idt, &urr->time_quota, upf_format_urr_time_idt,
                    &urr->time_threshold);
      }

    /* Andy add */
    if (0 != vec_len (urr->linked_urr_ids))
      {
        s = format (s, " related urr Ids: [");
        vec_foreach_index (i, urr->linked_urr_ids)
        {
          if (0 != vec_elt (urr->linked_urr_ids, i))
            {
              s = format (s, "%s%u", i != 0 ? "," : "",
                          vec_elt (urr->linked_urr_ids, i));
            }
        }
        s = format (s, "]");
      }
    if (urr->monitoring_time.base)
      s = format (s, "  Monitoring Time: %U\n", upf_format_urr_time_abs,
                  &urr->monitoring_time);
  }
  /* end modified by liuyu UPF_CMD_MOD 2022-08-22 */
  return s;
}

u8 *upf_format_upf_pdi(u8 *s, va_list *args)
{
    upf_main_t *gtm = &g_upf_main;
    upf_pdi_t *v = va_arg (*args, upf_pdi_t *);

    s = format (s,"  PDI:\n    Fields: 0x%08x\n", v->fields);
    s = format (s, "    Source Interface: %U\n", upf_format_source_interface, &v->src_intf);

    if (v->fields & F_PDI_SRC_INTF_TYPE)
      s = format (s, "    Source Interface Type: %U\n", upf_format_tgpp_interface_type, &v->source_interface_type);

    if (v->fields & F_PDI_NWI)
    {
        if (SRC_INTF_5G_VN != v->src_intf)
        {
            if (!pool_is_free_index (gtm->nwis, v->nwi))
            {
              upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, v->nwi);
              s = format (s, "    Network Instance: %U\n", upf_format_network_instance, nwi ? nwi->name : NULL);
            }
        }
        else
        {
            upf_5glan_nwi_t *vn_nwi = upf_5glan_index_to_nwi (v->nwi);
            s = format (s, "    Network Instance: %U\n", upf_format_network_instance, vn_nwi ? vn_nwi->name : NULL);
        }
    }

    if (v->fields & F_PDI_LOCAL_F_TEID)
    {
        s = format (s, "    Choose Id:    %u (0x%08x)\n", v->teid.choose_id, v->teid.choose_id);
        s = format (s, "    Local F-TEID: %u (0x%08x)\n", v->teid.teid, v->teid.teid);
        if (v->teid.flags & F_TEID_V4)
            s = format (s, "            IPv4: %U\n", format_ip4_address, &v->teid.ip4);
        if (v->teid.flags & F_TEID_V6)
            s = format (s, "            IPv6: %U\n", format_ip6_address, &v->teid.ip6);
    }

    if (v->fields & F_PDI_UE_IP_ADDR)
    {
        s = format (s, "    UE IP address:\n");
        if (v->ue_addr.flags & IE_UE_IP_ADDRESS_V4)
            s = format (s, "      IPv4 address: %U\n", format_ip4_address, &v->ue_addr.ip4);
        if (v->ue_addr.flags & IE_UE_IP_ADDRESS_V6)
            s = format (s, "      IPv6 address: %U\n", format_ip6_address, &v->ue_addr.ip6);
        if (v->ue_addr.flags & IE_UE_IP_ADDRESS_IPv6D)
            s = format (s, "      Prefix Delegation Length: %d\n", v->ue_addr.prefix_delegation_length);
    }

    if (v->fields & F_PDI_APPLICATION_ID)
        s = format (s, "    APP ID: %v\n", v->app_name);
    if (v->fields & F_PDI_SDF_FILTER)
        s = format (s, "    SDF Filter:\n");

    acl_rule_t *acl = NULL;
    vec_foreach (acl, v->acl)
    {
        s = format (s, "      %U", upf_format_acl_rule, acl);
    }

    if (v->fields & F_PDI_QFI)
    {
        s = format (s, "    QFI: %u\n", v->qfi);
    }

    if (v->fields & F_PDI_FRAMED_ROUTE)
    {
        fib_prefix_t *framed_route;
        vec_foreach (framed_route, v->framed_route)
        {
            s = format (s, "    Framed Route: %U/%d\n", format_ip46_address,
                &framed_route->fp_addr, IP46_TYPE_IP4, framed_route->fp_len);
        }
    }

    if (v->fields & F_PDI_FRAMED_IPV6_ROUTE)
    {
        fib_prefix_t *framed_ipv6_route;
        vec_foreach (framed_ipv6_route, v->framed_ipv6_route)
        {
            s = format (s, "    Framed IPv6 Route: %U/%d\n", format_ip46_address,
                    &framed_ipv6_route->fp_addr, IP46_TYPE_IP6, framed_ipv6_route->fp_len);
        }
      }

    if (v->fields & F_PDI_IP_MULTICAST_ADDRESSING_INFO)
    {
        pfcp_ip_multicast_addressing_info_t *info;
        vec_foreach (info, v->ip_multicast_addressing_info)
        {
            s = format (s, "    Pdi IP Multicast Addressing Info:\n%U", upf_format_ip_multicast_addressing_info, info);
        }
    }

    if (v->fields & F_PDI_ETHERNET_PACKET_FILTER)
    {
        upf_ethernet_packet_filter_t *f;
        vec_foreach (f, v->eth_rule)
        {
            s = format (s, "    Ethernet packet filter:\n%U", upf_format_ethernet_packet_filter, f, 0);
        }
    }

    return s;
}

u8 *upf_format_upf_pdr(u8 *s, va_list *args)
{
    upf_pdr_t *v = va_arg (*args, upf_pdr_t *);

    s = format (s, "PDR: %u\n  Precedence: %u  active: %u\n", v->id, v->precedence, v->is_active);
    s = format (s, "%U", upf_format_upf_pdi, &v->pdi);

    if (v->activate_predefined_rules)
        s = format (s, "  Predefined Rule Name: %v\n", v->activate_predefined_rules);

    if (v->pkt_detection_carry_on_info.flag)
    {
        s = format (s, "  Pkt Detection Carry On Info: %u\n", v->pkt_detection_carry_on_info.flag);
    }

    pfcp_ip_multicast_addressing_info_t *mip_info = NULL;
    vec_foreach(mip_info, v->ip_multicast_addressing_info)
    {
        s = format (s, "  Pdr IP Multicast Aaddressing Info:\n%U", upf_format_ip_multicast_addressing_info, mip_info);
    }

    if (v->outer_header_removal < ARRAY_LEN (g_outer_header_removal_str))
        s = format (s, "  Outer Header Removal: %s\n", g_outer_header_removal_str[v->outer_header_removal]);

    s = format (s, "  FAR Id: %u\n  URR Ids: [", v->far_id);

    size_t j;
    vec_foreach_index (j, v->urr_ids)
        s = format (s, "%s%u", j != 0 ? "," : "", vec_elt (v->urr_ids, j));
    s = format (s, "]");
    s = format (s, "  QER Ids: [");
    vec_foreach_index (j, v->qer_ids)
        s = format (s, "%s%u", j != 0 ? "," : "", vec_elt (v->qer_ids, j));
    s = format (s, "]\n");

    return s;
}

u8 *upf_format_upf_far(u8 *s, va_list *args)
{
    upf_far_t *far = va_arg (*args, upf_far_t *);
    u8 verbose = va_arg (*args, int);
    upf_nwi_t *nwi = NULL;
    upf_main_t *gtm = &g_upf_main;

    if (!pool_is_free_index (gtm->nwis, far->forward.nwi))
      nwi = pool_elt_at_index (gtm->nwis, far->forward.nwi);

    s = format (s,
                "FAR: %u\n"
                "  Apply Action: %08x == %U\n",
                far->id, far->apply_action, format_flags, far->apply_action,
                g_apply_action_flags);

    s = format (s,
                "  Forward:\n"
                "    Destination Interface: %U\n", upf_format_destination_interface, &far->forward.dst_intf);
    if (far->forward.flags & FAR_F_DEST_INTF_TYPE)
      s = format (s, "    Destination Interface Type: %U\n", upf_format_tgpp_interface_type, &far->forward.dest_interface_type);

    if (far->forward.flags & FAR_F_FORWARDING_POLICY)
      {
        upf_forward_policy_t *forward_policy = NULL;
        if (!pool_is_free_index (gtm->forward_policys,
                                 far->forward.forward_policy_index))
          forward_policy = pool_elt_at_index (
              gtm->forward_policys, far->forward.forward_policy_index);
        s = format (s, "    Forwarding Policy: %s\n",
                    forward_policy ? forward_policy->name : NULL);
      }
    else if (far->forward.flags & FAR_F_NETWORK_INSTANCE)
      {
        if (far->forward.dst_intf == DST_INTF_5G_VN)
        {
            upf_5glan_nwi_t *nwi_inst = upf_5glan_index_to_nwi(far->forward.nwi);
            s = format (s, "    Network Instance: %U\n", upf_format_network_instance,
                        nwi_inst ? nwi_inst->name : NULL);
        }
        else
        {
            s = format (s, "    Network Instance: %U\n", upf_format_network_instance,
                        nwi ? nwi->name : NULL);
        }
        if (verbose)
          s = format (s, "    Forward Fib Index: %u\n", far->forward.fib_index);
      }

    if (far->forward.flags & FAR_F_REDIRECT_INFORMATION)
      s = format (s, "    Redirect Information: %U\n",
                  upf_format_redirect_information,
                  &far->forward.redirect_information);
    if (far->forward.flags & FAR_F_OUTER_HEADER_CREATION)
      s = format (s, "    Outer Header Creation: %U\n",
                  upf_format_outer_header_creation,
                  &far->forward.outer_header_creation);
    if (far->forward.flags & FAR_F_TRANSPORT_LEVEL_MARKING)
      s = format (s, "    Transport Level Marking: %U\n",
                  upf_format_transport_level_marking,
                  &far->forward.transport_level_marking);
    if (far->forward.flags & FAR_F_HEADER_ENRICHMENT)
      {
        pfcp_header_enrichment_t *header_enrichment;

        s = format (s, "    Header Enrichment:\n");
        vec_foreach (header_enrichment, far->forward.header_enrichment)
        {
          s = format (s, "      %s:%s\n", header_enrichment->name,
                      header_enrichment->value);
        }
      }

    if (far->forward.flags & FAR_F_PROXYING)
      {
        s = format (s, "    proxying: %U\n",upf_format_proxying,&far->forward.proxying);
      }

    if (far->apply_action & F_APPLY_BUFF)
      {
        if (far->bar_id != ~0)
          {
            s = format (s, "  BAR Id: %u\n", far->bar_id);
          }
        s = format (s, "  UL Buf: %u, DL Buf: %u\n", far->uplink_buf_n,
                    far->downlink_buf_n);
      }

    if (far->apply_action & F_APPLY_DUPL)
    {
        upf_far_duplicate_t *dupl_far = NULL;
        u8 index = 1;
        vec_foreach(dupl_far, far->duplicates)
        {
            s = format (s,
            "  Duplicate: %u\n"
            "    Destination Interface: %U\n", index, upf_format_destination_interface, &dupl_far->dst_intf);

            if (dupl_far->flags & FAR_D_OUTER_HEADER_CREATION)
            {
                s = format (s, "    Outer Header Creation: %U\n",
                    upf_format_outer_header_creation,
                    &dupl_far->outer_header_creation);
            }

            index++;
        }
    }
    return s;
}

char *sx_tunnel_str[SESSION_TUNNEL_NUM] =
{
    "none",
    "gtp",
    "gre",
    "vxlan",
    "ipsec",
    "l2tp",
};

u8 *upf_format_session_tunnel_pair (u8 *s, va_list *args)
{
    upf_session_tunnel_pair_t *v = va_arg (*args, upf_session_tunnel_pair_t *);

    s = format (s, "  Session-Tunnel-Pair: %s  instance:%u\n",
            v->type < SESSION_TUNNEL_NUM ? sx_tunnel_str[v->type] : "unknow", v->instance_id);
    return s;
}

u8 *upf_format_upf_urr(u8 *s, va_list *args)
{
  upf_urr_t *urr = va_arg (*args, upf_urr_t *);
  /* *INDENT-OFF* */
  s = format (s,
              "URR: %u\n"
              "  Measurement Method: %04x == %U\n"
              "  Reporting Triggers: %04x == %U\n"
              "  Status: %04x == %U\n",
              urr->id, urr->methods, format_flags, (u64)urr->methods,
              g_urr_method_flags, urr->triggers, format_flags,
              (u64)urr->triggers, g_urr_trigger_flags, urr->status,
              format_flags, (u64)urr->status, g_urr_status_flags);
  /* *INDENT-ON* */
  s = format (s, "  Start Time: %U\n", format_time_float, 0, urr->start_time);
  if (urr->methods & SX_URR_VOLUME)
    {
      urr_volume_t *v = &urr->volume;
      /* *INDENT-OFF* */
      s = format (s, "  Volume\n");
      if ((v->threshold.fields & PFCP_VOLUME_ULVOL) || (v->quota.fields & PFCP_VOLUME_ULVOL))
          s = format (s, "    Up:    %U\n           %U\n",
                  upf_format_urr_counter, &v->measure, &v->threshold, offsetof (urr_counter_t, ul),
                  upf_format_urr_quota, &v->measure, &v->quota, offsetof (urr_counter_t, ul));

      if ((v->threshold.fields & PFCP_VOLUME_DLVOL) || (v->quota.fields & PFCP_VOLUME_DLVOL))
          s = format (s, "    Down:  %U\n           %U\n",
                  upf_format_urr_counter, &v->measure, &v->threshold, offsetof (urr_counter_t, dl),
                  upf_format_urr_quota, &v->measure, &v->quota, offsetof (urr_counter_t, dl));

      if ((v->threshold.fields & PFCP_VOLUME_TOVOL) || (v->quota.fields & PFCP_VOLUME_TOVOL))
          s = format (s, "    Total: %U\n           %U\n",
                  upf_format_urr_counter, &v->measure, &v->threshold, offsetof (urr_counter_t, total),
                  upf_format_urr_quota, &v->measure, &v->quota, offsetof (urr_counter_t, total));
      /* *INDENT-ON* */
    }
  if (urr->measurement_period.base != 0)
    {
      s = format (s, "  Measurement Period: %U\n", upf_format_urr_time, &urr->measurement_period);
    }

  if (urr->methods & SX_URR_TIME)
    {
      s = format (s, "  Time\n    Quota:     %U\n    Threshold: %U\n",
                  upf_format_urr_time_idt, &urr->time_quota, upf_format_urr_time_idt, &urr->time_threshold);
    }
  if (urr->far_id)
    {
      s = format (s, "  far_id_for_quota_action: %d\n", urr->far_id);
    }
  if (urr->monitoring_time.base != 0)
    {
      s = format (s, "  Monitoring Time: %U\n", upf_format_urr_time_abs, &urr->monitoring_time);
      if (urr->status & URR_AFTER_MONITORING_TIME)
        {
          s = format (s, "  Usage Before Monitoring Time\n");
          if (urr->methods & SX_URR_VOLUME)
            {
              urr_measure_t *v = &urr->usage_before_monitoring_time.volume;
              s = format (s,
                          "    Volume\n"
                          "      Up:    %20" PRIu64 ", Pkts: %10" PRIu64 "\n"
                          "      Down:  %20" PRIu64 ", Pkts: %10" PRIu64 "\n"
                          "      Total: %20" PRIu64 ", Pkts: %10" PRIu64
                          "\n",
                          v->bytes.ul, v->packets.ul, v->bytes.dl,
                          v->packets.dl, v->bytes.total, v->packets.total);
            }
          if (urr->methods & SX_URR_TIME)
            {
              s = format (s, "    Start Time %U, End Time %U, %9.3f secs\n",
                          format_time_float, 0,
                          urr->usage_before_monitoring_time.start_time,
                          format_time_float, 0, urr->start_time,
                          urr->start_time -
                              urr->usage_before_monitoring_time.start_time);
            }
        }
    }

  /* Andy added: */
  if (urr->linked_urr_ids)
    {
      size_t index;
      s = format (s, "   Linked URR Ids: [");
      vec_foreach_index (index, urr->linked_urr_ids)
      {
        s = format (s, "%s%u", index != 0 ? "," : "", vec_elt (urr->linked_urr_ids, index));
      }
      s = format (s, "]\n");
    }

  if (urr->measurement_information)
    {
      s = format (s, "  Measurement Information: %u\n", urr->measurement_information);
      s = format (s, "     MBQE:%s, INAM:%s, RADI:%s, ISTM:%s, MNOP:%s\n",
          (urr->measurement_information & MEASUREMENT_INFORMATION_MBQE) ? "true":"false",
          (urr->measurement_information & MEASUREMENT_INFORMATION_INAM) ? "true":"false",
          (urr->measurement_information & MEASUREMENT_INFORMATION_RADI) ? "true":"false",
          (urr->measurement_information & MEASUREMENT_INFORMATION_ISTM) ? "true":"false",
          (urr->measurement_information & MEASUREMENT_INFORMATION_MNOP) ? "true":"false");
    }

  if (urr->traf_inact_detect_timer.base != 0)
    {
      upf_urr_traf_inactive_detect_t *t = &urr->traf_inact_detect;
      s = format (s, "  %U, has_reported:%u, is_active:%u\n", upf_format_inspur_traf_inact_detect_type, &t->type, t->has_reported, t->is_active);
      s = format (s, "%U\n", upf_format_urr_time, &urr->traf_inact_detect_timer);
    }

  return s;
}

u8 *upf_format_upf_qer(u8 *s, va_list *args)
{
  upf_qer_t *qer = va_arg (*args, upf_qer_t *);
  /* *INDENT-OFF* */
  s = format (s,
              "QER: %u\n"
              "  QFI: %u\n"
              "  RQI: %u\n"
              "  UL Gate: %d == %U\n"
              "  DL Gate: %d == %U\n"
              "  UL MBR: %ukbps\n"
              "  DL MBR: %ukbps\n"
              "  UL policer MBR: %ukbps\n"
              "  DL policer MBR: %ukbps\n"
              "  UL GBR: %ukbps\n"
              "  DL GBR: %ukbps\n"
              "  Averaging Window: %u ms\n"
              "  UL PKT Rate: unit: %u max: %u  a_unit: %u a_max: %u\n"
              "  DL PKT Rate: unit: %u max: %u  a_unit: %u a_max: %u\n",
              qer->id, qer->qos_flow_identifier, qer->reflective_qos,
              qer->gate_status[UPF_UL], format_flags,
              (u64)qer->gate_status[UPF_UL] + 1, g_qer_gate_status_flags,
              qer->gate_status[UPF_DL], format_flags,
              (u64)qer->gate_status[UPF_DL] + 1, g_qer_gate_status_flags,
              qer->mbr.ul, qer->mbr.dl, qer->pol_mbr[UPF_UL] - g_upf_qer_mbr_offset,
              qer->pol_mbr[UPF_DL] - g_upf_qer_mbr_offset, qer->gbr.ul, qer->gbr.dl,
              qer->averaging_window ? qer->averaging_window : 1000,
              qer->packet_rate.ul.unit, qer->packet_rate.ul.max, qer->packet_rate.a_ul.unit,
              qer->packet_rate.a_ul.max, qer->packet_rate.dl.unit, qer->packet_rate.dl.max,
              qer->packet_rate.a_dl.unit, qer->packet_rate.a_dl.max);
  /* *INDENT-ON* */

  return s;
}

u8 *upf_format_upf_bar(u8 *s, va_list *args)
{
  upf_bar_t *bar = va_arg (*args, upf_bar_t *);
  /* *INDENT-OFF* */
  s = format (s,
              "BAR: %u\n"
              "  Downlink Data Notification Delay: %u ms\n"
              "  Downlink Buffering Duration: %u seconds\n"
              "  Downlink Buffering Suggested Packet Count: %u\n"
              "  Suggested Buffering Packets Count: %u\n",
              bar->id, bar->downlink_data_notification_delay * 50,
              bar->dl_buffering_duration,
              bar->dl_buffering_suggested_packet_count,
              bar->suggested_buffering_packets_count);
  /* *INDENT-ON* */
  return s;
}

u8 *upf_format_per_qos_flow_ctrl (u8 *s, va_list *args)
{
    upf_per_qos_flow_control_t *v = va_arg (*args, upf_per_qos_flow_control_t *);
    u32 fields = v->fields;

    s = format (s, "    QFI : ");

    upf_per_qos_monitor_flow_control_t *flow_ctrl;
    vec_foreach (flow_ctrl, v->flow_ctrl)
    {
      s = format (s, "%u, ", flow_ctrl->qfi); //upf_format_qfi
    }

    s = format (s, "      Requested Qos monitoring: %U\n", upf_format_requested_qos_monitoring, &v->requested_qos_monitor);
    s = format (s, "      Reporting frequency     : %U\n", upf_format_reporting_frequency, &v->reporting_frequency);

    if (ISSET_BIT(fields, QOS_MONITORING_PER_QOS_FLOW_CONTROL_INFO_PKT_DELAY_THRESHOLD))
        s = format (s, "      Packet Delay Threshold  : %U\n", upf_format_packet_delay_thresholds, &v->packet_delay_threshold);

    if (ISSET_BIT(fields, QOS_MONITORING_PER_QOS_FLOW_CONTROL_INFO_MIN_WAIT_TIME))
        s = format (s, "      Minimum Wait Time       : %U\n", upf_format_minimum_wait_time, &v->minimum_wait_time);

    if (ISSET_BIT(fields, QOS_MONITORING_PER_QOS_FLOW_CONTROL_INFO_MEASURE_PERIOD))
        s = format (s, "      Measurement period      : %u\n", v->measurement_period); /* upf_format_measurement_period */

    return s;
}

u8 *upf_format_upf_srr(u8 *s, va_list *args)
{
    upf_srr_t *v = va_arg (*args, upf_srr_t *);
    s = format (s, "SRR: %u\n", v->id);

    if (v->per_qos_flow_ctrl)
    {
        s = format (s, "  Qos monitoring per qos flow control Info: \n");
        upf_per_qos_flow_control_t *q;
        vec_foreach (q, v->per_qos_flow_ctrl)
        {
            s = format (s, "%U", upf_format_per_qos_flow_ctrl, q);
        }
    }
    return s;
}

u8 *format_vn_multicast_group_addr(u8 *s, va_list *args)
{
    ip46_address_t *v = va_arg (*args, ip46_address_t *);
    ip46_address_t *m;

    vec_foreach (m, v)
    {
        if (ip46_address_is_ip4(m))
        {
            if (!VN_SWITCH(VN_SWITCH_IGMP_SNOOPING))
                continue;
        }
        else
        {
            if (!VN_SWITCH(VN_SWITCH_MLD_SNOOPING))
                continue;
        }

        s = format (s, "  %U\n", format_ip46_address, m, IP46_TYPE_ANY);
    }
    return s;
}

u8 *upf_format_sx_session (u8 *s, va_list *args)
{
  upf_session_t *sx = va_arg (*args, upf_session_t *);
  int rule = va_arg (*args, int);
  u8 verbose = va_arg (*args, int);
  struct rules *rules = upf_get_rules (sx, rule);
  upf_main_t *gtm = &g_upf_main;

  s = format (s,"%U", upf_format_simple_sx_session, sx, SX_ACTIVE);

  s = format (s, "  Sx Association: %u (prev:%u,cur:%u,next:%u)\n", sx->assoc.node,
              sx->assoc.prev, sx - gtm->sessions, sx->assoc.next);
  s = format (s, "  Sx data active: %u , detective priod: %u s\n",
              sx->up_inactive_timer.status, sx->up_inactive_timer.period);

  if (verbose && (sx->flags & SX_USER_ID))
    s = format (s, "  %U\n", upf_format_user_id, &sx->user_id);
  if (vec_len (sx->dnn))
    s = format (s, "  DNN: %v\n", sx->dnn);
  if (sx->pdn_type < PDN_TYPE_NON_IP)
    s = format (s, "  PDN Type: %U\n", upf_format_pdn_type, &sx->pdn_type);

  if(sx->rat_type == RAT_TYPE_UTRAN)
    s = format (s, "RAT Type : %s\n", "UTRAN");

  if(sx->rat_type == RAT_TYPE_LTE_NR)
    s = format (s, "RAT Type : %s\n", "NR");

  if (sx->s_nssai)
      s = format (s, "  S-NSSAI: %U\n", upf_format_s_nssai, &sx->s_nssai);
  if (sx->flags & SX_TUNNEL_ID)
    s = format (s, "%U", upf_format_session_tunnel_pair, &sx->tunnel);

  upf_pdr_t *pdr;
  vec_foreach (pdr, rules->pdr)
  {
      s = format (s, "%U", upf_format_upf_pdr, pdr);
      if (verbose)
      {
          s = format(s, "%U", format_vn_sx_params, rules);
      }
  }

  upf_far_t *far;
  vec_foreach (far, rules->far)
  {
    s = format (s, "%U", upf_format_upf_far, far, verbose);
  }

  upf_urr_t *urr;
  vec_foreach (urr, rules->urr)
  {
    s = format (s, "%U", upf_format_upf_urr, urr);
  }

  upf_qer_t *qer;
  vec_foreach (qer, rules->qer)
  {
    s = format (s, "%U", upf_format_upf_qer, qer);
  }

  upf_bar_t *bar;
  vec_foreach (bar, rules->bar)
  {
    s = format (s, "%U", upf_format_upf_bar, bar);
  }

  upf_srr_t *srr;
  vec_foreach (srr, rules->srr)
  {
    s = format (s, "%U", upf_format_upf_srr, srr);
  }

  if(sx->flags & SX_L2TP_TUNNEL_INFO)
  {
    pfcp_l2tp_tunnel_information_t *l2tp_tunnel_info = &sx->upf_l2tp_tunnel_information;
    s = format (s,
                "l2tp tunnel info:\n"
                "  lns address: %U\n"
                "  password   : %U\n"
                "  preference : %U\n",
                upf_format_l2tp_lns_address, &l2tp_tunnel_info->lns_address,
                upf_format_l2tp_tunnel_password, &l2tp_tunnel_info->tunnel_password,
                upf_format_l2tp_tunnel_preference, &l2tp_tunnel_info->tunnel_preference
    );
  }

  if(sx->flags & SX_L2TP_SESSION_INFO)
  {
    pfcp_l2tp_session_information_t *l2tp_session_info = &sx->upf_l2tp_session_information;
    s = format (s,
                "l2tp session info:\n"
                "  calling number: %U\n"
                "  called number : %U\n"
                "  session indications : %U\n"
                "  user authentication : \n%U",
                upf_format_l2tp_calling_number, &l2tp_session_info->calling_number,
                upf_format_l2tp_called_number, &l2tp_session_info->called_number,
                upf_format_l2tp_session_indications, &l2tp_session_info->l2tp_session_indications,
                upf_format_l2tp_user_authentication, &l2tp_session_info->l2tp_user_authentication
    );
  }

  if (sx->multicast_group_addr)
    s = format (s, "Snooping Multicast Group Address: \n%U", format_vn_multicast_group_addr, sx->multicast_group_addr);

  s = format (s,
              "OTHER: g_single_trace_flag:%u, list=>[%u][%u][%u][%u][%u][%u][%u][%u]\n",
              sx->single_trace_flag, sx->single_trace_list[0], sx->single_trace_list[1],
              sx->single_trace_list[2], sx->single_trace_list[3], sx->single_trace_list[4],
              sx->single_trace_list[5], sx->single_trace_list[6], sx->single_trace_list[7]);
  return s;
}

u8 *upf_format_sx_node_association (u8 *s, va_list *args)
{
  upf_node_assoc_t *node = va_arg (*args, upf_node_assoc_t *);
  u8 verbose = va_arg (*args, int);
  upf_main_t *gtm = &g_upf_main;
  u32 idx = node->sessions;
  u32 i = 0;
  pfcp_user_plane_ip_resource_information_t *tmp_ip_resource_information;
  /* begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
  s = format (s,
              "Node: %U\n"
              "Node idx: %u\n"
              "  CP Recover Time-stamp: %U\n"
              "  UP Recover Time-stamp: %U\n"
              "  CP supporting feature: %U\n"
              "  Sess: ",
              upf_format_node_id, &node->node_id, node - gtm->nodes,
              upf_format_time_stamp, &node->recovery_time_stamp,
              upf_format_time_stamp, &node->up_recovery_time_stamp,
              upf_format_cp_function_features, &node->cp_feature);
  /* end modified by liuyu UPF_CMD_MOD 2022-08-22 */
  while (idx != ~0)
    {
      CHECK_POOL_IS_VALID_CONTINUE(gtm->sessions, idx);
      upf_session_t *sx = pool_elt_at_index (gtm->sessions, idx);

      if (verbose)
        {
          if (i > 0 && (i % 8) == 0)
            s = format (s, "\n            ");

          s = format (s, " 0x%016" PRIx64, sx->cp_seid);
        }

      i++;
      idx = sx->assoc.next;
    }
  /* begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
  if (verbose)
    s = format (s, "\n  %u Session(s) info\n", i);
  else
    s = format (s, "%u\n", i);

  s = format (s, "\n  User-plane IPaddress Res Info:\n");
  vec_foreach (tmp_ip_resource_information,
               node->user_plane_ip_resource_information)
  {
    if (tmp_ip_resource_information->flags &
        USER_PLANE_IP_RESOURCE_INFORMATION_V4)
      s = format (s, "    IPv4 addr: %U", format_ip4_address,
                  &tmp_ip_resource_information->ip4);
    if (tmp_ip_resource_information->flags &
        USER_PLANE_IP_RESOURCE_INFORMATION_V6)
      s = format (s, "    IPv6 addr: %U", format_ip6_address,
                  &tmp_ip_resource_information->ip6);
    if (tmp_ip_resource_information->flags &
        USER_PLANE_IP_RESOURCE_INFORMATION_ASSONI)
      s = format (s, "    networkInstance: %U", upf_format_network_instance,
                  tmp_ip_resource_information->network_instance);

    s = format (s, "    TEID-range-indication: %u TEID-range: %u\n",
                tmp_ip_resource_information->teid_range_indication,
                tmp_ip_resource_information->teid_range);
    // if (tmp_ip_resource_information->flags &
    // USER_PLANE_IP_RESOURCE_INFORMATION_ASSONI) s = format (s, "    Network
    // Instance: %U ", format_network_instance,
    // tmp_ip_resource_information->network_instance);
    /* end modified by liuyu UPF_CMD_MOD 2022-08-22 */
  }

  return s;
}

u8 *upf_format_pfcp_endpoint (u8 *s, va_list *args)
{
  upf_pfcp_endpoint_t *ep = va_arg (*args, upf_pfcp_endpoint_t *);

  s = format (s, "%U [@%u]", format_ip46_address, &ep->key.addr, IP46_TYPE_ANY,
              ep->key.fib_index);

  return s;
}

/* *INDENT-OFF* */
upf_pfcp_rule_vector_fns (far, ({ upf_pfcp_clean_far_buffering_list (sx, p); }), ({}))
    upf_pfcp_rule_vector_fns (urr, ({vec_free (p->linked_urr_ids); clib_spinlock_free (&p->lock); }),
                                   ({clib_spinlock_init (&urr->lock); urr->start_time = unix_time_now (); }))
    upf_pfcp_rule_vector_fns (qer, ({ clib_spinlock_free (&p->lock); }), ({ clib_spinlock_init (&qer->lock); }))
    upf_pfcp_rule_vector_fns (bar, ({}), ({}))
    upf_pfcp_rule_vector_fns (srr, ({}), ({}))
    upf_pfcp_rule_vector_fns (sdf, ({}), ({}))
    upf_pfcp_rule_vector_fns (eth, ({}), ({}))
/* *INDENT-ON* */

//begin liukang add for l2tp switch 2021/06/18

extern upf_l2tp_para upf_l2tp;

uword
unformat_upf_l2tp_flags (unformat_input_t * input, va_list * args)
{
  u32 *result = va_arg (*args, u32 *);
  u32 flags = 0;

  if (unformat (input, "enable"))
    flags = 1;
  else if (unformat (input, "disable"))
    flags = 0;
  else
    return 0;

  *result = flags;
  return 1;
}

static clib_error_t *
upf_set_upf_l2tp (vlib_main_t * vm,
       unformat_input_t * input, vlib_cli_command_t * cmd)
{
  clib_error_t *error;
  u32 flags;

  if (!unformat (input, "%U", unformat_upf_l2tp_flags, &flags))
    {
      error = clib_error_return (0, "unknown flags `%U'",
                 format_unformat_error, input);
      goto done;
    }

    g_upf_l2tp_switch = flags;

  printf("g_upf_l2tp_switch = %d\n",g_upf_l2tp_switch);
  return (NULL);
done:
  return error;
}

VLIB_CLI_COMMAND (upf_l2tp_command, static) = {
  .path = "upf l2tp",
  .short_help = "upf l2tp [enable|disable]",
  .function = upf_set_upf_l2tp,
};

uword
unformat_upf_l2tp_authen_type (unformat_input_t * input, va_list * args)
{
  u32 *result = va_arg (*args, u32 *);
  u32 flags = 0;

  if (unformat (input, "chap"))
    flags = 0;
  else if (unformat (input, "pap"))
    flags = 1;
  else
    return 1;

  *result = flags;
  return 1;
}

static clib_error_t *
upf_set_upf_l2tp_authen_type (vlib_main_t * vm,
       unformat_input_t * input, vlib_cli_command_t * cmd)
{
  clib_error_t *error;
  u32 flags;

  if (!unformat (input, "%U", unformat_upf_l2tp_authen_type, &flags))
    {
      error = clib_error_return (0, "unknown flags `%U'",
                 format_unformat_error, input);
      goto done;
    }

    g_upf_l2tp_authen_type = flags;

  printf("g_upf_l2tp_authen_type = %d\n",g_upf_l2tp_authen_type);
  return (NULL);
done:
  return error;
}

VLIB_CLI_COMMAND (upf_l2tp_authen_type_command, static) = {
  .path = "upf l2tp authen type",
  .short_help = "upf l2tp authen type [chap|pap]",
  .function = upf_set_upf_l2tp_authen_type,
};

static clib_error_t *
upf_show_upf_l2tp_authen_type (vlib_main_t * vm,
       unformat_input_t * input, vlib_cli_command_t * cmd)
{
  //clib_error_t *error;

  if(g_upf_l2tp_authen_type == 0)
  {
     vlib_cli_output (vm, "l2tp authen type chap");
  }
  else
  {
     vlib_cli_output (vm, "l2tp authen type pap");
  }
  return (NULL);

}


VLIB_CLI_COMMAND (show_upf_l2tp_authen_type_command, static) = {
  .path = "show upf l2tp authen type",
  .short_help = "show upf l2tp authen type",
  .function = upf_show_upf_l2tp_authen_type,
};


uword
unformat_upf_l2tp_challenge_flags (unformat_input_t * input, va_list * args)
{
  u32 *result = va_arg (*args, u32 *);
  u32 flags = 0;

  if (unformat (input, "enable"))
    flags = 1;
  else if (unformat (input, "disable"))
    flags = 0;
  else
    return 0;

  *result = flags;
  return 1;
}

static clib_error_t *
upf_set_upf_l2tp_challenge (vlib_main_t * vm,
       unformat_input_t * input, vlib_cli_command_t * cmd)
{
  clib_error_t *error;
  u32 flags;

  if (!unformat (input, "%U", unformat_upf_l2tp_challenge_flags, &flags))
    {
      error = clib_error_return (0, "unknown flags `%U'",
                 format_unformat_error, input);
      goto done;
    }

    upf_l2tp.is_challenge = flags;

  printf("is_challenge = %d\n", upf_l2tp.is_challenge);
  return (NULL);
done:
  return error;
}

VLIB_CLI_COMMAND (upf_l2tp_challenge_command, static) = {
  .path = "upf l2tp challenge",
  .short_help = "upf l2tp challenge [enable|disable]",
  .function = upf_set_upf_l2tp_challenge,
};


static clib_error_t *
upf_set_upf_l2tp_hostname (vlib_main_t * vm,
       unformat_input_t * input, vlib_cli_command_t * cmd)
{
    unformat_input_t _line_input, *line_input = &_line_input;
    clib_error_t *error = 0;

    u8 *hostName = NULL;
    u8 hostName_flag = 0;

   /* Get a line of input. */
    if (unformat_user (input, unformat_line_input, line_input))
    {
        while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
        {
            if (unformat (line_input, "%s", &hostName))
            {
                hostName_flag = 1;
            }
            else
            {
              error = unformat_parse_error (line_input);
              goto done;
            }
        }
    }
    else
    {
        return clib_error_return (0, "[err] need input paramter");
    }

    if (hostName_flag != 1)
    {
        error = clib_error_return (0, "[err] need hostName");
        goto done;
    }

    upf_l2tp.have_hostname = 1;
    strcpy(upf_l2tp.host_name,(char *)hostName);

    return (NULL);
done:
  return error;
}

VLIB_CLI_COMMAND (upf_l2tp_hostname_command, static) = {
  .path = "upf l2tp hostname",
  .short_help = "upf l2tp hostname <hostname>",
  .function = upf_set_upf_l2tp_hostname,
};

static clib_error_t *
upf_show_upf_l2tp_info (vlib_main_t * vm,
       unformat_input_t * input, vlib_cli_command_t * cmd)
{
  //clib_error_t *error;

  if(upf_l2tp.is_challenge)
  {
      vlib_cli_output (vm, "challenge: enable\n");
  }
  else
  {
      vlib_cli_output (vm, "challenge: disable\n");
  }

  if(upf_l2tp.have_hostname)
  {
      vlib_cli_output (vm, "hostname: %s\n",upf_l2tp.host_name);
  }
  else
  {
      vlib_cli_output (vm, "not set hostname\n");
  }
  return (NULL);

}


VLIB_CLI_COMMAND (show_upf_l2tp_info_command, static) = {
  .path = "show upf l2tp info",
  .short_help = "show upf l2tp info",
  .function = upf_show_upf_l2tp_info,
};


//end liukang add for l2tp switch 2021/06/18

static int ipsx_info_id_compare (const void *p1, const void *p2)
{
    const sx_info_t *a = (sx_info_t *)p1;
    const sx_info_t *b = (sx_info_t *)p2;

    /* compare rule_ids */
    return -intcmp (a->sx_index, b->sx_index);
}

static int ipsx_make_pending_rules (upf_vnip_sessions_t *sx)
{
    struct rules_vnip *pending = vnip_get_rules (sx, VN_PENDING);
    struct rules_vnip *active = vnip_get_rules (sx, VN_ACTIVE);

    if (pending->sx_infos)
        return 0;

    if (active->sx_infos)
    {
        pending->sx_infos = vec_dup (active->sx_infos);
    }
  return 0;
}

sx_info_t *ipupf_get_rules_by_id (struct rules_vnip *rules, u32 sx_index)
{
    sx_info_t r = {.sx_index = sx_index};
    return vec_bsearch (&r, rules->sx_infos, ipsx_info_id_compare);
}

sx_info_t *ipupf_get_rules (upf_vnip_sessions_t *sx, int rule, u32 sx_index)
{
    struct rules_vnip *rules = vnip_get_rules (sx, rule);
    sx_info_t r = {.sx_index = sx_index};

    if (rule == VN_PENDING)
      if (ipsx_make_pending_rules (sx) != 0)
        return NULL;
    return vec_bsearch (&r, rules->sx_infos, ipsx_info_id_compare);
}

int ipsx_create_rules (upf_vnip_sessions_t *sx, sx_info_t *t)
{
    struct rules_vnip *rules = vnip_get_rules (sx, VN_PENDING);

    if (ipsx_make_pending_rules (sx) != 0)
        return -1;

    if (!t)
        return -1;

    vec_add1 (rules->sx_infos, *t);
    vec_sort_with_function (rules->sx_infos, ipsx_info_id_compare);
#if 0 /* test */
    upf_trace ("t->ip_pdrs_index:%u  t->sx_index:%u", t->ip_pdrs_index, t->sx_index);
    sx_info_t *k;
    vec_foreach(k, rules->sx_infos)
        upf_trace ("%u: k->ip_pdrs_index:%u  k->sx_index:%u", k - rules->sx_infos, k->ip_pdrs_index, k->sx_index);
#endif
    return 0;
}

int ipsx_delete_rules (upf_vnip_sessions_t *sx, u32 sx_index)
{
    struct rules_vnip *rules;
    sx_info_t r = {.sx_index = sx_index};
    sx_info_t *p;

    if (ipsx_make_pending_rules (sx) != 0)
      return -1;

    rules = vnip_get_rules (sx, VN_PENDING);
    if (!(p = vec_bsearch (&r, rules->sx_infos, ipsx_info_id_compare)))
      return -1;

    vec_delete (rules->sx_infos, 1, p - rules->sx_infos);
    return 0;
}

/* neil.fan@20220303 add 5 APIs for ip-sessions to support active/pending begin */
upf_vnip_sessions_t *ip_sessions_create(upf_5glan_ip_pdrs_t *ip_pdr, ip_sx_kv_t *kv)
{
    upf_main_t *gtm = &g_upf_main;

    u32 ip_sx_index = upf_ip_session_instance_alloc();

    CHECK_POOL_IS_VALID_RET(gtm->vnip_sessions, ip_sx_index, NULL);
    upf_vnip_sessions_t *p = pool_elt_at_index (gtm->vnip_sessions, ip_sx_index);
    clib_memset (p, 0, sizeof (*p));

    clib_memcpy(&p->vrf, &ip_pdr->vrf, sizeof(ip46_address_fib_t));
    clib_spinlock_init (&p->lock);
    p->table_id = ip_pdr->table_id;

    kv->v.ip_sxs_index = p - gtm->vnip_sessions;
    kv->v.unused = 0;

    clib_bihash_add_del_24_8 (&gtm->vnip_sxs_by_ip, &kv->kv, 1 /* add */);

    upf_debug ("ip-sessions create ok, %U/%u table_id:%u self-index:%u ",
        format_ip46_address, &kv->k.dst, IP46_TYPE_ANY, kv->k.mask, kv->k.table_id, p - gtm->vnip_sessions);

    return p;
}

void ip_sessions_free (upf_vnip_sessions_t *p, int rule)
{
    upf_main_t *gtm = &g_upf_main;

    if (p->flags & VN_DELETING)
    {
        upf_debug ("ip-sessions delete ok, %U/%u table_id:%u self-index:%u ",
            format_ip46_address, &p->vrf.addr, IP46_TYPE_ANY, p->vrf.prefix, p->table_id, p - gtm->vnip_sessions);
        upf_ip_session_instance_free(p - gtm->vnip_sessions);
        return;
    }

    /* free pending resource */
    struct rules_vnip *rules = vnip_get_rules (p, rule);
    if (rules->sx_infos)
    {
        vec_free (rules->sx_infos);
        memset (rules, 0, sizeof (*rules));
    }
}

u32 ip_sessions_append_usr(upf_vnip_sessions_t *p, upf_5glan_ip_pdrs_t *ip_pdr)
{
    sx_info_t *v_sx = &ip_pdr->sx_info;

    if (p->flags & VN_UPDATING)
    {
        upf_err("ip-session is updating, sx_index:%u", v_sx->sx_index);
        return ~0;
    }
    p->flags |= VN_UPDATING;

    /* refer session info to the ip(key) */
    int r = ipsx_create_rules(p, v_sx);
    if (r)
    {
        upf_err("Failed to add sx_info, %u", v_sx->sx_index);
        return ~0;
    }

    clib_atomic_fetch_xor (&p->active, VN_PENDING);

    return 0;
}

u32 ip_sessions_remove_usr(upf_vnip_sessions_t *p, upf_5glan_ip_pdrs_t *ip_pdr)
{
    sx_info_t *v_sx = &ip_pdr->sx_info;

    if (!(p->flags & VN_UPDATING))
    {
        p->flags |= VN_UPDATING;
        upf_trace ("# ip-session first set updating, vn_session_index:%u", v_sx->sx_index);
    }
    else
    {
        upf_trace ("## ip-session has set updating, vn_session_index:%u", v_sx->sx_index);

        /* make pending and active equal based on active to solve the rcu callback issue when pfcp asso release */
        struct rules_vnip *pending = vnip_get_rules (p, VN_PENDING);
        struct rules_vnip *active = vnip_get_rules (p, VN_ACTIVE);
        if (pending->sx_infos)
        {
            int i = 0, j = 0;

            if (active->sx_infos)
            {
                while ((i < vec_len(pending->sx_infos)) && (j < vec_len(active->sx_infos)))
                {
                    if (vec_elt(pending->sx_infos, i).sx_index != vec_elt(active->sx_infos, j).sx_index)
                    {
                        vec_delete (pending->sx_infos, 1, i);
                        continue;
                    }
                    i++;
                    j++;
                }
            }

            while (i < vec_len(pending->sx_infos))
                vec_delete (pending->sx_infos, 1, i);
        }
    }

    /* search session info by session index, and remove this session if found it */
    if (ipsx_delete_rules(p, v_sx->sx_index) != 0)
    {
        upf_warn("sx_delete_sx_info fail, sx_index:%u", v_sx->sx_index);
        return ~0;
    }

    if (~0 != ip_pdr->ip_sxs_index)
        ip_pdr->ip_sxs_index = ~0;

    clib_atomic_fetch_xor (&p->active, VN_PENDING);

    return 0;
}

u8 *format_simple_ip_session (u8 *s, va_list *args)
{
    upf_vnip_sessions_t *ipsx = va_arg (*args, upf_vnip_sessions_t *);
    int rule = va_arg (*args, int);
    struct rules_vnip *rules = vnip_get_rules (ipsx, rule);
    sx_info_t *sx_infos;
    int i = 0;

    s = format (s, "    talbe_id: %u   ip: %U/%u  flags:0x%x\n",
                ipsx->table_id, format_ip46_address, &ipsx->vrf.addr, IP46_TYPE_ANY, ipsx->vrf.prefix, ipsx->flags);

    s = format (s, "    Active:\n");
    vec_foreach(sx_infos, rules->sx_infos)
    {
        s = format(s, "        %u: sx_index:%u  ip_pdrs_index:%u\n", i++, sx_infos->sx_index, sx_infos->ip_pdrs_index);
    }

  return s;
}

static int
format_ip_session_cb_24_8 (clib_bihash_kv_24_8_t *kvp, void *arg)
{
    upf_main_t *um = &g_upf_main;
    upf_vnip_sessions_t *ipsx = NULL;
    ip_session_show_t *sh = (ip_session_show_t *)arg;
    vlib_main_t *vm = (vlib_main_t *)sh->arg;

    if ((sh->flag & SHOW_ALL_KV) || (sh->flag & SHOW_ALL_KEY))
    {
        ip_sx_kv_t *kv = (ip_sx_kv_t *)kvp;
        if (sh->limit > 0)
        {
            sh->limit -= 1;
            vlib_cli_output (vm, "key: table_id:%u  %U/%u\n"
                                 "val: ip_sxs_index:%u",
                kv->k.table_id, format_ip46_address, &kv->k.dst, IP46_TYPE_ANY, kv->k.mask, kv->v.ip_sxs_index);
        }
        sh->count++;

        if (sh->flag & SHOW_ALL_KEY)
            return (BIHASH_WALK_CONTINUE);

        CHECK_POOL_IS_VALID_RET(um->vnip_sessions, kvp->value, BIHASH_WALK_CONTINUE);
        ipsx = pool_elt_at_index (um->vnip_sessions, kvp->value);
        if (sh->limit > 0)
            vlib_cli_output (vm, "%U", format_simple_ip_session, ipsx, VN_ACTIVE);

        return (BIHASH_WALK_CONTINUE);
    }

    CHECK_POOL_IS_VALID_RET(um->vnip_sessions, kvp->value, BIHASH_WALK_CONTINUE);
    ipsx = pool_elt_at_index (um->vnip_sessions, kvp->value);
    if (sh->table_id == ipsx->table_id)
    {
        if ((sh->flag & SHOW_BY_TABLE_ID)
            || ((sh->flag & SHOW_BY_TABLE_ID_AND_IP_MASK)
                && (ip46_address_is_equal (&sh->ip, &ipsx->vrf.addr) && (sh->mask == ipsx->vrf.prefix))))
        {
            sh->count++;
            if (sh->limit > 0)
            {
                sh->limit -= 1;
                vlib_cli_output (vm, "%U", format_simple_ip_session, ipsx, VN_ACTIVE);
            }
        }
    }

    return (BIHASH_WALK_CONTINUE);
}

static clib_error_t *
upf_show_ip_sessions_command_fn (vlib_main_t *vm, unformat_input_t *main_input,
                             vlib_cli_command_t *cmd)
{
    unformat_input_t _line_input, *line_input = &_line_input;
    upf_main_t *gtm = &g_upf_main;
    clib_error_t *error = NULL;
    ip46_address_t ip = {0};
    u8 has_ip = 0, has_table_id = 0, has_mask = 0, has_all = 0, has_all_key = 0, has_hash = 0;
    u32 limit = 100;
    u32 table_id = 0;
    u32 mask = 0;

    if (unformat_user (main_input, unformat_line_input, line_input))
    {
        while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
        {
            if (unformat (line_input, "table_id %u", &table_id))
                has_table_id = 1;
            else if (unformat (line_input, "ip %U", unformat_ip46_address, &ip, IP46_TYPE_ANY))
                has_ip = 1;
            else if (unformat (line_input, "mask %u", &mask))
                has_mask = 1;
            else if (unformat (line_input, "all_key"))
                has_all_key = 1;
            else if (unformat (line_input, "all"))
                has_all = 1;
            else if (unformat (line_input, "limit %u", &limit))
                ;
            else if (unformat (line_input, "hash"))
                has_hash = 1;
            else
            {
                error = unformat_parse_error (line_input);
                unformat_free (line_input);
                goto done;
            }
        }
        unformat_free (line_input);
    }

    if (!has_mask && has_ip)
    {
        if (ip46_address_is_ip4(&ip))
            mask = 32;
        else
            mask = 64;
    }

    if (has_ip)
        vlib_cli_output (vm, "input(default) ip: %U/%u table_id: %u", format_ip46_address, &ip, IP46_TYPE_ANY, mask, table_id);

    if (has_ip && has_hash)
    {
        ip_sx_kv_t kv = {.k.dst = ip, .k.mask = mask, .k.table_id = table_id};

        if (clib_bihash_search_inline_24_8 (&gtm->vnip_sxs_by_ip, &kv.kv))
        {
            vlib_cli_output (vm, "hash search fail");
        }
        else
        {
            CHECK_POOL_IS_VALID_RET(g_upf_main.vnip_sessions, kv.v.ip_sxs_index, error);
            upf_vnip_sessions_t *ipsx = pool_elt_at_index (g_upf_main.vnip_sessions, kv.v.ip_sxs_index);
            vlib_cli_output (vm, "%U", format_simple_ip_session, ipsx, VN_ACTIVE);
        }
        goto done;
    }

    ip_session_show_t ip_sh =
        {.arg = vm, .flag = 0, .count = 0, .limit = limit, .ip = ip, .table_id = table_id, .mask = mask};

    if (has_ip)
        ip_sh.flag = SHOW_BY_TABLE_ID_AND_IP_MASK;
    else if (has_table_id)
        ip_sh.flag = SHOW_BY_TABLE_ID;
    else if (has_all)
        ip_sh.flag = SHOW_ALL_KV;
    else if (has_all_key)
        ip_sh.flag = SHOW_ALL_KEY;

    if (ip_sh.flag)
    {
        clib_bihash_foreach_key_value_pair_24_8 (&gtm->vnip_sxs_by_ip, format_ip_session_cb_24_8, &ip_sh);

        vlib_cli_output (vm, "----------------------------------------------------");
        vlib_cli_output (vm, "Total IP-Sessions Num = %d", ip_sh.count);
    }
    else
    {
        vlib_cli_output (vm, "input error");
    }

done:
    return error;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_show_ip_sessions_command, static) = {
    .path = "show upf ip sessions",
    .short_help = "show upf ip sessions [all] [all_key] [limit <val>]\n"
                  "                                           [table_id <val> ip <val> | mask <val> | hash]\n"
                  "  examples:\n"
                  "    show upf ip sessions all limit 50\n"
                  "    show upf ip sessions table_id 10000 ip ************ mask 32 hash",
    .function = upf_show_ip_sessions_command_fn,
};
/* *INDENT-ON* */
/* neil.fan@20220303 add 5 APIs for ip-sessions to support active/pending end */

/* neil.fan@20220225 add for ip-sessions function, ip create if not found, and append session info to ip */
u32 ip_sessions_ref(upf_session_t *sx, upf_5glan_ip_pdrs_t *ip_pdr)
{
    upf_main_t *gtm = &g_upf_main;
    upf_vnip_sessions_t *p;
    u32 is_new_ip = 0;

    if (!ip_pdr)
        return ~0;

    /* session info has been appended */
    if (~0 != ip_pdr->ip_sxs_index)
    {
        return ~0;
    }

    ip_sx_kv_t kv = {0};
    kv.k.dst = ip_pdr->vrf.addr;
    kv.k.mask = ip_pdr->vrf.prefix;
    kv.k.table_id = ip_pdr->table_id;

    /* if hash has been created by other session, here append session info only */
    if (!clib_bihash_search_inline_24_8 (&gtm->vnip_sxs_by_ip, &kv.kv))
    {
        CHECK_POOL_IS_VALID_RET(gtm->vnip_sessions, kv.v.ip_sxs_index, ~0);
        p = pool_elt_at_index (gtm->vnip_sessions, kv.v.ip_sxs_index);

        upf_trace ("ip-session hash search, table_id:%u  %U/%u  key:%lx %lx %lx  v:%lx", kv.k.table_id, format_ip46_address,
            &kv.k.dst, IP46_TYPE_ANY, kv.k.mask, kv.kv.key[0], kv.kv.key[1], kv.kv.key[2], kv.kv.value);
    }
    else
    {
        p = ip_sessions_create(ip_pdr, &kv);
        is_new_ip = 1;
        upf_trace ("ip-session hash create, table_id:%u  %U/%u  key:%lx %lx %lx  v:%lx", kv.k.table_id, format_ip46_address,
            &kv.k.dst, IP46_TYPE_ANY, kv.k.mask, kv.kv.key[0], kv.kv.key[1], kv.kv.key[2], kv.kv.value);
    }
    ip_pdr->ip_sxs_index = kv.v.ip_sxs_index;

    ip_sessions_append_usr(p, ip_pdr);

    /* add to ip-sessions process list to clear VN_UPDATING flag */
    APPEND_U_INDEX(sx->ip_sessions_idx, p - gtm->vnip_sessions);

    return (is_new_ip ? 0 : ~0);
}

/* neil.fan@20220225 add for ip-sessions function, decorrelate session from the ip, and remove ip if no sessions refers */
u32 ip_sessions_unref(upf_session_t *sx, upf_5glan_ip_pdrs_t *ip_pdr)
{
    upf_main_t *gtm = &g_upf_main;
    ip_sx_kv_t kv = {0};
    upf_vnip_sessions_t *p;

    if (!ip_pdr)
        return ~0;

    /* session info has been appended */
    if (~0 != ip_pdr->ip_sxs_index)
    {
        CHECK_POOL_IS_VALID_RET(gtm->vnip_sessions, ip_pdr->ip_sxs_index, ~0);
        p = pool_elt_at_index (gtm->vnip_sessions, ip_pdr->ip_sxs_index);
    }
    else
    {
        kv.k.dst = ip_pdr->vrf.addr;
        kv.k.mask = ip_pdr->vrf.prefix;
        kv.k.table_id = ip_pdr->table_id;

        if (!clib_bihash_search_inline_24_8 (&gtm->vnip_sxs_by_ip, &kv.kv))
        {
            return ~0;
        }
        CHECK_POOL_IS_VALID_RET(gtm->vnip_sessions, ip_pdr->ip_sxs_index, ~0);
        p = pool_elt_at_index (gtm->vnip_sessions, kv.v.ip_sxs_index);
    }

    if (!p)
    {
        upf_err(" ip-sessions instance is null, ip_pdr->ip_sxs_index:%u", ip_pdr->ip_sxs_index);
        return ~0;
    }

    if (p->flags & VN_DELETING)
        return ~0;

    ip_sessions_remove_usr(p, ip_pdr);

    /* add to ip-sessions process list to clear VN_UPDATING flag */
    APPEND_U_INDEX(sx->ip_sessions_idx, p - gtm->vnip_sessions);

    /* neil.fan@20220317 modify using "active" to check because of active/pending has switched before */
    struct rules_vnip *rules = vnip_get_rules (p, VN_ACTIVE);
    if (vec_len (rules->sx_infos))
        return ~0; /* exists other sessions */

    kv.k.dst = ip_pdr->vrf.addr;
    kv.k.mask = ip_pdr->vrf.prefix;
    kv.k.table_id = ip_pdr->table_id;

    upf_debug ("ip-session hash remove, table_id:%u  %U/%u  key:%lx %lx %lx", kv.k.table_id, format_ip46_address,
        &kv.k.dst, IP46_TYPE_ANY, kv.k.mask, kv.kv.key[0], kv.kv.key[1], kv.kv.key[2]);

    clib_bihash_add_del_24_8 (&gtm->vnip_sxs_by_ip, &kv.kv, 0 /* delete */);
    /* upf_ip_session_instance_free(kv.v); maybe used by active rules, so release in rcu callback */
    p->flags |= VN_DELETING;

    return 0;
}

/* neil.fan@20220225 add for vrf add or delete only when ip create or remove */
void sx_vn_ip_sx_vrf_add_del(void *ip, void *unused, int is_add)
{
    upf_5glan_ip_pdrs_t *ip_pdr = ip;
    upf_session_t *sx = (upf_session_t *)unused;

    upf_trace ("# is_add:%d ip_pdr: %U/%u table_id:%u ip_sxs_field:%u ip_sxs_index:%u ",
        is_add, format_ip46_address, &ip_pdr->vrf.addr, IP46_TYPE_ANY, ip_pdr->vrf.prefix, ip_pdr->table_id,
        ip_pdr->ip_sxs_field, ip_pdr->ip_sxs_index);

    if (ip_pdr->ip_sxs_field & F_IP_EMPTY)
        return;

    if (is_add)
    {
        if (ip_sessions_ref(sx, ip_pdr))
            return;
    }
    else
    {
        if (ip_sessions_unref(sx, ip_pdr))
            return;
    }

    pfcp_rpc_msg_t *msg;
    u32 msg_size = CLIB_CACHE_LINE_ROUND(sizeof(sx_add_del_vrf_ip_t) + PFCP_RPC_BASIC);
    if (g_upf_auxiliary_switch & UPF_LINUX_MALLOC_SWITCH)
    {
        msg = malloc(msg_size);
    }
    else
    {
        msg = clib_mem_alloc_aligned_no_fail (msg_size, CLIB_CACHE_LINE_BYTES);
    }
    memset (msg, 0, msg_size);

    msg->magic = PFCP_RPC_MAGIC;
    msg->type = PFCP_RPC_SX_ADD_DEL_VRF_IP;

    sx_add_del_vrf_ip_t *p = &msg->data.sx_add_del_vrf_ip;
    p->is_add = is_add;
    p->fib_index = ~0;
    p->session_index = ip_pdr->ip_sxs_index; /* dpo result: ip-sessions index */
    p->table_id = ip_pdr->table_id;

    if (ip46_address_is_ip4 (&ip_pdr->vrf.addr))
    {
        p->pfx.fp_addr.ip4.as_u32 = ip_pdr->vrf.addr.ip4.as_u32;
        p->pfx.fp_len = ip_pdr->vrf.prefix;
        p->pfx.fp_proto = FIB_PROTOCOL_IP4;
    }
    else
    {
        p->pfx.fp_addr.ip6.as_u64[0] = ip_pdr->vrf.addr.ip6.as_u64[0];
        p->pfx.fp_addr.ip6.as_u64[1] = ip_pdr->vrf.addr.ip6.as_u64[1];
        p->pfx.fp_len = ip_pdr->vrf.prefix;
        p->pfx.fp_proto = FIB_PROTOCOL_IP6;
    }

    upf_debug("## ip pdrs idx[%u] is_add:%u table_id:%u IP:%U/%u vn_sx_index:%u, fp_proto:%u",
            ip_pdr->ip_sxs_index, is_add, ip_pdr->table_id,
            format_ip46_address, &ip_pdr->vrf.addr, IP46_TYPE_ANY, p->pfx.fp_len,
            p->session_index, p->pfx.fp_proto);
    pfcp_thread_call_main_thread (msg);
}

static clib_error_t *
iupf_alloc_fixed_seid_command_fn(vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
  while (unformat_check_input (input) != UNFORMAT_END_OF_INPUT)
  {
    if (unformat (input, "on"))
    {
      g_alloc_fixed_seid_switch = SWITCH_ON;
      g_pfcp_thread_inc[0] = 0;
    }
    else if (unformat (input, "off"))
      g_alloc_fixed_seid_switch = SWITCH_OFF;
    else if (unformat (input, "show"))
      {
        vlib_cli_output (vm, "switch is [%s]\n", g_alloc_fixed_seid_switch == SWITCH_ON ? "on" : "off");
        return 0;
      }
    else
      return 0;
  }
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (iupf_alloc_fixed_seid_command, static) = {
    .path = "upf alloc fixed seid switch",
    .short_help = "upf alloc fixed seid switch[on | off | show]",
    .function = iupf_alloc_fixed_seid_command_fn,
};
/* *INDENT-OFF* */

