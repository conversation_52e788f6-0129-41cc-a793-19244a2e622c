


/*
 * Copyright(c) 2018 Travelping GmbH.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <assert.h>

#include <arpa/inet.h>
#include <errno.h>
#include <inttypes.h>
#include <math.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/mman.h>
#include <sys/queue.h>
#include <sys/resource.h>
#include <sys/time.h>
#include <unistd.h>
#include <vnet/fib/ip4_fib.h>
#include <vnet/fib/ip6_fib.h>
#include <vnet/ip/ip6_hop_by_hop.h>
#include <vppinfra/format.h>
#include <vppinfra/types.h>
#include <vppinfra/vec.h>
#include <vpp/stats/stat_segment.h>

#include "pfcp.h"
#include "upf.h"
#include "upf_pfcp.h"
#include "upf_pfcp_api.h"
#include "upf_pfcp_server.h"
#include <upf/upf_5glan.h>
#include "upf_duplicate_buffer.h"

#define INIT_ACL_RULE_NUM 256

/* BEGIN: Add by wangjunjie02 for X-Online-Host on 20210508 */
char g_x_online_host_enable_flag = 1;
/* END: Add by wangjunjie02 for X-Online-Host on 20210508 */
static int upf_pfcp_node_msg (sx_msg_t *msg);
static int upf_pfcp_session_msg (sx_msg_t *msg);

extern u32 g_upf_pfcp_log_switch;
extern u32 g_n4_dev_session_num;
extern u32 g_cheating_prevent_switch;
extern u32 g_upf_l2tp_switch;
extern u32 g_upf_l2tp_authen_type;
extern void upf_pfcp_clean_far_buffering_list (upf_session_t *sx, upf_far_t *far);

ip46_address_t g_invalid_ip = {0};
u32 g_upf_load_control_swtich = SWITCH_OFF;

typedef struct
{
    u8 metric_threshold;
    u8 last_metric_reported;
    u8 resv[2];
    u32 interval;
}upf_load_control_param_t;
upf_load_control_param_t g_upf_load_ctrl_param = {.metric_threshold = 0, .interval = 2};

typedef struct
{
    u8 last_metric_reported;
    u8 resv[3];
    u32 interval;
}upf_overload_control_param_t;
upf_overload_control_param_t g_upf_overload_ctrl_param = {.interval = 2};

u32 g_upf_pfcp_snssai_check_switch = SWITCH_OFF;

u32 g_upf_n9_tunnel_inactive_timer = 0;
struct upf_per_qos_timer_t
{
    u32 t1; /* seconds */
    u32 t2; /* seconds */
    u32 t3; /* seconds */
};
struct upf_per_qos_timer_t g_upf_per_qos_timer_config = {30, 2, 4};

u32 upf_len2mask (u8 len)
{
  return (len ? (~(((u32)1 << (32 - len)) - 1)) : 0);
}

u32 upf_convert_fd_to_hs (upf_pfd_ctx_t *tmp_filter_node, upf_pdr_t *pdr)
{
  char *hs_fd = tmp_filter_node->hs_fd;
  u32 len = 0, ip_filed = 0;
  u32 subnet = 0;
  u32 broadcast = 0;
  if (((tmp_filter_node->flags & F_PFD_C_FD) != 1) ||
      (tmp_filter_node->fd_filter.type == IPFILTER_IPV6))
    return 0;

  // protocal
  if (tmp_filter_node->fd_filter.proto == (u8)~0)
    len += snprintf (hs_fd + len, MAX_ONE_RULE_LEN - len, "...");
  else
    len += snprintf (hs_fd + len, MAX_ONE_RULE_LEN - len, "\\x%x..",
                     tmp_filter_node->fd_filter.proto);
  if (tmp_filter_node->fd_filter.direction == ACL_OUT)
    ip_filed = UPF_ACL_FIELD_SRC;
  else
    ip_filed = UPF_ACL_FIELD_DST;
  // ip addr
  if (tmp_filter_node->fd_filter.address[ip_filed].address.ip4.as_u32 != 0)
    {
      subnet =
          clib_host_to_net_u32 (tmp_filter_node->fd_filter.address[ip_filed]
                                    .address.ip4.as_u32) &
          upf_len2mask (tmp_filter_node->fd_filter.address[ip_filed].mask);
      broadcast =
          ~(subnet ^
            upf_len2mask (tmp_filter_node->fd_filter.address[ip_filed].mask));

      if (tmp_filter_node->fd_filter.direction == ACL_IN)
        {
          if (pdr != NULL &&
              (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_V4) != 0)
            len += snprintf (
                hs_fd + len, MAX_ONE_RULE_LEN - len, "\\x%x\\x%x\\x%x\\x%x",
                pdr->pdi.ue_addr.ip4.data[0], pdr->pdi.ue_addr.ip4.data[1],
                pdr->pdi.ue_addr.ip4.data[2], pdr->pdi.ue_addr.ip4.data[3]);
          else
            len += snprintf (hs_fd + len, MAX_ONE_RULE_LEN - len, "....");
        }

      len += snprintf (hs_fd + len, MAX_ONE_RULE_LEN - len,
                       "[\\x%x-\\x%x][\\x%x-\\x%x][\\x%x-\\x%x][\\x%x-\\x%x]",
                       ((u8 *)(&subnet))[3], ((u8 *)(&broadcast))[3],
                       ((u8 *)(&subnet))[2], ((u8 *)(&broadcast))[2],
                       ((u8 *)(&subnet))[1], ((u8 *)(&broadcast))[1],
                       ((u8 *)(&subnet))[0], ((u8 *)(&broadcast))[0]);

      if (tmp_filter_node->fd_filter.direction == ACL_OUT)
        {
          if (pdr != NULL &&
              (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_V4) != 0)
            len += snprintf (
                hs_fd + len, MAX_ONE_RULE_LEN - len, "\\x%x\\x%x\\x%x\\x%x",
                pdr->pdi.ue_addr.ip4.data[0], pdr->pdi.ue_addr.ip4.data[1],
                pdr->pdi.ue_addr.ip4.data[2], pdr->pdi.ue_addr.ip4.data[3]);
          else
            len += snprintf (hs_fd + len, MAX_ONE_RULE_LEN - len, "....");
        }
    }
  else
    {
      if (tmp_filter_node->fd_filter.direction == ACL_IN)
        {
          if (pdr != NULL &&
              (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_V4) != 0)
            len += snprintf (
                hs_fd + len, MAX_ONE_RULE_LEN - len,
                "\\x%x\\x%x\\x%x\\x%x....", pdr->pdi.ue_addr.ip4.data[0],
                pdr->pdi.ue_addr.ip4.data[1], pdr->pdi.ue_addr.ip4.data[2],
                pdr->pdi.ue_addr.ip4.data[3]);
          else
            len += snprintf (hs_fd + len, MAX_ONE_RULE_LEN - len, ".{8}");
        }
      else if (tmp_filter_node->fd_filter.direction == ACL_OUT)
        {
          if (pdr != NULL &&
              (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_V4) != 0)
            len += snprintf (
                hs_fd + len, MAX_ONE_RULE_LEN - len,
                "....\\x%x\\x%x\\x%x\\x%x", pdr->pdi.ue_addr.ip4.data[0],
                pdr->pdi.ue_addr.ip4.data[1], pdr->pdi.ue_addr.ip4.data[2],
                pdr->pdi.ue_addr.ip4.data[3]);
          else
            len += snprintf (hs_fd + len, MAX_ONE_RULE_LEN - len, ".{8}");
        }
      else
        len += snprintf (hs_fd + len, MAX_ONE_RULE_LEN - len, ".{8}");
    }

  // port
  if (tmp_filter_node->fd_filter.port[ip_filed].min != 0)
    {
      if (tmp_filter_node->fd_filter.direction == ACL_IN)
        len += snprintf (hs_fd + len, MAX_ONE_RULE_LEN - len, "..");
      if (((u8 *)(&tmp_filter_node->fd_filter.port[ip_filed].min))[0] <=
          ((u8 *)(&tmp_filter_node->fd_filter.port[ip_filed].max))[0])
        len += snprintf (
            hs_fd + len, MAX_ONE_RULE_LEN - len, "[\\x%x-\\x%x][\\x%x-\\x%x]",
            ((u8 *)(&tmp_filter_node->fd_filter.port[ip_filed].min))[1],
            ((u8 *)(&tmp_filter_node->fd_filter.port[ip_filed].max))[1],
            ((u8 *)(&tmp_filter_node->fd_filter.port[ip_filed].min))[0],
            ((u8 *)(&tmp_filter_node->fd_filter.port[ip_filed].max))[0]);
      else
        len += snprintf (
            hs_fd + len, MAX_ONE_RULE_LEN - len, "[\\x%x-\\x%x][^\\x%x-\\x%x]",
            ((u8 *)(&tmp_filter_node->fd_filter.port[ip_filed].min))[1],
            ((u8 *)(&tmp_filter_node->fd_filter.port[ip_filed].max))[1],
            ((u8 *)(&tmp_filter_node->fd_filter.port[ip_filed].max))[0] + 1,
            ((u8 *)(&tmp_filter_node->fd_filter.port[ip_filed].min))[0] - 1);
    }

  return len;
}

u8 *upf_format_acl4_rule (u8 *s, va_list *args)
{
  u8 *src_mac1, *src_mac2, *dst_mac1, *dst_mac2;
  struct acl_ipv4_rules *rule = va_arg (*args, struct acl_ipv4_rules *);
  u32 ip_s =
      clib_net_to_host_u32 (rule->field[UPF_ACL_IPV4_SRC_FIELD].value.u32);
  u32 ip_d =
      clib_net_to_host_u32 (rule->field[UPF_ACL_IPV4_DST_FIELD].value.u32);

  src_mac1 = &rule->field[UPF_ACL_IPV4_SRC_MAC_FIRST4_FIELD].value.u8;
  dst_mac1 = &rule->field[UPF_ACL_IPV4_DST_MAC_FIRST4_FIELD].value.u8;
  src_mac2 = &rule->field[UPF_ACL_IPV4_SRC_MAC_LAST2_FIELD].value.u8;
  dst_mac2 = &rule->field[UPF_ACL_IPV4_DST_MAC_LAST2_FIELD].value.u8;

  s = format (
      s,
      "%02x:%02x:%02x:%02x:%02x:%02x => %02x:%02x:%02x:%02x:%02x:%02x "
      "s:%u/0x%hhx c:%u/0x%hhx eth type: 0x%04x/%04x ip proto: 0x%hhx/0x%hhx "
      "%U/%u:%hu-%hu => %U/%u:%hu-%hu ",
      src_mac1[0], src_mac1[1], src_mac1[2], src_mac1[3], src_mac2[0],
      src_mac2[1], dst_mac1[0], dst_mac1[1], dst_mac1[2], dst_mac1[3],
      dst_mac2[0], dst_mac2[1],
      rule->field[UPF_ACL_IPV4_S_VLAN_FIELD].value.u16,
      rule->field[UPF_ACL_IPV4_S_VLAN_FIELD].mask_range.u16,
      rule->field[UPF_ACL_IPV4_C_VLAN_FIELD].value.u16,
      rule->field[UPF_ACL_IPV4_C_VLAN_FIELD].mask_range.u16,
      clib_net_to_host_u16 (
          rule->field[UPF_ACL_IPV4_ETH_TYPE_FIELD].value.u16),
      rule->field[UPF_ACL_IPV4_ETH_TYPE_FIELD].mask_range.u16,
      rule->field[UPF_ACL_IPV4_PROTO_FIELD].value.u8,
      rule->field[UPF_ACL_IPV4_PROTO_FIELD].mask_range.u8, format_ip4_address,
      &ip_s, rule->field[UPF_ACL_IPV4_SRC_FIELD].mask_range.u32,
      rule->field[UPF_ACL_IPV4_SRCP_FIELD].value.u16,
      rule->field[UPF_ACL_IPV4_SRCP_FIELD].mask_range.u16, format_ip4_address,
      &ip_d, rule->field[UPF_ACL_IPV4_DST_FIELD].mask_range.u32,
      rule->field[UPF_ACL_IPV4_DSTP_FIELD].value.u16,
      rule->field[UPF_ACL_IPV4_DSTP_FIELD].mask_range.u16);

  s = format (s, "intfc 0x%hhx/0x%hhx qfi 0x%hhx/0x%hhx ",
              rule->field[UPF_ACL_IPV4_SOURCE_INTERFACE_FIELD].value.u8,
              rule->field[UPF_ACL_IPV4_SOURCE_INTERFACE_FIELD].mask_range.u8,
              rule->field[UPF_ACL_IPV4_QFI_FIELD].value.u8,
              rule->field[UPF_ACL_IPV4_QFI_FIELD].mask_range.u8);

  s = format (s, "category-priority-userdata: 0x%x-0x%x-0x%x",
              rule->data.category_mask, (u16) ~(rule->data.priority - 1),
              rule->data.userdata);

  return s;
}

static inline void
upf_dump_ip4_rules (const struct acl_ipv4_rules *rule, int32_t num)
{
  int32_t i;

  for (i = 0; i < num; i++, rule++)
    {
      upf_debug ("\t%d:", i + 1);
      upf_debug ("%U", upf_format_acl4_rule, rule);
    }
}

static struct upf_acl_param_t upf_acl_ipv4_param = {
    .socket_id = VEC_NUMA_UNSPECIFIED,
    .rule_size = UPF_ACL_RULE_SZ (ARRAY_LEN (acl_ipv4_defs)),
    .max_rule_num = 0x100,
};

static struct upf_acl_config_t upf_acl_ipv4_build_param = {
    .num_categories = 1,
    .num_fields = ARRAY_LEN (acl_ipv4_defs),
};

static struct upf_acl_ctx *
upf_acl4_init (const struct acl_ipv4_rules *rules, uint32_t rules_nb)
{
  struct upf_acl_ctx *ctx;

  upf_debug ("Creating PDR context with %u rules\n", rules_nb);

  upf_debug ("IPv4 entries [%u]:\n", rules_nb);
  upf_dump_ip4_rules (rules, rules_nb);

  ctx = upf_acl_create (&upf_acl_ipv4_param);
  if (ctx == NULL)
    {
      upf_err ("Failed to create ACL context\n");
      return NULL;
    }

  if (upf_acl_add_rules (ctx, (const struct upf_acl_rule *)rules, rules_nb) <
      0)
    {
      upf_err ("add rules failed\n");
      return NULL;
    }

  clib_memcpy (&upf_acl_ipv4_build_param.defs, acl_ipv4_defs,
               sizeof (acl_ipv4_defs));

  if (upf_acl_build (ctx, &upf_acl_ipv4_build_param) != 0)
    {
      upf_err ("Failed to build ACL trie\n");
      return NULL;
    }

  upf_acl_dump (ctx);

  return ctx;
}

static int
upf_extend_rules (struct acl_ipv4_rules **rules, uint32_t cur_cnt,
              uint32_t *cur_sz)
{
  if (*rules == NULL)
    {
      *rules =
          clib_mem_alloc (INIT_ACL_RULE_NUM * sizeof (struct acl_ipv4_rules));
      if (*rules == NULL)
        return -1;
      clib_memset (*rules, 0,
                   INIT_ACL_RULE_NUM * sizeof (struct acl_ipv4_rules));
      *cur_sz = INIT_ACL_RULE_NUM;
      return 0;
    }

  if (cur_cnt >= *cur_sz)
    {
      *rules = clib_mem_realloc (*rules, *cur_sz,
                                 *cur_sz * sizeof (struct acl_ipv4_rules) * 2);
      if (*rules == NULL)
        return -1;
      /* clean reallocated extra space */
      clib_memset (&(*rules)[*cur_sz], 0,
                   *cur_sz * sizeof (struct acl_ipv4_rules));
      *cur_sz *= 2;
    }

  return 0;
}

void upf_convert_rule (struct acl_ipv4_rules *rule, upf_pdr_t *pdr, acl_rule_t *fd, u32 dir)
{
  rule->data.category_mask = ACL_CATEGORY_MASK;
  rule->data.priority = (u16) ~(pdr->precedence) + 1;

  rule->field[UPF_ACL_IPV4_SOURCE_INTERFACE_FIELD].value.u8 =
      pdr->pdi.src_intf;
  rule->field[UPF_ACL_IPV4_SOURCE_INTERFACE_FIELD].mask_range.u8 = 0xFF;

  rule->field[UPF_ACL_IPV4_QFI_FIELD].value.u8 = pdr->pdi.qfi;
  rule->field[UPF_ACL_IPV4_QFI_FIELD].mask_range.u8 = 0xFF;

  if (fd)
    {
      rule->field[UPF_ACL_IPV4_PROTO_FIELD].value.u8 =
          (fd->proto == 0xFF ? 0 : fd->proto);
      rule->field[UPF_ACL_IPV4_PROTO_FIELD].mask_range =
          rule->field[UPF_ACL_IPV4_PROTO_FIELD].value;

      rule->field[UPF_ACL_IPV4_SRC_FIELD].value.u32 =
          clib_host_to_net_u32 (fd->address[dir].address.ip4.as_u32);
      rule->field[UPF_ACL_IPV4_SRC_FIELD].mask_range.u32 =
          fd->address[dir].mask;
      rule->field[UPF_ACL_IPV4_SRCP_FIELD].value.u16 = fd->port[dir].min;
      rule->field[UPF_ACL_IPV4_SRCP_FIELD].mask_range.u16 =
          fd->port[dir].max == 0 ? 0xFFFF : fd->port[dir].max;

      dir = dir ^ UPF_ACL_FIELD_DST;

      rule->field[UPF_ACL_IPV4_DST_FIELD].value.u32 =
          clib_host_to_net_u32 (fd->address[dir].address.ip4.as_u32);
      rule->field[UPF_ACL_IPV4_DST_FIELD].mask_range.u32 =
          fd->address[dir].mask;
      rule->field[UPF_ACL_IPV4_DSTP_FIELD].value.u16 = fd->port[dir].min;
      rule->field[UPF_ACL_IPV4_DSTP_FIELD].mask_range.u16 =
          fd->port[dir].max == 0 ? 0xFFFF : fd->port[dir].max;
#if 0
      if (fd->ethernet_filter_id)
        {
          if (fd->src_mac.u.first_4 != 0 || fd->src_mac.u.last_2 != 0)
            {
              rule->field[UPF_ACL_IPV4_SRC_MAC_FIRST4_FIELD].value.u32 =
                  clib_host_to_net_u32 (fd->src_mac.u.first_4);
              rule->field[UPF_ACL_IPV4_SRC_MAC_FIRST4_FIELD].mask_range.u32 =
                  ~0;
              rule->field[UPF_ACL_IPV4_SRC_MAC_LAST2_FIELD].value.u16 =
                  clib_host_to_net_u16 (fd->src_mac.u.last_2);
              rule->field[UPF_ACL_IPV4_SRC_MAC_LAST2_FIELD].mask_range.u16 =
                  ~0;
            }
          if (fd->dst_mac.u.first_4 != 0 || fd->dst_mac.u.last_2 != 0)
            {
              rule->field[UPF_ACL_IPV4_DST_MAC_FIRST4_FIELD].value.u32 =
                  clib_host_to_net_u32 (fd->dst_mac.u.first_4);
              rule->field[UPF_ACL_IPV4_DST_MAC_FIRST4_FIELD].mask_range.u32 =
                  ~0;
              rule->field[UPF_ACL_IPV4_DST_MAC_LAST2_FIELD].value.u16 =
                  clib_host_to_net_u16 (fd->dst_mac.u.last_2);
              rule->field[UPF_ACL_IPV4_DST_MAC_LAST2_FIELD].mask_range.u16 =
                  ~0;
            }
          if (fd->s_tag)
            {
              rule->field[UPF_ACL_IPV4_S_VLAN_FIELD].value.u16 = fd->s_tag;
              rule->field[UPF_ACL_IPV4_S_VLAN_FIELD].mask_range.u16 = ~0;
            }
          if (fd->c_tag)
            {
              rule->field[UPF_ACL_IPV4_C_VLAN_FIELD].value.u16 = fd->c_tag;
              rule->field[UPF_ACL_IPV4_C_VLAN_FIELD].mask_range.u16 = ~0;
            }
          if (fd->ethertype)
            {
              // FIXME: endian ???
              rule->field[UPF_ACL_IPV4_ETH_TYPE_FIELD].value.u16 =
                  fd->ethertype;
              rule->field[UPF_ACL_IPV4_ETH_TYPE_FIELD].mask_range.u16 = ~0;
            }
        }
#endif
    }
}

u8 *
upf_format_acl6_rule (u8 *s, va_list *args)
{
  u32 i;
  u8 *src_mac1, *src_mac2, *dst_mac1, *dst_mac2;
  ip6_address_t src, dst, smask, dmask;
  struct acl_ipv6_rules *rule = va_arg (*args, struct acl_ipv6_rules *);

  for (i = 0; i < 4; i++)
    {
      src.as_u32[i] = rule->field[UPF_ACL_IPV6_SRC0_FIELD + i].value.u32;
      smask.as_u32[i] =
          rule->field[UPF_ACL_IPV6_SRC0_FIELD + i].mask_range.u32;
      dst.as_u32[i] = rule->field[UPF_ACL_IPV6_DST0_FIELD + i].value.u32;
      dmask.as_u32[i] =
          rule->field[UPF_ACL_IPV6_DST0_FIELD + i].mask_range.u32;
    }

  src_mac1 = &rule->field[UPF_ACL_IPV6_SRC_MAC_FIRST4_FIELD].value.u8;
  dst_mac1 = &rule->field[UPF_ACL_IPV6_DST_MAC_FIRST4_FIELD].value.u8;
  src_mac2 = &rule->field[UPF_ACL_IPV6_SRC_MAC_LAST2_FIELD].value.u8;
  dst_mac2 = &rule->field[UPF_ACL_IPV6_DST_MAC_LAST2_FIELD].value.u8;

  s = format (s,
              "%02x:%02x:%02x:%02x:%02x:%02x => %02x:%02x:%02x:%02x:%02x:%02x "
              "s:%u/0x%hhx c:%u/0x%hhx eth type: 0x%04x/%04x ",
              src_mac1[0], src_mac1[1], src_mac1[2], src_mac1[3], src_mac2[0],
              src_mac2[1], dst_mac1[0], dst_mac1[1], dst_mac1[2], dst_mac1[3],
              dst_mac2[0], dst_mac2[1],
              rule->field[UPF_ACL_IPV6_S_VLAN_FIELD].value.u16,
              rule->field[UPF_ACL_IPV6_S_VLAN_FIELD].mask_range.u16,
              rule->field[UPF_ACL_IPV6_C_VLAN_FIELD].value.u16,
              rule->field[UPF_ACL_IPV6_C_VLAN_FIELD].mask_range.u16,
              clib_net_to_host_u16 (
                  rule->field[UPF_ACL_IPV6_ETH_TYPE_FIELD].value.u16),
              rule->field[UPF_ACL_IPV6_ETH_TYPE_FIELD].mask_range.u16);

  s = format (s, "0x%hhx/0x%hhx %U/%U:%hu-%hu => %U/%U:%hu-%hu ",
              rule->field[UPF_ACL_IPV6_PROTO_FIELD].value.u8,
              rule->field[UPF_ACL_IPV6_PROTO_FIELD].mask_range.u8,
              format_ip6_address, &src, format_ip6_address, &smask,
              rule->field[UPF_ACL_IPV6_SRCP_FIELD].value.u16,
              rule->field[UPF_ACL_IPV6_SRCP_FIELD].mask_range.u16,
              format_ip6_address, &dst, format_ip6_address, &dmask,
              rule->field[UPF_ACL_IPV6_DSTP_FIELD].value.u16,
              rule->field[UPF_ACL_IPV6_DSTP_FIELD].mask_range.u16);

  s = format (s, "intfc 0x%hhx/0x%hhx qfi 0x%hhx/0x%hhx ",
              rule->field[UPF_ACL_IPV6_SOURCE_INTERFACE_FIELD].value.u16,
              rule->field[UPF_ACL_IPV6_SOURCE_INTERFACE_FIELD].mask_range.u16,
              rule->field[UPF_ACL_IPV6_QFI_FIELD].value.u16,
              rule->field[UPF_ACL_IPV6_QFI_FIELD].mask_range.u16);

  s = format (s, "category-priority-userdata: 0x%x-0x%x-0x%x",
              rule->data.category_mask, (u16) ~(rule->data.priority - 1),
              rule->data.userdata);
  return s;
}

static inline void
upf_dump_ip6_rules (const struct acl_ipv6_rules *rule, int32_t num, int32_t extra)
{
  int32_t i;

  for (i = 0; i < num; i++, rule++)
    {
      upf_debug ("\t%d:", i + 1);
      upf_debug ("%U", upf_format_acl6_rule, rule);
    }
}
static struct upf_acl_param_t upf_acl_ipv6_param = {
    .socket_id = VEC_NUMA_UNSPECIFIED,
    .rule_size = UPF_ACL_RULE_SZ (ARRAY_LEN (acl_ipv6_defs)),
    .max_rule_num = 0x10000,
};

static struct upf_acl_config_t upf_acl_ipv6_build_param = {
    .num_categories = 1,
    .num_fields = ARRAY_LEN (acl_ipv6_defs),
};

static struct upf_acl_ctx *
upf_acl6_init (const struct acl_ipv6_rules *rules, uint32_t rules_nb)
{
  struct upf_acl_ctx *ctx;

  upf_debug ("Creating PDR context with %u rules\n", rules_nb);

  upf_debug ("IPv6 entries [%u]:\n", rules_nb);
  upf_dump_ip6_rules (rules, rules_nb, 1);

  ctx = upf_acl_create (&upf_acl_ipv6_param);
  if (ctx == NULL)
    {
      upf_err ("Failed to create ACL context\n");
      return NULL;
    }

  if (upf_acl_add_rules (ctx, (const struct upf_acl_rule *)rules, rules_nb) <
      0)
    {
      upf_err ("add rules failed\n");
      return NULL;
    }

  clib_memcpy (&upf_acl_ipv6_build_param.defs, acl_ipv6_defs,
               sizeof (acl_ipv6_defs));

  if (upf_acl_build (ctx, &upf_acl_ipv6_build_param) != 0)
    {
      upf_err ("Failed to build ACL trie\n");
      return NULL;
    }

  upf_acl_dump (ctx);

  return ctx;
}

static int
upf_extend_rules6 (struct acl_ipv6_rules **rules, uint32_t cur_cnt,
               uint32_t *cur_sz)
{
  if (*rules == NULL)
    {
      *rules =
          clib_mem_alloc (INIT_ACL_RULE_NUM * sizeof (struct acl_ipv6_rules));
      if (*rules == NULL)
        return -1;
      clib_memset (*rules, 0,
                   INIT_ACL_RULE_NUM * sizeof (struct acl_ipv6_rules));
      *cur_sz = INIT_ACL_RULE_NUM;
      return 0;
    }

  if (cur_cnt >= *cur_sz)
    {
      *rules = clib_mem_realloc (*rules, *cur_sz,
                                 *cur_sz * sizeof (struct acl_ipv6_rules) * 2);
      if (*rules == NULL)
        return -1;
      /* clean reallocated extra space */
      clib_memset (&(*rules)[*cur_sz], 0,
                   *cur_sz * sizeof (struct acl_ipv6_rules));
      *cur_sz *= 2;
    }

  return 0;
}

void
upf_convert_rule6 (struct acl_ipv6_rules *rule, upf_pdr_t *pdr, acl_rule_t *fd,
               u32 dir)
{
  u32 i;
  const uint32_t nbu32 = sizeof (uint32_t) * CHAR_BIT;

  rule->data.category_mask = ACL_CATEGORY_MASK;
  rule->data.priority = (u16) ~(pdr->precedence) + 1;

  rule->field[UPF_ACL_IPV6_SOURCE_INTERFACE_FIELD].value.u16 =
      pdr->pdi.src_intf;
  rule->field[UPF_ACL_IPV6_SOURCE_INTERFACE_FIELD].mask_range.u16 = 0xFFFF;

  rule->field[UPF_ACL_IPV6_QFI_FIELD].value.u16 = pdr->pdi.qfi;
  rule->field[UPF_ACL_IPV6_QFI_FIELD].mask_range.u16 = 0xFFFF;

  if (fd)
    {
      rule->field[UPF_ACL_IPV6_PROTO_FIELD].value.u8 =
          (fd->proto == 0xFF ? 0 : fd->proto);
      rule->field[UPF_ACL_IPV6_PROTO_FIELD].mask_range =
          rule->field[UPF_ACL_IPV6_PROTO_FIELD].value;

      for (i = 0; i < 4; i++)
        {
          if (fd->address[dir].mask >= (i + 1) * nbu32)
            rule->field[UPF_ACL_IPV6_SRC0_FIELD + i].mask_range.u32 = nbu32;
          else
            rule->field[UPF_ACL_IPV6_SRC0_FIELD + i].mask_range.u32 =
                fd->address[dir].mask > (i * nbu32)
                    ? fd->address[dir].mask - (i * 32)
                    : 0;

          rule->field[UPF_ACL_IPV6_SRC0_FIELD + i].value.u32 =
              fd->address[dir].address.ip6.as_u32[i];
        }
      rule->field[UPF_ACL_IPV6_SRCP_FIELD].value.u16 = fd->port[dir].min;
      rule->field[UPF_ACL_IPV6_SRCP_FIELD].mask_range.u16 =
          fd->port[dir].max == 0 ? 0xFFFF : fd->port[dir].max;

      dir = dir ^ UPF_ACL_FIELD_DST;

      for (i = 0; i < 4; i++)
        {
          if (fd->address[dir].mask >= (i + 1) * nbu32)
            rule->field[UPF_ACL_IPV6_DST0_FIELD + i].mask_range.u32 = nbu32;
          else
            rule->field[UPF_ACL_IPV6_DST0_FIELD + i].mask_range.u32 =
                fd->address[dir].mask > (i * nbu32)
                    ? fd->address[dir].mask - (i * 32)
                    : 0;

          rule->field[UPF_ACL_IPV6_DST0_FIELD + i].value.u32 =
              fd->address[dir].address.ip6.as_u32[i];
        }
      rule->field[UPF_ACL_IPV6_DSTP_FIELD].value.u16 = fd->port[dir].min;
      rule->field[UPF_ACL_IPV6_DSTP_FIELD].mask_range.u16 =
          fd->port[dir].max == 0 ? 0xFFFF : fd->port[dir].max;
#if 0
      if (fd->ethernet_filter_id)
        {
          if (fd->src_mac.u.first_4 != 0 || fd->src_mac.u.last_2 != 0)
            {
              rule->field[UPF_ACL_IPV6_SRC_MAC_FIRST4_FIELD].value.u32 =
                  clib_host_to_net_u32 (fd->src_mac.u.first_4);
              rule->field[UPF_ACL_IPV6_SRC_MAC_FIRST4_FIELD].mask_range.u32 =
                  ~0;
              rule->field[UPF_ACL_IPV6_SRC_MAC_LAST2_FIELD].value.u16 =
                  clib_host_to_net_u16 (fd->src_mac.u.last_2);
              rule->field[UPF_ACL_IPV6_SRC_MAC_LAST2_FIELD].mask_range.u16 =
                  ~0;
            }
          if (fd->dst_mac.u.first_4 != 0 || fd->dst_mac.u.last_2 != 0)
            {
              rule->field[UPF_ACL_IPV6_DST_MAC_FIRST4_FIELD].value.u32 =
                  clib_host_to_net_u32 (fd->dst_mac.u.first_4);
              rule->field[UPF_ACL_IPV6_DST_MAC_FIRST4_FIELD].mask_range.u32 =
                  ~0;
              rule->field[UPF_ACL_IPV6_DST_MAC_LAST2_FIELD].value.u16 =
                  clib_host_to_net_u16 (fd->dst_mac.u.last_2);
              rule->field[UPF_ACL_IPV6_DST_MAC_LAST2_FIELD].mask_range.u16 =
                  ~0;
            }
          if (fd->s_tag)
            {
              rule->field[UPF_ACL_IPV6_S_VLAN_FIELD].value.u16 = fd->s_tag;
              rule->field[UPF_ACL_IPV6_S_VLAN_FIELD].mask_range.u16 = ~0;
            }
          if (fd->c_tag)
            {
              rule->field[UPF_ACL_IPV6_C_VLAN_FIELD].value.u16 = fd->c_tag;
              rule->field[UPF_ACL_IPV6_C_VLAN_FIELD].mask_range.u16 = ~0;
            }
          if (fd->ethertype)
            {
              // FIXME: endian ???
              rule->field[UPF_ACL_IPV6_ETH_TYPE_FIELD].value.u16 =
                  fd->ethertype;
              rule->field[UPF_ACL_IPV6_ETH_TYPE_FIELD].mask_range.u16 = ~0;
            }
        }
#endif
    }
}

void upf_fill_bdt_addr (filter_pdr_bdt *s_filters, acl_rule_t *fd_filter,
               int from_field)
{
  int field = 0;
  s_filters->sipaddr = fd_filter->address[from_field].address.ip4.as_u32;
  s_filters->smask =
      clib_host_to_net_u32 (upf_len2mask (fd_filter->address[from_field].mask));
  s_filters->sport_high = fd_filter->port[from_field].max;
  s_filters->sport_low = fd_filter->port[from_field].min;

  if (from_field == UPF_ACL_FIELD_SRC)
    field = UPF_ACL_FIELD_DST;
  else
    field = UPF_ACL_FIELD_SRC;
  s_filters->dipaddr = fd_filter->address[field].address.ip4.as_u32;
  s_filters->dmask =
      clib_host_to_net_u32 (upf_len2mask (fd_filter->address[field].mask));
  s_filters->dport_high = fd_filter->port[field].max;
  s_filters->dport_low = fd_filter->port[field].min;
}

int upf_bdt_database_update (upf_session_t *ss)
{
  int err = 0;
  int n = 0;
  u32 direction;
  u32 nb_acl4_rules = 0;
  struct acl_ipv4_rules *rules = NULL;
  upf_main_t *gtm = &g_upf_main;
  struct rules *pending = upf_get_rules (ss, SX_PENDING);
  struct rules *active = upf_get_rules (ss, SX_ACTIVE);
  upf_pdr_t *pdr;
  upf_far_t *far;
  upf_app_id_pfd_ctx_t *pfd = NULL;
  upf_pfd_ctx_t *pfd_content;
  acl_rule_t *filter;
  struct upf_acl_ctx *acl = pending->acl;

  if (upf_extend_rules (&rules, nb_acl4_rules, &nb_acl4_rules) != 0)
    {
      upf_err ("extend rules failed, no memory\n");
      return -1;
    }

  vec_foreach (pdr, pending->pdr)
  {
    if (NULL == (far = upf_get_far_by_id (pending, pdr->far_id)))
      {
        if (NULL == (far = upf_get_far_by_id (active, pdr->far_id)))
          {
            upf_err ("No FAR found: up_seid:0x%lx, far id:0x%x\n", ss->up_seid,
                     pdr->far_id);
            return -1;
          }
      }

    direction = IS_DL (pdr, far) ? UPF_ACL_FIELD_SRC : UPF_ACL_FIELD_DST;

    if (pdr->pdi.fields & F_PDI_APPLICATION_ID)
      {
        pool_foreach (
            pfd, gtm->pfd_list, ({
              if (!strcmp ((char *)pdr->pdi.app_name, (char *)pfd->app_name))
                {
                  pool_foreach (
                      pfd_content, pfd->pfd_contents, ({
                        if (pfd_content->flags & F_PFD_C_URL ||
                            pfd_content->flags & F_PFD_C_DN ||
                            pfd_content->fd_filter.type == IPFILTER_IPV6)
                          continue;
                        if (n >= nb_acl4_rules)
                          {
                            upf_extend_rules (&rules, nb_acl4_rules,
                                          &nb_acl4_rules);
                          }

                        rules[n].data.userdata =
                            (pdr - pending->pdr) + ACL_OFFSET;
                        upf_convert_rule (&rules[n], pdr, &pfd_content->fd_filter,
                                      UPF_ACL_FIELD_SRC);

                        n++;
                      }));
                }
            }));
      }
    else if (pdr->pdi.fields &
             (F_PDI_SDF_FILTER | F_PDI_ETHERNET_PACKET_FILTER))
      {
        vec_foreach (filter, pdr->pdi.acl)
        {
          /*there is only one sdf with "permit out ...."*/
          if (filter->type != IPFILTER_IPV6)
            {
              if (n >= nb_acl4_rules)
                {
                  upf_extend_rules (&rules, nb_acl4_rules, &nb_acl4_rules);
                }

              rules[n].data.userdata = (pdr - pending->pdr) + ACL_OFFSET;
              upf_convert_rule (&rules[n], pdr, filter, direction);

              n++;
            }
        }
      }
    else
      {
        if (n >= nb_acl4_rules)
          {
            upf_extend_rules (&rules, nb_acl4_rules, &nb_acl4_rules);
          }
        rules[n].data.userdata = (pdr - pending->pdr) + ACL_OFFSET;
        upf_convert_rule (&rules[n], pdr, NULL, 0);

        n++;
      }
  }

  if (0 != n)
    {
      pending->acl = upf_acl4_init (rules, n);
      if (pending->acl != NULL)
        {
          if (acl)
            {
              upf_acl_free (acl);
            }
        }
      else
        {
          upf_err ("upf_acl4_init failed\n");
          pending->acl = acl;
          err = -2;
        }
    }

  clib_mem_free (rules);

  return err;
}

void upf_fill_bdt_addr6 (filter_pdr_bdt6 *s_filters, acl_rule_t *fd_filter,
                int from_field)
{
  int field = 0, i;
  u16 ip_mask_len = fd_filter->address[from_field].mask;
  for (i = 0; i < 4; i++)
    {
      s_filters->sipaddr[i] =
          fd_filter->address[from_field].address.ip6.as_u32[i];
      if (ip_mask_len < 32)
        {
          s_filters->smask[i] = clib_host_to_net_u32 (upf_len2mask (ip_mask_len));
          ip_mask_len = 0;
        }
      else
        {
          ip_mask_len -= 32;
          s_filters->smask[i] = clib_host_to_net_u32 (upf_len2mask (32));
        }
    }
  s_filters->sport_high = fd_filter->port[from_field].max;
  s_filters->sport_low = fd_filter->port[from_field].min;

  if (from_field == UPF_ACL_FIELD_SRC)
    field = UPF_ACL_FIELD_DST;
  else
    field = UPF_ACL_FIELD_SRC;

  ip_mask_len = fd_filter->address[field].mask;
  for (i = 0; i < 4; i++)
    {
      s_filters->dipaddr[i] = fd_filter->address[field].address.ip6.as_u32[i];
      if (ip_mask_len < 32)
        {
          s_filters->dmask[i] = clib_host_to_net_u32 (upf_len2mask (ip_mask_len));
          ip_mask_len = 0;
        }
      else
        {
          ip_mask_len -= 32;
          s_filters->dmask[i] = clib_host_to_net_u32 (upf_len2mask (32));
        }
    }
  s_filters->dport_high = fd_filter->port[field].max;
  s_filters->dport_low = fd_filter->port[field].min;
}

int upf_bdt6_database_update (upf_session_t *ss)
{
  int err = 0;
  int n = 0;
  upf_main_t *gtm = &g_upf_main;
  struct rules *pending = upf_get_rules (ss, SX_PENDING);
  struct rules *active = upf_get_rules (ss, SX_ACTIVE);
  upf_pdr_t *pdr;
  upf_far_t *far;
  u8 direction;
  upf_app_id_pfd_ctx_t *pfd = NULL;
  upf_pfd_ctx_t *pfd_content = NULL;
  acl_rule_t *filter;
  u32 nb_acl6_rules = 0;
  struct upf_acl_ctx *acl = pending->acl6;
  struct acl_ipv6_rules *rules = NULL;

  vec_foreach (pdr, pending->pdr)
  {
    if (NULL == (far = upf_get_far_by_id (pending, pdr->far_id)))
      {
        if (NULL == (far = upf_get_far_by_id (active, pdr->far_id)))
          {
            upf_err ("No FAR found: up_seid:0x%lx, far id:0x%x\n", ss->up_seid,
                     pdr->far_id);
            return -1;
          }
      }

    direction = IS_DL (pdr, far) ? UPF_DL : UPF_UL;

    if (pdr->pdi.fields & F_PDI_APPLICATION_ID)
      {
        pool_foreach (
            pfd, gtm->pfd_list, ({
              if (!strcmp ((char *)pdr->pdi.app_name, (char *)pfd->app_name))
                {
                  pool_foreach (
                      pfd_content, pfd->pfd_contents, ({
                        if (pfd_content->flags & F_PFD_C_URL ||
                            pfd_content->flags & F_PFD_C_DN ||
                            (pfd_content->fd_filter.type == IPFILTER_IPV4))
                          continue;

                        if (n >= nb_acl6_rules)
                          {
                            upf_extend_rules6 (&rules, nb_acl6_rules,
                                           &nb_acl6_rules);
                          }

                        rules[n].data.userdata =
                            (pdr - pending->pdr) + ACL_OFFSET;
                        upf_convert_rule6 (&rules[n], pdr, &pfd_content->fd_filter,
                                       UPF_ACL_FIELD_SRC);

                        n++;
                      }));
                }
            }));
      }
    else if (pdr->pdi.fields &
             (F_PDI_SDF_FILTER | F_PDI_ETHERNET_PACKET_FILTER))
      {
        vec_foreach (filter, pdr->pdi.acl)
        {
          if (filter->type != IPFILTER_IPV4)
            {
              if (n >= nb_acl6_rules)
                {
                  upf_extend_rules6 (&rules, nb_acl6_rules, &nb_acl6_rules);
                }

              rules[n].data.userdata = (pdr - pending->pdr) + ACL_OFFSET;

              if (filter->id != 0)
                {
                  upf_sdf_t *sdf = upf_get_sdf (ss, SX_PENDING, filter->id);
                  if (sdf != NULL)
                    {
                      if (direction == UPF_DL)
                        upf_convert_rule6 (&rules[n], pdr, &sdf->bidirectional_sdf,
                                       UPF_ACL_FIELD_SRC);
                      else
                        upf_convert_rule6 (&rules[n], pdr, &sdf->bidirectional_sdf,
                                       UPF_ACL_FIELD_DST);
                    }
                }
              else
                {
                  if (direction == UPF_DL)
                    upf_convert_rule6 (&rules[n], pdr, filter, UPF_ACL_FIELD_SRC);
                  else
                    upf_convert_rule6 (&rules[n], pdr, filter, UPF_ACL_FIELD_DST);
                }

              n++;
            }
        }
      }
    else
      {
        if (n >= nb_acl6_rules)
          {
            upf_extend_rules6 (&rules, nb_acl6_rules, &nb_acl6_rules);
          }

        rules[n].data.userdata = (pdr - pending->pdr) + ACL_OFFSET;
        upf_convert_rule6 (&rules[n], pdr, NULL, UPF_ACL_FIELD_SRC);

        n++;
      }
  }

  if (0 != n)
    {
      pending->acl6 = upf_acl6_init (rules, n);
      if (pending->acl6 != NULL)
        {
          if (acl)
            {
              upf_acl_free (acl);
            }
        }
      else
        {
          upf_err ("upf_acl6_init failed\n");
          pending->acl6 = acl;
          err = -2;
        }
    }

  clib_mem_free (rules);
  return err;
}

void upf_make_hs_rule_url (char *str, char *url)
{
  char *p, *get = NULL;
  char *host = NULL;
  // char *method = NULL; /*http: or https:*/
  char *real_url = NULL;
  char *split = NULL;
  char tmp_str[MAX_URL_LEN] = {0};

  u32 len = 0;

  clib_strncpy (tmp_str, url, clib_strnlen (url, MAX_URL_LEN));

  if (strstr (tmp_str, "https:"))
    {
      clib_strtok (tmp_str, ":", &real_url);
      host = clib_strtok (real_url, "/", &get);
      len += sprintf (str + len, "\\x%x..", 0);
    }
  else
    {
      host = clib_strtok (tmp_str, "/", &get);

      if (get && strlen (get))
        {
          len += sprintf (str + len, " /");
          if (get[0] == '*')
            len += sprintf (str + len, ".+");
        }
      p = clib_strtok (get, "*", &split);
      if (p && strlen (p))
        {
          do
            {
              len += sprintf (str + len, "%s", p);

              p = clib_strtok (0, "*", &split);
              if (p && strlen (p))
                len += sprintf (str + len, ".+");
            }
          while (p && strlen (p));
        }
      /* BEGIN: Add by wangjunjie02 for X-Online-Host on 20210508 */
      //len += sprintf (str + len, ".+Host: ");
      if (0 == g_x_online_host_enable_flag){
        len += sprintf (str + len, ".+\r\nHost: ");
      }
      else{
        len += sprintf (str + len, ".+Host: ");
      }
      /* END: Add by wangjunjie02 for X-Online-Host on 20210508 */
    }

  if (host == NULL)
      return;

  if (host[0] == '*')
    len += sprintf (str + len, "[^/]+");

  p = clib_strtok (host, "*", &split);
  if (p && strlen (p))
    {
      do
        {
          len += sprintf (str + len, "%s", p);

          p = clib_strtok (0, "*", &split);
          if (p && strlen (p))
            len += sprintf (str + len, ".+");
        }
      while (p && strlen (p));
    }
}

int upf_make_hs_rule (upf_pfd_ctx_t *tmp_filter_node, int num,
              upf_app_id_pfd_ctx_t *tmp_filter_list, const char **hs_rules,
              u32 *id, u32 *flags)
{
    int len = 0;
    char *p;
    u8 *dn;
    upf_main_t *gtm = &g_upf_main;
    char str[MAX_URL_LEN] = {0};

    if (num > MAX_RULE_NUM)
    {
        return num;
    }

    char *tmp_rules = (char *)clib_mem_alloc (MAX_ONE_RULE_LEN * sizeof (char));
    memset (tmp_rules, 0, MAX_ONE_RULE_LEN * sizeof (char));

    if (tmp_filter_node->flags & (F_PFD_C_URL | F_PFD_C_CP))
    {
/* neil.fan@20230203 mask because:
 * 1) FD(flow description) + URL can be matched one direction flow, the reverse flow cannot be matched, eg:
 *  "upf set pfd rule app_id http1-2 url {www.baidu.com} fd {permit out 6 from ************** 80 to any} " --->
 *  "...[\x64-\x64][\xb4-\xb4][\x8e-\x8e][\xb-\xb]....[\x0-\x0][\x50-\x50].+.+Host: www.baidu.com"
 *  This rule can match DL URL packet, cannot match UL URL packet.
 * 2) FD and URL will be checked in upf_pdr_application_detection at the same time.
 */
#if 0
        if (tmp_filter_node->flags & F_PFD_C_FD)
        {
            upf_convert_fd_to_hs (tmp_filter_node, NULL);
            len += snprintf (tmp_rules, MAX_ONE_RULE_LEN, "%s.+",
                           tmp_filter_node->hs_fd);
        }
#endif
        /*http----"*.sina.com/news/index.html" convert to
        " /news/index.html.+Host .+.sina.com/r/n" for hs
        https------"www.sina.com" convert to "\x00\x00\x0cwww.sina.com"*/
        if (tmp_filter_node->flags & F_PFD_C_URL)
        {
            upf_make_hs_rule_url (str, (char *)tmp_filter_node->url);
            len += snprintf (tmp_rules + len, MAX_ONE_RULE_LEN, "%s", str);
        }
    }
    else if (tmp_filter_node->flags & F_PFD_C_DN)
    {
        dn = vec_dup (tmp_filter_node->dn);
        p = strtok ((char *)dn, ".");
        len = snprintf (tmp_rules, MAX_ONE_RULE_LEN, "\\x%02x%s", (u8)strlen (p),
                        p);

        while ((p = strtok (NULL, ".")))
          len += snprintf (tmp_rules + len, MAX_ONE_RULE_LEN, "\\x%02x%s",
                           (u8) (strlen (p)), p);
    }
    else
    {
        clib_mem_free (tmp_rules);
        return num;
    }

    if (!len)
    {
        clib_mem_free (tmp_rules);
        return num;
    }

    hs_rules[num] = tmp_rules;
    flags[num] = HS_FLAG_DOTALL | HS_FLAG_SINGLEMATCH;
    id[num] = tmp_filter_list - gtm->pfd_list;

    if (tmp_filter_node->flags & F_PFD_C_CP &&
        tmp_filter_node->flags & F_PFD_C_FD)
      id[num] |= CT_FLAG;

    num++;

    return num;
}

u32 upf_hs_database_update_global ()
{
    u32 num = 0, r = 0, i;
    uword *p;
    upf_main_t *gtm = &g_upf_main;
    hs_compile_error_t *compile_err;
    upf_pfd_ctx_t *tmp_filter_node;
    u32 *id;
    u32 *flags;
    const char **hs_rules;
    hs_scratch_t *tmp_scratch = NULL;
    vlib_thread_main_t *tm = vlib_get_thread_main ();
    vlib_thread_registration_t *tr;
    // char tmp_rules[MAX_RULE_NUM][MAX_ONE_RULE_LEN] = {{0}};

    upf_app_id_pfd_ctx_t *tmp_filter_list = NULL;

    if (!gtm->num_worker_threads)
    {
        p = hash_get_mem (tm->thread_registrations_by_name, "workers");
        if (p)
        {
            tr = (vlib_thread_registration_t *)p[0];
            if (tr)
            {
                gtm->num_worker_threads = tr->count;
                if (tr->count)
                {
                    gtm->first_worker_thread_index = tr->first_index;
                }
                else
                {
                    /* main thread */
                    gtm->num_worker_threads = 1;
                    gtm->first_worker_thread_index = 0;
                }
            }
        }
    }

    if (gtm->database != NULL)
    {
        for (i = 0; i < gtm->num_worker_threads; i++)
        {
            hs_free_scratch (gtm->scratch[i]);
        }

        hs_free_database (gtm->database);

        gtm->database = NULL;

        upf_debug ("database is free\n");
    }
    else
    {
        vec_validate_init_empty (gtm->scratch, gtm->num_worker_threads, NULL);

        upf_debug ("scratch is init\n");
    }

    /* *INDENT-OFF* */
    pool_foreach (tmp_filter_list, gtm->pfd_list, ({
                  pool_foreach (tmp_filter_node, tmp_filter_list->pfd_contents,
                                ({ num++; }));
                }));

    if (num == 0)
    {
        upf_debug (" no need to build hs database\n");
        return 0;
    }

    hs_rules = (const char **)clib_mem_alloc ((num + 1) * sizeof (char *));
    id = (u32 *)clib_mem_alloc ((num + 1) * sizeof (u32));
    flags = (u32 *)clib_mem_alloc ((num + 1) * sizeof (u32));

    num = 0;
    pool_foreach (tmp_filter_list, gtm->pfd_list, ({
                  pool_foreach (tmp_filter_node, tmp_filter_list->pfd_contents,
                                ({
                                  num = upf_make_hs_rule (tmp_filter_node, num,
                                                      tmp_filter_list,
                                                      hs_rules, id, flags);
                                }));
                }));

    if (num == 0)
    {
        clib_mem_free (id);
        clib_mem_free (flags);
        clib_mem_free (hs_rules);

        upf_trace ("hs rule :num is 0, no need to build hs database");
        r = 0;

        return r;
    }

    for (int i = 0; i < num; i++)
    {
        upf_trace ("hs rule : %s, num=%d, appid_idx=%d\n", hs_rules[i], i,
                 id[i]);
    }

    upf_debug ("hs start");

    if (hs_compile_multi (hs_rules, flags, id, num, HS_MODE_BLOCK, NULL,
                        &gtm->database, &compile_err) != HS_SUCCESS)
    {
        upf_debug ("ERROR: Unable to compile pattern \"%s\":%s\n",
                   hs_rules[compile_err->expression], compile_err->message);
        hs_free_compile_error (compile_err);
        r = -1;
    }

    upf_debug ("hs finish");

    if (hs_alloc_scratch (gtm->database, &tmp_scratch) != HS_SUCCESS)
    {
        upf_debug ("ERROR: Unable to allocate scratch space. Exiting.\n");
        hs_free_database (gtm->database);
        r = -1;
    }
    for (i = 0; i < gtm->num_worker_threads; i++)
    {
        if (hs_clone_scratch (tmp_scratch, &gtm->scratch[i]) != HS_SUCCESS)
        {
            upf_debug ("ERROR: Unable to allocate scratch space. Exiting.\n");
            r = -1;
        }
    }

    /* *INDENT-ON* */
    for (int i = 0; i < num; i++)
    {
        clib_mem_free ((char *)hs_rules[i]);
    }

    clib_mem_free (id);
    clib_mem_free (flags);
    clib_mem_free (hs_rules);
    hs_free_scratch (tmp_scratch);

    return r;
}

static u32 upf_pfcp_init_response_node_id (sx_msg_t *req, struct pfcp_response *r)
{
  if (pool_is_free_index (g_upf_main.pfcp_endpoints, req->pfcp_endpoint))
    {
      upf_err ("pfcp_endpoint %u is invalid!", req->pfcp_endpoint);
      return 1;
    }

  upf_pfcp_endpoint_t *ep = pool_elt_at_index (g_upf_main.pfcp_endpoints, req->pfcp_endpoint);
  if (ip46_address_is_ip4 (&ep->key.addr))
    {
      r->node_id.type = NID_IPv4;
      //add for upf link status by zhangxin on 2022-11-18 below
      g_upf_relate_IpAddr[1].srcIp4Addr = ep->key.addr.ip4;
      //add for upf link status by zhangxin on 2022-11-18 above
      ip46_address_set_ip4 (&r->node_id.ip, &ep->key.addr.ip4);
    }
  else
    {
      r->node_id.type = NID_IPv6;
      //add for upf link status by zhangxin on 2022-11-18 below
      g_upf_relate_IpAddr[1].srcIp6Addr = ep->key.addr.ip6;
      //add for upf link status by zhangxin on 2022-11-18 above
      ip46_address_set_ip6 (&r->node_id.ip, &ep->key.addr.ip6);
    }

  if (ip46_address_is_zero (&r->node_id.ip))
    {
      upf_err ("PFCP: No valid Node ID found!");
      return 1;
    }
  return 0;
}

/*************************************************************************/

int upf_pfcp_msg_handle (sx_msg_t *msg)
{
  switch (msg->hdr->type)
    {
    case PFCP_HEARTBEAT_REQUEST:
    case PFCP_HEARTBEAT_RESPONSE:
    case PFCP_PFD_MANAGEMENT_REQUEST:
    case PFCP_PFD_MANAGEMENT_RESPONSE:
    case PFCP_ASSOCIATION_SETUP_REQUEST:
    case PFCP_ASSOCIATION_SETUP_RESPONSE:
    case PFCP_ASSOCIATION_UPDATE_REQUEST:
    case PFCP_ASSOCIATION_UPDATE_RESPONSE:
    case PFCP_ASSOCIATION_RELEASE_REQUEST:
    case PFCP_ASSOCIATION_RELEASE_RESPONSE:
    case PFCP_VERSION_NOT_SUPPORTED_RESPONSE:
    case PFCP_NODE_REPORT_REQUEST:
    case PFCP_NODE_REPORT_RESPONSE:
      return upf_pfcp_node_msg (msg);

    case PFCP_SESSION_SET_DELETION_REQUEST:
    case PFCP_SESSION_SET_DELETION_RESPONSE:
    case PFCP_SESSION_ESTABLISHMENT_REQUEST:
    case PFCP_SESSION_ESTABLISHMENT_RESPONSE:
    case PFCP_SESSION_MODIFICATION_REQUEST:
    case PFCP_SESSION_MODIFICATION_RESPONSE:
    case PFCP_SESSION_DELETION_REQUEST:
    case PFCP_SESSION_DELETION_RESPONSE:
    case PFCP_SESSION_REPORT_REQUEST:
    case PFCP_SESSION_REPORT_RESPONSE:
      return upf_pfcp_session_msg (msg);

    default:
      upf_debug ("PFCP: msg type invalid: %d.", msg->hdr->type);
      break;
    }

  return -1;
}

/*************************************************************************/

static uword
upf_unformat_ipfilter_address_port (unformat_input_t *i, va_list *args)
{
  acl_rule_t *acl = va_arg (*args, acl_rule_t *);
  int field = va_arg (*args, int);
  ipfilter_address_t *ip = &acl->address[field];
  ipfilter_port_t *port = &acl->port[field];
  int is_ip4;

  ip->mask = ~0;
  port->min = 0;
  port->max = ~0;
  clib_memset (&ip->mask_address, 0, sizeof (ip46_address_t));

  if (unformat_check_input (i) == UNFORMAT_END_OF_INPUT)
    return 0;

  if (unformat (i, "any"))
    *ip = ACL_FROM_ANY;
  else if (unformat (i, "assigned"))
    *ip = ACL_TO_ASSIGNED;
  else if (unformat (i, "%U", unformat_ip46_address, &ip->address,
                     IP46_TYPE_ANY))
    {

      is_ip4 = ip46_address_is_ip4 (&ip->address);
      acl->type = is_ip4 ? IPFILTER_IPV4 : IPFILTER_IPV6;
      ip->mask = is_ip4 ? 32 : 128;

      // if (unformat_check_input (i) == UNFORMAT_END_OF_INPUT)
      // return 1;
      if (unformat (i, "/%d", &ip->mask))
        ;
      if (is_ip4)
        ip4_address_mask_from_width (&ip->mask_address.ip4, ip->mask);
      else
        ip6_address_mask_from_width (&ip->mask_address.ip6, ip->mask);
    }
  else
    {
      return 0;
    }

  if (unformat_check_input (i) == UNFORMAT_END_OF_INPUT)
    return 1;

  if (unformat (i, "%d-%d", &port->min, &port->max))
    ;
  else if (unformat (i, "%d", &port->min))
    port->max = port->min;

  return 1;
}

uword
upf_unformat_ipfilter (unformat_input_t *i, acl_rule_t *acl)
{
  int step = 0;

  acl->type = IPFILTER_WILDCARD;

  /* action dir proto from src to dst [options] */
  while (step < 5 && unformat_check_input (i) != UNFORMAT_END_OF_INPUT)
    {
      switch (step)
        {
        case 0: /* action */
          if (unformat (i, "permit"))
            {
              acl->action = ACL_PERMIT;
            }
          else if (unformat (i, "deny"))
            {
              acl->action = ACL_DENY;
            }
          else
            return 0;

          break;

        case 1: /* dir */
          if (unformat (i, "in"))
            {
              acl->direction = ACL_IN;
            }
          else if (unformat (i, "out"))
            {
              acl->direction = ACL_OUT;
            }
          else
            return 0;

          break;

        case 2: /* proto */
          if (unformat (i, "ip"))
            {
              acl->proto = ~0;
            }
          else if (unformat (i, "%u", &acl->proto))
            ;
          else
            return 0;

          break;

        case 3: /* from src */
          if (unformat (i, "from %U", upf_unformat_ipfilter_address_port, acl,
                        UPF_ACL_FIELD_SRC))
            ;
          else
            return 0;

          break;

        case 4:
          if (unformat (i, "to %U", upf_unformat_ipfilter_address_port, acl,
                        UPF_ACL_FIELD_DST))
            ;
          else
            return 0;

          break;

        default:
          return 0;
        }

      step++;
    }

  return 1;
}

static u8 *
upf_format_ipfilter_address_port (u8 *s, va_list *args)
{
  acl_rule_t *acl = va_arg (*args, acl_rule_t *);
  int field = va_arg (*args, int);
  int direction = va_arg (*args, int);
  ipfilter_address_t *ip = &acl->address[field];
  ipfilter_port_t *port = &acl->port[field];

  if (direction == ACL_IN &&
      ipfilter_address_cmp_const (ip, ACL_FROM_ANY) == 0)
    {
      s = format (s, "any");
    }
  else if (direction == ACL_OUT &&
           ipfilter_address_cmp_const (ip, ACL_TO_ASSIGNED) == 0)
    {
      //  s = format (s, "assigned");
      s = format (s, "any");
    }
  else
    {
      s = format (s, "%U", format_ip46_address, &ip->address, IP46_TYPE_ANY);
      if (ip->mask != (ip46_address_is_ip4 (&ip->address) ? 32 : 128))
        s = format (s, "/%u", ip->mask);
    }

  if (port->min != 0 || port->max != (u16)~0)
    {
      s = format (s, " %d", port->min);
      if (port->min != port->max)
        s = format (s, "-%d", port->max);
    }

  return s;
}

u8 *
upf_format_ipfilter (u8 *s, va_list *args)
{
  acl_rule_t *acl = va_arg (*args, acl_rule_t *);

  switch (acl->type)
    {
    case IPFILTER_IPV6:
      s = format (s, "v6: ");
      break;

    case IPFILTER_IPV4:
      s = format (s, "V4: ");
      break;

    default:
      s = format (s, "V4V6: ");
      break;
    }

  switch (acl->action)
    {
    case ACL_PERMIT:
      s = format (s, "permit ");
      break;

    case ACL_DENY:
      s = format (s, "deny ");
      break;

    default:
      s = format (s, "action_%d ", acl->action);
      break;
    }

  switch (acl->direction)
    {
    case ACL_IN:
      s = format (s, "in ");
      break;

    case ACL_OUT:
      s = format (s, "out ");
      break;

    default:
      s = format (s, "direction_%d ", acl->direction);
      break;
    }

  if (acl->proto == (u8)~0)
    s = format (s, "ip ");
  else
    s = format (s, "%d ", acl->proto);

  s = format (s, "from %U ", upf_format_ipfilter_address_port, acl,
              UPF_ACL_FIELD_SRC, ACL_IN);
  s = format (s, "to %U ", upf_format_ipfilter_address_port, acl,
              UPF_ACL_FIELD_DST, ACL_OUT);

  if (acl->app_index != ~0)
    s = format (s, "  app_index %u", acl->app_index);

  return s;
}

u8 *upf_ipfilter_encode (u8 *s, const acl_rule_t *acl)
{
  switch (acl->action)
    {
    case ACL_PERMIT:
      s = format (s, "permit ");
      break;

    case ACL_DENY:
      s = format (s, "deny ");
      break;

    default:
      s = format (s, "action_%d ", acl->action);
      break;
    }

  switch (acl->direction)
    {
    case ACL_IN:
      s = format (s, "in ");
      break;

    case ACL_OUT:
      s = format (s, "out ");
      break;

    default:
      s = format (s, "direction_%d ", acl->direction);
      break;
    }

  if (acl->proto == (u8)~0)
    s = format (s, "ip ");
  else
    s = format (s, "%d ", acl->proto);

  s = format (s, "from %U ", upf_format_ipfilter_address_port, acl,
              UPF_ACL_FIELD_SRC, ACL_IN);
  s = format (s, "to %U ", upf_format_ipfilter_address_port, acl,
              UPF_ACL_FIELD_DST, ACL_OUT);

  return s;
}

/*************************************************************************/

/* message helpers */
#define GET_1BIT(x, n) ((x >> n) & 0x1)
#define SET_1BIT(x, n) (x = (x | (0x1 << n)))

int
upf_pfcp_teid_range_allocate (upf_upip_res_t *res)
{
  // upf_main_t *gtm = &g_upf_main;
  int i;
  u8 teid_range_max;
  u8 teid_range;

  teid_range_max = pow (2, res->teidri) - 1;
  if (teid_range_max > 127)
    {
      upf_err ("pfcp: teid_range_max:%d\n", teid_range_max);
      return -1;
    }
  for (i = 0; i < teid_range_max + 1; i++)
    {
      teid_range = res->teid_range_acct % (teid_range_max + 1);
      res->teid_range_acct++;

      if (teid_range < 64)
        {
          if (0 == GET_1BIT (res->teid_range_status_low, teid_range))
            {
              SET_1BIT (res->teid_range_status_low, teid_range);
              break;
            }
        }
      else
        {
          if (0 == GET_1BIT (res->teid_range_status_high, (teid_range - 64)))
            {
              SET_1BIT (res->teid_range_status_high, (teid_range - 64));
              break;
            }
        }
    }

  if ((teid_range_max + 1) == i)
    {
      upf_err ("@@@ERROR The number of TEID range allocated overflow!");
      return -1;
    }
  return teid_range;
}

int upf_build_user_plane_ip_resource_information (pfcp_user_plane_ip_resource_information_t **upip)
{
  upf_main_t *gtm = &g_upf_main;
  upf_upip_res_t *res;
  int teid_range;

  /* *INDENT-OFF* */
  pool_foreach (res, gtm->upip_res, ({
      vec_alloc (*upip, 1);
      pfcp_user_plane_ip_resource_information_t *r = vec_end (*upip);

      if (res->nwi != ~0)
        {
          if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, res->nwi)))
          {
              upf_nwi_t *nwi = pool_elt_at_index (gtm->nwis, res->nwi);
              r->flags |= USER_PLANE_IP_RESOURCE_INFORMATION_ASSONI;
              r->network_instance = vec_dup (nwi->name);
          }
        }

      if (res->intf != (u8)~0)
        {
          r->flags |= USER_PLANE_IP_RESOURCE_INFORMATION_ASSOSI;
          r->source_intf = res->intf;
        }

      if (res->teidri != 0)
        {
          r->teid_range_indication = res->teidri;
          teid_range = upf_pfcp_teid_range_allocate (res);
          if (teid_range < 0)
            goto fail;
          r->teid_range = teid_range;
        }

      if (!is_zero_ip4_address (&res->ip4))
        {
          r->flags |= USER_PLANE_IP_RESOURCE_INFORMATION_V4;
          r->ip4 = res->ip4;
        }

      if (!is_zero_ip6_address (&res->ip6))
        {
          r->flags |= USER_PLANE_IP_RESOURCE_INFORMATION_V6;
          r->ip6 = res->ip6;
        }

      _vec_len (*upip)++;
    }));
    /* *INDENT-ON* */
  return 0;

fail:
  return -1;
}

/* message handlers */

static int upf_heartbeat_request_handle (sx_msg_t *req, pfcp_heartbeat_request_t *msg)
{
  int r = 0;
  sx_server_main_t *sx = &sx_server_main;
  pfcp_simple_response_t resp;
  upf_main_t *gtm = &g_upf_main;
  upf_node_assoc_t *node;

  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->recv_heartbeat.req_times++;


  vlib_node_increment_counter (vlib_get_main (), sx_api_process_node.index,
                               SX_PROCESS_HEARTBEAT_REQUEST, 1);

  memset (&resp, 0, sizeof (resp));
  SET_BIT (resp.grp.fields, PFCP_RESPONSE_RECOVERY_TIME_STAMP);
  resp.response.recovery_time_stamp = sx->start_time;

  upf_debug ("PFCP: start_time: %p, %d, %x.", &sx, sx->start_time,
             sx->start_time);
  /* *INDENT-OFF* */
  pool_foreach (node, gtm->nodes, ({
                  if (msg->recovery_time_stamp == node->recovery_time_stamp)
                  {
                      //n4_statistics->send_heartbeat.rsp_times++;
                      r = upf_pfcp_response_send (req, 0, PFCP_HEARTBEAT_RESPONSE,
                                              &resp.grp);

                      // Update N4 link status when heartbeat request is received
                      // added by caozhongwei 2025-07-11
                      upf_check_n4_link_status(gtm, node, 1 /* heartbeat received */);
                  }
                }));
  /* *INDENT-ON* */

  if (r == 0)
  {
      //n4_statistics->recv_heartbeat.handle_succ_times++;
      // smf and upf between heartbeat msg  not send to vpp-agent
      //upf_pfcp_events_publish (PFCP_RPC_PUBLISH_HEARTBEAT_REQUEST, NULL, msg);
  }
  else
  {
      //n4_statistics->recv_heartbeat.handle_fail_times++;
  }

  return 0;
}

static int
upf_heartbeat_response_handle (sx_msg_t *req, pfcp_simple_response_t *msg)
{
  upf_main_t *gtm = &g_upf_main;
  upf_node_assoc_t *n;

  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->recv_heartbeat.rsp_times++;

  vlib_node_increment_counter (vlib_get_main (), sx_api_process_node.index,
                               SX_PROCESS_HEARTBEAT_RESPONSE, 1);

  if (req->node == ~0 || pool_is_free_index (gtm->nodes, req->node))
    return -1;
  if (req->node == ~0 || pool_is_free_index (gtm->nodes, req->node))
    return -1;

  CHECK_POOL_IS_VALID_RET(gtm->nodes, req->node, -1);
  n = pool_elt_at_index (gtm->nodes, req->node);

  if (msg->response.recovery_time_stamp > n->recovery_time_stamp)
    {
      /*3GPP TS 23.007, Sect. 19A:
      If the value of a Recovery Time Stamp previously stored for a peer PFCP
      entity is smaller than the Recovery Time Stamp value received in the
      Heartbeat Request or Response messages, this indicates that the entity
      that sent the Heartbeat Request or Response messages has restarted. The
      received, new Recovery Time Stamp value shall be stored by the receiving
      entity, replacing the value previously stored for the peer PFCP entity.
        */
      // upf_pfcp_release_association(n);
      n->recovery_time_stamp = msg->response.recovery_time_stamp;
    }
  else if (msg->response.recovery_time_stamp < n->recovery_time_stamp)
    {
      /* 3GPP TS 23.007, Sect. 19A:
       *
       * If the value of a Recovery Time Stamp previously stored for a peer is
       * larger than the Recovery Time Stamp value received in the Heartbeat
       * Response message or the PFCP message, this indicates a possible race
       * condition (newer message arriving before the older one). The received
       * Sx node related message and the received new Recovery Time Stamp value
       * shall be discarded and an error may be logged.
       */
      upf_warn ("n->recovery_time_stamp:%U\n", upf_format_time_stamp,
                &n->recovery_time_stamp);
      // return -1;
    }
  else if (!gtm->heart_beat_disable)
    {
      if (n->HB_timer.period != gtm->pfcp_hb_interval)
        {
          upf_pfcp_server_timer_stop (n->HB_timer.handle);
          n->HB_timer.base = unix_time_now ();
          n->HB_timer.period = gtm->pfcp_hb_interval;
        }
      else
        {
          n->HB_timer.base += n->HB_timer.period;
        }
      upf_pfcp_server_timer_start (PFCP_SERVER_HB_TIMER, n - gtm->nodes,
                                   &n->HB_timer);
    }

    // smf and upf between heartbeat msg  not send to vpp-agent
    //upf_pfcp_events_publish (PFCP_RPC_PUBLISH_HEARTBEAT_RESPONSE, NULL, msg);

  return 0;
}

static int
upf_pfd_management_request_handle (sx_msg_t *req, pfcp_pfd_management_request_t *msg)
{
  pfcp_simple_response_t resp;
  upf_main_t *gtm = &g_upf_main;
  pfcp_application_id_pfds_t *aip;
  upf_pfd_ctx_t *upc = NULL;
  upf_app_id_pfd_ctx_t *aipc = NULL;
  pfcp_pfd_contents_t *ppc = NULL;
  uword *p = NULL;
  u8 *tmp_id = NULL;
  memset (&resp, 0, sizeof (resp));
  SET_BIT (resp.grp.fields, PFCP_RESPONSE_CAUSE);
  resp.response.cause = PFCP_CAUSE_REQUEST_REJECTED;

  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->pfd_managenent.req_times++;

  vec_foreach (aip, msg->application_id_pfds)
  {
    if (aip->application_id == NULL)
      continue;
    p = hash_get_mem (gtm->hash_app_by_appname, aip->application_id);
    if (p)
      {
        CHECK_POOL_IS_VALID_CONTINUE(gtm->pfd_list, p[0]);
        aipc = pool_elt_at_index (gtm->pfd_list, p[0]);
        vec_foreach (upc, aipc->pfd_contents)
        {
          vec_free (upc->url);
          vec_free (upc->dn);
          vec_free (upc->ct);
          memset (&upc->fd_filter, 0, sizeof (upc->fd_filter));
        }
        pool_free (aipc->pfd_contents);
      }
    else
      pool_get (gtm->pfd_list, aipc);
    memset (aipc, 0, sizeof (*aipc));
    aipc->app_name = vec_dup (aip->application_id);
    pfcp_pfd_t *pfd_context;
    vec_foreach (pfd_context, aip->pfd)
      {
        vec_foreach (ppc, pfd_context->pfd_contents)
        {
          pool_get (aipc->pfd_contents, upc);
          memset (upc, 0, sizeof (*upc));
          upc->flags = ppc->flags;
          ppc->flow_description_ha = vec_dup (ppc->flow_description);
          unformat_input_t fd;
          unformat_init_vector (&fd, ppc->flow_description);
          if (!upf_unformat_ipfilter (&fd, &upc->fd_filter))
            {
              upf_debug ("failed to parse pfd.fd '%v'", ppc->flow_description);
              break;
            }
          upc->fd_filter.app_index = aipc - gtm->pfd_list;
          upc->url = vec_dup (ppc->url);
          vec_add1 (upc->url, 0);
          upc->dn = vec_dup (ppc->domain);
          vec_add1 (upc->dn, 0);
          upc->ct = vec_dup (ppc->custom);
          vec_add1 (upc->ct, 0);
        }
      }
    if (!p)
      {
        tmp_id = vec_dup (aip->application_id);
        if (tmp_id)
          hash_set_mem (gtm->hash_app_by_appname, tmp_id, aipc - gtm->pfd_list);
      }
  }
  int r = upf_hs_database_update_global ();
  if (r == 0)
    {
      //n4_statistics->pfd_managenent.handle_succ_times++;
      resp.response.cause = PFCP_CAUSE_REQUEST_ACCEPTED;
      upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_PFD_MANAGEMENT, NULL, msg);
    }
  else
  {
      //n4_statistics->pfd_managenent.handle_fail_times++;
      //n4_statistics->pfd_managenent.cause_times[resp.response.cause]++;
  }
  //n4_statistics->pfd_managenent.rsp_times++;
  upf_pfcp_response_send (req, 0, PFCP_PFD_MANAGEMENT_RESPONSE, &resp.grp);

  return 0;
}

static int upf_asso_setup_alarm_handle (upf_node_assoc_t *n)
{
    upf_main_t *gtm = &g_upf_main;

    uword *p = hash_get_mem (gtm->asso_alarm_index, &n->node_id.ip);
    u32 status = 0;
    upf_asso_alarm_t *asso_alarm_status;
    if (p)
    {
        if (PREDICT_FALSE(pool_is_free_index(gtm->asso_alarm_status_list, p[0])))
        {
            upf_err ("PFCP: asso_alarm_index %u may be invalid!", p[0]);
            return 0;
        }
        asso_alarm_status = pool_elt_at_index (gtm->asso_alarm_status_list, p[0]);
        if (asso_alarm_status)
          status = asso_alarm_status->status;
    }
    else
    {
        pool_get (gtm->asso_alarm_status_list, asso_alarm_status);
        asso_alarm_status->remote_ip = n->node_id.ip;
        hash_set_mem_alloc (&gtm->asso_alarm_index, &asso_alarm_status->remote_ip, asso_alarm_status - gtm->asso_alarm_status_list);
        status = UPF_ALARM_TYPE_RECOVER;
    }
    if (status == UPF_ALARM_TYPE_PRODUCE)
    {
        asso_alarm_status->status = UPF_ALARM_TYPE_RECOVER;
        upf_alarm_msg_t *upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
        memset (upf_alarm, 0, sizeof (*upf_alarm));

        upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(pfcp_node_id_t), CLIB_CACHE_LINE_BYTES);
        memset (upf_alarm->data, 0, sizeof(pfcp_node_id_t));
        upf_alarm->alarm_id = UPF_ALARM_ASSOCIATION_TIMEOUT;
        upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
        upf_alarm->alarm_level = UPF_ALARM_LEVEL_SERIOUS;
        memcpy(upf_alarm->data, &n->node_id, sizeof(pfcp_node_id_t));

        for (int i = 0; i < gtm->num_pfcp_threads; i++)
        {
            pfcp_send_sx_alarm_to_thread(i, upf_alarm);
        }
    }
    return 0;
}

static int upf_association_setup_request_handle (sx_msg_t *req, pfcp_association_setup_request_t *msg)
{
  upf_main_t *gtm = &g_upf_main;
  pfcp_association_setup_response_t resp = {0};
  upf_node_assoc_t *n = NULL;
  int r = -1;

  upf_err ("Association Setup Req received, CP Node:%U", upf_format_node_id, &msg->request.node_id);
  //add for upf link status by zhangxin on 2022-11-18 below
  if (msg->request.node_id.ip.ip4.as_u32 != 0)
    {
      g_upf_relate_IpAddr[1].dstIp4Addr = msg->request.node_id.ip.ip4;
    }
  else if (msg->request.node_id.ip.ip6.as_u32[0] != 0)
    {
      g_upf_relate_IpAddr[1].dstIp6Addr = msg->request.node_id.ip.ip6;
    }
  //add for upf link status by zhangxin on 2022-11-18 above

  u32 is_create = 0;
  n = upf_get_association (&msg->request.node_id);
  if (n)
    {
        UPF_STATISTICS_ADD(ASSO_EXSITED);
      //if (n->recovery_time_stamp != msg->recovery_time_stamp)
        {
          if (1 != g_upf_main.context_release_disable)
              upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_DEL_ASSO, n, msg);
          if (0 == upf_pfcp_release_association (n))
            {
              is_create = 1; /* release success, create a new one */

              /* 3GPP TS 23.007, Sect. 19A:
               * A PFCP function that receives a PFCP Association Setup Request shall proceed with:
               * -   establishing the PFCP association and
               * -   deleting the existing PFCP association and associated PFCP sessions,
               * if a PFCP association was already established for the Node ID received
               * in the request, regardless of the Recovery Timestamp received in the request.
               */
              upf_warn ("The node(%U) context exist, release it.", upf_format_node_id, &n->node_id);
            }
        }
    }
  else
    is_create = 1;

  if (is_create)
    {
      /* alloc instance for rebuild association only */
      n = upf_new_association (req->fib_index, &req->lcl.address, &req->rmt.address, &msg->request.node_id);
      clib_spinlock_init (&n->lock);
    }
    n->recovery_time_stamp = msg->recovery_time_stamp;
    n->up_recovery_time_stamp = sx_server_main.start_time;


  if (ISSET_BIT (msg->grp.fields, ASSOCIATION_SETUP_REQUEST_CP_FUNCTION_FEATURES))
      n->cp_feature = msg->cp_function_features;

  /* The code shall be removed because this IE has been removed in r16, F-TEID shall be alloced by UP-Function */
#if 1
  r = upf_build_user_plane_ip_resource_information (&resp.user_plane_ip_resource_information);
  if (r)
    {
      UPF_STATISTICS_ADD(BUILD_UP_RS_FAILED);
      upf_err ("PFCP: upf_build_user_plane_ip_resource_information fail");
      goto out_send_resp;
    }
  else
    {
      SET_BIT (resp.grp.fields, ASSOCIATION_SETUP_RESPONSE_USER_PLANE_IP_RESOURCE_INFORMATION);

      n->user_plane_ip_resource_information = vec_dup (resp.user_plane_ip_resource_information);
      size_t i;
      vec_foreach_index (i, resp.user_plane_ip_resource_information)
        {
          vec_elt_at_index (n->user_plane_ip_resource_information, i)->network_instance =
                   vec_dup (vec_elt (resp.user_plane_ip_resource_information, i).network_instance);
        }
    }
#endif

    upf_asso_setup_alarm_handle(n);

out_send_resp:
  SET_BIT (resp.grp.fields, ASSOCIATION_SETUP_RESPONSE_CAUSE);
  SET_BIT (resp.grp.fields, ASSOCIATION_SETUP_RESPONSE_NODE_ID);
  r = upf_pfcp_init_response_node_id (req, &resp.response);

  if (r == 0)
    {
      resp.response.cause = PFCP_CAUSE_REQUEST_ACCEPTED;

      SET_BIT (resp.grp.fields, ASSOCIATION_SETUP_RESPONSE_RECOVERY_TIME_STAMP);
      resp.recovery_time_stamp = n->up_recovery_time_stamp;
      SET_BIT (resp.grp.fields, ASSOCIATION_SETUP_RESPONSE_UP_FUNCTION_FEATURES);
      resp.up_function_features = gtm->upf_features;

      upf_ue_ip_pool_t *ip_pool = NULL;
      vec_foreach (ip_pool, gtm->ip_pools)
      {
         if (vec_len(ip_pool->pool_id))
         {
             SET_BIT (resp.grp.fields, ASSOCIATION_SETUP_RESPONSE_UE_IP_ADDRESS_POOL_INFORMATION);
             vec_alloc (resp.ue_ip_address_pool_information, 1);
             pfcp_ue_ip_address_pool_information_t *pool_info = vec_end (resp.ue_ip_address_pool_information);
             SET_BIT (pool_info->grp.fields, UE_IP_ADDRESS_POOL_IDENTITY);
              vec_add1 (pool_info->pool_identity, (pfcp_ue_ip_address_pool_identity_t)ip_pool->pool_id);
             if (vec_len (ip_pool->nwi))
             {
                 SET_BIT (pool_info->grp.fields, POOL_NETWORK_INSTANCE);
                 pool_info->network_instance = vec_dup (ip_pool->nwi);
             }
             _vec_len (resp.ue_ip_address_pool_information)++;
         }
      }

      if (!gtm->heart_beat_disable)
        {
          n->HB_timer.base = unix_time_now ();
          n->HB_timer.period = gtm->pfcp_hb_interval;
          upf_pfcp_server_timer_start (PFCP_SERVER_HB_TIMER, n - gtm->nodes, &n->HB_timer);
        }

      upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_ADD_ASSO, n, msg);
    }
  else
    resp.response.cause = PFCP_CAUSE_REQUEST_REJECTED;

  upf_pfcp_response_send (req, 0, PFCP_ASSOCIATION_SETUP_RESPONSE, &resp.grp);

  return r;
}

static int upf_association_setup_response_handle (sx_msg_t *req, pfcp_association_setup_response_t *msg)
{
  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->association_setup.rsp_times++;

  upf_node_assoc_t *n;

  if (!(msg->response.cause == PFCP_CAUSE_REQUEST_ACCEPTED))
    {
      upf_err ("pfcp association fail, cuase=%d", msg->response.cause);
      return -1;
    }
  n = upf_get_association (&msg->response.node_id);
  if (n)
    {
      if (1 != g_upf_main.context_release_disable)
          upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_DEL_ASSO, n, msg);
      upf_pfcp_release_association (n);
      /* 3GPP TS 23.007, Sect. 19A:
       *
       A PFCP function that receives a PFCP Association Setup Request shall
       proceed with:
       -   establishing the PFCP association and
       -   deleting the existing PFCP association and associated PFCP sessions,
       if a PFCP association was already established for the Node ID received
       in the request, regardless of the Recovery Timestamp received in the
       request.

       */
      upf_warn ("The node(%U) context exist, release it.", upf_format_node_id,
                &n->node_id);
    }

  n = upf_new_association (req->fib_index, &req->lcl.address, &req->rmt.address,
                          &msg->response.node_id);
  clib_spinlock_init (&n->lock);
  n->recovery_time_stamp = msg->recovery_time_stamp;
  if (ISSET_BIT (msg->grp.fields,
                 ASSOCIATION_SETUP_RESPONSE_CP_FUNCTION_FEATURES))
    {
      n->cp_feature = msg->cp_function_features;
    }

  if (upf_build_user_plane_ip_resource_information (
          &n->user_plane_ip_resource_information))
    {
      upf_warn ("PFCP: upf_build_user_plane_ip_resource_information fail");
    }

  if (!g_upf_main.heart_beat_disable)
    {
      n->HB_timer.base = unix_time_now ();
      n->HB_timer.period = g_upf_main.pfcp_hb_interval;
      upf_pfcp_server_timer_start (PFCP_SERVER_HB_TIMER, n - g_upf_main.nodes,
                                   &n->HB_timer);
    }

  return 0;
}

static int upf_association_update_request_handle (sx_msg_t *req, pfcp_association_update_request_t *msg)
{
  upf_main_t *gtm = &g_upf_main;
  pfcp_association_update_response_t resp;
  upf_node_assoc_t *n;
  int r = 0;

  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->association_update.req_times++;

  upf_err ("Association Update Request message received! The CP Node is (%U)", upf_format_node_id,
    &msg->request.node_id);

  memset (&resp, 0, sizeof (resp));
  SET_BIT (resp.grp.fields, ASSOCIATION_UPDATE_RESPONSE_CAUSE);
  resp.response.cause = PFCP_CAUSE_REQUEST_REJECTED;

  SET_BIT (resp.grp.fields, ASSOCIATION_UPDATE_RESPONSE_NODE_ID);
  r = upf_pfcp_init_response_node_id (req, &resp.response);
  if (r)
      goto out_send_resp;

  n = upf_get_association (&msg->request.node_id);
  if (!n)
    {
      upf_warn ("The node(%U) context not exist.", upf_format_node_id,
                &msg->request.node_id);
      r = -1;
      goto out_send_resp;
    }

  if (ISSET_BIT (msg->grp.fields, ASSOCIATION_UPDATE_REQUEST_CP_FUNCTION_FEATURES))
    {
      n->cp_feature = msg->cp_function_features;
      upf_warn ("The node(%U) update cp_feature:%U", upf_format_node_id, &msg->request.node_id,
        upf_format_cp_function_features, &n->cp_feature);
    }

  SET_BIT (resp.grp.fields, ASSOCIATION_UPDATE_RESPONSE_UP_FUNCTION_FEATURES);
  /* currently no optional features are supported */
  resp.up_function_features = gtm->upf_features;

out_send_resp:
  if (r == 0)
    {
      //n4_statistics->association_update.handle_succ_times++;
      resp.response.cause = PFCP_CAUSE_REQUEST_ACCEPTED;
      upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_ADD_ASSO, n, msg);
    }
  else
  {
      //n4_statistics->association_update.handle_fail_times++;
      //n4_statistics->association_update.cause_times[resp.response.cause]++;
  }
  upf_debug ("PFCP: cause:%d", resp.response.cause);

  //n4_statistics->association_update.rsp_times++;
  upf_pfcp_response_send (req, 0, PFCP_ASSOCIATION_UPDATE_RESPONSE, &resp.grp);

  return r;
}

static int upf_association_update_response_handle (sx_msg_t *req, pfcp_association_update_response_t *msg)
{
  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->association_update.rsp_times++;
  return -1;
}

static int upf_association_release_request_handle (sx_msg_t *req, pfcp_association_release_request_t *msg)
{
  pfcp_simple_response_t resp;
  upf_node_assoc_t *n;
  int r = 0;

  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->association_release.req_times++;

  upf_err ("Association Release Request message received! The CP Node is (%U) .", upf_format_node_id,
    &msg->request.node_id);

  memset (&resp, 0, sizeof (resp));
  SET_BIT (resp.grp.fields, PFCP_RESPONSE_CAUSE);
  resp.response.cause = PFCP_CAUSE_REQUEST_REJECTED;

  SET_BIT (resp.grp.fields, ASSOCIATION_RELEASE_RESPONSE_NODE_ID);
  r = upf_pfcp_init_response_node_id (req, &resp.response);
  if (r)
      goto out_send_resp;

  n = upf_get_association (&msg->request.node_id);
  if (n)
    {
      if (1 != g_upf_main.context_release_disable)
          upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_DEL_ASSO, n, msg);
      upf_pfcp_release_association (n);
    }
  else
    {
      upf_err ("PFCP: Association not found!");
      r = -1;
    }

out_send_resp:
  if (r == 0)
    {
      //n4_statistics->association_release.handle_succ_times++;
      resp.response.cause = PFCP_CAUSE_REQUEST_ACCEPTED;
    }
  else
  {
      //n4_statistics->association_release.handle_fail_times++;
      //n4_statistics->association_release.cause_times[resp.response.cause]++;
  }
  upf_debug ("PFCP: cause:%d", resp.response.cause);

  //n4_statistics->association_release.rsp_times++;
  upf_pfcp_response_send (req, 0, PFCP_ASSOCIATION_RELEASE_RESPONSE,
                          &resp.grp);

  return r;
}

static int upf_association_release_response_handle (sx_msg_t *req,
                                     pfcp_simple_response_t *msg)
{
  //u32 cpu_index = os_get_thread_index ();
  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
  //n4_statistics->association_release.rsp_times++;
  return -1;
}

static int upf_pfcp_node_report_response_handle (sx_msg_t *req, pfcp_simple_response_t *msg)
{
  return -1;
}

static void upf_pfcp_simple_repsonse_send (sx_msg_t *req, u64 seid, u8 type, pfcp_cause_t cause, pfcp_offending_ie_t *err)
{
  sx_server_main_t *sx = &sx_server_main;
  pfcp_simple_response_t resp;

  memset (&resp, 0, sizeof (resp));
  SET_BIT (resp.grp.fields, PFCP_RESPONSE_CAUSE);
  resp.response.cause = cause;
  upf_debug ("pfcp decode err, cause=%d", resp.response.cause);
  switch (type)
    {
    case PFCP_HEARTBEAT_RESPONSE:
    case PFCP_PFD_MANAGEMENT_RESPONSE:
    case PFCP_SESSION_MODIFICATION_RESPONSE:
    case PFCP_SESSION_DELETION_RESPONSE:
    case PFCP_SESSION_REPORT_RESPONSE:
      break;

    default:
      upf_pfcp_init_response_node_id (req, &resp.response);
      break;
    }

  switch (type)
    {
    case PFCP_HEARTBEAT_RESPONSE:
    case PFCP_ASSOCIATION_SETUP_RESPONSE:
      SET_BIT (resp.grp.fields, PFCP_RESPONSE_RECOVERY_TIME_STAMP);
      resp.response.recovery_time_stamp = sx->start_time;
      break;

    default:
      break;
    }

  if (vec_len (err) != 0)
    {
      SET_BIT (resp.grp.fields, PFCP_RESPONSE_OFFENDING_IE);
      resp.response.offending_ie = err[0];
    }

  upf_pfcp_response_send (req, seid, type, &resp.grp);
}

static int
upf_pfcp_node_msg (sx_msg_t *msg)
{
  union
  {
    struct pfcp_group grp;
    pfcp_simple_response_t simple_response;
    pfcp_heartbeat_request_t heartbeat_request;
    pfcp_pfd_management_request_t pfd_management_request;
    pfcp_association_setup_request_t association_setup_request;
    pfcp_association_setup_response_t association_setup_response;
    pfcp_association_update_request_t association_update_request;
    pfcp_association_update_response_t association_update_response;
    pfcp_association_release_request_t association_release_request;
    pfcp_node_report_request_t node_report_request;
  } m;
  pfcp_offending_ie_t *err = NULL;
  int r = 0;

  if (msg->hdr->s_flag)
    {
      UPF_STATISTICS_ADD(INVALID_SESS_FLAG);
      upf_err ("PFCP: node msg with SEID.");
      return -1;
    }

  memset (&m, 0, sizeof (m));
  r = upf_pfcp_decode_msg (msg->hdr->type, &msg->hdr->msg_hdr.ies[0],
                       clib_net_to_host_u16 (msg->hdr->length) -
                           sizeof (msg->hdr->msg_hdr),
                       &m.grp, &err);
  if (r != 0)
    {
      UPF_STATISTICS_ADD(DOCODE_PFCP_NODE_MSG_FAILURE);
      upf_info ("PFCP: Decode pfcp message fail! message type: %d, cause=%d",
                msg->hdr->type, r);
      switch (msg->hdr->type)
        {
        case PFCP_HEARTBEAT_REQUEST:
        case PFCP_PFD_MANAGEMENT_REQUEST:
        case PFCP_ASSOCIATION_SETUP_REQUEST:
        case PFCP_ASSOCIATION_UPDATE_REQUEST:
        case PFCP_ASSOCIATION_RELEASE_REQUEST:
            upf_pfcp_simple_repsonse_send (msg, 0, msg->hdr->type + 1, r, err);
            break;

        default:
          break;
        }

      upf_pfcp_free_msg (msg->hdr->type, &m.grp);
      vec_free (err);
      return r;
    }

  switch (msg->hdr->type)
    {
    case PFCP_HEARTBEAT_REQUEST:
      r = upf_heartbeat_request_handle (msg, &m.heartbeat_request);
      break;

    case PFCP_HEARTBEAT_RESPONSE:
      r = upf_heartbeat_response_handle (msg, &m.simple_response);
      break;

    case PFCP_PFD_MANAGEMENT_REQUEST:
      r = upf_pfd_management_request_handle (msg, &m.pfd_management_request);
      break;

    case PFCP_ASSOCIATION_SETUP_REQUEST:
      r = upf_association_setup_request_handle (msg, &m.association_setup_request);
      break;

    case PFCP_ASSOCIATION_SETUP_RESPONSE:
      r = upf_association_setup_response_handle (msg, &m.association_setup_response);
      break;

    case PFCP_ASSOCIATION_UPDATE_REQUEST:
      r = upf_association_update_request_handle (msg, &m.association_update_request);
      break;

    case PFCP_ASSOCIATION_UPDATE_RESPONSE:
      r = upf_association_update_response_handle (msg, &m.association_update_response);
      break;

    case PFCP_ASSOCIATION_RELEASE_REQUEST:
      r = upf_association_release_request_handle (msg, &m.association_release_request);
      break;

    case PFCP_ASSOCIATION_RELEASE_RESPONSE:
      r = upf_association_release_response_handle (msg, &m.simple_response);
      break;

    case PFCP_NODE_REPORT_RESPONSE:
      r = upf_pfcp_node_report_response_handle (msg, &m.simple_response);
      break;

    default:
      break;
    }

  upf_pfcp_free_msg (msg->hdr->type, &m.grp);
  return 0;
}

#define OPT(MSG, FIELD, VALUE, DEFAULT) \
  ((ISSET_BIT ((MSG)->grp.fields, (FIELD))) ? MSG->VALUE : (DEFAULT))

upf_nwi_t *upf_lookup_nwi (u8 *name)
{
  upf_main_t *gtm = &g_upf_main;
  uword *p;

  if (pool_elts (gtm->nwis) == 0)
    return NULL;

  p = hash_get_mem (gtm->nwi_index_by_name, name);
  if (!p)
    return NULL;

  CHECK_POOL_IS_VALID_RET(gtm->nwis, p[0], NULL);
  return pool_elt_at_index (gtm->nwis, p[0]);
}

upf_nwi_t *upf_lookup_nwi_by_s_nssai (u32 s_nssai)
{
    upf_main_t *gtm = &g_upf_main;
    upf_nwi_t *nwi = NULL;

    if (pool_elts (gtm->nwis) == 0)
    {
        return NULL;
    }

    /* *INDENT-OFF* */
    pool_foreach (nwi, gtm->nwis, ({
                  if (nwi->s_nssai == s_nssai)
                  {
                      return nwi;
                  }
                }));
    /* *INDENT-ON* */

    return NULL;
}


int upf_string_len_to_delimiter(char * string, u8 delimiter)
{
    char *p;

    p = string;

    while (*p != 0 && *p != delimiter)
        p++;
    return p - string;
}

int upf_alloc_teid_by_up (upf_session_t *sess, struct pfcp_group *grp,
                  pfcp_create_pdr_t *pdr, upf_pdr_t *create, int estab_flag)
{
  u8 flag = 0;
  upf_upip_res_t *res;
  pfcp_created_pdr_t rep_created_pdr;
  upf_main_t *um = &g_upf_main;
  pfcp_session_establishment_response_t *establishment_resp = NULL;
  pfcp_session_modification_response_t *modification_resp = NULL;
  uword *p;
  memset (&rep_created_pdr, 0, sizeof (pfcp_created_pdr_t));

  pool_foreach (res, um->upip_res, ({
                  if (ISSET_BIT (pdr->pdi.grp.fields, PDI_NETWORK_INSTANCE))
                    {
                      if (pdr->pdi.network_instance)
                        {
                          p = hash_get_mem (um->nwi_index_by_name,
                                            pdr->pdi.network_instance);
                          if (!p || (res->nwi != p[0]))
                            continue;
                        }
                    }
                  upf_trace ("pdr->pdi.source_interface:%d res->intf:%d",
                             pdr->pdi.source_interface, res->intf);
                  if (pdr->pdi.source_interface == res->intf ||
                      res->intf == (u8)~0)
                    {
                      if (!is_zero_ip4_address (&res->ip4))
                        {
                          create->pdi.teid.flags |= F_TEID_V4;
                          create->pdi.teid.ip4 = res->ip4;
                          flag = 1;
                          break;
                        }

                      if (!is_zero_ip6_address (&res->ip6))
                        {
                          create->pdi.teid.flags |= F_TEID_V6;
                          create->pdi.teid.ip6 = res->ip6;
                          flag = 1;
                          break;
                        }
                    }
                }));
  if (!flag)
    {
      upf_info ("Can't find the gtpu endpoint IP address, %U\n", upf_format_f_teid, &pdr->pdi.f_teid);
      return -2;
    }
  else
    {
      if (pdr->pdi.f_teid.flags & F_TEID_CHID)
        {
          p = hash_get (sess->hash_teid_by_chooseid,
                        pdr->pdi.f_teid.choose_id);
          if (p)
          {
            create->pdi.teid.teid = p[0];
            create->pdi.teid.choose_id = pdr->pdi.f_teid.choose_id;
          }
        }
      if (!create->pdi.teid.teid)
        {
          clib_spinlock_lock (&sx_server_main.lock);
          create->pdi.teid.teid =
              (u32)clib_bitmap_first_clear (sx_server_main.bitmap_teid);
          clib_bitmap_set (sx_server_main.bitmap_teid, create->pdi.teid.teid,
                           1);
          clib_spinlock_unlock (&sx_server_main.lock);

          create->pdi.teid.choose_id = pdr->pdi.f_teid.choose_id;
          
          hash_set (sess->hash_teid_by_chooseid, pdr->pdi.f_teid.choose_id,
                    create->pdi.teid.teid);
        }

      if (um->stacking_switch)
      {
        for (int i = 0; i < UPF_STACKING_MAXNUM; i++)
        {
          if (um->stacking[i].is_used)
          {
              if (htonl(um->stacking[i].ueip.data_u32) >> (32 - um->stacking[i].uemask) == ntohl(create->pdi.ue_addr.ip4.data_u32) >> (32 - um->stacking[i].uemask))
              {
                create->pdi.teid.ip4 = um->stacking[i].n3ip;
                break;
              }
          }
        }
      }

      g_upf_main.teid_alloc_option = TEID_ALLOC_BY_UP;
      if (estab_flag == SESSION_ESTABLISHMENT_RESPONSE_FAILED_RULE_ID)
        {
          establishment_resp = (pfcp_session_establishment_response_t *)grp;
          SET_BIT (establishment_resp->grp.fields,
                   SESSION_ESTABLISHMENT_RESPONSE_CREATED_PDR);
          SET_BIT (rep_created_pdr.grp.fields, CREATED_PDR_PDR_ID);
          rep_created_pdr.pdr_id = create->id;
          SET_BIT (rep_created_pdr.grp.fields, CREATED_PDR_F_TEID);
          rep_created_pdr.f_teid = create->pdi.teid;
          vec_add1 (establishment_resp->created_pdr, rep_created_pdr);
        }
      else
        {
          modification_resp = (pfcp_session_modification_response_t *)grp;
          SET_BIT (modification_resp->grp.fields,
                   SESSION_MODIFICATION_RESPONSE_CREATED_PDR);
          SET_BIT (rep_created_pdr.grp.fields, CREATED_PDR_PDR_ID);
          rep_created_pdr.pdr_id = create->id;
          SET_BIT (rep_created_pdr.grp.fields, CREATED_PDR_F_TEID);
          rep_created_pdr.f_teid = create->pdi.teid;
          vec_add1 (modification_resp->created_pdr, rep_created_pdr);
        }
    }

  return 0;
}

int upf_alloc_ue_ip_by_up (upf_session_t *sess, struct pfcp_group *grp,
                   pfcp_create_pdr_t *pdr, upf_pdr_t *create, int estab_flag)
{
  pfcp_created_pdr_t rep_created_pdr;
  upf_nwi_t *nwi;
  upf_main_t *um = &g_upf_main;
  pfcp_session_establishment_response_t *establishment_resp = NULL;
  pfcp_session_modification_response_t *modification_resp = NULL;
  uword *p;
  upf_ue_ip_pool_t *ue_ip_pool;
  u32 fib_index;
  memset (&rep_created_pdr, 0, sizeof (pfcp_created_pdr_t));

  if (ISSET_BIT (pdr->pdi.grp.fields, PDI_NETWORK_INSTANCE))
    {

      p = hash_get_mem (um->nwi_index_by_name, pdr->pdi.network_instance);

      if (!p)
        {
          upf_info ("Can't find the network instance!");
          return -3;
        }

      CHECK_POOL_IS_VALID_RET(um->nwis, p[0], -3);
      nwi = pool_elt_at_index (um->nwis, p[0]);
      if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP4, nwi->vrf)))
        {
          if (~0 == (fib_index = fib_table_find (FIB_PROTOCOL_IP6, nwi->vrf)))
            {
              upf_info ("No the fib table relate to nwi!\n");
              return -3;
            }
        }

      p = hash_get (um->ip_pool_by_fib_indx, fib_index);
      if (!p)
        {
          upf_info ("Can't find the ue ip pool by fib_index: %d", fib_index);
          return -3;
        }
    }
  else
    {
      p = hash_get (um->ip_pool_by_fib_indx, 0);
      if (!p)
        {
          upf_info ("Can't find the ue ip pool by default fib_index");
          return -3;
        }
    }

  CHECK_POOL_IS_VALID_RET(um->ip_pools, p[0], -3);
  ue_ip_pool = pool_elt_at_index (um->ip_pools, p[0]);
  if (vec_len (pdr->pool_id) &&
      !vec_is_equal (ue_ip_pool->pool_id, *pdr->pool_id))
    {
      upf_info ("pool_id:%v selected by nwi is not equal pool_id:%v in pdr",
                ue_ip_pool->pool_id, *pdr->pool_id);
      // return -3;
    }

  clib_spinlock_lock (&ue_ip_pool->lock);

  if ((1 == ue_ip_pool->is_v4) &&
      (pdr->pdi.ue_ip_address.flags & IE_UE_IP_ADDRESS_CHV4))
    {
      uword bits;
      uword *p_ueip;
      p_ueip = hash_get (sess->hash_upip4_by_pool, p[0]);

      if (p_ueip)
        {
          create->pdi.ue_addr.ip4.as_u32 = p_ueip[0];
          upf_info ("sess up_seid:0x%lx, ue ip :%U has been allocated in "
                    "ip_pool: %d",
                    sess->up_seid, format_ip4_address,
                    &create->pdi.ue_addr.ip4, p[0]);
        }
      else
        {
          if (ue_ip_pool->size_v4 < (bits = clib_bitmap_first_clear (
                                         ue_ip_pool->bitmap_ue_ip4_pool)))
            {
              upf_info ("The UE IP pool is full! start %U size %u",
                        format_ip4_address, &ue_ip_pool->ip4_low,
                        ue_ip_pool->size_v4);
              clib_spinlock_unlock (&ue_ip_pool->lock);
              return -4;
            }

          clib_bitmap_set (ue_ip_pool->bitmap_ue_ip4_pool, bits, 1);

          create->pdi.ue_addr.ip4.as_u32 =
              ue_ip_pool->ip4_low.as_u32 + clib_host_to_net_u32 ((u32)bits);
          hash_set (sess->hash_upip4_by_pool, p[0],
                    create->pdi.ue_addr.ip4.as_u32);
          upf_info ("UE IP allocated by UPF: %U", format_ip4_address,
                    &create->pdi.ue_addr.ip4);
        }
      create->pdi.ue_addr.flags |= IE_UE_IP_ADDRESS_V4;
      create->pdi.ue_addr.flags &= ~IE_UE_IP_ADDRESS_CHV4;
    }
  if ((1 == ue_ip_pool->is_v6) &&
      (pdr->pdi.ue_ip_address.flags & IE_UE_IP_ADDRESS_CHV6))
    {
      uword bits;
      uword *p_ueip;
      p_ueip = hash_get (sess->hash_upip6_by_pool, p[0]);
      if (p_ueip)
        {

          create->pdi.ue_addr.ip6.as_u64[0] = p_ueip[0];
          create->pdi.ue_addr.ip6.as_u64[1] = ue_ip_pool->ip6_low.as_u64[1];
          upf_info ("sess up_seid:0x%lx, ue ip6 :%U has been allocated in "
                    "ip_pool: %d",
                    sess->up_seid, format_ip6_address,
                    &create->pdi.ue_addr.ip6, p[0]);
        }
      else
        {
          // upf_info("first:%u, get:%u,
          // pool_indx:%u",clib_bitmap_first_clear(ue_ip_pool->bitmap_ue_ip6_pool),clib_bitmap_get
          // (ue_ip_pool->bitmap_ue_ip6_pool,ue_ip_pool->ip6_low.as_u32),p[0]);
          if (ue_ip_pool->size_v6 < (bits = clib_bitmap_first_clear (
                                         ue_ip_pool->bitmap_ue_ip6_pool)))
            {
              upf_info ("The UE IP pool is full! Range from %U to %u",
                        format_ip6_address, &ue_ip_pool->ip6_low,
                        ue_ip_pool->size_v6);
              clib_spinlock_unlock (&ue_ip_pool->lock);
              return -4;
            }

          clib_bitmap_set (ue_ip_pool->bitmap_ue_ip6_pool, bits, 1);
          create->pdi.ue_addr.ip6.as_u64[0] =
              ue_ip_pool->ip6_low.as_u64[0] + clib_host_to_net_u64 (bits);
          create->pdi.ue_addr.ip6.as_u64[1] = ue_ip_pool->ip6_low.as_u64[1];
          hash_set (sess->hash_upip6_by_pool, p[0],
                    create->pdi.ue_addr.ip6.as_u64[0]);
          upf_info ("UE IP6 allocated by UPF: %U", format_ip6_address,
                    &create->pdi.ue_addr.ip6);
        }
      create->pdi.ue_addr.flags |= IE_UE_IP_ADDRESS_V6;
      create->pdi.ue_addr.flags &= ~IE_UE_IP_ADDRESS_CHV6;
    }
  clib_spinlock_unlock (&ue_ip_pool->lock);

  if (estab_flag == SESSION_ESTABLISHMENT_RESPONSE_FAILED_RULE_ID)
    {
      establishment_resp = (pfcp_session_establishment_response_t *)grp;
      SET_BIT (establishment_resp->grp.fields,
               SESSION_ESTABLISHMENT_RESPONSE_CREATED_PDR);
      SET_BIT (rep_created_pdr.grp.fields, CREATED_PDR_PDR_ID);
      rep_created_pdr.pdr_id = create->id;
      SET_BIT (rep_created_pdr.grp.fields, CREATED_PDR_UE_IP_ADDRESS);
      rep_created_pdr.ue_ip_address = create->pdi.ue_addr;
      vec_add1 (establishment_resp->created_pdr, rep_created_pdr);
    }
  else
    {
      modification_resp = (pfcp_session_modification_response_t *)grp;
      SET_BIT (modification_resp->grp.fields,
               SESSION_MODIFICATION_RESPONSE_CREATED_PDR);
      SET_BIT (rep_created_pdr.grp.fields, CREATED_PDR_PDR_ID);
      rep_created_pdr.pdr_id = create->id;
      SET_BIT (rep_created_pdr.grp.fields, CREATED_PDR_UE_IP_ADDRESS);
      rep_created_pdr.ue_ip_address = create->pdi.ue_addr;
      vec_add1 (modification_resp->created_pdr, rep_created_pdr);
    }

  return 0;
}

int upf_check_session_same_ueip(upf_session_t *sess)
{
    upf_main_t *gtm = &g_upf_main;

    if (sess == NULL)
    {
        upf_err ("The session is NULL!");
        return 0;
    }

    if (sess->flags & SX_UEIP_SET)
    {
        return 0; // nothing to do, avoid duplicate settings
    }

    uword *p = hash_get_mem(gtm->session_by_ueip, &sess->ue_address);
    if (p == NULL)
    {
        hash_set_mem (gtm->session_by_ueip, &sess->ue_address, sess - gtm->sessions);
        sess->flags |= SX_UEIP_SET;
        return 0;
    }

    upf_session_t *old_sess = sx_get_by_index((u32)p[0]);
    if (!old_sess)
    {
        sess->flags |= SX_UEIP_SET;
        return 0;
    }

    ulog_level_t lv = KEY_LOG_SWITCH(KL_SWITCH_UEIP_DUPLICATE) ? ULOG_LEVEL_FATAL : ULOG_LEVEL_ERR;
    ulog_log (lv, __FUNCTION__, __LINE__, "The same ueip %U has exist in session! duplicated switch is %U.", format_ip46_address,
                  &sess->ue_address, IP46_TYPE_ANY, format_duplicated_ueip_switch);
    if (g_upf_duplicated_ueip_delete == DUPLICATE_REPLACE)
    {
        hash_unset_mem (gtm->session_by_ueip, &sess->ue_address);
        hash_set_mem (gtm->session_by_ueip, &sess->ue_address, sess - gtm->sessions);

        old_sess->flags &= ~SX_UEIP_SET;
        sess->flags |= SX_UEIP_SET;

        upf_trace ("The same ueip delete info publish to vpp-agent.");
        upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_DEL_SESS, old_sess, NULL);
        if (upf_pfcp_session_disable (old_sess, false) != 0)
            upf_err ("The same ueip disable failed!");
        upf_pfcp_session_free (old_sess);

        return 0;
    }
    else //if (g_upf_duplicated_ueip_delete == DUPLICATE_REJECT)
    {
        upf_info ("The same ueip reject this session!");
        return -1;
    }
}

int upf_pfcp_acl_build(upf_pdi_t *pdi, pfcp_sdf_filter_t *sdf, acl_rule_t *acl)
{
    acl->app_index = ~0;

    if (sdf->flags & F_SDF_FD)
    {
        acl->flag = 1;
        unformat_input_t input;
        unformat_init_vector (&input, sdf->flow);
        if (!upf_unformat_ipfilter (&input, acl))
        {
            upf_warn ("failed to parse SDF '%s'", sdf->flow);
            return -1;
        }
        vec_free (input.buffer_marks);
        clib_memset (&input, 0, sizeof (input));
    }

    if (sdf->flags & F_SDF_TTC)
    {
        acl->tos = sdf->tos_traffic_class;
    }
    if (sdf->flags & F_SDF_BID)
    {
        acl->id = sdf->sdf_filter_id;
    }
    return 0;
}

static u32 upf_pfcp_pdr_appid_build (upf_pdr_t *pdr, uword index, u32 is_update, pfcp_application_id_t application_id)
{
  if (pool_is_free_index(g_upf_main.pfd_list, index))
    {
      upf_err ("pfd index:%llu is out of range!\n", index);
      return 1;
    }

  upf_app_id_pfd_ctx_t *pfd_list = pool_elt_at_index (g_upf_main.pfd_list, index);
  if (pfd_list->precedence)
    pdr->precedence = pfd_list->precedence;

  if (is_update)
    vec_reset_length (pdr->pdi.acl);

  upf_pfd_ctx_t *tmp_pfd;
  pool_foreach (tmp_pfd, pfd_list->pfd_contents, ({
                if (tmp_pfd->flags & F_PFD_C_FD)
                  vec_add1 (pdr->pdi.acl, tmp_pfd->fd_filter);
                if (tmp_pfd->flags & F_PFD_L7_MASK)
                    pdr->pdi.app_l7_rule = 1;
              }));

  pdr->pdi.fields |= F_PDI_APPLICATION_ID;
  pdr->pdi.app_index = index;

  /* neil.fan@20230104 note:
   * 1) create/update string only when the content modified;
   * 2) must free the old memory when the string updated to escape memory leak.
   */
  if (!vec_is_equal (pdr->pdi.app_name, application_id))
    {
      vec_free (pdr->pdi.app_name);
      pdr->pdi.app_name = vec_dup (application_id);
    }

  return 0;
}

static int upf_pfcp_add_pre_urr_to_sx (upf_session_t *sess, u32 urr_id, upf_urr_t *create_urr)
{
    struct rules *rules = upf_get_rules (sess, SX_PENDING);

    if (PREDICT_FALSE(!IS_PREDEF_RULE_ID(urr_id)))
    {
        upf_info ("pre-def URR ID: 0x%x format error!", urr_id);
        return -1;
    }

    if (upf_get_urr_by_id (rules, urr_id))
      return 0;

    create_urr->id = urr_id;
    if (upf_create_urr (sess, vec_bsearch (create_urr, g_upf_main.pre_urr, upf_urr_id_compare)))
    {
        upf_info ("Failed to add pre-def URR %u\n", urr_id);
        return -1;
    }
    return 0;
}

static int upf_pfcp_pdr_pre_urr_build (upf_session_t *sess, upf_pdr_t *create_pdr, u32 *urr_id_list, upf_urr_t *create_urr)
{
  pfcp_urr_id_t *urr_id;
  vec_foreach (urr_id, urr_id_list)
    {
      u32 found = 0;
      pfcp_urr_id_t *urr_id_tmp;
      vec_foreach (urr_id_tmp, create_pdr->urr_ids)
        {
          if (*urr_id_tmp == *urr_id)
            {
              found = 1;
              break;
            }
        }
      if (found)
        continue;

      vec_add1 (create_pdr->urr_ids, *urr_id);
      if (upf_pfcp_add_pre_urr_to_sx (sess, *urr_id, create_urr))
        {
          upf_info ("Failed to add the pre-def urr of the pre-def rule\n");
          return -1;
        }
    }
  return 0;
}

static int upf_create_pdr_handle (upf_session_t *sess, pfcp_create_pdr_t *create_pdr, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  upf_main_t *gtm = &g_upf_main;
  pfcp_create_pdr_t *pdr;
  upf_upip_res_t *res;
  upf_pdr_t create;
  int r = 0;
  upf_qer_t create_qer;
  upf_urr_t create_urr;
  upf_far_t create_far, *far_tmp = NULL;
  pfcp_qer_id_t *qer_id;
  pfcp_urr_id_t *urr_id;
  struct rules *rules;
  unformat_input_t input;
  u8 flag = 0;
  u8 is_inner_interface = 0;

  if (!create_pdr)
  {
      return r;
  }

  u16 failed_pdr_id = 0;
  vec_foreach (pdr, create_pdr)
  {
    failed_pdr_id = pdr->pdr_id;
    memset (&create, 0, sizeof (create));
    create.pdi.nwi = ~0;
    create.pdi.app_index = ~0;

    create.id = pdr->pdr_id;
    create.is_active = 1;
    create.precedence = pdr->precedence;
    if (SRC_INTF_5G_VN != pdr->pdi.source_interface)
        is_inner_interface = 0;
    else
        is_inner_interface = 1;

    if (!is_inner_interface && ISSET_BIT (pdr->pdi.grp.fields, PDI_NETWORK_INSTANCE))
      {
        create.pdi.fields |= F_PDI_NWI;
        upf_nwi_t *nwi = upf_lookup_nwi (pdr->pdi.network_instance);
        if (!nwi)
          {
            upf_warn ("PDR: %d, PDI for unknown network instance, NWI: %v (%d)\n",
                      pdr->pdr_id, pdr->pdi.network_instance, vec_len (pdr->pdi.network_instance));
            r = -1;
            break;
          }
        create.pdi.nwi = nwi - gtm->nwis;
      }

    if (is_inner_interface)
      {
        create.pdi.fields |= F_PDI_NWI;
        r = upf_5glan_nwi_proc(sess, ISSET_BIT (pdr->pdi.grp.fields, PDI_NETWORK_INSTANCE),
                               pdr->pdi.network_instance, &create.pdi.nwi);
        if (r)
          {
            upf_err("upf_5glan_nwi_proc fail, up_seid:0x%lx", sess->up_seid);
            break;
          }
      }

    create.pdi.src_intf = pdr->pdi.source_interface;

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_F_TEID))
      {
        create.pdi.fields |= F_PDI_LOCAL_F_TEID;
        if ((gtm->teid_alloc_option != TEID_ALLOC_BY_UP) && !(pdr->pdi.f_teid.flags & F_TEID_CH))
          {
            /* *INDENT-OFF* */
            pool_foreach (res, gtm->upip_res, ({
                            if (pdr->pdi.f_teid.flags & F_TEID_V4)
                              {
                                if (!ip4_address_compare (&res->ip4, &pdr->pdi.f_teid.ip4))
                                  flag = 1;
                              }
                            if ((pdr->pdi.f_teid.flags & F_TEID_V6))
                              {
                                if (!ip6_address_compare (&res->ip6, &pdr->pdi.f_teid.ip6))
                                  flag = 1;
                              }
                          }));
            /* *INDENT-ON* */
            if (!flag)
              {
                upf_info ("Can't find the gtpu endpoint IP address, %U\n", upf_format_f_teid, &pdr->pdi.f_teid);
                r = -1;
                break;
              }
            create.pdi.teid = pdr->pdi.f_teid;
            gtm->teid_alloc_option = TEID_ALLOC_BY_CP;
          }
        else if ((gtm->teid_alloc_option != TEID_ALLOC_BY_CP) && (pdr->pdi.f_teid.flags & F_TEID_CH))
          {
            if (gtm->stacking_switch)
            {
              if (ISSET_BIT (pdr->pdi.grp.fields, PDI_UE_IP_ADDRESS))
              {
                create.pdi.fields |= F_PDI_UE_IP_ADDR;
                create.pdi.ue_addr = pdr->pdi.ue_ip_address;
              }
            }
            r = upf_alloc_teid_by_up (sess, grp, pdr, &create, failed_rule_id_field);
            if (0 != r)
              break;
          }
        else if ((gtm->teid_alloc_option != TEID_ALLOC_BY_CP) &&
                  !(pdr->pdi.f_teid.flags & F_TEID_CH) &&
                  !(pdr->pdi.f_teid.flags & F_TEID_CHID) &&
                  (pdr->pdi.f_teid.flags & F_TEID_V6 || pdr->pdi.f_teid.flags & F_TEID_V4))
        {
            upf_debug ("Session Modify no choose ,no choose id, with TEID and UEIP!");
            pool_foreach (res, gtm->upip_res, ({
                            if (pdr->pdi.f_teid.flags & F_TEID_V4)
                              {
                                if (!ip4_address_compare (&res->ip4, &pdr->pdi.f_teid.ip4))
                                  flag = 1;
                                  break;
                              }
                            if ((pdr->pdi.f_teid.flags & F_TEID_V6))
                              {
                                if (!ip6_address_compare (&res->ip6, &pdr->pdi.f_teid.ip6))
                                  flag = 1;
                                  break;
                              }
                          }));
            if (!flag)
            {
                upf_info ("Session Modify Can't find the gtpu endpoint ip address, %U\n", upf_format_f_teid, &pdr->pdi.f_teid);
                r = -1;
                break;
            }
            create.pdi.teid = pdr->pdi.f_teid;
          }
        else
          {
            upf_debug ("teid alloc error, option:%u %U\n", gtm->teid_alloc_option, upf_format_f_teid, &pdr->pdi.f_teid);
            r = -2;
            break;
          }
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_UE_IP_ADDRESS))
      {
        create.pdi.fields |= F_PDI_UE_IP_ADDR;
        create.pdi.ue_addr = pdr->pdi.ue_ip_address;

        if ((create.pdi.ue_addr.flags & IE_UE_IP_ADDRESS_CHV4) ||
            ((create.pdi.ue_addr.flags & IE_UE_IP_ADDRESS_CHV6)))
          {
            if (!(gtm->upf_features & F_UPFF_UEIP))
              {
                upf_info ("The UPF do not support UEIP feature!");
                r = -1;
                break;
              }
            r = upf_alloc_ue_ip_by_up (sess, grp, pdr, &create,
                                   failed_rule_id_field);
            if (0 != r)
              break;
          }
      }
    //either sdf or appid
    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_SDF_FILTER)
        && ((!pdr->activate_predefined_rules && !ISSET_BIT (pdr->pdi.grp.fields, PDI_APPLICATION_ID))
            || pdr->precedence == UPF_PDR_DEFAULT_PRECEDENCE))
      {
        pfcp_sdf_filter_t *sdf;
        vec_foreach (sdf, pdr->pdi.sdf_filter)
        {
          create.pdi.fields |= F_PDI_SDF_FILTER;
          acl_rule_t *acl;
          APPEND_NEW_MEMBER(create.pdi.acl, acl);
          upf_pfcp_acl_build(&create.pdi, sdf, acl);

          /* neil.fan@20221213 add restriction: UE IP shall not be zero */
          if (!((ISSET_BIT (pdr->pdi.grp.fields, PDI_F_TEID)) || (ISSET_BIT (pdr->pdi.grp.fields, PDI_UE_IP_ADDRESS)))
             && !(ip46_address_is_zero(&acl->address[UPF_ACL_FIELD_DST].address)))
            {
              create.pdi.fields |= F_PDI_UE_IP_ADDR;
              if (ip46_address_is_ip4 (&acl->address[UPF_ACL_FIELD_DST].address))
                {
                  create.pdi.ue_addr.ip4 = acl->address[UPF_ACL_FIELD_DST].address.ip4;
                  create.pdi.ue_addr.flags = IE_UE_IP_ADDRESS_V4;
                }
              else
                {
                  create.pdi.ue_addr.flags = IE_UE_IP_ADDRESS_V6;
                  create.pdi.ue_addr.ip6 = acl->address[UPF_ACL_FIELD_DST].address.ip6;
                }
            }
        }
        if (r == -1)
          break;
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_ETHERNET_PDU_SESSION_INFORMATION))
      {
        if (pdr->pdi.ethernet_pdu_session_information.flags & F_ETHERNET_INDICATION)
          {
            create.pdi.fields |= F_PDI_ETH_PDU_SESSION_INFO;
            create.pdi.eth_pdu_flag = pdr->pdi.ethernet_pdu_session_information.flags;
          }
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_ETHERNET_PACKET_FILTER))
      {
        create.pdi.fields |= F_PDI_ETHERNET_PACKET_FILTER;

        /* copy as vec_dup */
        u32 len = vec_len(pdr->pdi.ethernet_packet_filter);
        vec_resize(create.pdi.eth_rule, len);
        clib_memcpy_fast(create.pdi.eth_rule, pdr->pdi.ethernet_packet_filter, len * sizeof(upf_ethernet_packet_filter_t));

        int i;
        vec_foreach_index (i, create.pdi.eth_rule)
        {
            pfcp_ethernet_packet_filter_t *src = vec_elt_at_index (pdr->pdi.ethernet_packet_filter, i);
            upf_ethernet_packet_filter_t *dst = vec_elt_at_index (create.pdi.eth_rule, i);

            dst->mac_address = vec_dup(src->mac_address);
            dst->acl = NULL;

            pfcp_sdf_filter_t *sdf;
            vec_foreach (sdf, src->sdf_filter)
            {
                acl_rule_t *acl;
                APPEND_NEW_MEMBER(dst->acl, acl);
                upf_pfcp_acl_build(&create.pdi, sdf, acl);
            }
        }
      }
    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_QFI))
      {
        create.pdi.fields |= F_PDI_QFI;
        create.pdi.qfi = pdr->pdi.qfi;
      }

    //either sdf or appid
    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_APPLICATION_ID) && !(ISSET_BIT (pdr->pdi.grp.fields, PDI_SDF_FILTER) && pdr->precedence == UPF_PDR_DEFAULT_PRECEDENCE))
      {
        uword *p = hash_get_mem (gtm->hash_app_by_appname, pdr->pdi.application_id);
        if (!p || upf_pfcp_pdr_appid_build (&create, p[0], 0 /* create */, pdr->pdi.application_id))
          {
            r = -1;
            upf_info ("PDR: %d, application id %v has not been configured",
                      pdr->pdr_id, pdr->pdi.application_id);
            break;
          }
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_FRAMED_ROUTE))
      {
        int ret = 0, len = 0;
        fib_prefix_t *prefix;
        pfcp_framed_route_t *framed_route;

        create.pdi.fields |= F_PDI_FRAMED_ROUTE;

        vec_foreach (framed_route, pdr->pdi.framed_route)
        {
          vec_alloc (create.pdi.framed_route, 1);
          prefix = vec_end (create.pdi.framed_route);
          prefix->fp_proto = FIB_PROTOCOL_IP4;
          len = upf_string_len_to_delimiter((char *)*framed_route, ' ');
          unformat_init_string (&input, (char *)*framed_route, len);
          if(vec_len(*framed_route) > len) // dotouch UE *framed_route is *************** *********** 1
          {
              ret = unformat (&input, "%U", unformat_ip46_address, &prefix->fp_addr, IP46_TYPE_IP4);
              prefix->fp_len = 32;
          }
          else  // mate40 UE *framed_route is ***************/24
             ret = unformat (&input, "%U/%d", unformat_ip46_address,
                          &prefix->fp_addr, IP46_TYPE_IP4, &prefix->fp_len);
          unformat_free (&input);
          if (!ret)
            {
              r = -1;
              upf_err ("PDR: %d, framed route %s parse failed", pdr->pdr_id,
                       *framed_route);
              break;
            }
          _vec_len (create.pdi.framed_route)++;
        }
        if (r)
          break;
      }
    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_FRAMED_IPV6_ROUTE))
      {
        int ret = 0, len = 0;
        fib_prefix_t *prefix;
        pfcp_framed_ipv6_route_t *framed_ipv6_route;

        create.pdi.fields |= F_PDI_FRAMED_IPV6_ROUTE;

        vec_foreach (framed_ipv6_route, pdr->pdi.framed_ipv6_route)
        {
          vec_alloc (create.pdi.framed_ipv6_route, 1);
          prefix = vec_end (create.pdi.framed_ipv6_route);

          prefix->fp_proto = FIB_PROTOCOL_IP6;
          len = upf_string_len_to_delimiter((char *)*framed_ipv6_route, ' ');
          unformat_init_string (&input, (char *)*framed_ipv6_route, len);
          if(vec_len(*framed_ipv6_route) > len) // *framed_route is *************** *********** 1
          {
              ret = unformat (&input, "%U", unformat_ip46_address,
                              &prefix->fp_addr, IP46_TYPE_IP6);
          }
          else  // *framed_route is ***************/24
              ret = unformat (&input, "%U/%d", unformat_ip46_address,
                              &prefix->fp_addr, IP46_TYPE_IP6, &prefix->fp_len);

          unformat_free (&input);
          if (!ret)
            {
              r = -1;
              upf_err ("PDR: %d, framed v6 route %s parse failed", pdr->pdr_id,
                       *framed_ipv6_route);
              break;
            }
          _vec_len (create.pdi.framed_ipv6_route)++;
        }
        if (r)
          break;
      }
    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_SOURCE_INTERFACE_TYPE))
      {
        create.pdi.fields |= F_PDI_SRC_INTF_TYPE;
        create.pdi.source_interface_type = pdr->pdi.source_interface_type;
      }

    if (ISSET_BIT (pdr->grp.fields, CREATE_PKT_DETECTION_CARRY_ON_INFO))
    {
        create.pkt_detection_carry_on_info = pdr->pkt_detection_carry_on_info;
        upf_debug("# wuwei pkt_detection_carry_on_info:0x%x ", create.pkt_detection_carry_on_info.flag);
    }

    if (ISSET_BIT (pdr->grp.fields, CREATE_IP_MULTICAST_ADDRESSING_INFO))
      {
        create.ip_multicast_addressing_info = vec_dup(pdr->ip_multicast_addressing_info);
        if (create.ip_multicast_addressing_info)
        {
            int k;
            vec_foreach_index (k, create.ip_multicast_addressing_info)
            {
                vec_elt(create.ip_multicast_addressing_info, k).source_ip_address
                    = vec_dup(vec_elt(pdr->ip_multicast_addressing_info, k).source_ip_address);
            }
        }

        pfcp_ip_multicast_addressing_info_t *mip_info;
        vec_foreach (mip_info, pdr->pdi.ip_multicast_addressing_info)
          {
            upf_debug(" pdr ip_multicast_addressing_info:\n%U", upf_format_ip_multicast_addressing_info, mip_info);
          }
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_IP_MULTICAST_ADDRESSING_INFO))
      {
        create.pdi.fields |= F_PDI_IP_MULTICAST_ADDRESSING_INFO;
        create.pdi.ip_multicast_addressing_info = vec_dup(pdr->pdi.ip_multicast_addressing_info);
        if (create.pdi.ip_multicast_addressing_info)
        {
            int k;
            vec_foreach_index (k, create.pdi.ip_multicast_addressing_info)
            {
                vec_elt(create.pdi.ip_multicast_addressing_info, k).source_ip_address
                    = vec_dup(vec_elt(pdr->pdi.ip_multicast_addressing_info, k).source_ip_address);

                if (vec_elt(create.pdi.ip_multicast_addressing_info, k).ip_multicast_address.flags & IE_IP_MULTICAST_ADDRESS_A)
                    create.pdi.fields |= F_PDI_IP_MULTICAST_ANY_FLAG;
            }
        }

        pfcp_ip_multicast_addressing_info_t *mip_info;
        vec_foreach (mip_info, pdr->pdi.ip_multicast_addressing_info)
        {
            upf_debug(" pdi ip_multicast_addressing_info:\n%U", upf_format_ip_multicast_addressing_info, mip_info);
        }
      }

    create.outer_header_removal = OPT (pdr, CREATE_PDR_OUTER_HEADER_REMOVAL,
                                       outer_header_removal.ohr_desc, ~0);
    create.far_id = OPT (pdr, CREATE_PDR_FAR_ID, far_id, ~0);

    if (create.far_id & 0x80000000)
      {
        rules = upf_get_rules (sess, SX_PENDING);
        if (!upf_get_far_by_id (rules, create.far_id))
          {
            create_far.id = create.far_id;
            if ((r = upf_create_far (sess,
                                    vec_bsearch (&create_far, gtm->pre_far,
                                                 upf_far_id_compare))) != 0)
              {
                upf_info ("Failed to add pre-def FAR %d\n", create.far_id);
                r = -1;
              }
          }
      }

    if (ISSET_BIT (pdr->grp.fields, CREATE_PDR_URR_ID))
      {
        vec_foreach (urr_id, pdr->urr_id)
          {
            vec_add1 (create.urr_ids, *urr_id);
            if (IS_PREDEF_RULE_ID(*urr_id))
              {
                r = upf_pfcp_add_pre_urr_to_sx (sess, *urr_id, &create_urr);
                if (r)
                  {
                    upf_info ("Failed to add pre-def URR %d\n", *urr_id);
                    break;
                  }
              }
          }
      }

    if (ISSET_BIT (pdr->grp.fields, CREATE_PDR_QER_ID))
      {
        vec_foreach (qer_id, pdr->qer_id)
        {
          vec_add1 (create.qer_ids, *qer_id);
          rules = upf_get_rules (sess, SX_PENDING);
          if (upf_get_qer_by_id (rules, *qer_id))
            continue;
          if (*qer_id & 0x80000000)
            {
              create_qer.id = *qer_id;
              if ((r = upf_create_qer (sess,
                                      vec_bsearch (&create_qer, gtm->pre_qer,
                                                   upf_qer_id_compare))) != 0)
                {
                  upf_info ("Failed to add pre-def QER %d\n", *qer_id);
                  r = -1;
                  break;
                }
            }
        }
      }

    // CREATE_PDR_ACTIVATE_PREDEFINED_RULES
    if (ISSET_BIT (pdr->grp.fields, CREATE_PDR_ACTIVATE_PREDEFINED_RULES))
      {
        predef_rule_t *pr;
        predef_appid_t *pr_appid;
        predef_rule_group_t *pr_group;
        predef_rule_name_t *rule_name;
        #if 0
        upf_dnn_t *dnn;
        dnn_rule_t *rule_id;
        int hit = 0;
        if (vec_len (sess->dnn))
          {
            vec_foreach (dnn, gtm->dnn)
            {
              if (vec_is_equal (dnn->name, sess->dnn))
                {
                  vec_foreach (rule_id, dnn->rule_id)
                  {
                    if (vec_is_equal (rule_id->rule,
                                      pdr->activate_predefined_rules))
                      {
                        hit = 1;
                        break;
                      }
                  }
                  break;
                }
            }
            if (!hit)
              {
                upf_info ("Failed to find dnn %v or rule\n", sess->dnn);
                r = -1;
                break;
              }
          }
        #endif
        upf_debug ("pdr %u bind to predefine rule %v", pdr->pdr_id,
                   pdr->activate_predefined_rules);
        uword *p =NULL;
        if(g_upf_rg_switch)
        {
            p = hash_get_mem (gtm->hash_rule_id_by_rule_group,
                                 pdr->activate_predefined_rules);
        }
        else
        {
            p = hash_get_mem (gtm->hash_rule_id_by_rule_name,
                                 pdr->activate_predefined_rules);
        }
        //either sdf or appid
        if (!(ISSET_BIT (pdr->pdi.grp.fields, PDI_SDF_FILTER) && pdr->precedence == UPF_PDR_DEFAULT_PRECEDENCE))
        {
            if (p)
            {
                create.activate_predefined_rules = vec_dup(pdr->activate_predefined_rules);
                // rule group switch
                if(g_upf_rg_switch)
                {
                    if (PREDICT_FALSE(pool_is_free_index(gtm->pre_rule_group, p[0])))
                    {
                        upf_info ("pre-def group rule index %d may be invalid \n", p[0]);
                        r = -1;
                        break;
                    }
                    pr_group = pool_elt_at_index (gtm->pre_rule_group, p[0]);
                    pool_foreach(rule_name, pr_group->rule_names, ({
                        if (rule_name)
                        {
                            uword *p1 = hash_get_mem (gtm->hash_rule_id_by_rule_name, rule_name->rule_name);
                            if (p1)
                            {
                                if (PREDICT_FALSE(pool_is_free_index(gtm->pre_rule, p1[0])))
                                {
                                    upf_info ("pre-def rule index %d may be invalid \n", p1[0]);
                                    r = -1;
                                    break;
                                }
                                pr = pool_elt_at_index (gtm->pre_rule, p1[0]);

                                r = upf_pfcp_pdr_pre_urr_build (sess, &create, pr->urr_ids, &create_urr);
                                if (r)
                                    break;

                                pool_foreach(pr_appid, pr->app_ids, ({
                                    if (vec_len (pr_appid->app_id))
                                      {
                                        uword *p2 = hash_get_mem (gtm->hash_app_by_appname, pr_appid->app_id);
                                        if (!p2 || upf_pfcp_pdr_appid_build (&create, p2[0], 0 /* create */, pr_appid->app_id))
                                          {
                                            r = -1;
                                            upf_info ("PDR: %d, application id %v has not been configured",
                                                pdr->pdr_id, pdr->pdi.application_id);
                                            break;
                                          }
                                      }
                                    vec_foreach (qer_id, pr_appid->qer_id)
                                    {
                                      vec_add1 (create.qer_ids, *qer_id);
                                      rules = upf_get_rules (sess, SX_PENDING);
                                      if (upf_get_qer_by_id (rules, *qer_id))
                                        continue;
                                      if (*qer_id & 0x80000000)
                                        {
                                          create_qer.id = *qer_id;
                                          if ((r = upf_create_qer (
                                                   sess, vec_bsearch (&create_qer, gtm->pre_qer,
                                                                      upf_qer_id_compare))) != 0)
                                            {
                                              upf_info ("Failed to add pre-def QER %d\n", *qer_id);
                                              r = -1;
                                              break;
                                            }
                                        }
                                    }

                                    /*predef far*/
                                    if (pr_appid->far_id)
                                      {
                                        rules = upf_get_rules (sess, SX_PENDING);
                                        if (pr_appid->far_id & 0x80000000)
                                          {
                                            create_far.id = pr_appid->far_id;
                                            if (!(far_tmp = vec_bsearch (&create_far, gtm->pre_far, upf_far_id_compare)))
                                                continue;
                                            if (pdr->pdi.source_interface == far_tmp->forward.dst_intf)
                                                continue;
                                            create.far_id = pr_appid->far_id;
                                            if ((r = upf_create_far (sess, far_tmp)) != 0)
                                              {
                                                upf_info ("Failed to add pre-def FAR %d\n",
                                                          pr_appid->far_id);
                                                r = -1;
                                              }
                                          }
                                      }
                                }));
                            }
                        }
                    }));
                }
                else
                {
                    upf_debug ("upf rule group switch is off!");
                    if (PREDICT_FALSE(pool_is_free_index(gtm->pre_rule, p[0])))
                    {
                        upf_info ("pre-def rule index %d may be invalid \n", p[0]);
                        r = -1;
                        break;
                    }
                    pr = pool_elt_at_index (gtm->pre_rule, p[0]);

                    r = upf_pfcp_pdr_pre_urr_build (sess, &create, pr->urr_ids, &create_urr);
                    if (r)
                        break;

                    pool_foreach(pr_appid, pr->app_ids, ({
                        if (vec_len (pr_appid->app_id))
                          {
                            uword *p = hash_get_mem (gtm->hash_app_by_appname, pr_appid->app_id);
                            if (!p || upf_pfcp_pdr_appid_build (&create, p[0], 0 /* create */, pr_appid->app_id))
                              {
                                r = -1;
                                upf_info ("PDR: %d, application id %v has not been configured",
                                    pdr->pdr_id, pdr->pdi.application_id);
                                break;
                              }
                          }
                        vec_foreach (qer_id, pr_appid->qer_id)
                        {
                          vec_add1 (create.qer_ids, *qer_id);
                          rules = upf_get_rules (sess, SX_PENDING);
                          if (upf_get_qer_by_id (rules, *qer_id))
                            continue;
                          if (*qer_id & 0x80000000)
                            {
                              create_qer.id = *qer_id;
                              if ((r = upf_create_qer (
                                       sess, vec_bsearch (&create_qer, gtm->pre_qer,
                                                          upf_qer_id_compare))) != 0)
                                {
                                  upf_info ("Failed to add pre-def QER %d\n", *qer_id);
                                  r = -1;
                                  break;
                                }
                            }
                        }

                        /*predef far*/
                        if (pr_appid->far_id)
                          {
                            rules = upf_get_rules (sess, SX_PENDING);
                            if (pr_appid->far_id & 0x80000000)
                              {
                                create_far.id = pr_appid->far_id;
                                if (!(far_tmp = vec_bsearch (&create_far, gtm->pre_far, upf_far_id_compare)))
                                    continue;
                                if (pdr->pdi.source_interface == far_tmp->forward.dst_intf)
                                    continue;
                                create.far_id = pr_appid->far_id;
                                if ((r = upf_create_far (sess, far_tmp)) != 0)
                                  {
                                    upf_info ("Failed to add pre-def FAR %d\n",
                                              pr_appid->far_id);
                                    r = -1;
                                  }
                              }
                          }
                    }));
                }
              }
            else
              {
                r = -1;
                upf_info ("PDR: %d, predefine rule %v has not been configured",
                          pdr->pdr_id, pdr->activate_predefined_rules);
                break;
              }
        }
      }

      if (NULL != upf_get_pdr (sess, SX_PENDING, pdr->pdr_id))
        {
          upf_err ("repeat create PDR:%u", pdr->pdr_id);
          r = -1;
          break;
        }
      if ((r = upf_pdr_create_by_priority (sess, &create)) != 0)
        {
          upf_err ("Failed to add PDR %d\n", pdr->pdr_id);
          break;
        }
  }

  if (r != 0)
    {
      if (r == -2)
        response->cause = PFCP_CAUSE_INVALID_F_TEID_ALLOCATION_OPTION;
      else
        response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_PDR;
      failed_rule_id->id = failed_pdr_id;
    }

  return r;
}

static int upf_update_pdr_handle (upf_session_t *sess, pfcp_update_pdr_t *update_pdr, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  upf_main_t *gtm = &g_upf_main;
  pfcp_update_pdr_t *pdr;
  unformat_input_t input;
  int r = 0;
  pfcp_qer_id_t *qer_id;
  upf_qer_t update_qer;
  upf_urr_t update_urr;
  upf_far_t update_far;
  struct rules *rules;

  if (!update_pdr)
  {
      return r;
  }

  u16 failed_pdr_id;
  vec_foreach (pdr, update_pdr)
  {
    failed_pdr_id = pdr->pdr_id;
    upf_pdr_t *update;

    update = upf_get_pdr (sess, SX_PENDING, pdr->pdr_id);
    if (!update)
      {
        upf_debug ("Sx Session %" PRIu64 ", update PDR Id %d not found.\n",
                   sess->cp_seid, pdr->pdr_id);
        r = -1;
        break;
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_SOURCE_INTERFACE))
      {
        if (((update->pdi.src_intf == SRC_INTF_5G_VN) || (pdr->pdi.source_interface == SRC_INTF_5G_VN))
          && (update->pdi.src_intf != pdr->pdi.source_interface))
          {
            upf_debug ("Sx Session %" PRIu64 ", update PDR Id %d, not support change src interface\n",
                       sess->cp_seid, pdr->pdr_id);
            r = -1;
            break;
          }

        update->pdi.src_intf = pdr->pdi.source_interface;
      }

    if ((ISSET_BIT (pdr->pdi.grp.fields, PDI_NETWORK_INSTANCE))
        && (SRC_INTF_5G_VN != pdr->pdi.source_interface))
      {
          update->pdi.fields |= F_PDI_NWI;
          if (vec_len (pdr->pdi.network_instance) != 0)
            {
              upf_nwi_t *nwi = upf_lookup_nwi (pdr->pdi.network_instance);
              if (!nwi)
                {
                  upf_warn ("PDR: %d, PDI for unknown network instance\n",
                            pdr->pdr_id);
                  r = -1;
                  break;
                }
              update->pdi.nwi = nwi - gtm->nwis;
            }
          else
            update->pdi.nwi = ~0;
      }

      if (SRC_INTF_5G_VN == pdr->pdi.source_interface)
      {
        /* the 5glan nwi should not changed, including the default nwi */
        r = upf_5glan_nwi_proc(sess, ISSET_BIT(pdr->pdi.grp.fields, PDI_NETWORK_INSTANCE),
            pdr->pdi.network_instance, &update->pdi.nwi);
        if (r)
        {
            upf_warn ("PDR: %d, Vn Pdr upf_5glan_nwi_proc fail \n", pdr->pdr_id);
            r = -1;
            break;
        }
        update->pdi.fields |= F_PDI_NWI;
      }

    if (ISSET_BIT (pdr->grp.fields, UPDATE_PDR_PRECEDENCE))
      {
        if (update->precedence != pdr->precedence)
          {
            update->precedence = pdr->precedence;
            struct rules *rules = upf_get_rules (sess, SX_PENDING);
            pdr_edge_t *edge = &rules->pdr_edge[update->pdi.src_intf];
            if (edge->end - edge->start > 1)
            {
                qsort (rules->pdr + edge->start, edge->end - edge->start, sizeof (upf_pdr_t),
                    (void *)(upf_pfcp_pdr_priority_and_id_compare));
            }
            update = upf_get_pdr (sess, SX_PENDING, pdr->pdr_id);
          }
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_F_TEID))
      {
        update->pdi.fields |= F_PDI_LOCAL_F_TEID;
        /* TODO validate TEID and mask */
        update->pdi.teid = pdr->pdi.f_teid;
      }
    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_UE_IP_ADDRESS))
      {
        update->pdi.fields |= F_PDI_UE_IP_ADDR;
        update->pdi.ue_addr = pdr->pdi.ue_ip_address;
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_SDF_FILTER))
      {
        pfcp_sdf_filter_t *pdr_sdf_filter;
        acl_rule_t *tmp_acl_rule;
        vec_free (update->pdi.acl);
        vec_foreach (pdr_sdf_filter, pdr->pdi.sdf_filter)
        {
          update->pdi.fields |= F_PDI_SDF_FILTER;
          vec_alloc (update->pdi.acl, 1);
          tmp_acl_rule = vec_end (update->pdi.acl);
          memset (tmp_acl_rule, 0, sizeof (acl_rule_t));

          if (pdr_sdf_filter->flags & F_SDF_FD)
            {
              tmp_acl_rule->flag = 1;
              unformat_init_vector (&input, pdr_sdf_filter->flow);
              if (!upf_unformat_ipfilter (&input, tmp_acl_rule))
                {
                  upf_err ("failed to parse SDF '%s'", pdr_sdf_filter->flow);
                  r = -1;
                  break;
                }
              vec_free (input.buffer_marks);
              clib_memset (&input, 0, sizeof (input));
            }
          if (pdr_sdf_filter->flags & F_SDF_TTC)
            {
              tmp_acl_rule->tos = pdr_sdf_filter->tos_traffic_class;
            }
          if (pdr_sdf_filter->flags & F_SDF_BID)
            {
              tmp_acl_rule->id = pdr_sdf_filter->sdf_filter_id;
            }
          _vec_len (update->pdi.acl)++;
          if (!((ISSET_BIT (pdr->pdi.grp.fields, PDI_F_TEID)) ||
                (ISSET_BIT (pdr->pdi.grp.fields, PDI_UE_IP_ADDRESS))))
            {
              update->pdi.fields |= F_PDI_UE_IP_ADDR;
              if (ip46_address_is_ip4 (
                      &tmp_acl_rule->address[UPF_ACL_FIELD_DST].address))
                {
                  update->pdi.ue_addr.flags = IE_UE_IP_ADDRESS_V4;
                  update->pdi.ue_addr.ip4.as_u32 =
                      tmp_acl_rule->address[UPF_ACL_FIELD_DST]
                          .address.ip4.as_u32;
                }
              else
                {
                  update->pdi.ue_addr.flags = IE_UE_IP_ADDRESS_V6;
                  update->pdi.ue_addr.ip6.as_u64[0] =
                      tmp_acl_rule->address[UPF_ACL_FIELD_DST]
                          .address.ip6.as_u64[0];
                  update->pdi.ue_addr.ip6.as_u64[1] =
                      tmp_acl_rule->address[UPF_ACL_FIELD_DST]
                          .address.ip6.as_u64[1];
                }
            }
        }
        if (r == -1)
          break;
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_ETHERNET_PACKET_FILTER))
      {
        update->pdi.fields |= F_PDI_ETHERNET_PACKET_FILTER;
        vec_reset_length (update->pdi.eth_rule);
        u32 len = vec_len(pdr->pdi.ethernet_packet_filter);
        vec_resize(update->pdi.eth_rule, len);
        clib_memcpy_fast(update->pdi.eth_rule, pdr->pdi.ethernet_packet_filter, len * sizeof(upf_ethernet_packet_filter_t));

        int i;
        vec_foreach_index (i, pdr->pdi.ethernet_packet_filter)
        {
            pfcp_ethernet_packet_filter_t *src = vec_elt_at_index (pdr->pdi.ethernet_packet_filter, i);
            upf_ethernet_packet_filter_t *dst = vec_elt_at_index (update->pdi.eth_rule, i);

            dst->mac_address = vec_dup(src->mac_address);
            dst->acl = NULL;

            pfcp_sdf_filter_t *sdf;
            vec_foreach (sdf, src->sdf_filter)
            {
                acl_rule_t *acl;
                APPEND_NEW_MEMBER(dst->acl, acl);
                upf_pfcp_acl_build(&update->pdi, sdf, acl);
            }
        }
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_QFI))
      {
        update->pdi.fields |= F_PDI_QFI;
        update->pdi.qfi = pdr->pdi.qfi;
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_APPLICATION_ID))
      {
        uword *p = hash_get_mem (gtm->hash_app_by_appname, pdr->pdi.application_id);
        if (!p || upf_pfcp_pdr_appid_build (update, p[0], 1 /* update */, pdr->pdi.application_id))
          {
            r = -1;
            upf_err ("PDR: %d, application id %v has not been configured\n",
                     pdr->pdr_id, pdr->pdi.application_id);
            break;
          }
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_FRAMED_ROUTE))
      {
        int ret;
        fib_prefix_t *prefix;
        pfcp_framed_route_t *framed_route;

        update->pdi.fields |= F_PDI_FRAMED_ROUTE;
        vec_reset_length (update->pdi.framed_route);

        vec_foreach (framed_route, pdr->pdi.framed_route)
        {
          vec_alloc (update->pdi.framed_route, 1);
          prefix = vec_end (update->pdi.framed_route);

          prefix->fp_proto = FIB_PROTOCOL_IP4;
          unformat_init_string (&input, (char *)*framed_route,
                                vec_len (*framed_route));
          ret = unformat (&input, "%U/%d", unformat_ip46_address,
                          &prefix->fp_addr, IP46_TYPE_IP4, &prefix->fp_len);
          unformat_free (&input);
          if (!ret)
            {
              r = -1;
              upf_err ("PDR: %d,framed route %s parse failed", pdr->pdr_id,
                       *framed_route);
              break;
            }
          _vec_len (update->pdi.framed_route)++;
        }
        if (r)
          break;
      }

    if (ISSET_BIT (pdr->pdi.grp.fields, PDI_FRAMED_IPV6_ROUTE))
      {
        int ret;
        fib_prefix_t *prefix;
        pfcp_framed_ipv6_route_t *framed_ipv6_route;

        update->pdi.fields |= F_PDI_FRAMED_IPV6_ROUTE;
        vec_reset_length (update->pdi.framed_ipv6_route);

        vec_foreach (framed_ipv6_route, pdr->pdi.framed_ipv6_route)
        {
          vec_alloc (update->pdi.framed_ipv6_route, 1);
          prefix = vec_end (update->pdi.framed_ipv6_route);

          prefix->fp_proto = FIB_PROTOCOL_IP6;
          unformat_init_string (&input, (char *)*framed_ipv6_route,
                                vec_len (*framed_ipv6_route));
          ret = unformat (&input, "%U/%d", unformat_ip46_address,
                          &prefix->fp_addr, IP46_TYPE_IP6, &prefix->fp_len);
          unformat_free (&input);
          if (!ret)
            {
              r = -1;
              upf_err ("PDR: %d,framed v6 route %s parse failed", pdr->pdr_id,
                       *framed_ipv6_route);
              break;
            }
          _vec_len (update->pdi.framed_ipv6_route)++;
        }
        if (r)
          break;
      }
    if (ISSET_BIT (pdr->grp.fields, UPDATE_PDR_OUTER_HEADER_REMOVAL))
      update->outer_header_removal = OPT (pdr, UPDATE_PDR_OUTER_HEADER_REMOVAL,
                                          outer_header_removal.ohr_desc, ~0);
    if (ISSET_BIT (pdr->grp.fields, UPDATE_PDR_FAR_ID))
      update->far_id = OPT (pdr, UPDATE_PDR_FAR_ID, far_id, ~0);
    if (ISSET_BIT (pdr->grp.fields, UPDATE_PDR_URR_ID))
      {
        pfcp_urr_id_t *urr_id;
        vec_reset_length (update->urr_ids);

        vec_foreach (urr_id, pdr->urr_id)
        {
          vec_add1 (update->urr_ids, *urr_id);
          if (IS_PREDEF_RULE_ID(*urr_id))
            {
              r = upf_pfcp_add_pre_urr_to_sx (sess, *urr_id, &update_urr);
              if (r)
                {
                  upf_info ("Failed to add the pre-def urr id\n");
                  break;
                }
            }
        }
      }

    if (ISSET_BIT (pdr->grp.fields, UPDATE_PDR_QER_ID))
      {
        pfcp_qer_id_t *qer_id;
        vec_reset_length (update->qer_ids);
        vec_foreach (qer_id, pdr->qer_id)
        {
          vec_add1 (update->qer_ids, *qer_id);
        }
      }

    // UPDATE_PDR_ACTIVATE_PREDEFINED_RULES
    if (ISSET_BIT (pdr->grp.fields, UPDATE_PDR_ACTIVATE_PREDEFINED_RULES))
      {
        predef_rule_t *pr;
        upf_debug ("pdr bind to predefine rule %v", pdr->activate_predefined_rules);
        uword *p = hash_get_mem (gtm->hash_rule_id_by_rule_name, pdr->activate_predefined_rules);
        if (p)
          {
            if (PREDICT_FALSE(pool_is_free_index(gtm->pre_rule, p[0])))
            {
                upf_info ("pre-def index %u may be invalid\n", p[0]);
                r = -1;
                break;
            }
            pr = pool_elt_at_index (gtm->pre_rule, p[0]);
            vec_foreach (qer_id, pr->qer_ids)
            {
              vec_add1 (update->qer_ids, *qer_id);
              rules = upf_get_rules (sess, SX_PENDING);
              if (upf_get_qer_by_id (rules, *qer_id))
                continue;
              if (*qer_id & 0x80000000)
                {
                  update_qer.id = *qer_id;
                  if ((r = upf_create_qer (sess, vec_bsearch (&update_qer, gtm->pre_qer, upf_qer_id_compare))) != 0)
                    {
                      upf_info ("Failed to add pre-def QER %d\n", *qer_id);
                      r = -1;
                      break;
                    }
                }
            }

            r = upf_pfcp_pdr_pre_urr_build (sess, update, pr->urr_ids, &update_urr);
            if (r)
              {
                break;
              }

            /*predef far*/
            if (pr->far_id)
              {
                update->far_id = pr->far_id;
                rules = upf_get_rules (sess, SX_PENDING);
                if (pr->far_id & 0x80000000)
                  {
                    update_far.id = pr->far_id;
                    if ((r = upf_create_far (
                             sess, vec_bsearch (&update_far, gtm->pre_far,
                                                upf_far_id_compare))) != 0)
                      {
                        upf_err ("Failed to add pre-def FAR %d\n", pr->far_id);
                        r = -1;
                      }
                  }
              }
            if (vec_len (pr->app_id))
              {
                uword *p = hash_get_mem (gtm->hash_app_by_appname, pr->app_id);
                if (!p || upf_pfcp_pdr_appid_build (update, p[0], 1 /* update */, pr->app_id))
                  {
                    r = -1;
                    upf_err ("PDR: %d, application id %v has not been configured",
                        pdr->pdr_id, pdr->pdi.application_id);
                    break;
                  }
              }
          }
        update->is_active = 1;
      }

    if (ISSET_BIT (pdr->grp.fields, UPDATE_PDR_DEACTIVATE_PREDEFINED_RULES))
      {
        /* neil.fan@20230206 add to support deactive predefined rules: check rule name and not support several rule names now */
        if (vec_is_equal(pdr->deactivate_predefined_rules, update->activate_predefined_rules))
          update->is_active = 0;
        else
          {
            r = -1;
            upf_debug ("PDR: %d, deactivate_predefined_rules %v has not been configured before",
                pdr->pdr_id, pdr->deactivate_predefined_rules);
            break;
          }
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_PDR;
      failed_rule_id->id = failed_pdr_id;
    }

  return r;
}

static int upf_remove_pdr_handle (upf_session_t *sess, pfcp_remove_pdr_t *remove_pdr, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  pfcp_remove_pdr_t *pdr;
  int r = 0;

  if (!remove_pdr)
  {
      return r;
  }

  vec_foreach (pdr, remove_pdr)
  {
    /* remove pdr from original session or 5glan session */
    if ((r = upf_pdr_delete (sess, pdr->pdr_id)) != 0)
      {
        upf_debug ("Failed to delete PDR %d\n", pdr->pdr_id);
        failed_rule_id->id = pdr->pdr_id;
        break;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_PDR;
    }

  return r;
}

void upf_ip_udp_gtpu_rewrite (upf_far_forward_t *ff, int is_ip4)
{
  union
  {
    ip4_gtpu_header_t *h4;
    ip6_gtpu_header_t *h6;
    u8 *rw;
  } r = {.rw = 0};
  int len = is_ip4 ? sizeof *r.h4 : sizeof *r.h6;
  u32 flow_hash;

  len = len + GTPU_V1_HDR_LEN_PDU_SESSION_CONTAINER; // Include the gtpu
                                                     // extension hdr length

  vec_validate_aligned (r.rw, len, CLIB_CACHE_LINE_BYTES);

  udp_header_t *udp;
  gtpu_header_t *gtpu;
  /* Fixed portion of the (outer) ip header */
  if (is_ip4)
    {
      ip4_header_t *ip = &r.h4->ip4;
      udp = &r.h4->udp;
      gtpu = &r.h4->gtpu;
      ip->ip_version_and_header_length = 0x45;
      ip->ttl = 254;
      ip->protocol = IP_PROTOCOL_UDP;

      ip->dst_address = ff->outer_header_creation.ip.ip4;

      if (g_upf_main.lb_flow.lb_switch)
      {
        //11(N3 3Gpp Access)
        //12(N3 Trusted Non-3Gpp Access)
        //13(N3 Untrusted Non-3Gpp Access)
        //14(N3 for data forwarding)
        if (ff->dest_interface_type <= 14 && ff->dest_interface_type >= 11)
          ip->src_address = g_upf_main.lb_flow.n3ip.ip4;
        else if (ff->dest_interface_type == 15)
          ip->src_address = g_upf_main.lb_flow.n9ip.ip4;
      }

      /* we fix up the ip4 header length and checksum after-the-fact */
      // ip->checksum = ip4_header_checksum (ip);
      flow_hash = ip4_compute_flow_hash (ip, IP_FLOW_HASH_DEFAULT);
    }
  else
    {
      ip6_header_t *ip = &r.h6->ip6;
      udp = &r.h6->udp;
      gtpu = &r.h6->gtpu;
      ip->ip_version_traffic_class_and_flow_label =
          clib_host_to_net_u32 (6 << 28);
      ip->hop_limit = 255;
      ip->protocol = IP_PROTOCOL_UDP;

      ip->dst_address = ff->outer_header_creation.ip.ip6;
      if (g_upf_main.lb_flow.lb_switch)
      {
        if (ff->dest_interface_type == 14)
          ip->src_address = g_upf_main.lb_flow.n3ip.ip6;
        else if (ff->dest_interface_type == 15)
          ip->src_address = g_upf_main.lb_flow.n9ip.ip6;
      }
      flow_hash = ip6_compute_flow_hash (ip, IP_FLOW_HASH_DEFAULT);
    }

  /* UDP header, randomize src port on something, maybe? */
  udp->src_port = clib_host_to_net_u16 ((u16)flow_hash);
  udp->dst_port = clib_host_to_net_u16 (UDP_DST_PORT_GTPU);

  /* GTPU header */
  gtpu->ver_flags = GTPU_V1_VER | GTPU_PT_GTP | GTPU_E_BIT;
  gtpu->type = GTPU_TYPE_GTPU;
  gtpu->teid = clib_host_to_net_u32 (ff->outer_header_creation.teid);
  gtpu->next_ext_type = GTP_EX_TYPE_PDU_SESS;

  pdu_sess_container_ex_t *container = (pdu_sess_container_ex_t *)(gtpu + 1);
  container->ext_hdr_len = 1;
  container->data[0] = 0;

  if (ff->rewrite != NULL)
  {
      vec_free (ff->rewrite);
      ff->rewrite = NULL;
  }

  ff->rewrite = r.rw;

  _vec_len (ff->rewrite) = len;

  return;
}

void upf_ip_udp_rewrite (upf_far_forward_t *ff, int is_ip4)
{
  union
  {
    ip4_udp_header_t *h4;
    ip6_udp_header_t *h6;
    u8 *rw;
  } r = {.rw = 0};
  int len = is_ip4 ? sizeof *r.h4 : sizeof *r.h6;

  vec_validate_aligned (r.rw, len, CLIB_CACHE_LINE_BYTES);

  udp_header_t *udp;
  /* Fixed portion of the (outer) ip header */
  if (is_ip4)
    {
      ip4_header_t *ip = &r.h4->ip4;
      udp = &r.h4->udp;
      ip->ip_version_and_header_length = 0x45;
      ip->ttl = 254;
      ip->protocol = IP_PROTOCOL_UDP;

      ip->dst_address = ff->outer_header_creation.ip.ip4;

      /* we fix up the ip4 header length and checksum after-the-fact */
      ip->checksum = ip4_header_checksum (ip);
    }
  else
    {
      ip6_header_t *ip = &r.h6->ip6;
      udp = &r.h6->udp;
      ip->ip_version_traffic_class_and_flow_label =
          clib_host_to_net_u32 (6 << 28);
      ip->hop_limit = 255;
      ip->protocol = IP_PROTOCOL_UDP;

      ip->dst_address = ff->outer_header_creation.ip.ip6;
    }

  /*neil.fan@20221103 modify: specify a random port */
  udp->src_port = clib_host_to_net_u16 (49846);
  udp->dst_port = clib_host_to_net_u16 (ff->outer_header_creation.port);

  if (ff->rewrite != NULL)
  {
      vec_free (ff->rewrite);
      ff->rewrite = NULL;
  }
  ff->rewrite = r.rw;

  _vec_len (ff->rewrite) = len;

  return;
}

/* from src/vnet/ip/ping.c */
u32 upf_ip46_fib_index_get_by_table_id (u32 table_id, int is_ip4)
{
  u32 fib_index = is_ip4 ? ip4_fib_index_from_table_id (table_id)
                         : ip6_fib_index_from_table_id (table_id);
  return fib_index;
}

u32 upf_fib_index_by_table_id(u32 table_id, u32 *fib_index)
{
    u32 index;

    index = fib_table_find (FIB_PROTOCOL_IP4, table_id);
    if (~0 != index)
      {
        *fib_index = index;
        return 0;
      }

    index = fib_table_find (FIB_PROTOCOL_IP6, table_id);
    if (~0 != index)
      {
        *fib_index = index;
        return 0;
      }

    return ~0;
}

static int upf_create_far_handle (upf_session_t *sess, pfcp_create_far_t *create_far, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
    struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
    upf_main_t *gtm = &g_upf_main;
    pfcp_create_far_t *create;
    upf_far_t far;
    int r = 0;
    u32 i, j;
    pfcp_far_id_t far_id = {0};

    if (!create_far)
        return r;

    vec_foreach (create, create_far)
    {
        far_id = create->far_id;
        memset (&far, 0, sizeof (far));
        far.forward.nwi = ~0;
        far.forward.dst_intf = ~0;
        far.id = create->far_id;
        far.apply_action = create->apply_action;

        if ((far.apply_action & F_APPLY_FORW) && !(create->grp.fields & CREATE_FAR_FORWARDING_PARAMETERS))
        {
            upf_info ("FAR: %d, FAR apply_action is FORWARD, no FORWARDING_PARAMETERS included!\n", create->far_id);
            r = -1;
            break;
        }

        if (far.apply_action & F_APPLY_BUFF)
        {
            far.bar_id = OPT (create, CREATE_FAR_BAR_ID, bar_id, ~0);
        }

        pfcp_forwarding_parameters_t *forw_para = &create->forwarding_parameters;
        if (ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_DESTINATION_INTERFACE_TYPE))
        {
            far.forward.flags |= FAR_F_DEST_INTF_TYPE;
            far.forward.dest_interface_type = forw_para->destination_interface_type;
        }

        if (ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_FORWARDING_POLICY))
        {
            uword *p;
            upf_forward_policy_t *forward_policy;
            far.forward.flags |= FAR_F_FORWARDING_POLICY;

            far.forward.table_id = 0;
            far.forward.forward_policy_index = ~0;
            p = hash_get_mem (gtm->forward_policy_by_name, forw_para->forwarding_policy.identifier);
            if (!p)
            {
                upf_info ("FAR: %d, Parameter with unknown forwarding policy:%s\n", create->far_id,
                      (char *)forw_para->forwarding_policy.identifier);
                r = -1;
                break;
            }
            if (PREDICT_TRUE(!pool_is_free_index(gtm->forward_policys, p[0])))
            {
                forward_policy = pool_elt_at_index (gtm->forward_policys, p[0]);

                far.forward.table_id = forward_policy->vrf;
                far.forward.forward_policy_index = p[0];
                if (0 != upf_fib_index_by_table_id(far.forward.table_id, &far.forward.fib_index))
                  {
                    upf_info ("vrf(table_id) %u in not (yet) defined", far.forward.table_id);
                  }
            }
        }
        else if (ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_NETWORK_INSTANCE))
        {
            far.forward.flags |= FAR_F_NETWORK_INSTANCE;
            upf_nwi_t *nwi = NULL;
            if ((ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_DESTINATION_INTERFACE))
                && (DST_INTF_5G_VN != forw_para->destination_interface))
            {
                nwi = upf_lookup_nwi (forw_para->network_instance);
                if (!nwi)
                {
                    upf_info ("FAR: %d, Parameter with unknown network instance\n", create->far_id);
                    r = -1;
                    break;
                }

                far.forward.table_id = nwi->vrf;
                far.forward.nwi = nwi - gtm->nwis;
                if (0 != upf_fib_index_by_table_id(far.forward.table_id, &far.forward.fib_index))
                {
                    upf_info ("vrf(table_id) %u in not (yet) defined", far.forward.table_id);
                }
            }
        }

        if (ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_DESTINATION_INTERFACE))
        {
            far.forward.dst_intf = forw_para->destination_interface;
            if ((DST_INTF_5G_VN == forw_para->destination_interface))
            {
                upf_5glan_nwi_t *nwi_inst = NULL;
                if (ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_NETWORK_INSTANCE))
                {
                    nwi_inst = upf_5glan_nwi_lookup(forw_para->network_instance);
                    if (!nwi_inst)
                    {
                        /* add for the situation group level session create without 5G VN Internal pdr */
                        int ret = upf_5glan_nwi_add_del (forw_para->network_instance, 1);
                        upf_warn("upf 5glan nwi:%v(%d) add, ret:%d", forw_para->network_instance,
                          vec_len(forw_para->network_instance), ret);
                        nwi_inst = upf_5glan_nwi_lookup (forw_para->network_instance);
                        if (!nwi_inst)
                        {
                            upf_warn("upf 5glan nwi:%v(%d) lookup failed!", forw_para->network_instance,
                                vec_len(forw_para->network_instance));
                            r = -1; /* because of the upf_create_pdr_handle has processed and the nwi has initialized */
                            break;
                        }
                    }
                }
                else
                {
                    nwi_inst = upf_5glan_default_nwi_inst();
                }

                far.forward.nwi = upf_5glan_nwi_to_index(nwi_inst);
                if (~0 == far.forward.nwi)
                {
                    if (ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_NETWORK_INSTANCE))
                        upf_warn ("5glan NWI: %v nwi_to_index fail, %p!", forw_para->network_instance, nwi_inst);
                    else
                        upf_warn ("5glan default NWI nwi_to_index fail, %p!", nwi_inst);
                    r = -1;
                    break;
                }

                far.forward.table_id = nwi_inst->vrf;
                if (0 != upf_fib_index_by_table_id(far.forward.table_id, &far.forward.fib_index))
                {
                    upf_info ("vrf(table_id) %u in not (yet) defined, nwi-name:%U",
                        far.forward.table_id, upf_format_network_instance, nwi_inst->name);
                }
            }
        }

        if (ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_REDIRECT_INFORMATION))
        {
            far.forward.flags |= FAR_F_REDIRECT_INFORMATION;
            upf_cpy_redirect_information (&far.forward.redirect_information, &forw_para->redirect_information);
        }

        if (ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_OUTER_HEADER_CREATION))
        {
            pfcp_outer_header_creation_t *ohc = &forw_para->outer_header_creation;
            far.forward.flags |= FAR_F_OUTER_HEADER_CREATION;
            far.forward.outer_header_creation = forw_para->outer_header_creation;

            if ((ohc->description & OUTER_HEADER_CREATION_ANY_IP4) || (ohc->description & OUTER_HEADER_CREATION_ANY_IP6))
            {
                u32 fib_index;
                int is_ip4 = !!(ohc->description & OUTER_HEADER_CREATION_ANY_IP4);

                if (ip46_address_is_zero (&ohc->ip))
                {
                    upf_err ("FAR: %d, outer header creation IP is zero\n", create->far_id);
                    r = -1;
                    goto set_cause;
                }

                if (!is_ip4 && ip6_address_is_link_local_unicast(&ohc->ip.ip6))
                {
                    upf_err ("FAR: %d, outer header creation IP is fe80\n", create->far_id);
                    r = -1;
                    goto set_cause;
                }

                if (upf_lookup_upf_ip_blacklist(&ohc->ip))
                {
                    upf_err ("FAR: %d, outer header creation IP in upf ip blacklist\n", create->far_id);
                    r = -1;
                    goto set_cause;
                }

                fib_index = upf_ip46_fib_index_get_by_table_id (far.forward.table_id, is_ip4);
                if (~0 == fib_index)
                {
                    upf_err ("FAR: %d, Network instance with invalid VRF for IPv%d\n", create->far_id, is_ip4 ? 4 : 6);
                    r = -1;
                    break;
                }

                if (ohc->description & OUTER_HEADER_CREATION_GTP_ANY)
                    upf_ip_udp_gtpu_rewrite (&far.forward, is_ip4);
                else if (ohc->description & OUTER_HEADER_CREATION_UDP_ANY)
                    upf_ip_udp_rewrite (&far.forward, is_ip4);
            }
        }

        if (ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_TRANSPORT_LEVEL_MARKING))
        {
            far.forward.flags |= FAR_F_TRANSPORT_LEVEL_MARKING;
            far.forward.transport_level_marking = ((forw_para->transport_level_marking & 0xFF) &
                                                  ((forw_para->transport_level_marking >> 8) & 0xFF));
        }

        if (ISSET_BIT (forw_para->grp.fields, FORWARDING_PARAMETERS_HEADER_ENRICHMENT))
        {
            pfcp_header_enrichment_t *header_enrichment;
            far.forward.flags |= FAR_F_HEADER_ENRICHMENT;
            vec_add2 (far.forward.header_enrichment, header_enrichment, vec_len (forw_para->header_enrichment));

            for (i = 0; i < vec_len (forw_para->header_enrichment); i++)
            {
                header_enrichment[i].name = vec_dup (forw_para->header_enrichment[i].name);
                header_enrichment[i].value = vec_dup (forw_para->header_enrichment[i].value);
            }
        }

        // Add for 5glan multicast N19 duplicating packet by liupeng on 2022-02-22
        r = handle_far_duplicating_para(sess, &far, create);
        if (r)
        {
            upf_err("handle_far_duplicating_para failed.");
            break;
        }

        //begin liukang add for proxying 2022/03/29
        if (ISSET_BIT (create->forwarding_parameters.grp.fields,
                   FORWARDING_PARAMETERS_PROXYING))
        {

           far.forward.flags |= FAR_F_PROXYING;
           far.forward.proxying = create->forwarding_parameters.proxying;

        }
        //end liukang add for proxying 2022/03/29

        vec_foreach_index (j, gtm->pre_far)
        {
            upf_far_t *tmp_far;
            tmp_far = vec_elt_at_index (gtm->pre_far, j);
        CHECK_VEC_VALUE_ISVALID_CONTINUE(gtm->pre_far, tmp_far);
            if (tmp_far->id != far.id)
                continue;

            if (gtm->pre_far->forward.flags & FAR_F_HEADER_ENRICHMENT)
            {
                pfcp_header_enrichment_t *header_enrichment;

                far.forward.flags |= FAR_F_HEADER_ENRICHMENT;

                vec_add2 (far.forward.header_enrichment, header_enrichment,
                          vec_len (gtm->pre_far->forward.header_enrichment));

                int hit_dnn, hit_ueip, hit_rattype, hit_uli;
                hit_dnn = 0;
                hit_ueip = 0;
                hit_rattype = 1;
                hit_uli = 1;

                for (i = 0; i < vec_len (gtm->pre_far->forward.header_enrichment); i++)
                {
                    switch(sess->user_id.flags & (1LLU << i))
                    {
                        case USER_ID_IMSI:
                            header_enrichment[i].name = vec_dup(gtm->pre_far->forward.header_enrichment[i].name);
                            header_enrichment[i].value = vec_dup(sess->user_id.imsi_str);
                            break;
                        case USER_ID_IMEI:
                            header_enrichment[i].name = vec_dup(gtm->pre_far->forward.header_enrichment[i].name);
                            header_enrichment[i].value = vec_dup(sess->user_id.imei_str);
                            break;
                        case USER_ID_MSISDN:
                            header_enrichment[i].name = vec_dup(gtm->pre_far->forward.header_enrichment[i].name);
                            header_enrichment[i].value = vec_dup(sess->user_id.msisdn_str);
                            break;
                        case USER_ID_NAI:
                            header_enrichment[i].name = vec_dup(gtm->pre_far->forward.header_enrichment[i].name);
                            header_enrichment[i].value = vec_dup(sess->user_id.nai);
                            break;
                        default:
                        {
                            if (sess->dnn != NULL && !hit_dnn)
                            {
                                // gtm->pre_far->forward.header_enrichment last is dnn
                                header_enrichment[i].name = vec_dup(gtm->pre_far->forward.header_enrichment[i].name);
                                header_enrichment[i].value = vec_dup(sess->dnn);
                                hit_dnn = 1;
                            }
                            else if (!hit_ueip)
                            {
                                upf_pdr_t *pdr;
                                u8 * ue_ip_tmp;
                                struct rules *pending = upf_get_rules (sess, SX_PENDING);
                                vec_foreach (pdr, pending->pdr)
                                {
                                    if (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_V4) {
                                        ue_ip_tmp = format(0, "%U", format_ip4_address, pdr->pdi.ue_addr.ip4.as_u8);
                                        header_enrichment[i].name = vec_dup(gtm->pre_far->forward.header_enrichment[i].name);
                                        header_enrichment[i].value = vec_dup(ue_ip_tmp);
                                        hit_ueip = 1;
                                        break;
                                    }
                                }

                            }
                            else if (sess->rat_type != 0xFF && !hit_rattype)
                            {
                                header_enrichment[i].name = vec_dup(gtm->pre_far->forward.header_enrichment[i].name);
                                header_enrichment[i].value = vec_dup(&sess->rat_type);
                                hit_rattype = 1;
                            }
                            else if (sess->user_location_information.geographic_location != NULL && !hit_uli)
                            {
                                header_enrichment[i].name = vec_dup(gtm->pre_far->forward.header_enrichment[i].name);
                                header_enrichment[i].value = vec_dup((u8 *)&sess->user_location_information);
                                hit_uli = 1;
                            }
                            else
                            {
                                upf_debug("null to set, user id flags is %d, i is %d", sess->user_id.flags, i);
                            }
                            break;
                        }
                    } /* end of default */
                } /* end of switch */
            }

            // if found, break.
            if (gtm->pre_far->forward.flags & FAR_F_REDIRECT_INFORMATION)
            {
                far.forward.flags |= FAR_F_REDIRECT_INFORMATION;
                upf_cpy_redirect_information (&far.forward.redirect_information, &tmp_far->forward.redirect_information);
            }
            break;
        } /* end of vec_foreach_index */

        if ((r = upf_create_far (sess, &far)) != 0)
        {
          upf_err ("Failed to add FAR %d\n", create->far_id);
          break;
        }
    }

set_cause:
    if (r != 0)
    {
        response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

        SET_BIT (grp->fields, failed_rule_id_field);
        failed_rule_id->type = FAILED_RULE_TYPE_FAR;
        failed_rule_id->id = far_id;
    }

    return r;
}

static int upf_update_far_handle (upf_session_t *sess, pfcp_update_far_t *update_far, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
    struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
    upf_main_t *gtm = &g_upf_main;
    pfcp_update_far_t *update;
    int r = 0, j = 0;
    u32 i;
    pfcp_far_id_t far_id = {0};

    if (!update_far)
        return r;

    vec_foreach (update, update_far)
    {
        far_id = update->far_id;
        upf_far_t *far = upf_get_far (sess, SX_PENDING, update->far_id);
        if (!far)
        {
            upf_info ("Sx Session %" PRIu64 ", update FAR Id %d not found.\n", sess->cp_seid, update->far_id);
            r = -1;
            break;
        }

        if (ISSET_BIT (update->grp.fields, UPDATE_FAR_APPLY_ACTION))
            far->apply_action = update->apply_action;

        if (ISSET_BIT (update->grp.fields, UPDATE_FAR_UPDATE_FORWARDING_PARAMETERS))
        {
            pfcp_update_forwarding_parameters_t *forw_para = &update->update_forwarding_parameters;
            if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_DESTINATION_INTERFACE))
                far->forward.dst_intf = forw_para->destination_interface;

            if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_FORWARDING_POLICY))
            {
                uword *p;
                upf_forward_policy_t *forward_policy;
                far->forward.flags |= FAR_F_FORWARDING_POLICY;

                if (vec_len (forw_para->forwarding_policy.identifier) != 0)
                {
                    p = hash_get_mem (gtm->forward_policy_by_name, forw_para->forwarding_policy.identifier);
                    if (!p)
                    {
                        upf_info ("FAR: %d, Update Parameter with unknown forwarding policy\n", update->far_id);
                        r = -1;
                        break;
                    }
                    forward_policy = pool_elt_at_index (gtm->forward_policys, p[0]);
                    far->forward.table_id = forward_policy->vrf;
                    far->forward.forward_policy_index = p[0];
                    if (0 != upf_fib_index_by_table_id(far->forward.table_id, &far->forward.fib_index))
                    {
                        upf_info ("vrf(table_id) %d in not (yet) defined", far->forward.table_id);
                        r = -1;
                        break;
                    }
                }
                else
                {
                    far->forward.table_id = 0;
                    far->forward.forward_policy_index = ~0;
                }
            }
            else if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_NETWORK_INSTANCE))
            {
                far->forward.flags |= FAR_F_NETWORK_INSTANCE;
                if (DST_INTF_5G_VN != far->forward.dst_intf)
                {
                    if (vec_len (forw_para->network_instance) != 0)
                    {
                        upf_nwi_t *nwi = upf_lookup_nwi (forw_para->network_instance);
                        if (!nwi)
                        {
                            upf_info ("FAR: %d, Update Parameter with unknown network instance\n", update->far_id);
                            r = -1;
                            break;
                        }
                        far->forward.table_id = nwi->vrf;
                        far->forward.nwi = nwi - gtm->nwis;
                        if (0 != upf_fib_index_by_table_id(far->forward.table_id, &far->forward.fib_index))
                        {
                            upf_info ("vrf(table_id) %d in not (yet) defined", far->forward.table_id);
                            r = -1;
                            break;
                        }
                    }
                    else
                    {
                        far->forward.table_id = 0;
                        far->forward.nwi = ~0;
                    }
                }
            }

            if (DST_INTF_5G_VN == far->forward.dst_intf)
            {
                upf_5glan_nwi_t *nwi_inst = NULL;
                if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_NETWORK_INSTANCE))
                {
                    if (vec_len (forw_para->network_instance) == 0)
                    {
                        upf_warn("5glan far update_forwarding_parameters.network_instance length is 0!");
                        r = -1;
                        break;
                    }
                    nwi_inst = upf_5glan_nwi_lookup(forw_para->network_instance);
                    if (!nwi_inst)
                    {
                        int ret = upf_5glan_nwi_add_del (forw_para->network_instance, 1);
                        upf_warn("upf 5glan nwi:%v(%d) add, ret:%d", forw_para->network_instance,
                            vec_len(forw_para->network_instance), ret);
                        nwi_inst = upf_5glan_nwi_lookup (forw_para->network_instance);
                    }
                }
                else
                {
                    if (~0 == far->forward.nwi)
                    {
                        nwi_inst = upf_5glan_default_nwi_inst();
                    }
                }

                if (nwi_inst)
                    far->forward.nwi = upf_5glan_nwi_to_index(nwi_inst);
                if (~0 == far->forward.nwi)
                {
                    if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_NETWORK_INSTANCE))
                        upf_warn ("5glan NWI: %v nwi_to_index fail, %p!", forw_para->network_instance, nwi_inst);
                    else
                        upf_warn ("5glan default NWI nwi_to_index fail, %p!", nwi_inst);
                    r = -1;
                    break;
                }
                far->forward.table_id = nwi_inst->vrf;
                far->forward.nwi = upf_5glan_nwi_to_index(nwi_inst);
                if (0 != upf_fib_index_by_table_id(far->forward.table_id, &far->forward.fib_index))
                {
                    upf_info ("vrf(table_id) %d in not (yet) defined", far->forward.table_id);
                    r = -1;
                    break;
                }
            }

            if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_DESTINATION_INTERFACE_TYPE))
            {
                far->forward.flags |= FAR_F_DEST_INTF_TYPE;
                far->forward.dest_interface_type = forw_para->destination_interface_type;
            }

            if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_REDIRECT_INFORMATION))
            {
                far->forward.flags |= FAR_F_REDIRECT_INFORMATION;
                upf_free_redirect_information (&far->forward.redirect_information);
                upf_cpy_redirect_information (&far->forward.redirect_information, &forw_para->redirect_information);
            }

            if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_OUTER_HEADER_CREATION))
            {
                pfcp_outer_header_creation_t *ohc = &forw_para->outer_header_creation;
                u32 fib_index;
                int is_ip4 = !!(ohc->description & OUTER_HEADER_CREATION_ANY_IP4);

                if (ip46_address_is_zero (&ohc->ip))
                {
                    upf_warn ("FAR: %d, outer header creation IP is zero\n", update->far_id);
                    r = -1;
                    goto set_cause;
                }

                if (!is_ip4 && ip6_address_is_link_local_unicast(&ohc->ip.ip6))
                {
                    upf_err ("FAR: %d, outer header creation IP is fe80\n", update->far_id);
                    r = -1;
                    goto set_cause;
                }

                if (upf_lookup_upf_ip_blacklist(&ohc->ip))
                {
                    upf_err ("FAR: %d, outer header creation IP in upf ip blacklist\n",
                             update->far_id);
                    r = -1;
                    goto set_cause;
                }

                if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_PFCPSMREQ_FLAGS) &&
                    (forw_para->pfcpsmreq_flags & PFCPSMREQ_SNDEM))
                    upf_pfcp_send_end_marker (sess, update->far_id);

                far->forward.flags |= FAR_F_OUTER_HEADER_CREATION;
                far->forward.outer_header_creation = *ohc;

                fib_index = upf_ip46_fib_index_get_by_table_id (far->forward.table_id, is_ip4);
                if (~0 == fib_index)
                {
                    upf_info ("FAR: %d, table_id:%u, Network instance with invalid VRF for IPv%d\n",
                        update->far_id, far->forward.table_id, is_ip4 ? 4 : 6);
                    r = -1;
                    break;
                }

                if (ohc->description & OUTER_HEADER_CREATION_GTP_ANY)
                    upf_ip_udp_gtpu_rewrite (&far->forward, is_ip4);
                else if (ohc->description & OUTER_HEADER_CREATION_UDP_ANY)
                    upf_ip_udp_rewrite (&far->forward, is_ip4);

                vec_foreach_index (j, gtm->pre_far)
                {
                    upf_far_t *tmp_far, *tmp_far1;
                    tmp_far = vec_elt_at_index (gtm->pre_far, j);
                CHECK_VEC_VALUE_ISVALID_CONTINUE(gtm->pre_far, tmp_far);
                    if ((tmp_far->id != far->id) && (tmp_far->forward.flags & FAR_F_OUTER_HEADER_CREATION)
                         && (tmp_far->forward.dst_intf == 0))
                    {
                        tmp_far1 = upf_get_far (sess, SX_PENDING, tmp_far->id);
                        if (!tmp_far1)
                        {
                            upf_err("session can not find pre far id %u.\n", tmp_far->id);
                            continue;
                        }
                        upf_debug("old teid is %u.\n", tmp_far1->forward.outer_header_creation.teid);
                        tmp_far1->forward.flags |= FAR_F_OUTER_HEADER_CREATION;
                        tmp_far1->forward.outer_header_creation = *ohc;

                        if (ohc->description & OUTER_HEADER_CREATION_GTP_ANY)
                          upf_ip_udp_gtpu_rewrite (&tmp_far1->forward, is_ip4);
                        else if (ohc->description & OUTER_HEADER_CREATION_UDP_ANY)
                          upf_ip_udp_rewrite (&tmp_far1->forward, is_ip4);

                        upf_debug("new teid is %u.\n", tmp_far1->forward.outer_header_creation.teid);
                    }
                }
            }

            if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_TRANSPORT_LEVEL_MARKING))
            {
                far->forward.flags |= FAR_F_TRANSPORT_LEVEL_MARKING;
                far->forward.transport_level_marking = ((forw_para->transport_level_marking & 0xFF) &
                                                       ((forw_para->transport_level_marking >> 8) & 0xFF));
            }

            if (ISSET_BIT (forw_para->grp.fields, UPDATE_FORWARDING_PARAMETERS_HEADER_ENRICHMENT))
            {
                pfcp_header_enrichment_t *header_enrichment;
                far->forward.flags |= FAR_F_HEADER_ENRICHMENT;

                for (i = 0; i < vec_len (far->forward.header_enrichment); i++)
                {
                    vec_free (far->forward.header_enrichment[i].name);
                    vec_free (far->forward.header_enrichment[i].value);
                }
                vec_free (far->forward.header_enrichment);
                vec_add2 (far->forward.header_enrichment, header_enrichment, vec_len (forw_para->header_enrichment));

                for (i = 0; i < vec_len (forw_para->header_enrichment); i++)
                {
                    header_enrichment[i].name = vec_dup (forw_para->header_enrichment[i].name);
                    header_enrichment[i].value = vec_dup (forw_para->header_enrichment[i].value);
                }
            }
        // TODO: forwarding_policy
        }

        if(ISSET_BIT (update->grp.fields, UPDATE_FAR_BAR_ID))
        {
            far->bar_id = OPT (update, UPDATE_FAR_BAR_ID, bar_id, ~0);
        }

        // Add for 5glan multicast N19 duplicating packet by liupeng on 2022-02-22
        r = handle_far_update_duplicating_para(sess, far, update);
        if (r)
        {
            upf_err("handle_far_update_duplicating_para failed.");
            break;
        }
    }

set_cause:
    if (r != 0)
    {
        response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;
        SET_BIT (grp->fields, failed_rule_id_field);
        failed_rule_id->type = FAILED_RULE_TYPE_FAR;
        failed_rule_id->id = far_id;
    }

    return r;
}

static int upf_remove_far_handle (upf_session_t *sess, pfcp_remove_far_t *remove_far, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  pfcp_remove_far_t *far;
  int r = 0;
  // upf_main_t *gtm = &g_upf_main;

  if (!remove_far)
  {
      return r;
  }

  vec_foreach (far, remove_far)
  {
    if ((r = upf_delete_far (sess, far->far_id)) != 0)
      {
        upf_info ("Failed to add FAR %d\n", far->far_id);
        failed_rule_id->id = far->far_id;
        r = -1;
        break;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_FAR;
    }

  return r;
}

static int upf_predef_create_urr_handle (pfcp_create_urr_t *urr, upf_urr_t *update, f64 now)
{
  int r = 0;

  update->methods = urr->measurement_method;
  update->triggers = OPT (urr, UPDATE_URR_REPORTING_TRIGGERS,
                          reporting_triggers, update->triggers);
  update->update_flags = 0;
  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_MEASUREMENT_PERIOD))
    {
      update->update_flags |= SX_URR_UPDATE_MEASUREMENT_PERIOD;
      update->measurement_period.period = urr->measurement_period;
      update->measurement_period.base = now;
    }

  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_VOLUME_THRESHOLD))
    {
      update->update_flags |= SX_URR_UPDATE_VOL_THRESHOLD;
      update->volume.threshold.ul = urr->volume_threshold.ul;
      update->volume.threshold.dl = urr->volume_threshold.dl;
      update->volume.threshold.total = urr->volume_threshold.total;
      update->volume.threshold.fields = urr->volume_threshold.fields;
      clib_atomic_fetch_and (&update->status, ~URR_REACHED_THRESHOLD);
    }
  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_VOLUME_QUOTA))
    {
      update->update_flags |= SX_URR_UPDATE_VOL_QUOTA;
      update->volume.quota.ul = urr->volume_quota.ul;
      update->volume.quota.dl = urr->volume_quota.dl;
      update->volume.quota.total = urr->volume_quota.total;
      update->volume.quota.fields = urr->volume_quota.fields;
      memset (&update->volume.measure.consumed, 0, sizeof (urr_counter_t));
      clib_atomic_fetch_and (&update->status, ~URR_OVER_QUOTA);
    }

  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_TIME_THRESHOLD))
    {
      update->update_flags |= SX_URR_UPDATE_TIME_THRESHOLD;
      update->time_threshold.total_time = urr->time_threshold;
      clib_atomic_fetch_and (&update->status, ~URR_REACHED_THRESHOLD);
    }
  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_TIME_QUOTA))
    {
      update->update_flags |= SX_URR_UPDATE_TIME_QUOTA;
      update->time_quota.total_time = urr->time_quota;
      clib_atomic_fetch_and (&update->status, ~URR_OVER_QUOTA);
    }

  // TODO: quota_holding_time;
  // TODO: dropped_dl_traffic_threshold;

  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_QUOTA_HOLDING_TIME))
    {
      update->update_flags |= SX_URR_UPDATE_QUOTA_HOLDING_TIME;
      update->quota_holding_time.total_time = urr->quota_holding_time;
    }

  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_MONITORING_TIME))
    {
      update->update_flags |= SX_URR_UPDATE_MONITORING_TIME;
      update->monitoring_time.base = urr->monitoring_time;
    }
    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_INACTIVITY_DETECTION_TIME) &&
        urr->inactivity_detection_time)
    {
      update->update_flags |= SX_URR_UPDATE_IDT;
      update->quota_holding_time.period = urr->inactivity_detection_time;
      update->idt.period = urr->inactivity_detection_time;
    }

  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_SUBSEQUENT_VOLUME_THRESHOLD))
    {
      update->update_flags |= SX_URR_UPDATE_SUB_VOL_THRESHOLD;
      update->sub_volume.threshold.ul = urr->subsequent_volume_threshold.ul;
      update->sub_volume.threshold.dl = urr->subsequent_volume_threshold.dl;
      update->sub_volume.threshold.total = urr->subsequent_volume_threshold.total;
      update->sub_volume.threshold.fields = urr->subsequent_volume_threshold.fields;
    }
  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_SUBSEQUENT_VOLUME_QUOTA))
    {
      update->update_flags |= SX_URR_UPDATE_SUB_VOL_QUOTA;
      update->sub_volume.quota.ul = urr->subsequent_volume_quota.ul;
      update->sub_volume.quota.dl = urr->subsequent_volume_quota.dl;
      update->sub_volume.quota.total = urr->subsequent_volume_quota.total;
      update->sub_volume.quota.fields = urr->subsequent_volume_quota.fields;
    }
  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_SUBSEQUENT_TIME_THRESHOLD))
    {
      update->update_flags |= SX_URR_UPDATE_SUB_TIME_THRESHOLD;
      update->sub_time_threshold.total_time = urr->subsequent_time_threshold;
    }
  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_SUBSEQUENT_TIME_QUOTA))
    {
      update->update_flags |= SX_URR_UPDATE_SUB_TIME_QUOTA;
      update->sub_time_quota.total_time = urr->subsequent_time_quota;
    }
  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_FAR_ID_FOR_QUOTE_ACTION))
    {
      update->far_id = urr->far_id_for_quota_action;
    }

  if (ISSET_BIT (urr->grp.fields, CREATE_URR_LINKED_URR_ID))
    {
      upf_debug (
          "pfcp linked urr id update process, linked urr id vec-len=%u.",
          vec_len (update->linked_urr_ids));

      pfcp_linked_urr_id_t *linked_urr_id;
      vec_reset_length (update->linked_urr_ids);

      vec_foreach (linked_urr_id, urr->linked_urr_id)
      {
        upf_debug ("*linked_urr_id=%u.", *linked_urr_id);
        vec_add1 (update->linked_urr_ids, *linked_urr_id);
      }
    }

  if (ISSET_BIT (urr->grp.fields, UPDATE_URR_MEASUREMENT_INFORMATION))
    {
      update->measurement_information = urr->measurement_information.flags;
    }

  return r;
}

static int upf_create_urr_handle (upf_session_t *sess, pfcp_create_urr_t *create_urr, f64 now, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  pfcp_create_urr_t *urr;
  upf_urr_t create;
  int r = 0;
  pfcp_linked_urr_id_t *linked_urr_id;
  upf_urr_t *oldURR;

  if (!create_urr)
  {
      return r;
  }

  vec_foreach (urr, create_urr)
  {
    /* if urr id already exist and is predefine urr, process as update. */
    oldURR = upf_get_urr (sess, SX_PENDING, urr->urr_id);
    if (NULL != oldURR)
      {
        if (urr->urr_id & 0x80000000)
          {
            if (0 != upf_predef_create_urr_handle (urr, oldURR, now))
              {
                upf_info ("Failed to update URR %d \n", urr->urr_id);
                failed_rule_id->id = urr->urr_id;
                r = -1;
                break;
              }
            else
              {
                continue;
              }
          }
        else
          {
            /* exception */
            upf_info ("Failed to add repeat URR %d \n", urr->urr_id);
            failed_rule_id->id = urr->urr_id;
            r = -1;
            break;
          }
      }

    memset (&create, 0, sizeof (create));

    create.measurement_period.handle = create.monitoring_time.handle =
        create.time_threshold.handle = create.time_quota.handle =
        create.idt.handle = create.stop_of_traffic.handle = create.mac_detect.handle =
        create.traf_inact_detect_timer.handle = ~0;

    create.id = urr->urr_id;
    create.methods = urr->measurement_method;
    create.triggers =
        OPT (urr, CREATE_URR_REPORTING_TRIGGERS, reporting_triggers, 0);
    create.start_time = now;

    if (create.triggers & REPORTING_TRIGGER_STOP_OF_TRAFFIC)
      {
        create.update_flags |= SX_URR_UPDATE_STOP_TRAFFIC;
        create.stop_of_traffic.base = now;
        create.stop_of_traffic.period = PFCP_HB_INTERVAL;
      }

    if (ISSET_BIT (urr->grp.fields, CREATE_URR_MEASUREMENT_PERIOD))
      {
        create.update_flags |= SX_URR_UPDATE_MEASUREMENT_PERIOD;
        create.measurement_period.period = urr->measurement_period;
        create.measurement_period.base = now;
      }

    if (ISSET_BIT (urr->grp.fields, CREATE_URR_VOLUME_THRESHOLD))
      {
        create.update_flags |= SX_URR_UPDATE_VOL_THRESHOLD;
        create.volume.threshold.ul = urr->volume_threshold.ul;
        create.volume.threshold.dl = urr->volume_threshold.dl;
        create.volume.threshold.total = urr->volume_threshold.total;
        create.volume.threshold.fields = urr->volume_threshold.fields;
      }

    if (ISSET_BIT (urr->grp.fields, CREATE_URR_VOLUME_QUOTA))
      {
        create.update_flags |= SX_URR_UPDATE_VOL_QUOTA;
        create.volume.quota.ul = urr->volume_quota.ul;
        create.volume.quota.dl = urr->volume_quota.dl;
        create.volume.quota.total = urr->volume_quota.total;
        create.volume.quota.fields = urr->volume_quota.fields;
      }

    if (ISSET_BIT (urr->grp.fields, CREATE_URR_TIME_THRESHOLD))
      {
        create.update_flags |= SX_URR_UPDATE_TIME_THRESHOLD;
        create.time_threshold.total_time = urr->time_threshold;
      }
    if (ISSET_BIT (urr->grp.fields, CREATE_URR_TIME_QUOTA))
      {
        create.update_flags |= SX_URR_UPDATE_TIME_QUOTA;
        create.time_quota.total_time = urr->time_quota;
      }

    // TODO: quota_holding_time;
    // TODO: dropped_dl_traffic_threshold;
    if (ISSET_BIT (urr->grp.fields, CREATE_URR_QUOTA_HOLDING_TIME))
      {
        create.update_flags |= SX_URR_UPDATE_QUOTA_HOLDING_TIME;
        create.quota_holding_time.total_time = urr->quota_holding_time;
      }

    if (ISSET_BIT (urr->grp.fields, CREATE_URR_MONITORING_TIME))
      {
        create.update_flags |= SX_URR_UPDATE_MONITORING_TIME;
        create.monitoring_time.base = urr->monitoring_time;
      }
    if (ISSET_BIT (urr->grp.fields, CREATE_URR_INACTIVITY_DETECTION_TIME) &&
        urr->inactivity_detection_time)
      {
        create.update_flags |= SX_URR_UPDATE_IDT;
        create.quota_holding_time.period = urr->inactivity_detection_time;
        create.idt.period = urr->inactivity_detection_time;
      }

    // TODO: subsequent_volume_threshold;
    // TODO: subsequent_time_threshold;
    if (ISSET_BIT (urr->grp.fields, CREATE_URR_SUBSEQUENT_VOLUME_THRESHOLD))
      {
        create.update_flags |= SX_URR_UPDATE_SUB_VOL_THRESHOLD;
        create.sub_volume.threshold.ul = urr->subsequent_volume_threshold.ul;
        create.sub_volume.threshold.dl = urr->subsequent_volume_threshold.dl;
        create.sub_volume.threshold.total =
            urr->subsequent_volume_threshold.total;
      }
    if (ISSET_BIT (urr->grp.fields, CREATE_URR_SUBSEQUENT_VOLUME_QUOTA))
      {
        create.update_flags |= SX_URR_UPDATE_SUB_VOL_QUOTA;
        create.sub_volume.quota.ul = urr->subsequent_volume_quota.ul;
        create.sub_volume.quota.dl = urr->subsequent_volume_quota.dl;
        create.sub_volume.quota.total = urr->subsequent_volume_quota.total;
      }
    if (ISSET_BIT (urr->grp.fields, CREATE_URR_SUBSEQUENT_TIME_THRESHOLD))
      {
        create.update_flags |= SX_URR_UPDATE_SUB_TIME_THRESHOLD;
        create.sub_time_threshold.total_time = urr->subsequent_time_threshold;
      }
    if (ISSET_BIT (urr->grp.fields, CREATE_URR_SUBSEQUENT_TIME_QUOTA))
      {
        create.update_flags |= SX_URR_UPDATE_SUB_TIME_QUOTA;
        create.sub_time_quota.total_time = urr->subsequent_time_quota;
      }

      //liukang add ethernet inactivity timer 2022/06/01
     if (ISSET_BIT (urr->grp.fields, CREATE_URR_ETHERNET_INACTIVITY_TIMER))
      {
        create.update_flags |= SX_URR_UPDATE_ETH_INACT_TIMER;
        create.mac_detect.period = urr->ethernet_inactivity_timer;
        create.mac_detect.base = now;
      }

    if (ISSET_BIT (urr->grp.fields, CREATE_URR_FAR_ID_FOR_QUOTE_ACTION))
      {
        create.far_id = urr->far_id_for_quota_action;
      }

    /* Andy added */
    if (ISSET_BIT (urr->grp.fields, CREATE_URR_LINKED_URR_ID))
      {
        upf_debug ("pfcp linked urr id process.");
        vec_foreach (linked_urr_id, urr->linked_urr_id)
        {
          upf_debug ("*linked_urr_id=%u.", *linked_urr_id);
          vec_add1 (create.linked_urr_ids, *linked_urr_id);
        }
      }

    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_MEASUREMENT_INFORMATION))
      {
        create.measurement_information = urr->measurement_information.flags;
      }

    if (ISSET_BIT (urr->grp.fields, CREATE_URR_TRAFFIC_INACTIVE_DETECTION))
      {
        if (urr->traffic_inactive_detect.flags & F_INACTIVE_TIMER)
          create.traf_inact_detect_timer.period = urr->traffic_inactive_detect.inactive_detect_timer;
        else
          {
            if (urr->traffic_inactive_detect.type == TRAFFIC_INACTIVE_DETECT_N9_TUNNEL)
              create.traf_inact_detect_timer.period = g_upf_n9_tunnel_inactive_timer;
          }
        if (!create.traf_inact_detect_timer.period)
          {
            r = -1;
            upf_info ("Create URR err, urr_id:%u, traffic inactive detect timer is zero, %U\n",
                urr->urr_id, upf_format_inspur_traffic_inactivity_detection, &urr->traffic_inactive_detect);
            failed_rule_id->id = urr->urr_id;
            break;
          }
        create.update_flags |= SX_URR_UPDATE_TRAFFIC_INACT_TIMER;
        create.traf_inact_detect_timer.base = now;
        create.traf_inact_detect.type = urr->traffic_inactive_detect.type;
      }

    // TODO: measurement_information;
    // TODO: time_quota_mechanism;

    if ((r = upf_create_urr (sess, &create)) != 0)
      {
        upf_info ("Failed to add URR %d\n", urr->urr_id);
        failed_rule_id->id = urr->urr_id;
        r = -1;
        break;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_URR;
    }

  return r;
}

static int upf_update_urr_handle (upf_session_t *sess, pfcp_update_urr_t *update_urr, f64 now, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  pfcp_update_urr_t *urr;
  int r = 0;

  if (!update_urr)
  {
      return r;
  }

  vec_foreach (urr, update_urr)
  {
    upf_urr_t *update;

    update = upf_get_urr (sess, SX_PENDING, urr->urr_id);
    if (!update)
      {
        upf_info ("Sx Session %" PRIu64 ", update URR Id %d not found.\n",
                  sess->cp_seid, urr->urr_id);
        failed_rule_id->id = urr->urr_id;
        r = -1;
        break;
      }

    update->methods = urr->measurement_method;
    update->triggers = OPT (urr, UPDATE_URR_REPORTING_TRIGGERS,
                            reporting_triggers, update->triggers);
    update->update_flags = 0;
    //memset (&update->volume.threshold, 0, sizeof (urr_counter_t));
    //memset (&update->volume.quota, 0, sizeof (urr_counter_t));
    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_MEASUREMENT_PERIOD))
      {
        update->update_flags |= SX_URR_UPDATE_MEASUREMENT_PERIOD;
        update->measurement_period.period = urr->measurement_period;
        update->measurement_period.base = now;
      }

    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_VOLUME_THRESHOLD))
      {
        update->update_flags |= SX_URR_UPDATE_VOL_THRESHOLD;
        update->volume.threshold.ul = urr->volume_threshold.ul;
        update->volume.threshold.dl = urr->volume_threshold.dl;
        update->volume.threshold.total = urr->volume_threshold.total;
        update->volume.threshold.fields = urr->volume_threshold.fields;
        clib_atomic_fetch_and (&update->status, ~URR_REACHED_THRESHOLD);
      }
    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_VOLUME_QUOTA))
      {
        update->update_flags |= SX_URR_UPDATE_VOL_QUOTA;
        update->volume.quota.ul = urr->volume_quota.ul;
        update->volume.quota.dl = urr->volume_quota.dl;
        update->volume.quota.total = urr->volume_quota.total;
        update->volume.quota.fields = urr->volume_quota.fields;
        memset (&update->volume.measure.consumed, 0, sizeof (urr_counter_t));
        clib_atomic_fetch_and (&update->status, ~URR_OVER_QUOTA);
      }

    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_TIME_THRESHOLD))
      {
        update->update_flags |= SX_URR_UPDATE_TIME_THRESHOLD;
        update->time_threshold.total_time = urr->time_threshold;
        clib_atomic_fetch_and (&update->status, ~URR_REACHED_THRESHOLD);
      }
    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_TIME_QUOTA))
      {
        update->update_flags |= SX_URR_UPDATE_TIME_QUOTA;
        update->time_quota.total_time = urr->time_quota;
        clib_atomic_fetch_and (&update->status, ~URR_OVER_QUOTA);
      }

    // TODO: dropped_dl_traffic_threshold;

    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_QUOTA_HOLDING_TIME))
      {
        update->update_flags |= SX_URR_UPDATE_QUOTA_HOLDING_TIME;
        update->quota_holding_time.total_time = urr->quota_holding_time;
      }

    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_MONITORING_TIME))
      {
        update->update_flags |= SX_URR_UPDATE_MONITORING_TIME;
        update->monitoring_time.base = urr->monitoring_time;
      }
    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_INACTIVITY_DETECTION_TIME) &&
        urr->inactivity_detection_time)
      {
        update->update_flags |= SX_URR_UPDATE_IDT;
        update->quota_holding_time.period = urr->inactivity_detection_time;
        update->idt.period = urr->inactivity_detection_time;
      }

    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_SUBSEQUENT_VOLUME_THRESHOLD))
      {
        update->update_flags |= SX_URR_UPDATE_SUB_VOL_THRESHOLD;
        update->sub_volume.threshold.ul = urr->subsequent_volume_threshold.ul;
        update->sub_volume.threshold.dl = urr->subsequent_volume_threshold.dl;
        update->sub_volume.threshold.total = urr->subsequent_volume_threshold.total;
        update->sub_volume.threshold.fields = urr->subsequent_volume_threshold.fields;
      }
    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_SUBSEQUENT_VOLUME_QUOTA))
      {
        update->update_flags |= SX_URR_UPDATE_SUB_VOL_QUOTA;
        update->sub_volume.quota.ul = urr->subsequent_volume_quota.ul;
        update->sub_volume.quota.dl = urr->subsequent_volume_quota.dl;
        update->sub_volume.quota.total = urr->subsequent_volume_quota.total;
        update->sub_volume.quota.fields = urr->subsequent_volume_quota.fields;
      }
    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_SUBSEQUENT_TIME_THRESHOLD))
      {
        update->update_flags |= SX_URR_UPDATE_SUB_TIME_THRESHOLD;
        update->sub_time_threshold.total_time = urr->subsequent_time_threshold;
      }
    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_SUBSEQUENT_TIME_QUOTA))
      {
        update->update_flags |= SX_URR_UPDATE_SUB_TIME_QUOTA;
        update->sub_time_quota.total_time = urr->subsequent_time_quota;
      }
    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_FAR_ID_FOR_QUOTE_ACTION))
      {
        update->far_id = urr->far_id_for_quota_action;
      }

    /* Andy added   linked_urr_id*/
    if (ISSET_BIT (urr->grp.fields, CREATE_URR_LINKED_URR_ID))
      {
        upf_debug (
            "pfcp linked urr id update process, linked urr id vec-len=%u.",
            vec_len (update->linked_urr_ids));

        pfcp_linked_urr_id_t *linked_urr_id;
        vec_reset_length (update->linked_urr_ids);

        vec_foreach (linked_urr_id, urr->linked_urr_id)
        {
          upf_debug ("*linked_urr_id=%u.", *linked_urr_id);
          vec_add1 (update->linked_urr_ids, *linked_urr_id);
        }
      }

    if (ISSET_BIT (urr->grp.fields, UPDATE_URR_MEASUREMENT_INFORMATION))
      {
        update->measurement_information = urr->measurement_information.flags;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_URR;
    }

  return r;
}

static int upf_remove_urr_handle (upf_session_t *sess, pfcp_remove_urr_t *remove_urr, f64 now, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  pfcp_remove_urr_t *urr;
  int r = 0;

  if (!remove_urr)
  {
      return r;
  }

  vec_foreach (urr, remove_urr)
  {
    if ((r = upf_delete_urr (sess, urr->urr_id)) != 0)
      {
        upf_info ("Failed to add URR %d\n", urr->urr_id);
        failed_rule_id->id = urr->urr_id;
        r = -1;
        break;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_URR;
    }

  return r;
}

static int upf_create_qer_handle (upf_session_t *sess, pfcp_create_qer_t *create_qer, f64 now, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  upf_main_t *gtm = &g_upf_main;
  pfcp_create_qer_t *qer;
  upf_qer_t create;
  int r = 0;

  if (!create_qer)
  {
      return r;
  }

  vec_foreach (qer, create_qer)
  {
    clib_memset (&create, 0, sizeof (create));

    create.id = qer->qer_id;

    if (ISSET_BIT (qer->grp.fields, CREATE_QER_QER_CORRELATION_ID))
      {
        create.mbr_policer.k.id = qer->qer_correlation_id;

        create.gbr_policer.k.gbr = 1;
        create.gbr_policer.k.id = qer->qer_correlation_id;
      }
    else
      {
        create.mbr_policer.k.session = sess - gtm->sessions;
        create.mbr_policer.k.id = create.id;

        create.gbr_policer.k.session = sess - gtm->sessions;
        create.gbr_policer.k.gbr = 1;
        create.gbr_policer.k.id = create.id;
      }
    create.mbr_policer.v = ~0;
    create.gbr_policer.v = ~0;

    create.gate_status[UPF_UL] = qer->gate_status.ul;
    create.gate_status[UPF_DL] = qer->gate_status.dl;

    if (ISSET_BIT (qer->grp.fields, CREATE_QER_MBR))
      {
        create.flags |= SX_QER_MBR;
        create.mbr.ul = qer->mbr.ul;
        create.mbr.dl = qer->mbr.dl;
      }

    if (ISSET_BIT (qer->grp.fields, CREATE_QER_GBR))
      {
        create.flags |= SX_QER_GBR;
        create.gbr.ul = qer->gbr.ul;
        create.gbr.dl = qer->gbr.dl;
      }

    // TODO: gbr;
    // TODO: packet_rate;
    if (ISSET_BIT (qer->grp.fields, CREATE_QER_DL_FLOW_LEVEL_MARKING))
      {
        create.flags |= SX_QER_DL_FLOW_LEVEL_MARKING;
        create.dl_flow_level_marking.flags = qer->dl_flow_level_marking.flags;
        create.dl_flow_level_marking.tos_traffic_class =
            qer->dl_flow_level_marking.tos_traffic_class;
        create.dl_flow_level_marking.service_class_indicator =
            qer->dl_flow_level_marking.service_class_indicator;
      }
    if (ISSET_BIT (qer->grp.fields, CREATE_QER_QOS_FLOW_IDENTIFIER))
      {
        create.flags |= SX_QER_QOS_FLOW_IDENTIFIER;
        create.qos_flow_identifier = qer->qos_flow_identifier;
      }
    if (ISSET_BIT (qer->grp.fields, CREATE_QER_REFLECTIVE_QOS))
      {
        create.flags |= SX_QER_REFLECTIVE_QOS;
        create.reflective_qos = qer->reflective_qos;
      }
    if (ISSET_BIT (qer->grp.fields, CREATE_QER_AVERAGING_WINDOW))
      {
        create.flags |= SX_QER_AVERAGING_WINDOW;
        create.averaging_window = qer->averaging_window;
      }

    if ((r = upf_create_qer (sess, &create)) != 0)
      {
        upf_info ("Failed to add QER %d\n", qer->qer_id);
        failed_rule_id->id = qer->qer_id;
        r = -1;
        break;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_QER;
    }

  return r;
}

static int upf_update_qer_handle (upf_session_t *sess, pfcp_update_qer_t *update_qer, f64 now, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  upf_main_t *gtm = &g_upf_main;
  pfcp_update_qer_t *qer;
  int r = 0;

  if (!update_qer)
  {
      return r;
  }

  vec_foreach (qer, update_qer)
  {
    upf_qer_t *update;

    update = upf_get_qer (sess, SX_PENDING, qer->qer_id);
    if (!update)
      {
        upf_info ("Sx Session %" PRIu64 ", update QER Id %d not found.\n",
                  sess->cp_seid, qer->qer_id);
        failed_rule_id->id = qer->qer_id;
        r = -1;
        break;
      }

    if (ISSET_BIT (qer->grp.fields, CREATE_QER_QER_CORRELATION_ID))
      {
        update->mbr_policer.k.id = qer->qer_correlation_id;

        update->gbr_policer.k.gbr = 1;
        update->gbr_policer.k.id = qer->qer_correlation_id;
      }
    else
      {
        update->mbr_policer.k.session = sess - gtm->sessions;
        update->mbr_policer.k.id = update->id;

        update->gbr_policer.k.session = sess - gtm->sessions;
        update->gbr_policer.k.gbr = 1;
        update->gbr_policer.k.id = update->id;
      }
    update->mbr_policer.v = ~0;
    update->gbr_policer.v = ~0;

    if (ISSET_BIT (qer->grp.fields, UPDATE_QER_GATE_STATUS))
      {
        update->gate_status[UPF_UL] = qer->gate_status.ul;
        update->gate_status[UPF_DL] = qer->gate_status.dl;
      }

    if (ISSET_BIT (qer->grp.fields, UPDATE_QER_MBR))
      {
        update->flags |= SX_QER_MBR;
        update->mbr.ul = qer->mbr.ul;
        update->mbr.dl = qer->mbr.dl;
      }

    if (ISSET_BIT (qer->grp.fields, UPDATE_QER_GBR))
      {
        update->flags |= SX_QER_GBR;
        update->gbr.ul = qer->gbr.ul;
        update->gbr.dl = qer->gbr.dl;
      }

    // TODO: gbr;
    // TODO: packet_rate;
    if (ISSET_BIT (qer->grp.fields, UPDATE_QER_DL_FLOW_LEVEL_MARKING))
      {
        update->flags |= SX_QER_DL_FLOW_LEVEL_MARKING;
        update->dl_flow_level_marking.flags = qer->dl_flow_level_marking.flags;
        update->dl_flow_level_marking.tos_traffic_class =
            qer->dl_flow_level_marking.tos_traffic_class;
        update->dl_flow_level_marking.service_class_indicator =
            qer->dl_flow_level_marking.service_class_indicator;
      }
    if (ISSET_BIT (qer->grp.fields, UPDATE_QER_QOS_FLOW_IDENTIFIER))
      {
        update->flags |= SX_QER_QOS_FLOW_IDENTIFIER;
        update->qos_flow_identifier = qer->qos_flow_identifier;
      }
    if (ISSET_BIT (qer->grp.fields, UPDATE_QER_REFLECTIVE_QOS))
      {
        update->flags |= SX_QER_REFLECTIVE_QOS;
        update->reflective_qos = qer->reflective_qos;
      }
    if (ISSET_BIT (qer->grp.fields, UPDATE_QER_AVERAGING_WINDOW))
      {
        update->flags |= SX_QER_AVERAGING_WINDOW;
        update->averaging_window = qer->averaging_window;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_QER;
    }

  return r;
}

static int upf_remove_qer_handle (upf_session_t *sess, pfcp_remove_qer_t *remove_qer, f64 now, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  pfcp_remove_qer_t *qer;
  int r = 0;

  if (!remove_qer)
  {
      return r;
  }

  vec_foreach (qer, remove_qer)
  {
    if ((r = upf_delete_qer (sess, qer->qer_id)) != 0)
      {
        upf_info ("Failed to add QER %d\n", qer->qer_id);
        failed_rule_id->id = qer->qer_id;
        r = -1;
        break;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_QER;
    }

  return r;
}

static int upf_create_bar_handle (upf_session_t *sess, pfcp_create_bar_t *create_bar, f64 now, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  pfcp_create_bar_t *create;
  upf_bar_t bar;
  int r = 0;

  if (!create_bar)
  {
      return r;
  }

  vec_foreach (create, create_bar)
  {
    clib_memset (&bar, 0, sizeof (bar));

    bar.id = create->bar_id;
    //remove 2 lines below for default value by fanbinfeng on 20210817
    bar.suggested_buffering_packets_count = UPF_BUF_PKTS_NUM_PER_SESSION;
    bar.dl_buffering_suggested_packet_count = UPF_BUF_PKTS_NUM_PER_SESSION;

    if (ISSET_BIT (create->grp.fields,
                   CREATE_BAR_DOWNLINK_DATA_NOTIFICATION_DELAY))
      bar.downlink_data_notification_delay =
          create->downlink_data_notification_delay;

    if (ISSET_BIT (create->grp.fields,
                   CREATE_BAR_SUGGESTED_BUFFERING_PACKETS_COUNT))
      {
        bar.suggested_buffering_packets_count =
            create->suggested_buffering_packets_count;
        bar.dl_buffering_suggested_packet_count =
            bar.suggested_buffering_packets_count / 2;
      }

    if ((r = upf_create_bar (sess, &bar)) != 0)
      {
        upf_info ("Failed to add BAR %d\n", create->bar_id);
        failed_rule_id->id = create->bar_id;
        r = -1;
        break;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_BAR;
    }

  return r;
}

static int upf_update_bar_handle (upf_session_t *sess, pfcp_update_bar_request_t *update_bar, f64 now,
                                  struct pfcp_group *grp, int failed_rule_id_field,  pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  pfcp_update_bar_request_t *update;
  int r = 0;

  if (!update_bar)
  {
      return r;
  }

  vec_foreach (update, update_bar)
  {
    upf_bar_t *bar;

    bar = upf_get_bar (sess, SX_PENDING, update->bar_id);
    if (!bar)
      {
        upf_info ("Sx Session %" PRIu64 ", update BAR Id %d not found.\n",
                  sess->cp_seid, update->bar_id);
        failed_rule_id->id = update->bar_id;
        r = -1;
        break;
      }

    if (ISSET_BIT (update->grp.fields,
                   UPDATE_BAR_REQUEST_DOWNLINK_DATA_NOTIFICATION_DELAY))
      bar->downlink_data_notification_delay =
          update->downlink_data_notification_delay;

    if (ISSET_BIT (update->grp.fields,
                   UPDATE_BAR_REQUEST_SUGGESTED_BUFFERING_PACKETS_COUNT))
      {
        bar->suggested_buffering_packets_count =
            update->suggested_buffering_packets_count;
        bar->dl_buffering_suggested_packet_count =
            bar->suggested_buffering_packets_count / 2;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_BAR;
    }

  return r;
}

static int upf_update_bar_response_handle (upf_session_t *sess, pfcp_update_bar_response_t *update_bar)
{
  pfcp_update_bar_response_t *update;
  int r = 0;

  vec_foreach (update, update_bar)
  {
    upf_bar_t *bar;

    bar = upf_get_bar (sess, SX_PENDING, update->bar_id);
    if (!bar)
      {
        upf_info ("Sx Session %" PRIu64 ", update BAR Id %d not found.\n",
                  sess->cp_seid, update->bar_id);
        r = -1;
        break;
      }

    if (ISSET_BIT (update->grp.fields,
                   UPDATE_BAR_RESPONSE_DOWNLINK_DATA_NOTIFICATION_DELAY))
      bar->downlink_data_notification_delay =
          update->downlink_data_notification_delay;

    if (ISSET_BIT (update->grp.fields,
                   UPDATE_BAR_RESPONSE_DL_BUFFERING_DURATION))
      {
        switch (update->dl_buffering_duration.unit)
          {
          case 0:
            bar->dl_buffering_duration =
                update->dl_buffering_duration.value * 2;
            break;
          case 2:
            bar->dl_buffering_duration =
                update->dl_buffering_duration.value * 10 * 60;
            break;
          case 3:
            bar->dl_buffering_duration =
                update->dl_buffering_duration.value * 3600;
            break;
          case 4:
            bar->dl_buffering_duration =
                update->dl_buffering_duration.value * 3600 * 10;
            break;
          case 7:
            bar->dl_buffering_duration = ~0;
            break;
          default:
            bar->dl_buffering_duration =
                update->dl_buffering_duration.value * 60;
            break;
          }
        bar->dl_buffering_start_time = 0;
      }

    if (ISSET_BIT (update->grp.fields,
                   UPDATE_BAR_RESPONSE_DL_BUFFERING_SUGGESTED_PACKET_COUNT))
      bar->dl_buffering_suggested_packet_count =
          update->dl_buffering_suggested_packet_count;

    if (ISSET_BIT (update->grp.fields,
                   UPDATE_BAR_RESPONSE_SUGGESTED_BUFFERING_PACKETS_COUNT))
      bar->suggested_buffering_packets_count =
          update->suggested_buffering_packets_count;
  }

  return r;
}

static int upf_remove_bar_handle (upf_session_t *sess, pfcp_remove_bar_t *remove_bar, f64 now, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
  struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
  pfcp_remove_bar_t *remove;
  int r = 0;

  vec_foreach (remove, remove_bar)
  {
    if ((r = upf_delete_bar (sess, remove->bar_id)) != 0)
      {
        upf_info ("Failed to remove BAR %d\n", remove->bar_id);
        failed_rule_id->id = remove->bar_id;
        r = -1;
        break;
      }
  }

  if (r != 0)
    {
      response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

      SET_BIT (grp->fields, failed_rule_id_field);
      failed_rule_id->type = FAILED_RULE_TYPE_BAR;
    }

  return r;
}

upf_per_qos_flow_control_t *upf_per_qos_flow_control_init_by_pfcp(pfcp_qos_monitoring_per_qos_flow_control_info_t *src, f64 now)
{
    upf_per_qos_flow_control_t *dest = NULL;
    upf_per_qos_flow_control_t *p;
    pfcp_qos_monitoring_per_qos_flow_control_info_t *ctrl;
    vec_foreach (ctrl, src)
    {
        upf_per_qos_flow_control_t t = {0};
        t.requested_qos_monitor = ctrl->requested_qos_monitor;
        t.reporting_frequency = ctrl->reporting_frequency;

        t.fields = ctrl->grp.fields;
        if (ISSET_BIT(t.fields, QOS_MONITORING_PER_QOS_FLOW_CONTROL_INFO_PKT_DELAY_THRESHOLD))
            t.packet_delay_threshold = ctrl->packet_delay_threshold;
        if (ISSET_BIT(t.fields, QOS_MONITORING_PER_QOS_FLOW_CONTROL_INFO_MIN_WAIT_TIME))
            t.minimum_wait_time = ctrl->minimum_wait_time;
        if (ISSET_BIT(t.fields, QOS_MONITORING_PER_QOS_FLOW_CONTROL_INFO_MEASURE_PERIOD))
            t.measurement_period = ctrl->measurement_period;

        pfcp_qfi_t *qfi;
        upf_per_qos_monitor_flow_control_t flow_ctrl = {0};
        vec_foreach (qfi, ctrl->qfi)
        {
            flow_ctrl.qfi = *qfi;
            if (t.reporting_frequency.flags & IE_REPORTING_FREQUENCYE_PERIOD)
            {
                if (upf_monitor_send_timer_init(&flow_ctrl.periodic_report, t.measurement_period, now))
                {
                    upf_info ("frequency_period: timer init fail, measurement period:%u ", t.measurement_period);
                    goto fail;
                }
                flow_ctrl.qmp_trigger_flag |= QMP_TRIG_PERIOD;
            }

            if (t.reporting_frequency.flags & (IE_REPORTING_FREQUENCYE_EVENT | IE_REPORTING_FREQUENCYE_SESRL))
            {
                if (upf_monitor_send_timer_init(&flow_ctrl.default_report, g_upf_per_qos_timer_config.t1, now))
                {
                    upf_info ("default timer init fail, detect period:%u ", g_upf_per_qos_timer_config.t1);
                    goto fail;
                }
                flow_ctrl.qmp_trigger_flag |= QMP_TRIG_DEFAULT;
            }

            vec_add1(t.flow_ctrl, flow_ctrl);
        }
        vec_add1(dest, t);
    }
    return dest;

fail:
    vec_foreach (p, dest)
    {
        vec_free(p->flow_ctrl);
    }
    vec_free(dest);
    return NULL;
}

static int upf_create_srr_handle (upf_session_t *sess, pfcp_create_srr_t *create_srr, f64 now, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
    struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
    pfcp_create_srr_t *create;
    upf_srr_t srr;
    int r = 0;

    if (!create_srr)
    {
        return r;
    }

    vec_foreach (create, create_srr)
    {
        clib_memset (&srr, 0, sizeof (srr));
        srr.id = create->srr_id;
        srr.per_qos_flow_ctrl = upf_per_qos_flow_control_init_by_pfcp (create->qos_monitor_per_qos_ctrl, now);

        if ((r = upf_create_srr (sess, &srr)) != 0)
        {
              upf_info ("Failed to add SRR %d\n", srr.id);
              failed_rule_id->id = srr.id;
              r = -1;
              break;
        }
    }

    if (r != 0)
    {
        response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;
        SET_BIT (grp->fields, failed_rule_id_field);
        failed_rule_id->type = FAILED_RULE_TYPE_SRR;
    }
    return r;
}

static int upf_update_srr_handle (upf_session_t *sess, pfcp_update_srr_t *update_srr, f64 now,
                                  struct pfcp_group *grp, int failed_rule_id_field,  pfcp_failed_rule_id_t *failed_rule_id)
{
    struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
    pfcp_update_srr_t *update;
    int r = 0;

    if (!update_srr)
        return r;

    vec_foreach (update, update_srr)
    {
        upf_srr_t *srr = upf_get_srr (sess, SX_PENDING, update->srr_id);
        if (!srr)
        {
            upf_info ("Sx Session 0x%llx, update SRR Id %d not found.\n", sess->cp_seid, update->srr_id);
            failed_rule_id->id = update->srr_id;
            r = -1;
            break;
        }
    }

    if (r != 0)
    {
        response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;
        SET_BIT (grp->fields, failed_rule_id_field);
        failed_rule_id->type = FAILED_RULE_TYPE_SRR;
    }

    return r;
}

static int upf_remove_srr_handle (upf_session_t *sess, pfcp_remove_srr_t *remove_srr, f64 now, struct pfcp_group *grp,
                                  int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
    struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
    pfcp_remove_srr_t *srr;
    int r = 0;

    if (!remove_srr)
        return r;

    vec_foreach (srr, remove_srr)
    {
        if ((r = upf_delete_srr (sess, srr->srr_id)) != 0)
        {
            upf_info ("Failed to add SRR %d\n", srr->srr_id);
            failed_rule_id->id = srr->srr_id;
            r = -1;
            break;
        }
    }

    if (r != 0)
    {
        response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;
        SET_BIT (grp->fields, failed_rule_id_field);
        failed_rule_id->type = FAILED_RULE_TYPE_SRR;
    }

    return r;
}

//begin liukang add for l2tp 2021/06/16
#define PAP_HTVPN_L2TPD "user \"%s\"\n"    \
                        "password  \"%s\"\n" \
                        "+pap\n" \
                        "unit 0\n" \
                        "nodeflate\n" \
                        "nobsdcomp\n" \
                        "auth\n"     \
                        "persist\n"  \
                        "nopcomp\n"  \
                        "noaccomp\n" \
                        "noipdefault\n" \
                        "maxfail 0\n"  \
                        "debug\n"  \
                        "logfile /tmp/ppp.log\n"

#define PAP_SECRETS   "# Secrets for authentication using PAP\n" \
                      "*   *   %s   *\n"

#define XL2TPD_CONF_DEFAULT   "[lac htvpn]\n" \
                              "name=%s\n"   \
                              "lns = %s\n"  \
                              "ppp debug = yes\n" \
                              "challenge = %s\n" \
                              "pppoptfile = /etc/ppp/peers/htvpn.l2tpd\n"

#define XL2TPD_CONF_HOSTNAME   "[lac htvpn]\n" \
                              "name=%s\n"   \
                              "lns = %s\n"  \
                              "ppp debug = yes\n" \
                              "hostname = %s\n"  \
                              "challenge = %s\n" \
                              "pppoptfile = /etc/ppp/peers/htvpn.l2tpd\n"
#define XL2TPD_CONF   "[lac htvpn]\n" \
                      "name=%s\n"   \
                      "lns = %s\n"  \
                      "ppp debug = yes\n" \
                      "hostname = upf\n"  \
                      "challenge = yes\n" \
                      "pppoptfile = /etc/ppp/peers/htvpn.l2tpd\n" \
                      "calling number = %s\n"

#define CHAP_XL2TPD_CONF_DEFAULT  "[lac htvpn]\n" \
                                  "lns = %s\n"  \
                                  "ppp debug = yes\n" \
                                  "challenge = %s\n" \
                                  "pppoptfile = /etc/ppp/peers/htvpn.l2tpd\n"

#define CHAP_XL2TPD_CONF_HOSTNAME   "[lac htvpn]\n" \
                              "lns = %s\n"  \
                              "ppp debug = yes\n" \
                              "hostname = %s\n"  \
                              "challenge = %s\n" \
                              "pppoptfile = /etc/ppp/peers/htvpn.l2tpd\n"


#define CHAP_XL2TPD_CONF  "[lac htvpn]\n" \
                          "lns = %s\n"  \
                          "ppp debug = yes\n" \
                          "hostname = upf\n"  \
                          "challenge = yes\n" \
                          "pppoptfile = /etc/ppp/peers/htvpn.l2tpd\n" \
                          "calling number = %s\n"

#define CHAP_HTVPN_L2TPD "remotename htvpn\n" \
                         "user \"test\"\n"    \
                         "password  \"123456\"\n" \
                         "unit 0\n" \
                         "nodeflate\n" \
                         "nobsdcomp\n" \
                         "auth\n"     \
                         "persist\n"  \
                         "nopcomp\n"  \
                         "noaccomp\n" \
                         "maxfail 0\n"  \
                         "debug\n"    \
                         "noipdefault\n"

#define L2TP_SECRETS  "# Secrets for authenticating l2tp tunnels\n" \
                      "# us    them    secret\n" \
                      " *    *    test\n"

upf_l2tp_para upf_l2tp;


int upf_get_n6_address(char *address)
{
    char buf[256] = {0};
    FILE *fp = NULL;
    fp = fopen("/etc/xl2tpd/xl2tpd.conf","r");
    while(fgets(buf,256,fp) !=NULL)
    {
        buf[strlen(buf)-1] = '\0';
        if(strstr(buf,"lns") && strstr(buf,"."))
        {
            char *p = buf;
            int i = 0;
            for(i = 0;i<strlen(buf);i++)
            {
                if(isdigit(*p))
                {
                    break;
                }
                p++;
            }
            strcpy(address,p);
            fclose(fp);
            return 0;
        }
    }
    fclose(fp);
    return 0;
}

int upf_start_xl2tpd()
{
    FILE *ptr;
    char pid_buf[64] = {0};
    char cmd_buf[64] = {0};
    if((ptr = popen("pidof xl2tpd","r"))!= NULL)
    {
            fgets(pid_buf,64,ptr);
            pid_buf[strlen(pid_buf)-1] = '\0';
            upf_debug("buf = %s\n", pid_buf);
            sprintf(cmd_buf,"kill %s",pid_buf);
            upf_debug("cmd_buf = %s\n", cmd_buf);
            system(cmd_buf);


            if(access("/var/run/xl2tpd/l2tp-control",F_OK) == 0)
            {
                upf_debug("/var/run/xl2tpd/l2tp-control exist!\n");
            }
            else
            {
                upf_debug("/var/run/xl2tpd/l2tp-control not exist!\n");
                system("mkdir /var/run/xl2tpd;touch /var/run/xl2tpd/l2tp-control");
            }

            system("/usr/sbin/xl2tpd &");
            usleep(200000);
            system("echo \"c htvpn\" > /var/run/xl2tpd/l2tp-control");
            usleep(500000);
            pclose(ptr);
    }
    else
    {
            return -1;
    }
    return 0;

}

static int upf_l2tp_user_info_handle (upf_session_t *sess, pfcp_cmcc_l2tp_user_information_t *l2tp_user_info, f64 now,
                                      struct pfcp_group *grp, int failed_rule_id_field, pfcp_failed_rule_id_t *failed_rule_id)
{
    struct pfcp_response *response = (struct pfcp_response *)(grp + 1);
    u8 flags1 = 0;

    pfcp_l2tp_session_information_t *l2tp_session_info = &sess->upf_l2tp_session_information;
    pfcp_l2tp_tunnel_information_t *l2tp_tunnel_info = &sess->upf_l2tp_tunnel_information;

    pfcp_l2tp_lns_address_t *lns_address = &l2tp_tunnel_info->lns_address;
    pfcp_l2tp_tunnel_password_t *tunnel_passwd = &l2tp_tunnel_info->tunnel_password;

    pfcp_l2tp_user_authentication_t *user_authentication = &l2tp_session_info->l2tp_user_authentication;

    flags1 = user_authentication->flags;


    char username[128] = {0};
    char pap_passwd[128] = {0};
    //char chap_challenge[128] = {0};
    char l2tp_msg[2048] = {0};
    int r = 0;
    char address_n6[64] = {0};
    char calling_number[16] = {0};

    pfcp_user_id_t user_id = sess->user_id;
    if(user_id.imei_len > 0)
    {
        strcpy(calling_number,(char *)user_id.imei_str);
    }
    FILE *fp = NULL;

    //flags1 = l2tp_user_info->flags1;
    if(flags1 == 0)
    {
        return 0;
    }

    u8 *s = NULL;

    if(user_authentication->proxy_authen_name_len > 0)
    {
        strncpy(username,(char *)user_authentication->proxy_authen_name,user_authentication->proxy_authen_name_len);
        upf_debug("username = %s\n",username);
    }

    if(tunnel_passwd->tunnel_password_len > 0)
    {
        strncpy(pap_passwd,(char *)tunnel_passwd->tunnel_password,tunnel_passwd->tunnel_password_len);
        upf_debug("pap_passwd = %s\n",pap_passwd);
    }
    if(lns_address->is_ip4 == 1)
    {
        s = format(s, "%U", format_ip4_address, &lns_address->lns_ipv4);
        strcpy(address_n6,(char *)s);
    }
    else
    {
        s = format(s, "%U", format_ip6_address, &lns_address->lns_ipv6);
        strcpy(address_n6,(char *)s);
    }

    if (user_id.imei_len > 0)
    {
        upf_debug("calling number = %s", calling_number);
    }
    upf_debug("address_n6 = %s\n",address_n6);

    //if(flags1 & L2TP_PROXY_AUTHEN_CHALLENGE)
    if(g_upf_l2tp_authen_type == 0)  //chap
    {
        //strncpy(chap_challenge,(char *)l2tp_user_info->cha_challenge,l2tp_user_info->chap_challenge_len);
        system("rm /etc/xl2tpd -rf");
        system("cp /etc/xl2tpd_chap /etc/xl2tpd -raf");
        system("rm /etc/ppp -rf");
        system("cp /etc/ppp_chap /etc/ppp -raf");
        //r = upf_get_n6_address(lns_address,address_n6);
        memset(l2tp_msg,0,2048);

        {
            if(upf_l2tp.have_hostname)
            {
                if(upf_l2tp.is_challenge)
                {
                    sprintf(l2tp_msg,CHAP_XL2TPD_CONF_HOSTNAME,address_n6,upf_l2tp.host_name,"yes");
                }
                else
                {
                    sprintf(l2tp_msg,CHAP_XL2TPD_CONF_HOSTNAME,address_n6,upf_l2tp.host_name,"no");
                }
            }
            else
            {
                if(upf_l2tp.is_challenge)
                {
                    sprintf(l2tp_msg,CHAP_XL2TPD_CONF_DEFAULT,address_n6,"yes");
                }
                else
                {
                    sprintf(l2tp_msg,CHAP_XL2TPD_CONF_DEFAULT,address_n6,"no");
                }
            }
        }


        fp = fopen("/etc/xl2tpd/xl2tpd.conf","w");
        if(fp != NULL)
        {
            fprintf(fp,"%s",l2tp_msg);
            fclose(fp);
        }

        fp = NULL;

        fp = fopen("/etc/xl2tpd/l2tp-secrets","w");
        if(fp != NULL)
        {
            fprintf(fp,"%s",L2TP_SECRETS);
            fclose(fp);
        }

        if (user_id.imei_len > 0)
        {
            char calling_number_msg[128] = {0};
            sprintf(calling_number_msg,"echo \"calling number = %s\" >> /etc/xl2tpd/xl2tpd.conf",calling_number);
            system(calling_number_msg);

        }

        upf_debug("chap  upf_start_xl2tpd");
        upf_start_xl2tpd();
    }
    else  //pap
    {
        system("rm /etc/xl2tpd -rf");
        system("rm /etc/ppp -rf");
        system("cp /etc/xl2tpd_pap /etc/xl2tpd -raf");
        system("cp /etc/ppp_pap /etc/ppp -raf");

        sprintf(l2tp_msg,PAP_HTVPN_L2TPD,username,pap_passwd);
        fp = fopen("/etc/ppp/peers/htvpn.l2tpd","w");
        if(fp != NULL)
        {
            fprintf(fp,"%s",l2tp_msg);
            fclose(fp);
        }

        memset(l2tp_msg,0,2048);
        sprintf(l2tp_msg,PAP_SECRETS,pap_passwd);
        fp = NULL;
        fp = fopen("/etc/ppp/pap-secrets","w");
        if(fp != NULL)
        {
            fprintf(fp,"%s",l2tp_msg);
            fclose(fp);
        }

        //r = upf_get_n6_address(address_n6);
        memset(l2tp_msg,0,2048);

        {
            if(upf_l2tp.have_hostname)
            {
                if(upf_l2tp.is_challenge)
                {
                    sprintf(l2tp_msg,XL2TPD_CONF_HOSTNAME,username,address_n6,upf_l2tp.host_name,"yes");
                }
                else
                {
                    sprintf(l2tp_msg,XL2TPD_CONF_HOSTNAME,username,address_n6,upf_l2tp.host_name,"no");
                }
            }
            else
            {
                if(upf_l2tp.is_challenge)
                {
                    sprintf(l2tp_msg,XL2TPD_CONF_DEFAULT,username,address_n6,"yes");
                }
                else
                {
                    sprintf(l2tp_msg,XL2TPD_CONF_DEFAULT,username,address_n6,"no");
                }
            }
            //sprintf(l2tp_msg, XL2TPD_CONF_DEFAULT, username, address_n6);
        }

        fp = NULL;
        fp = fopen("/etc/xl2tpd/xl2tpd.conf","w");
        if(fp != NULL)
        {
            fprintf(fp,"%s",l2tp_msg);
            fclose(fp);
        }

        fp = NULL;
        fp = fopen("/etc/xl2tpd/l2tp-secrets","w");
        if(fp != NULL)
        {
            fprintf(fp,"%s",L2TP_SECRETS);
            fclose(fp);
        }

        if (user_id.imei_len > 0)
        {
            char calling_number_msg[128] = {0};
            sprintf(calling_number_msg,"echo \"calling number = %s\" >> /etc/xl2tpd/xl2tpd.conf",calling_number);
            system(calling_number_msg);
        }

        upf_debug("pap  upf_start_xl2tpd");
        upf_start_xl2tpd();
    }

    if (r != 0)
    {
        response->cause = PFCP_CAUSE_RULE_CREATION_MODIFICATION_FAILURE;

        SET_BIT (grp->fields, failed_rule_id_field);
        failed_rule_id->type = FAILED_RULE_TYPE_BAR;
    }

    return r;
}

int upf_load_lns_push_info(pfcp_l2tp_session_information_t *l2tp_session_info,pfcp_l2tp_created_l2tp_session_t *lns_info)
{
    char ppp_address[64] = {0};
    FILE *ptr = NULL;
    char t_buf[256] = {0};
    char cmd_str[256] = {0};
    ip4_address_t ms_ipv4;

    u8 flags1 = 0;


    flags1 = l2tp_session_info->l2tp_session_indications.flags;
    if(flags1 == 0)
    {
        return 0;
    }

    sleep(1);
    //usleep(400000);

    int i = 0;
    char cmd_buf[64] = {0};
    for(i = 0;i < 10;i++)
    {
        sprintf(cmd_buf,"ifconfig ppp%d",i);
        upf_debug("get ppp%d address = %s\n",i,cmd_buf);
        if((ptr = popen(cmd_buf,"r"))!= NULL)
        {
            while(fgets(t_buf,256,ptr)!=NULL)
            {
                char *p = strstr(t_buf,"inet");
                char *q = NULL;
                if(p != NULL)
                {
                    p = p + strlen("inet");
                    p++;

                    q = p;
                    while(*p!=' ')
                       p++;
                    *p = '\0';
                    strcpy(ppp_address,q);
                    upf_debug("ppp%d address = %s\n",i,ppp_address);
                    struct in_addr t_addr = {0};
                    inet_aton(ppp_address, &t_addr);
                    ms_ipv4.data_u32 = t_addr.s_addr;
                    //ms_ipv4.as_u8[3] = ms_ipv4.as_u8[3] + 1;
                    lns_info->lns_address.is_ip4 = 1;
                    lns_info->lns_address.lns_ipv4 = ms_ipv4;

                    SET_BIT (lns_info->grp.fields, CREATED_L2TP_SESSION_LNS_ADDRESS);

                    //s = format(s, "%U", format_ip4_address, &ms_ipv4);
                    //strcpy(ppp_address,(char *)s);
                    upf_debug("ms_ue address = %s\n",ppp_address);

                    sprintf(cmd_str,"ifconfig ppp%d *******/24",i);
                    system(cmd_str);
                    //printf("lns_info->ms_ipv4.as_u8[0] = %u\n",lns_info->ms_ipv4.as_u8[0]);
                    //printf("lns_info->ms_ipv4.as_u8[1] = %u\n",lns_info->ms_ipv4.as_u8[1]);
                    //printf("lns_info->ms_ipv4.as_u8[2] = %u\n",lns_info->ms_ipv4.as_u8[2]);
                    //printf("lns_info->ms_ipv4.as_u8[3] = %u\n",lns_info->ms_ipv4.as_u8[3]);
                    return 1;
                }
            }
            pclose(ptr);
            //return 0;
        }
    }

    return -1;

}

int upf_pfcp_load_info_report (pfcp_load_control_information_t *lc)
{
    if (g_upf_load_control_swtich == SWITCH_OFF)
        return 0;

    static f64 prev_now = 0;
    if (sx_server_main.now - prev_now >= g_upf_load_ctrl_param.interval)
        prev_now = sx_server_main.now;
    else
    {
        return 0;
    }

    if (!g_upf_main.lc_max_pps)
        return 0;

    u32 current_rate = stat_segment_main.directory_vector[STAT_COUNTER_INPUT_RATE].value;
    lc->metric = round (current_rate / (g_upf_main.lc_max_pps * 10000)); /* (pps / 1000000) * 100 */
    if (lc->metric < g_upf_load_ctrl_param.metric_threshold)
        return 0;

    SET_BIT (lc->grp.fields, LOAD_CONTROL_INFORMATION_SEQUENCE_NUMBER);
    lc->sequence_number = unix_time_now ();
    SET_BIT (lc->grp.fields, LOAD_CONTROL_INFORMATION_METRIC);
    if (lc->metric > 100)
        lc->metric = 100; /* refer to: 29244 h40 8.2.34 */

    g_upf_load_ctrl_param.last_metric_reported = lc->metric;
    return 1;
}

int upf_pfcp_overload_report (pfcp_overload_control_information_t *oc)
{
    upf_main_t *gtm = &g_upf_main;
    static u32 upf_sequence_number = 1;
    static f64 prev_now = 0;

    if (!gtm->threshold_value)
        return 0;

    if (sx_server_main.now - prev_now >= g_upf_overload_ctrl_param.interval) /* 2s */
        prev_now = sx_server_main.now;
    else
    {
        return 0;
    }

    SET_BIT (oc->grp.fields, OVERLOAD_CONTROL_INFORMATION_SEQUENCE_NUMBER);
    SET_BIT (oc->grp.fields, OVERLOAD_CONTROL_INFORMATION_METRIC);
    SET_BIT (oc->grp.fields, OVERLOAD_CONTROL_INFORMATION_TIMER);
    oc->timer = 1; /* 2s (000-00001) */
    oc->sequence_number = upf_sequence_number++;

    u32 base = gtm->threshold_value + g_n4_dev_session_num;
    upf_trace ("session_num %u  threshold_value:%u  dev_num:%u\n", gtm->session_num, gtm->threshold_value, g_n4_dev_session_num);
    if (gtm->session_num > base)
    {
        u32 val = round ((f64)(gtm->session_num - base) * 100 / base);
        oc->metric = val <= 100 ? (u8)val : 100;
    }
    else
    {
        oc->metric = 0;
    }
    g_upf_overload_ctrl_param.last_metric_reported = oc->metric;
    return 1;
}

pfcp_usage_report_t *upf_usage_report_init (upf_urr_t *urr, u32 trigger, pfcp_usage_report_t **report)
{
  pfcp_usage_report_t *r;

  vec_alloc (*report, 1);
  r = vec_end (*report);

  SET_BIT (r->grp.fields, USAGE_REPORT_URR_ID);
  r->urr_id = urr->id;

  SET_BIT (r->grp.fields, USAGE_REPORT_UR_SEQN);
  r->ur_seqn = urr->seq_no;
  urr->seq_no++;

  SET_BIT (r->grp.fields, USAGE_REPORT_USAGE_REPORT_TRIGGER);
  r->usage_report_trigger = trigger;

  return r;
}

#define set_threshlod_report_measurement(D)                         \
  if (urr->volume.threshold.D &&                                    \
      (urr->volume.measure.bytes.D >= urr->volume.threshold.D))     \
    {                                                               \
      r->volume_measurement.volume.D = urr->volume.threshold.D;     \
      urr->volume.measure.bytes.D =                                 \
          urr->volume.measure.bytes.D - urr->volume.threshold.D;    \
    }                                                               \
  else                                                              \
    {                                                               \
      r->volume_measurement.volume.D = urr->volume.measure.bytes.D; \
      urr->volume.measure.bytes.D = 0;                              \
    }

#define set_quota_report_measurement(D)                             \
  if (urr->volume.quota.D &&                                        \
      (urr->volume.measure.bytes.D >= urr->volume.quota.D))         \
    {                                                               \
      r->volume_measurement.volume.D =                              \
          urr->volume.quota.D + urr->volume.threshold.D;            \
      urr->volume.measure.bytes.D =                                 \
          urr->volume.measure.bytes.D - urr->volume.quota.D;        \
    }                                                               \
  else                                                              \
    {                                                               \
      r->volume_measurement.volume.D = urr->volume.measure.bytes.D; \
      urr->volume.measure.bytes.D = 0;                              \
    }

pfcp_usage_report_t * upf_usage_report_build (upf_session_t *sess, upf_urr_t *urr, u32 trigger, f64 now,
                                              pfcp_usage_report_t **report, u32 pdr_index)
{
  pfcp_usage_report_t *r;
  u32 start, end;

  if (urr->status & URR_AFTER_MONITORING_TIME)
    {
      r = upf_usage_report_init (urr, USAGE_REPORT_TRIGGER_MONITORING_TIME,
                             report);

      SET_BIT (r->grp.fields, USAGE_REPORT_USAGE_INFORMATION);
      r->usage_information = USAGE_INFORMATION_BEFORE;

      /* TODO: apply proper rounding, the f64 to u32 conversion works a trunc
       */
      start = urr->usage_before_monitoring_time.start_time;
      end = urr->start_time;
      if (urr->methods & SX_URR_VOLUME)
        {
          SET_BIT (r->grp.fields, USAGE_REPORT_VOLUME_MEASUREMENT);
          r->volume_measurement.fields = 7;

          r->volume_measurement.volume.ul =
              urr->usage_before_monitoring_time.volume.bytes.ul;
          r->volume_measurement.volume.dl =
              urr->usage_before_monitoring_time.volume.bytes.dl;
          r->volume_measurement.volume.total =
              urr->usage_before_monitoring_time.volume.bytes.total;
        }
      else if (urr->methods & SX_URR_TIME)
        {
          SET_BIT (r->grp.fields, USAGE_REPORT_DURATION_MEASUREMENT);
          r->duration_measurement = end - start;
        }
      SET_BIT (r->grp.fields, USAGE_REPORT_TIME_OF_FIRST_PACKET);
      r->time_of_first_packet = urr->traffic.first_seen;
      SET_BIT (r->grp.fields, USAGE_REPORT_TIME_OF_LAST_PACKET);
      r->time_of_last_packet = end;
      urr->monitoring_time.base = 0;

      _vec_len (*report)++;
    }

  r = upf_usage_report_init (urr, trigger, report);

  if (urr->status & URR_AFTER_MONITORING_TIME)
    {
      SET_BIT (r->grp.fields, USAGE_REPORT_USAGE_INFORMATION);
      r->usage_information = USAGE_INFORMATION_AFTER;
      clib_atomic_fetch_and (&urr->status, ~URR_AFTER_MONITORING_TIME);
    }

  /* TODO: apply proper rounding, the f64 to u32 conversion works a trunc */
  start = urr->start_time;
  end = now;

  if ((trigger & (USAGE_REPORT_TRIGGER_START_OF_TRAFFIC |
                  USAGE_REPORT_TRIGGER_STOP_OF_TRAFFIC)) == 0)
    {
      SET_BIT (r->grp.fields, USAGE_REPORT_START_TIME);
      SET_BIT (r->grp.fields, USAGE_REPORT_END_TIME);

      r->start_time = start;
      r->end_time = end;

      SET_BIT (r->grp.fields, USAGE_REPORT_TIME_OF_FIRST_PACKET);
      if (!urr->traffic.first_seen)
        urr->traffic.first_seen = start;
      r->time_of_first_packet = urr->traffic.first_seen;
      SET_BIT (r->grp.fields, USAGE_REPORT_TIME_OF_LAST_PACKET);
      r->time_of_last_packet = end;
    }
  else
    {
      upf_pdr_t *pdr = NULL;
      if (pdr_index != UPF_INVALID_PDR)
        {
          pdr = upf_get_pdr_by_index(sess, pdr_index);
          if (pdr && pdr->pdi.app_name)
            {
              SET_BIT (r->grp.fields, USAGE_REPORT_APPLICATION_DETECTION_INFORMATION);
              SET_BIT (r->application_detection_information.grp.fields, APPLICATION_DETECTION_INFORMATION_APPLICATION_ID);
              r->application_detection_information.application_id = vec_dup(pdr->pdi.app_name);
            }
        }

      if (trigger & USAGE_REPORT_TRIGGER_START_OF_TRAFFIC)
        {
          SET_BIT (r->grp.fields, USAGE_REPORT_TIME_OF_FIRST_PACKET);
          r->time_of_first_packet = urr->traffic.first_seen = now;
          urr->traffic.last_seen = 0;

          /* neil.fan@20221214 fix inconsistency issue for both 'start' and 'stop' report with or without appid */
          /* neil.fan@20230103 fix report inconsistency issue caused by pdr index changed */
          urr->traffic.pdr_id = pdr ? pdr->id : UPF_INVALID_PDR;
        }
      if (trigger & USAGE_REPORT_TRIGGER_STOP_OF_TRAFFIC)
        {
          SET_BIT (r->grp.fields, USAGE_REPORT_TIME_OF_LAST_PACKET);
          SET_BIT (r->grp.fields, USAGE_REPORT_TIME_OF_FIRST_PACKET);
          r->time_of_first_packet = urr->traffic.first_seen;
          r->time_of_last_packet = urr->traffic.last_seen =
              now - PFCP_HB_INTERVAL;
          urr->traffic.first_seen = 0;
        }
    }

  if (urr->methods & SX_URR_VOLUME)
    {
      SET_BIT (r->grp.fields, USAGE_REPORT_VOLUME_MEASUREMENT);

      if (trigger & USAGE_REPORT_TRIGGER_VOLUME_QUOTA)
        {
            r->volume_measurement.fields = (u8)urr->volume.quota.fields;
            r->volume_measurement.volume.ul = urr->volume.measure.consumed.ul;
            r->volume_measurement.volume.dl = urr->volume.measure.consumed.dl;
            r->volume_measurement.volume.total = urr->volume.measure.consumed.total;
        }

      if (trigger & USAGE_REPORT_TRIGGER_VOLUME_THRESHOLD)
        {
          r->volume_measurement.fields = (u8)urr->volume.threshold.fields;
          r->volume_measurement.volume.ul = urr->volume.measure.bytes.ul;
          r->volume_measurement.volume.dl = urr->volume.measure.bytes.dl;
          r->volume_measurement.volume.total = urr->volume.measure.bytes.total;
          urr_pfcp_lock (urr);
          memset (&urr->volume.measure.packets, 0, sizeof (urr->volume.measure.packets));
          memset (&urr->volume.measure.bytes, 0, sizeof (urr->volume.measure.bytes));
          urr_pfcp_unlock (urr);
          clib_atomic_fetch_and (&urr->status, ~URR_REACHED_THRESHOLD);
        }

      if (trigger & (USAGE_REPORT_TRIGGER_TERMINATION_REPORT | USAGE_REPORT_TRIGGER_LINKED_USAGE_REPORTING))
        {
          if (urr->update_flags & SX_URR_UPDATE_VOL_THRESHOLD)
            {
              r->volume_measurement.fields = (u8)urr->volume.threshold.fields;
              r->volume_measurement.volume.ul = urr->volume.measure.bytes.ul;
              r->volume_measurement.volume.dl = urr->volume.measure.bytes.dl;
              r->volume_measurement.volume.total = urr->volume.measure.bytes.total;
            }
          else if (urr->update_flags & SX_URR_UPDATE_VOL_QUOTA)
            {
              r->volume_measurement.fields = (u8)urr->volume.quota.fields;
              r->volume_measurement.volume.ul = urr->volume.measure.consumed.ul;
              r->volume_measurement.volume.dl = urr->volume.measure.consumed.dl;
              r->volume_measurement.volume.total = urr->volume.measure.consumed.total;
            }
        }
    }
  else if (urr->methods & SX_URR_TIME)
    {
      SET_BIT (r->grp.fields, USAGE_REPORT_DURATION_MEASUREMENT);
      if (trigger & USAGE_REPORT_TRIGGER_TIME_THRESHOLD)
        {
          if (!(urr->update_flags & SX_URR_UPDATE_TIME_QUOTA))
            {
              /*just for offline charging*/
              urr->time_threshold.consumed = 0;
              clib_atomic_fetch_and (&urr->status, ~URR_REACHED_THRESHOLD);
            }
          r->duration_measurement = urr->time_threshold.period
                                        ? urr->time_threshold.period
                                        : (end - start);
        }
      else if (trigger & USAGE_REPORT_TRIGGER_TIME_QUOTA)
        r->duration_measurement =
            urr->time_quota.period ? urr->time_quota.period : (end - start);
      else
        r->duration_measurement = end - start;
    }
  if (sess->user_id.flags & USER_ID_IMSI)
    {
      upf_log_ex ("imsi: %s trigger: %U\n", sess->user_id.imsi_str,
                  upf_format_usage_report_trigger, &trigger);
    }

  _vec_len (*report)++;

  urr->start_time = now;
  urr->reported_times++;

  return r;
}

upf_ip_blacklist_t *upf_lookup_upf_ip_blacklist (ip46_address_t *ip)
{
  upf_main_t *gtm = &g_upf_main;
  uword *p;

  assert (ip);

  if (pool_elts (gtm->upf_ip_blacklist) == 0)
  {
      return NULL;
  }

  p = hash_get_mem (gtm->blacklist_index_by_upf_ip, ip);

  if (!p)
  {
      return NULL;
  }

  CHECK_POOL_IS_VALID_RET(gtm->upf_ip_blacklist, p[0], NULL);
  return pool_elt_at_index (gtm->upf_ip_blacklist, p[0]);
}

u8 upf_ip_whitelist_is_empty(u8 *action)
{
    upf_main_t *gtm = &g_upf_main;
    upf_whitelist_t *upf_whitelist = NULL;
    u8 ret = 1;

    /* *INDENT-OFF* */
    pool_foreach (upf_whitelist, gtm->upf_whitelist, ({

            if (action && (strcmp((const char *)action, (const char *)&upf_whitelist->key.action) == 0))
            {
                ret = 0;
                break;
            }
          }));
    /* *INDENT-ON* */

    return ret;
}

//begin liukang add for num seg 2022/12/30
upf_whitelist_t *upf_lookup_upf_num_whitelist (ip46_address_t *ip, u8 *url, u8 *num, u8 *action)
{
  upf_main_t *gtm = &g_upf_main;
  //uword *p;
  //upf_whitelist_key upf_whitelist_key = {0};
  upf_whitelist_t *ret = NULL;
  upf_whitelist_t *upf_whitelist = NULL;

  char numseg[64] = {0};
  char numseg_b[32] = {0};
  char numseg_e[32] = {0};
  char *p = NULL;

  if (pool_elts (gtm->upf_whitelist) == 0)
  {
      goto done;
  }

  pool_foreach(upf_whitelist, gtm->upf_whitelist, ({

            if (action && (strcmp((const char *)action, (const char *)&upf_whitelist->key.action) != 0))
            {
                upf_debug("upf_lookup_upf_num_whitelist action cannot match!");
                continue;
            }

            /*begin add by wangxiaolong 2021.4.29 for cmd show    */
            if (upf_whitelist->key.flags & F_WHITELIST_URL)
            {

                if(strcmp((const char *)url, (const char *)&upf_whitelist->key.url) != 0)
                {
                    upf_debug("upf_lookup_upf_num_whitelist url cannot match!");
                    continue;
                }
            }
            if ((num) && (num[0] != '0'))
            {
                if (upf_whitelist->key.flags & F_WHITELIST_NUMSEG)
                {
                    if (upf_whitelist->key.isopen)
                    {
                        strcpy(numseg,(char *)upf_whitelist->key.numseg);
                        if(strchr(numseg,'-')!=NULL)
                        {
                            p = strchr(numseg,'-');
                            *p = '\0';
                            strcpy(numseg_b,numseg);
                            p++;
                            strcpy(numseg_e,p);

                            upf_debug("upf_lookup_upf_num_whitelist numseg_b = %s  numseg_e = %s num = %s!",numseg_b,numseg_e,(char *)num);
                            if((strcmp(numseg_b,(char *)num) <= 0)  && (strcmp(numseg_e,(char *)num) >= 0))
                            {
                                ret = upf_whitelist;
                                goto done;
                            }
                        }
                        else
                        {
                            if(strcmp(numseg,(char *)num) == 0)
                            {
                                ret = upf_whitelist;
                                goto done;
                            }
                        }
                    }

                }
                upf_debug("upf_lookup_upf_num_whitelist num = %s cannot match!",numseg);
           }
          }));

  upf_debug ("whitelist not find \n");
done:
    /*if (upf_whitelist_key.url)
    {
        vec_free(upf_whitelist_key.url);
    }

    if (upf_whitelist_key.action)
    {
        vec_free(upf_whitelist_key.action);
    }*/

    return ret;
}
//end liukang add for num seg 2022/12/30

upf_whitelist_t *upf_lookup_upf_ip_whitelist (ip46_address_t *ip, u8 *url, u8 *num, u8 *action)
{
  upf_main_t *gtm = &g_upf_main;
  uword *p;
  upf_whitelist_key upf_whitelist_key = {0};
  upf_whitelist_t *ret = NULL;
  upf_whitelist_t *upf_whitelist = NULL;

  if (pool_elts (gtm->upf_whitelist) == 0)
  {
      goto done;
  }

  if (ip)
  {
      upf_whitelist_key.ip = *ip;
      upf_whitelist_key.flags |= F_WHITELIST_IP;
  }
  else
  {
      memset((void *)&upf_whitelist_key.ip, 0, sizeof(upf_whitelist_key.ip));
  }

  if (url)
  {
      strcpy((char *)upf_whitelist_key.url, (char *)url);
      upf_whitelist_key.flags |= F_WHITELIST_URL;
  }
  else
  {
      memset((void *)&upf_whitelist_key.url, 0, sizeof(upf_whitelist_key.url));
  }

  if ((num) && (num[0] != '0'))
  {
      memcpy((char *)upf_whitelist_key.numseg, (char *)num, 8);
      upf_whitelist_key.flags |= F_WHITELIST_NUMSEG;
  }
  else
  {
      memset((void *)&upf_whitelist_key.numseg, 0, sizeof(upf_whitelist_key.numseg));
  }

  if (action)
  {
      strcpy((char *)upf_whitelist_key.action, (char *)action);
  }
  else
  {
      goto done;
  }

  p = hash_get_mem (gtm->whitelist_index, &upf_whitelist_key);

  if (p)
  {
      CHECK_POOL_IS_VALID_RET(gtm->upf_whitelist, p[0], NULL);
      ret = pool_elt_at_index (gtm->upf_whitelist, p[0]);

      goto done;
  }

  if (upf_whitelist_key.flags & F_WHITELIST_NUMSEG)
  {
       /* *INDENT-OFF* */
       pool_foreach (upf_whitelist, gtm->upf_whitelist, ({
           if (upf_whitelist->key.flags & F_WHITELIST_NUMSEG)
           {
               if (strstr((const char*)num, (const char*)upf_whitelist->key.numseg))
               {
                   if (upf_whitelist->key.isopen)
                   {
                       ret = upf_whitelist;
                       goto done;
                   }
               }
           }
       }));
       /* *INDENT-ON* */
  }

  upf_debug ("whitelist not find \n");
done:
    /*if (upf_whitelist_key.url)
    {
        vec_free(upf_whitelist_key.url);
    }

    if (upf_whitelist_key.action)
    {
        vec_free(upf_whitelist_key.action);
    }*/

    return ret;
}

upf_graylist_t *upf_lookup_upf_ip_graylist (ip46_address_t *ip, u8 *url, u8 *dnn, u8 *num, u8 *action)
{
  upf_main_t *gtm = &g_upf_main;
  uword *p;
  upf_graylist_key_t upf_graylist_key = {0};
  upf_graylist_t *ret = NULL;

  if (ip)
  {
      upf_graylist_key.ip = *ip;
      upf_graylist_key.flags |= F_GRAYLIST_IP;
  }
  else
  {
      memset((void *)&upf_graylist_key.ip, 0, sizeof(upf_graylist_key.ip));
  }

  if (url)
  {
      strcpy((char *)upf_graylist_key.url, (char *)url);
      upf_graylist_key.flags |= F_GRAYLIST_URL;
  }
  else
  {
      memset((void *)&upf_graylist_key.url, 0, sizeof(upf_graylist_key.url));
  }

  if ((num) && (num[0] != '0'))
  {
      memcpy((char *)upf_graylist_key.numseg, (char *)num, strlen((char *)num));
      upf_graylist_key.flags |= F_GRAYLIST_NUMSEG;
  }
  else
  {
      memset((void *)&upf_graylist_key.numseg, 0, sizeof(upf_graylist_key.numseg));
  }

  if (action)
  {
      strcpy((char *)upf_graylist_key.action, (char *)action);
  }
  else
  {
      goto done;
  }

  if (pool_elts (gtm->upf_graylist) == 0)
  {
      goto done;
  }

  p = hash_get_mem (gtm->graylist_index, &upf_graylist_key);

  if (p)
  {
      CHECK_POOL_IS_VALID_RET(gtm->upf_graylist, p[0], NULL);
      ret = pool_elt_at_index (gtm->upf_graylist, p[0]);

      goto done;
  }

#if 0
  if (upf_graylist_key.flags & F_GRAYLIST_NUMSEG)
  {
       /* *INDENT-OFF* */
       pool_foreach (upf_graylist, gtm->upf_graylist, ({
           if ((strlen(upf_graylist->key.dnn) == 0 || vec_is_equal (upf_graylist->key.dnn, dnn)) && upf_graylist->key.flags & F_GRAYLIST_NUMSEG)
           {
               if (strstr((const char*)num, (const char*)upf_graylist->key.numseg))
               {
                   if (upf_graylist->key.isopen)
                   {
                       ret = upf_graylist;
                       goto done;
                   }
               }
           }
       }));
       /* *INDENT-ON* */
  }
#endif

  upf_debug ("graylist not find \n");
done:

    /*begin add by huqingyang 2021.5.11 for dnn graylist  */
    #if 0
    if (NULL != dnn)
    {
        if (strlen((char *)dnn))
        {

           /* *INDENT-OFF* */
           ret = NULL;
           pool_foreach (upf_graylist, gtm->upf_graylist, ({
               if ( vec_is_equal (upf_graylist->key.dnn, dnn) && (upf_graylist->key.flags & F_GRAYLIST_DNN))
               {
                   ret = upf_graylist;
                   return ret;
               }
           }));
           /* *INDENT-ON* */
        }
    }
    #endif
    /*end add by huqingyang 2021.5.11 for dnn graylist  */
    return ret;
}


static_mac_address_t *upf_lookup_upf_static_mac_address (mac_address_t *mac, u32 vlan_id)
{
  upf_main_t *gtm = &g_upf_main;
  uword *p;
  static_mac_address_key_t static_mac_address_key;

  assert (mac);

  if (pool_elts (gtm->static_mac_address) == 0)
  {
      return NULL;
  }

  static_mac_address_key.mac = *mac;
  static_mac_address_key.vlan_id = vlan_id;

  p = hash_get_mem (gtm->static_mac_address_index, &static_mac_address_key);

  if (!p)
  {
      return NULL;
  }

  CHECK_POOL_IS_VALID_RET(gtm->static_mac_address, p[0], NULL);
  return pool_elt_at_index (gtm->static_mac_address, p[0]);
}

u8 upf_n6_vlan_list_is_empty()
{
    upf_main_t *gtm = &g_upf_main;

    if (pool_elts (gtm->n6_vlan_list) == 0)
    {
        return 1;
    }
    return 0;
}

n6_vlan_list_t *upf_lookup_upf_n6_vlan_list (u8 *name, u32 vlan_id, u32 nwi)
{
  upf_main_t *gtm = &g_upf_main;
  uword *p;
  n6_vlan_list_key_t n6_vlan_list_key;

  assert (name);

  if (pool_elts (gtm->n6_vlan_list) == 0)
  {
      return NULL;
  }

  if (name)
  {
    p = hash_get_mem (gtm->nwi_index_by_name, name);

    if (!p)
    {
      return NULL;
    }

    n6_vlan_list_key.nwi = p[0];
  }
  else
  {
      n6_vlan_list_key.nwi = nwi;
  }

  n6_vlan_list_key.vlan_id = vlan_id;

  p = hash_get_mem (gtm->n6_vlan_list_index, &n6_vlan_list_key);

  if (!p)
  {
      return NULL;
  }

  CHECK_POOL_IS_VALID_RET(gtm->n6_vlan_list, p[0], NULL);
  return pool_elt_at_index (gtm->n6_vlan_list, p[0]);
}

static int upf_pfcp_check_userid(upf_grab_msg_t grab_msg, sx_msg_t *req)
{
  pfcp_user_id_t *user_id;

  user_id = &req->user_id;

  if (grab_msg.user_id_type == UPF_USER_ID_INVALID)
  {
    return 0;
  }

  if (((grab_msg.user_id_type == UPF_USER_ID_MSISDN) && memcmp(grab_msg.user_id, user_id->msisdn_str, sizeof(grab_msg.user_id)) == 0) ||
      ((grab_msg.user_id_type == UPF_USER_ID_IMSI) && memcmp(grab_msg.user_id, user_id->imsi_str, sizeof(grab_msg.user_id)) == 0) ||
      ((grab_msg.user_id_type == UPF_USER_ID_IMEI) && memcmp(grab_msg.user_id, user_id->imei_str, sizeof(grab_msg.user_id)) == 0))
  {
    return 0;
  }
  upf_err("######### wuwei grab_msg.user_id:%s, user_id->msisdn_str:%s\n", grab_msg.user_id, user_id->msisdn_str);

  return -1;
}

static int upf_pfcp_check_ip(upf_grab_msg_t grab_msg, sx_msg_t *req, u8 is_ip4)
{
  int ret = -1;

  if (is_ip4)
  {
    if (grab_msg.src_addr.ip4.data_u32 != ~0 && grab_msg.dst_addr.ip4.data_u32 != ~0)
    {
      if ((grab_msg.src_addr.ip4.data_u32 == req->rmt.address.ip4.data_u32 &&
          grab_msg.dst_addr.ip4.data_u32 == req->lcl.address.ip4.data_u32) ||
          (grab_msg.src_addr.ip4.data_u32 == req->lcl.address.ip4.data_u32 &&
          grab_msg.dst_addr.ip4.data_u32 == req->rmt.address.ip4.data_u32))
      {
        ret = 0;
      }
    }
    else if (grab_msg.src_addr.ip4.data_u32 != ~0)
    {
      if (grab_msg.src_addr.ip4.data_u32 == req->rmt.address.ip4.data_u32 ||
          grab_msg.src_addr.ip4.data_u32 == req->lcl.address.ip4.data_u32)
      {
        ret = 0;
      }
    }
    else if (grab_msg.dst_addr.ip4.data_u32 != ~0)
    {
      if (grab_msg.dst_addr.ip4.data_u32 == req->rmt.address.ip4.data_u32 ||
          grab_msg.dst_addr.ip4.data_u32 == req->lcl.address.ip4.data_u32)
      {
        ret = 0;
      }
    }
    else
    {
      ret = 0;
    }
  }
  else
  {
    if (clib_net_to_host_u16(grab_msg.src_addr.ip6.as_u16[0]) == 0)
    {
        ret = 0;
    }
    else
    {
      if (grab_msg.src_addr.ip6.as_u64[0] != ~0 && grab_msg.src_addr.ip6.as_u64[1] != ~0 &&
            grab_msg.dst_addr.ip6.as_u64[0] != ~0 && grab_msg.dst_addr.ip6.as_u64[1] != ~0)
      {
        if ((ip6_address_compare(&grab_msg.src_addr.ip6, &req->rmt.address.ip6) == 0 &&
            ip6_address_compare(&grab_msg.dst_addr.ip6, &req->lcl.address.ip6) == 0) ||
            (ip6_address_compare(&grab_msg.src_addr.ip6, &req->rmt.address.ip6) == 0 &&
            ip6_address_compare(&grab_msg.dst_addr.ip6, &req->lcl.address.ip6) == 0))
        {
          ret = 0;
        }
      }
      else if (grab_msg.src_addr.ip6.as_u64[0] != ~0 && grab_msg.src_addr.ip6.as_u64[1] != ~0)
      {
        if (ip6_address_compare(&grab_msg.src_addr.ip6, &req->rmt.address.ip6) == 0 &&
            ip6_address_compare(&grab_msg.src_addr.ip6, &req->lcl.address.ip6) == 0)
        {
          ret = 0;
        }
      }
      else if (grab_msg.dst_addr.ip6.as_u64[0] != ~0 && grab_msg.dst_addr.ip6.as_u64[1] != ~0)
      {
        if (ip6_address_compare(&grab_msg.dst_addr.ip6, &req->rmt.address.ip6) == 0 &&
            ip6_address_compare(&grab_msg.dst_addr.ip6, &req->lcl.address.ip6) == 0)
        {
          ret = 0;
        }
      }
      else
      {
        ret = 0;
      }
    }
  }
  upf_debug("######## ret:%d\n", ret);

  return ret;
}

static int upf_pfcp_check_port(upf_grab_msg_t grab_msg, sx_msg_t *req)
{
  int ret = -1;
  u16 rmt_port = clib_net_to_host_u16(req->rmt.port);
  u16 lcl_port = clib_net_to_host_u16(req->lcl.port);

  if (grab_msg.src_port != UPF_INVALID_PORT_VALUE && grab_msg.dst_port != UPF_INVALID_PORT_VALUE)
  {
    if ((grab_msg.src_port == rmt_port && grab_msg.dst_port == lcl_port) ||
      (grab_msg.src_port == lcl_port && grab_msg.dst_port == rmt_port))
    {
      ret = 0;
    }
  }
  else if ((grab_msg.src_port != UPF_INVALID_PORT_VALUE) && (grab_msg.src_port != 0))
  {
    if (grab_msg.src_port == rmt_port || grab_msg.src_port == lcl_port)
    {
      ret = 0;
    }
  }
  else if ((grab_msg.dst_port != UPF_INVALID_PORT_VALUE) && (grab_msg.dst_port != 0))
  {
    if (grab_msg.dst_port == rmt_port || grab_msg.dst_port == lcl_port)
    {
      ret = 0;
    }
  }
  else
  {
    ret = 0;
  }
  upf_debug("######## ret:%d\n", ret);

  return ret;
}

static int upf_pfcp_check_element_id(upf_grab_msg_t grab_msg)
{
  if ((u32)~0 != grab_msg.ne_instance_id && grab_msg.ne_instance_id != g_local_ne_id)
  {
    upf_debug("######## ret:%d\n", -1);
    return -1;
  }

  return 0;
}

static int upf_pfcp_check_interface_type(upf_grab_msg_t grab_msg)
{
  int ret = -1;

  if (grab_msg.interface_type & UPF_SINGLE_TRACE_INTF_N4)
  {
    ret = 0;
  }

  upf_debug("######## ret:%d\n", ret);
  return ret;
}

static void upf_pfcp_fill_single_trace_push(sx_msg_t *req, u16 task_id, upf_single_trace_push_t *push_msg)
{
  struct timeval subtle_time;

  push_msg->task_id = task_id;
  push_msg->utc_time = time(NULL);
  gettimeofday(&subtle_time, NULL);
  push_msg->subtle_time = subtle_time.tv_usec;
  push_msg->protocol = 8805;
  clib_memcpy_fast(&push_msg->src_addr, &req->rmt.address, sizeof(push_msg->src_addr));
  clib_memcpy_fast(&push_msg->dst_addr, &req->lcl.address, sizeof(push_msg->dst_addr));
  push_msg->ne_instance_id = g_local_ne_id;
  push_msg->msg_type = req->hdr->type;
  push_msg->result = req->cause;
  push_msg->interface_type = UPF_SINGLE_TRACE_INTF_N4;
  //push_msg->raw_data_len = req->hdr->length + 32;
  //push_msg->raw_data = req->data;

  return;
}

void upf_pfcp_single_trace_update(upf_session_t *sess, sx_msg_t *req, u8 is_ip4)
{
  u16 task_id = 0, j = 0;

  memset(sess->single_trace_list, ~0, sizeof(sess->single_trace_list));

  for (task_id = 0; task_id < UPF_U16_MAX_SIZE-1; task_id++)
  {
    if (g_upf_single_trace[task_id].is_used == 0)
    {
      continue;
    }

    if ((upf_pfcp_check_userid(g_upf_single_trace[task_id], req) == -1) ||
        (upf_pfcp_check_ip(g_upf_single_trace[task_id], req, 1) == -1) ||
        (upf_pfcp_check_port(g_upf_single_trace[task_id], req) == -1) ||
        (upf_pfcp_check_element_id(g_upf_single_trace[task_id]) == -1))
    {
      continue;
    }

    for (j = 0; j < UPF_STRING_LEN8; j++)
    {
      if (sess->single_trace_list[j] == (u16)~0)
      {
        sess->single_trace_list[j] = task_id;
        break;
      }
    }

    if (j == UPF_STRING_LEN8)
    {
      upf_err("session->single_trace_list is full, the max num = %d\n", UPF_STRING_LEN8);
      return;
    }
  }

  sess->single_trace_flag = g_single_trace_flag;
  return;
}

void upf_pfcp_node_single_trace_push(sx_msg_t *req, u8 * data, u32 data_len, u8 is_ip4, u8 direction)
{
  u16 task_id = 0;
  upf_single_trace_push_t push_msg;

  for (task_id = 0; task_id < UPF_U16_MAX_SIZE-1; task_id++)
  {
    if (g_upf_single_trace[task_id].is_used == 1)
    {
      if ((upf_pfcp_check_userid(g_upf_single_trace[task_id], req) == -1) ||
        (upf_pfcp_check_ip(g_upf_single_trace[task_id], req, is_ip4) == -1) ||
        (upf_pfcp_check_port(g_upf_single_trace[task_id], req) == -1) ||
        (upf_pfcp_check_element_id(g_upf_single_trace[task_id]) == -1) ||
        (upf_pfcp_check_interface_type(g_upf_single_trace[task_id]) == -1))
      {
        continue;
      }

      upf_pfcp_fill_single_trace_push(req, task_id, &push_msg);
      push_msg.raw_data_len = data_len;
      push_msg.raw_data = data;
      push_msg.direction = direction;

      upf_debug("######## PFCP_RPC_PUBLISH_SINGLE_TRACE_PUSH, msg_type:%s\n", upf_pfcp_msg_desc[req->hdr->type]);
      if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N4)
      {
          upf_pfcp_events_publish (PFCP_RPC_PUBLISH_SINGLE_TRACE_PUSH, NULL, &push_msg);
      }
    }
  }

  return;
}

void upf_pfcp_single_trace_push(upf_session_t *sess, sx_msg_t *req, u8 * data, u32 data_len, u8 direction)
{
  int i = 0;
  u16 task_id = ~0;
  upf_single_trace_push_t push_msg;

  for (i = 0; i < UPF_STRING_LEN8; i++)
  {
    if (sess->single_trace_list[i] != (u16)~0)
    {
      task_id = sess->single_trace_list[i];

      if ((upf_pfcp_check_userid(g_upf_single_trace[task_id], req) == -1) ||
        (upf_pfcp_check_ip(g_upf_single_trace[task_id], req, 1) == -1) ||
        (upf_pfcp_check_port(g_upf_single_trace[task_id], req) == -1) ||
        (upf_pfcp_check_element_id(g_upf_single_trace[task_id]) == -1) ||
        (upf_pfcp_check_interface_type(g_upf_single_trace[task_id]) == -1))
      {
        continue;
      }

      upf_pfcp_fill_single_trace_push(req, task_id, &push_msg);
      push_msg.raw_data_len = data_len;
      push_msg.raw_data = data;
      push_msg.direction = direction;
      upf_debug("######## PFCP_RPC_PUBLISH_SINGLE_TRACE_PUSH, msg_type:%s\n", upf_pfcp_msg_desc[req->hdr->type]);

      if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N4)
      {
          upf_pfcp_events_publish (PFCP_RPC_PUBLISH_SINGLE_TRACE_PUSH, sess, &push_msg);
      }
    }
  }

  return;
}

void upf_dnn_init2(pfcp_session_establishment_request_t *req, sx_msg_t *msg)
{
    u8 *tmp_dnn = NULL;
    u8 *p, len = 0;
    int length = 0;
    upf_main_t *gtm = &g_upf_main;

    if (req->apn_dnn == NULL)
    {
        return;
    }

    tmp_dnn = vec_dup (req->apn_dnn);
    length = vec_len (tmp_dnn);

    p = tmp_dnn;
    while (length)
    {
        if (!p || (*p > 30) || *p > length) /*avoid incorrect DNN format*/
            break;
        len = *p;
        *p = '.';
        p = p + len + 1;
        length -= len + 1;
    }

    p = tmp_dnn + 1;
    //DNN=NI+OI
    if (clib_strstr((char *)p, ".gprs"))
    {
        msg->dnn = format (msg->dnn, "%s", p);
    }
    else
    {
        if (g_home_plmn.mnc > 99)
        {
            msg->dnn = format (msg->dnn, "%s.mnc%u.mcc%u.gprs", p, g_home_plmn.mnc, g_home_plmn.mcc);
        }
        else
        {
            msg->dnn = format (msg->dnn, "%s.mnc0%u.mcc%u.gprs", p, g_home_plmn.mnc, g_home_plmn.mcc);
        }
    }

    if (g_upf_dnn_switch)
    {
        upf_dnn_t *dnn;
        int hit = 0;
        vec_foreach (dnn, gtm->dnn)
        {
           if (vec_is_equal (dnn->name, msg->dnn))
           {
               hit = 1;
               break;
           }
        }
        if (!hit)
        {
           vec_alloc (gtm->dnn, 1);
           dnn = vec_end (gtm->dnn);
           dnn->name = vec_dup ((u8 *)msg->dnn);
           clib_spinlock_init (&dnn->lock);
           _vec_len (gtm->dnn)++;
        }
    }

    vec_free(tmp_dnn);
}

static void upf_dnn_init(pfcp_session_establishment_request_t *msg, upf_session_t *sess)
{
    upf_main_t *gtm = &g_upf_main;
    u8 *tmp_dnn = NULL;
    u8 *p, len = 0;
    int length = 0;

    if (msg->apn_dnn == NULL)
    {
        return;
    }

    tmp_dnn = vec_dup (msg->apn_dnn);
    length = vec_len (tmp_dnn);

    p = tmp_dnn;
    while (length)
    {
        if (!p || (*p > 30) || *p > length) /*avoid incorrect DNN format*/
            break;
        len = *p;
        *p = '.';
        p = p + len + 1;
        length -= len + 1;
    }

    p = tmp_dnn + 1;
    //DNN=NI+OI
    if (clib_strstr((char *)p, ".gprs"))
    {
        sess->dnn = format (sess->dnn, "%s", p);
    }
    else
    {
        if (g_home_plmn.mnc > 99)
        {
            sess->dnn = format (sess->dnn, "%s.mnc%u.mcc%u.gprs", p, g_home_plmn.mnc, g_home_plmn.mcc);
        }
        else
        {
            sess->dnn = format (sess->dnn, "%s.mnc0%u.mcc%u.gprs", p, g_home_plmn.mnc, g_home_plmn.mcc);
        }
    }

    if (g_upf_dnn_switch)
    {
        upf_dnn_t *dnn;
        int hit = 0;
        vec_foreach (dnn, gtm->dnn)
        {
           if (vec_is_equal (dnn->name, sess->dnn))
           {
               hit = 1;
               break;
           }
        }
        if (!hit)
        {
           vec_alloc (gtm->dnn, 1);
           dnn = vec_end (gtm->dnn);
           dnn->name = vec_dup ((u8 *)sess->dnn);
           clib_spinlock_init (&dnn->lock);
           _vec_len (gtm->dnn)++;
        }
    }
    vec_free(tmp_dnn);
}

static u32 upf_s_nssai_init (pfcp_session_establishment_request_t *msg, upf_session_t *sess)
{
    upf_s_nssai_t *r = upf_s_nssai_lookup (msg->s_nssai);
    if ((g_upf_pfcp_snssai_check_switch == SWITCH_ON) && !r)
    {
        upf_debug ("S-NSSAI:%U check failed!", upf_format_s_nssai, &msg->s_nssai);
        return 1;
    }
    sess->s_nssai = msg->s_nssai;

    return 0;
}

void upf_user_id_init(pfcp_user_id_t *user_id, upf_session_t *sess)
{
    upf_main_t *gtm = &g_upf_main;

    sess->flags |= SX_USER_ID;
    memcpy(&sess->user_id, user_id, sizeof(pfcp_user_id_t));
    if (sess->user_id.flags & USER_ID_IMSI)
      {
        upf_imsi_to_sess_idxs_t sess_idxs = {0};
        uword *p = hash_get_mem (gtm->session_by_imsi, sess->user_id.imsi_str);
        if (p)
        {
            sess_idxs.p = *p;
            if (!sess_idxs.sess_idx1)
                sess_idxs.sess_idx1 = sess - gtm->sessions;
            else if (!sess_idxs.sess_idx2)
                sess_idxs.sess_idx2 = sess - gtm->sessions;
            else
                upf_err("sess idx already exists!");
        }
        else
        {
            sess_idxs.sess_idx1 = sess - gtm->sessions;
        }
        upf_trace("sess_idx1:%u, sess_idx2:%u, p:%llu", sess_idxs.sess_idx1, sess_idxs.sess_idx2, sess_idxs.p);
        hash_set_mem (gtm->session_by_imsi, sess->user_id.imsi_str, sess_idxs.p);
      }

    // Add for get ue identity info by liupeng on 2021-09-06 below
    if (sess->user_id.flags & USER_ID_MSISDN)
    {
        hash_set_mem (gtm->session_by_msisdn, sess->user_id.msisdn_str, sess - gtm->sessions);
    }
    if (sess->user_id.flags & USER_ID_IMEI)
    {
        hash_set_mem (gtm->session_by_imei, sess->user_id.imei_str, sess - gtm->sessions);
    }
    // Add for get ue identity info by liupeng on 2021-09-06 above

    if (sess->user_id.flags & USER_ID_NAI)
        sess->user_id.nai = vec_dup(sess->user_id.nai);
    else
        sess->user_id.nai = NULL;
}

static void upf_tunnel_type_id_init(pfcp_session_establishment_request_t *msg, upf_session_t *sess)
{
    char *p = (char *)msg->tunnel_id.tunnelid;
    char *sx_tunnel_prefix = "session-tunnel-pair-";
    int prefix_len = strlen(sx_tunnel_prefix);

    if (strncmp(p, sx_tunnel_prefix, prefix_len))
        return;
    p += prefix_len;

    session_by_tunnel_t kv;
    if (0 == strncmp(p, "gre-", 4))
    {
        p += 4; /* skip 'gre-' */
        kv.k.type = SESSION_TUNNEL_GRE;
    }
    else if (0 == strncmp(p, "vxlan-", 6))
    {
        p += 6; /* skip 'vxlan-' */
        kv.k.type = SESSION_TUNNEL_VXLAN;
    }
    else
    {
        return;
    }

    kv.k.instance_id = (u32)atoi(p);
    kv.v.session_index = sess - g_upf_main.sessions;
    kv.v.reverse = ~0;
    if (clib_bihash_add_del_8_8 (&g_upf_main.session_by_tunnel, (clib_bihash_kv_8_8_t *)&kv, 1))
    {
        upf_debug(" add session-tunnel fail, %U ", upf_format_session_tunnel_pair, &kv.k);
        return;
    }

    sess->flags |= SX_TUNNEL_ID;
    sess->tunnel = kv.k;
}

static int upf_handle_session_common_info(pfcp_session_establishment_request_t *msg, upf_session_t *sess)
{
    int r = 0;
    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_PDN_TYPE))
        sess->pdn_type = msg->pdn_type.type;

    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_APN_DNN))
        upf_dnn_init(msg, sess);

    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_S_NSSAI))
        r = upf_s_nssai_init(msg, sess);

    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_USER_ID))
        upf_user_id_init(&msg->user_id, sess);

    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_TUNNEL_ID))
        upf_tunnel_type_id_init(msg, sess);

    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_CMCC_RAT_TYPE))
        sess->rat_type = msg->rat_type;
    return r;
}

#if 0
static void upf_cmcc_l2tp_user_info_init(pfcp_session_establishment_request_t *msg, upf_session_t *sess)
{
    sess->l2tp_user_information.flags1 = msg->l2tp_user_information.flags1;
    sess->l2tp_user_information.flags2 = msg->l2tp_user_information.flags2;
    if(sess->l2tp_user_information.flags1 & F_L2TP_USER_INFO_NAME)
    {
        sess->l2tp_user_information.username_len = msg->l2tp_user_information.username_len;
        vec_add (sess->l2tp_user_information.username, msg->l2tp_user_information.username, msg->l2tp_user_information.username_len);
    }
    if(sess->l2tp_user_information.flags1 & F_L2TP_USER_INFO_PWD)
    {
        sess->l2tp_user_information.pap_password_len = msg->l2tp_user_information.pap_password_len;
        vec_add (sess->l2tp_user_information.pap_password, msg->l2tp_user_information.pap_password, msg->l2tp_user_information.pap_password_len);
    }
    if(sess->l2tp_user_information.flags1 & F_L2TP_USER_INFO_CHA)
    {
        sess->l2tp_user_information.chap_challenge_len = msg->l2tp_user_information.chap_challenge_len;
        vec_add (sess->l2tp_user_information.cha_challenge, msg->l2tp_user_information.cha_challenge, msg->l2tp_user_information.chap_challenge_len);
    }
    if(sess->l2tp_user_information.flags1 & F_L2TP_USER_INFO_RSP)
    {
        sess->l2tp_user_information.chap_challenge_response_len = msg->l2tp_user_information.chap_challenge_response_len;
        vec_add (sess->l2tp_user_information.chap_challenge_response, msg->l2tp_user_information.chap_challenge_response, msg->l2tp_user_information.chap_challenge_response_len);
    }
    if(sess->l2tp_user_information.flags1 & F_L2TP_USER_INFO_ID)
    {
        sess->l2tp_user_information.identifier = msg->l2tp_user_information.identifier;
    }
    if(sess->l2tp_user_information.flags1 & F_L2TP_USER_INFO_LCP_FLAG)
    {
        sess->l2tp_user_information.lcp_flag = msg->l2tp_user_information.lcp_flag;
    }
    if(sess->l2tp_user_information.flags2 & F_L2TP_USER_INFO_IPCP)
    {
        sess->l2tp_user_information.ipcp_request_configuration_flag = msg->l2tp_user_information.ipcp_request_configuration_flag;
    }
}


static void upf_cmcc_l2tp_tunnel_info_init(pfcp_session_establishment_request_t *msg, upf_session_t *sess)
{
    sess->l2tp_tunnel_information.flags1 = msg->l2tp_tunnel_information.flags1;
    sess->l2tp_tunnel_information.flags2 = msg->l2tp_tunnel_information.flags2;
    if(sess->l2tp_tunnel_information.flags1 & F_L2TP_TUNNEL_INFORMATION_LNS_IPV4)
    {
        sess->l2tp_tunnel_information.lns_ipv4 = msg->l2tp_tunnel_information.lns_ipv4;
    }
    if(sess->l2tp_tunnel_information.flags1 & F_L2TP_TUNNEL_INFORMATION_LNS_IPV6)
    {
        sess->l2tp_tunnel_information.lns_ipv6 = msg->l2tp_tunnel_information.lns_ipv6;
    }
    if(sess->l2tp_tunnel_information.flags1 & F_L2TP_TUNNEL_INFORMATION_T_PRE)
    {
        sess->l2tp_tunnel_information.tunnel_preference = msg->l2tp_tunnel_information.tunnel_preference;
    }
    if(sess->l2tp_tunnel_information.flags1 & F_L2TP_TUNNEL_INFORMATION_T_PASS)
    {
        sess->l2tp_tunnel_information.tunnel_password_len = msg->l2tp_tunnel_information.tunnel_password_len;
          vec_add (sess->l2tp_tunnel_information.tunnel_password, msg->l2tp_tunnel_information.tunnel_password, msg->l2tp_tunnel_information.tunnel_password_len);
    }
    if(sess->l2tp_tunnel_information.flags1 & F_L2TP_TUNNEL_INFORMATION_TAID)
    {
        sess->l2tp_tunnel_information.tunnel_assignment_id = msg->l2tp_tunnel_information.tunnel_assignment_id;
    }
    if(sess->l2tp_tunnel_information.flags1 & F_L2TP_TUNNEL_INFORMATION_TCID)
    {
        sess->l2tp_tunnel_information.tunnel_client_auth_id_len = msg->l2tp_tunnel_information.tunnel_client_auth_id_len;
          vec_add (sess->l2tp_tunnel_information.tunnel_client_auth_id, msg->l2tp_tunnel_information.tunnel_client_auth_id, msg->l2tp_tunnel_information.tunnel_client_auth_id_len);
    }
    if(sess->l2tp_tunnel_information.flags1 & F_L2TP_TUNNEL_INFORMATION_TSID)
    {
        sess->l2tp_tunnel_information.tunnel_server_auth_id_len = msg->l2tp_tunnel_information.tunnel_server_auth_id_len;
          vec_add (sess->l2tp_tunnel_information.tunnel_server_auth_id, msg->l2tp_tunnel_information.tunnel_server_auth_id, msg->l2tp_tunnel_information.tunnel_server_auth_id_len);
    }
    if(sess->l2tp_tunnel_information.flags1 & F_L2TP_TUNNEL_INFORMATION_AUTH_METHOD)
    {
        sess->l2tp_tunnel_information.vendor_tunnel_user_auth_method_len = msg->l2tp_tunnel_information.vendor_tunnel_user_auth_method_len;
          vec_add (sess->l2tp_tunnel_information.vendor_tunnel_user_auth_method, msg->l2tp_tunnel_information.vendor_tunnel_user_auth_method, msg->l2tp_tunnel_information.vendor_tunnel_user_auth_method_len);
    }
    if(sess->l2tp_tunnel_information.flags2 & F_L2TP_TUNNEL_INFORMATION_USERNAME)
    {
        sess->l2tp_tunnel_information.vendor_tunnel_overide_username_len = msg->l2tp_tunnel_information.vendor_tunnel_overide_username_len;
          vec_add (sess->l2tp_tunnel_information.vendor_tunnel_overide_username, msg->l2tp_tunnel_information.vendor_tunnel_overide_username, msg->l2tp_tunnel_information.vendor_tunnel_overide_username_len);
    }
    if(sess->l2tp_tunnel_information.flags2 & F_L2TP_TUNNEL_INFORMATION_PGID)
    {
        sess->l2tp_tunnel_information.vendor_tunnel_private_group_id_len = msg->l2tp_tunnel_information.vendor_tunnel_private_group_id_len;
          vec_add (sess->l2tp_tunnel_information.vendor_tunnel_private_group_id, msg->l2tp_tunnel_information.vendor_tunnel_private_group_id, msg->l2tp_tunnel_information.vendor_tunnel_private_group_id_len);
    }
}
#endif


static int upf_cp_cheating_prevention(ip46_address_t *pfcp_remote_ip, ip46_address_t *cp_seid_ip, upf_node_assoc_t *assoc)
{
    if (SWITCH_OFF == g_cheating_prevent_switch)
        return 0;

    /* neil.fan@20220621 note:
     * 1) both SMF and UPF not support SMF Set currently, so, just compare the pfcp src ip and cp f_seid_ip.
     * 2) if support SMF Set, maybe checking the pfcp src ip is one of the ip of the CP-Node by association or not.
     */
    if (!ip46_address_is_equal(pfcp_remote_ip, cp_seid_ip))
    {
        upf_debug(" %U error, should be %U \n", format_ip46_address, pfcp_remote_ip, IP46_TYPE_ANY,
            format_ip46_address, cp_seid_ip, IP46_TYPE_ANY);
        return -1;
    }

    return 0;
}

//begin liukang add for l2tp 2022/08/08
static void upf_l2tp_tunnel_info_init(pfcp_session_establishment_request_t *msg, upf_session_t *sess)
{
    sess->upf_l2tp_tunnel_information.lns_address = msg->upf_l2tp_tunnel_information.lns_address;

    sess->upf_l2tp_tunnel_information.tunnel_password.tunnel_password_len = msg->upf_l2tp_tunnel_information.tunnel_password.tunnel_password_len;
    vec_add (sess->upf_l2tp_tunnel_information.tunnel_password.tunnel_password, msg->upf_l2tp_tunnel_information.tunnel_password.tunnel_password,
        msg->upf_l2tp_tunnel_information.tunnel_password.tunnel_password_len);

    sess->upf_l2tp_tunnel_information.tunnel_preference.tunnel_preference = msg->upf_l2tp_tunnel_information.tunnel_preference.tunnel_preference;

}

static void upf_l2tp_session_info_init(pfcp_session_establishment_request_t *msg, upf_session_t *sess)
{
    pfcp_l2tp_calling_number_t *l2tp_calling_number = NULL;
    pfcp_l2tp_called_number_t *l2tp_called_number = NULL;
    pfcp_l2tp_user_authentication_t *l2tp_user_authentication = NULL;

    l2tp_calling_number = &(msg->upf_l2tp_session_information.calling_number);
    l2tp_called_number = &(msg->upf_l2tp_session_information.called_number);

    l2tp_user_authentication = &(msg->upf_l2tp_session_information.l2tp_user_authentication);

    if(l2tp_calling_number->calling_number_len != 0)
    {
        sess->upf_l2tp_session_information.calling_number.calling_number_len = l2tp_calling_number->calling_number_len;
        vec_add (sess->upf_l2tp_session_information.calling_number.calling_number, l2tp_calling_number->calling_number,
            l2tp_calling_number->calling_number_len);
    }

    if(l2tp_called_number->called_number_len != 0)
    {
        sess->upf_l2tp_session_information.called_number.called_number_len = l2tp_called_number->called_number_len;
        vec_add (sess->upf_l2tp_session_information.called_number.called_number, l2tp_called_number->called_number,
            l2tp_called_number->called_number_len);
    }

    sess->upf_l2tp_session_information.l2tp_user_authentication.proxy_authen_type_value = l2tp_user_authentication->proxy_authen_type_value;
    sess->upf_l2tp_session_information.l2tp_user_authentication.flags = l2tp_user_authentication->flags;
    if((l2tp_user_authentication->flags & L2TP_PROXY_AUTHEN_NAME) == L2TP_PROXY_AUTHEN_NAME)
    {
        sess->upf_l2tp_session_information.l2tp_user_authentication.proxy_authen_name_len = l2tp_user_authentication->proxy_authen_name_len;
        vec_add (sess->upf_l2tp_session_information.l2tp_user_authentication.proxy_authen_name, l2tp_user_authentication->proxy_authen_name,
            l2tp_user_authentication->proxy_authen_name_len);
    }

    if((l2tp_user_authentication->flags & L2TP_PROXY_AUTHEN_CHALLENGE) == L2TP_PROXY_AUTHEN_CHALLENGE)
    {
        vec_add (sess->upf_l2tp_session_information.l2tp_user_authentication.proxy_authen_challenge, l2tp_user_authentication->proxy_authen_challenge,
            l2tp_user_authentication->proxy_authen_challenge_len);
        sess->upf_l2tp_session_information.l2tp_user_authentication.proxy_authen_challenge_len = l2tp_user_authentication->proxy_authen_challenge_len;
    }

    if((l2tp_user_authentication->flags & L2TP_PROXY_AUTHEN_RESPONSE) == L2TP_PROXY_AUTHEN_RESPONSE)
    {
        vec_add (sess->upf_l2tp_session_information.l2tp_user_authentication.proxy_authen_response, l2tp_user_authentication->proxy_authen_response,
            l2tp_user_authentication->proxy_authen_response_len);
        sess->upf_l2tp_session_information.l2tp_user_authentication.proxy_authen_response_len = l2tp_user_authentication->proxy_authen_response_len;
    }

    if((l2tp_user_authentication->flags & L2TP_PROXY_AUTHEN_ID) == L2TP_PROXY_AUTHEN_ID)
    {
        sess->upf_l2tp_session_information.l2tp_user_authentication.proxy_authen_id = l2tp_user_authentication->proxy_authen_id;
    }

    sess->upf_l2tp_session_information.l2tp_session_indications = msg->upf_l2tp_session_information.l2tp_session_indications;
}

//end liukang add for l2tp 2022/08/08


#if 0
static int upf_handle_establishment_l2tp(pfcp_session_establishment_request_t *msg, upf_session_t *sess,
                                         pfcp_session_establishment_response_t *resp)
{
    int r;
    sx_server_main_t *sxsm = &sx_server_main;
    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_CMCC_RAT_TYPE))
        sess->rat_type = msg->rat_type;

    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_CMCC_L2TP_USER_INFORMATION))
        upf_cmcc_l2tp_user_info_init(msg, sess);

    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_CMCC_L2TP_TUNNEL_INFORMATION))
        upf_cmcc_l2tp_tunnel_info_init(msg, sess);

    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_CMCC_USER_LOCATION_INFORMATION))
    {
        sess->user_location_information.geographic_location_type = msg->user_location_information.geographic_location_type;
        vec_add (sess->user_location_information.geographic_location, msg->user_location_information.geographic_location, strlen((const char *)msg->user_location_information.geographic_location)+1);
    }

    //begin liukang add for l2tp 2021/07/01
    if(g_upf_l2tp_switch)
    {
        if ((r = upf_l2tp_user_info_handle (sess, &msg->l2tp_user_information, sxsm->now, &resp->grp,
                     SESSION_ESTABLISHMENT_RESPONSE_FAILED_RULE_ID, &resp->failed_rule_id)) != 0)
        {
            upf_err ("upf_l2tp_user_info_handle failed %d\n", r);
            return r;
        }

        int lns = upf_load_lns_push_info(&msg->l2tp_user_information, &resp->lns_push_information);
        if(lns == 1)
        {
            SET_BIT (resp->grp.fields, SESSION_ESTABLISHMENT_RESPONSE_CMCC_LNS_PUSH_INFORMATION);
        }

        pfcp_create_pdr_t *pdr;
        pfcp_create_pdr_t *create_pdr = msg->create_pdr;
        vec_foreach (pdr, create_pdr)
        {
           if(pdr->pdi.source_interface == 1)
           {
              //printf("pdr->pdi.source_interface == 1!\n");
              pfcp_ue_ip_address_t *ue_ip = &(pdr->pdi.ue_ip_address);

              ue_ip->flags = IE_UE_IP_ADDRESS_V4;
              ue_ip->ip4.as_u32 = resp->lns_push_information.ms_ipv4.as_u32;
              SET_BIT (pdr->pdi.grp.fields, PDI_UE_IP_ADDRESS);
              break;
           }
        }
    }
    //end liukang add for l2tp 2021/07/01

    //begin liukang add for l2tp lns push info 2021/06/17
    //if(g_upf_l2tp_switch)
    //{
    //   int lns = upf_load_lns_push_info(&msg->l2tp_user_information,&resp.lns_push_information);
      // if(lns == 1)
       //{
    //SET_BIT (resp.grp.fields,
    //                    SESSION_ESTABLISHMENT_RESPONSE_CMCC_LNS_PUSH_INFORMATION);
      // }
    //}
    //end liukang add for l2tp lns push info 2021/06/17

    return 0;
}
#endif

//begin liukang mode for l2tp 2022/08/08
static int upf_handle_establishment_l2tp(pfcp_session_establishment_request_t *msg, upf_session_t *sess,
                                   pfcp_session_establishment_response_t *resp)
{
    int r;
    sx_server_main_t *sxsm = &sx_server_main;

    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_L2TP_TUNNEL_INFORMATION))
    {
        upf_l2tp_tunnel_info_init(msg, sess);
        sess->flags |= SX_L2TP_TUNNEL_INFO;
    }

    if (ISSET_BIT (msg->grp.fields, SESSION_ESTABLISHMENT_REQUEST_L2TP_SESSION_INFORMATION))
    {
        upf_l2tp_session_info_init(msg, sess);
        sess->flags |= SX_L2TP_SESSION_INFO;
    }


//begin liukang add for l2tp 2021/07/01
    if(g_upf_l2tp_switch)
    {
       if ((r = upf_l2tp_user_info_handle (sess, &msg->l2tp_user_information, sxsm->now, &resp->grp,
               SESSION_ESTABLISHMENT_RESPONSE_FAILED_RULE_ID, &resp->failed_rule_id)) != 0)
       {
          upf_err ("handle_l2tp_user_info failed %d\n", r);
          return r;
       }

       int lns = upf_load_lns_push_info(&sess->upf_l2tp_session_information, &resp->created_l2tp_session);
       if(lns == 1)
       {
           SET_BIT (resp->grp.fields, SESSION_ESTABLISHMENT_RESPONSE_CREATED_L2TP_SESSION);
       }

       if(lns == 1)
       {
           pfcp_create_pdr_t *pdr;
           pfcp_create_pdr_t *create_pdr = msg->create_pdr;
           vec_foreach (pdr, create_pdr)
           {
               if(pdr->pdi.source_interface == 1)
               {
                   //printf("pdr->pdi.source_interface == 1!\n");
                   pfcp_ue_ip_address_t *ue_ip = &(pdr->pdi.ue_ip_address);

                   ue_ip->flags = IE_UE_IP_ADDRESS_V4;
                   ue_ip->ip4.as_u32 = resp->created_l2tp_session.lns_address.lns_ipv4.as_u32;
                   SET_BIT (pdr->pdi.grp.fields, PDI_UE_IP_ADDRESS);
                   break;
               }
           }
       }
       else
       {
            upf_debug("l2tp alloc ue ip address failed!\n");
       }
    }
//end liukang add for l2tp 2021/07/01

//begin liukang add for l2tp lns push info 2021/06/17
//if(upf_l2tp_switch)
//{
//   int lns = load_lns_push_info(&msg->l2tp_user_information,&resp.lns_push_information);
// if(lns == 1)
 //{
//SET_BIT (resp.grp.fields,
//                    SESSION_ESTABLISHMENT_RESPONSE_CMCC_LNS_PUSH_INFORMATION);
// }
//}
//end liukang add for l2tp lns push info 2021/06/17

    return 0;
}

//end liukang mode for l2tp 2022/08/08


int upf_pfcp_session_create_preprocess(sx_msg_t *req, pfcp_session_establishment_request_t *msg, session_id_t *sxid,
                                 pfcp_session_establishment_response_t *resp, upf_node_assoc_t **assoc)
{
    upf_main_t *gtm = &g_upf_main;

    //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[os_get_thread_index()].upf_stat.n4_statistics;
    //n4_statistics->sess_estab.req_times++;
    vlib_node_increment_counter (vlib_get_main (), sx_api_process_node.index, SX_PROCESS_SESSION_ESTABLISHMENT_REQUEST, 1);

    SET_BIT (resp->grp.fields, SESSION_ESTABLISHMENT_RESPONSE_CAUSE);
    resp->response.cause = PFCP_CAUSE_REQUEST_REJECTED;

    SET_BIT (resp->grp.fields, SESSION_ESTABLISHMENT_RESPONSE_NODE_ID);
    upf_pfcp_init_response_node_id (req, &resp->response);

    sxid->cp.seid = msg->f_seid.seid;
    sxid->up.seid = PFCP_SEID_DEFAULT;

    if (ip46_address_is_zero (&resp->response.node_id.ip))
    {
        upf_warn ("PFCP: No valid Node ID found, cp_seid:0x%lx\n", sxid->cp.seid);
        UPF_STATISTICS_ADD(INVALID_NODE_ID);
        return -1;
    }

    *assoc = upf_get_association (&msg->request.node_id);
    if (!(*assoc))
    {
        UPF_STATISTICS_ADD(ASSO_NOT_FOUND);
        resp->response.cause = PFCP_CAUSE_NO_ESTABLISHED_PFCP_ASSOCIATION;
        upf_warn ("PFCP: Association not found! cp_seid:0x%lx\n", sxid->cp.seid);
        return -1;
    }

    if (ip46_address_is_ip4 (&req->lcl.address))
    {
        resp->up_f_seid.flags |= IE_F_SEID_IP_ADDRESS_V4;
        resp->up_f_seid.ip4 = req->lcl.address.ip4;
        ip_set (&sxid->up.address, &req->lcl.address.ip4, 1);
        ip_set (&sxid->cp.address, &msg->f_seid.ip4, 1);
    }
    else
    {
        resp->up_f_seid.flags |= IE_F_SEID_IP_ADDRESS_V6;
        resp->up_f_seid.ip6 = req->lcl.address.ip6;
        ip_set (&sxid->up.address, &req->lcl.address.ip6, 0);
        ip_set (&sxid->cp.address, &msg->f_seid.ip6, 0);
    }

    if (upf_cp_cheating_prevention(&req->rmt.address, &sxid->cp.address, *assoc))
    {
        resp->response.cause = PFCP_CAUSE_REQUEST_REJECTED;
        upf_warn ("PFCP: CP cheating, reject! cp_seid:0x%lx\n", sxid->cp.seid);
        UPF_STATISTICS_ADD(CHEATING_FAILED);
        return -1;
    }

    if ((gtm->threshold_value) && (gtm->session_num >= gtm->threshold_value + g_n4_dev_session_num))
    {
        u8 prior = 15; /* lowest priority default */
        if (req->hdr->mp_flag)
        prior = req->hdr->session_hdr.priority;

        if (iupf_overload_pfcp_shape(prior))
        {
            resp->response.cause = PFCP_CAUSE_PFCP_ENTITY_IN_CONGESTION;
            upf_warn ("PFCP: session msg drop for overload, cp_seid:0x%lx", sxid->cp.seid);
            return -1;
        }
    }

    return 0;
}

int upf_handle_establishment_rules(pfcp_session_establishment_request_t *msg, upf_session_t *sess,
                                   pfcp_session_establishment_response_t *resp)
{
    int r;
    sx_server_main_t *sxsm = &sx_server_main;
    int id_feild = SESSION_ESTABLISHMENT_RESPONSE_FAILED_RULE_ID;

    if ((r = upf_create_pdr_handle (sess, msg->create_pdr, &resp->grp, id_feild, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_pdr_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_create_far_handle (sess, msg->create_far, &resp->grp, id_feild, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_far_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_create_urr_handle (sess, msg->create_urr, sxsm->now, &resp->grp, id_feild, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_urr_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_create_qer_handle (sess, msg->create_qer, sxsm->now, &resp->grp, id_feild, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_qer_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_create_bar_handle (sess, msg->create_bar, sxsm->now, &resp->grp, id_feild, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_bar_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_create_srr_handle (sess, msg->create_srr, sxsm->now, &resp->grp, id_feild, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_srr_handle failed %d\n", r);
        return r;
    }

    if (msg->user_plane_inactivity_timer)
    {
        sess->up_inactive_timer.timer.base = sxsm->now;
        sess->up_inactive_timer.timer.period = PFCP_HB_INTERVAL;
        sess->up_inactive_timer.period = msg->user_plane_inactivity_timer;
    }

    return r;
}

int upf_pfcp_session_create_post_process(pfcp_session_establishment_response_t *resp, upf_node_assoc_t *assoc, int is_sx_succ)
{
    //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[os_get_thread_index()].upf_stat.n4_statistics;

    if (assoc->cp_feature & F_CPFF_LOAD)
    {
        if (upf_pfcp_load_info_report (&resp->load_control_information))
            SET_BIT (resp->grp.fields, SESSION_ESTABLISHMENT_RESPONSE_LOAD_CONTROL_INFORMATION);
    }
    if (assoc->cp_feature & F_CPFF_OVRL)
    {
        if (is_sx_succ)
            g_upf_main.session_num++;
        if (upf_pfcp_overload_report (&resp->overload_control_information))
            SET_BIT (resp->grp.fields, SESSION_ESTABLISHMENT_RESPONSE_OVERLOAD_CONTROL_INFORMATION);
    }

    /*if (is_sx_succ)
    {
        n4_statistics->sess_estab.handle_succ_times++;
    }

    else
    {
        n4_statistics->sess_estab.handle_fail_times++;
        n4_statistics->sess_estab.cause_times[resp->response.cause]++;
    }
    n4_statistics->sess_estab.rsp_times++;*/

    return 0;
}

static int upf_session_establishment_request_handle (sx_msg_t *req, pfcp_session_establishment_request_t *msg)
{
    pfcp_session_establishment_response_t resp = {0};
    upf_session_t *sess = NULL;
    upf_node_assoc_t *assoc;
    session_id_t sxid = {0};
    u64 t1 = unix_time_now_nsec();

    /* association, overload, n4_statistics... */
    int r = upf_pfcp_session_create_preprocess(req, msg, &sxid, &resp, &assoc);
    if (r)
    {
        goto out_send_resp;
    }

    sess = upf_pfcp_session_create (assoc, req->fib_index, &sxid, req->seq_no);
    if (!sess)
    {
        r = -1;
        goto out_send_resp;
    }
    sxid.up.seid = sess->up_seid;
    memcpy(&sess->node_id, &msg->request.node_id, sizeof(sess->node_id));

    upf_pfcp_session_update (sess);

    if ((r = upf_handle_session_common_info(msg, sess)) != 0)
        goto out_send_resp;

    if ((r = upf_handle_establishment_l2tp(msg, sess, &resp)) != 0)
        goto out_send_resp;

    if ((r = upf_handle_establishment_rules(msg, sess, &resp)) != 0)
        goto out_send_resp;

    upf_trace ("%U", upf_format_sx_session, sess, SX_PENDING, 0);

    r = upf_session_update_apply (sess);
    upf_debug ("upf_session_update_apply result: %d cp_seid:0x%lx up_seid:0x%lx\n", r, sxid.cp.seid, sxid.up.seid);

    upf_session_update_finish (sess);

    /* load, overload, n4_statistics... */
    upf_pfcp_session_create_post_process(&resp, assoc, !r);

out_send_resp:
    if (r == 0)
    {
        sess->create_session_time = t1;// record create session succsessed time
        upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_ADD_SESS, sess, msg);

        SET_BIT (resp.grp.fields, SESSION_ESTABLISHMENT_RESPONSE_UP_F_SEID);
        resp.up_f_seid.seid = sess->up_seid;
        resp.response.cause = PFCP_CAUSE_REQUEST_ACCEPTED;

        if(sess)
            vnet_iupf_ue_stat_add_del(sess->user_id.imsi_str, 1, sess->up_seid, sess->create_session_time, 0);
    }
    upf_pfcp_response_send (req, sxid.cp.seid, PFCP_SESSION_ESTABLISHMENT_RESPONSE, &resp.grp);

    ulog_level_t lv = g_upf_pfcp_log_switch == SWITCH_ON ? ULOG_LEVEL_ERR : ULOG_LEVEL_DEBUG;
    ulog_log (lv, __FUNCTION__, __LINE__, "PFCP Establishment Req cp_seid:0x%lx up_seid:0x%lx cause:%u "
         "Usecond delay:%lu UE:%U IMSI:%s\n", sxid.cp.seid, sxid.up.seid, resp.response.cause,
        (unix_time_now_nsec() - t1) / 1000, format_ip46_address, sess ? &sess->ue_address : &g_invalid_ip, IP46_TYPE_ANY,
        sess ? sess->user_id.imsi_str : NULL);

    if ((r != 0) && (sess))
    {
        if (upf_pfcp_session_disable (sess, false) != 0)
            upf_err ("failed to remove UPF session 0x%016" PRIx64, sess->cp_seid);
        upf_pfcp_session_free (sess);
    }
    return r;
}

void upf_session_cleaning_flow_table (upf_session_t *sx, int rule)
{
  struct rules *rules = upf_get_rules (sx, rule);
  upf_pdr_t *pdr;
  u32 *flow_table_id;
  flowtable_main_t *fm = &g_flowtable_main;
  vlib_main_t *vm = vlib_get_main ();
  flow_entry_t *flow = NULL;

  upf_debug ("@@@@@@session_up_seid:0x%lx", sx->up_seid);
  if (!rules->pdr)
    return;

  if (sx->pdn_type == PDN_TYPE_ETHERNET)
    fm = &eth_flowtable_main;

  clib_spinlock_lock (&sx->lock);
  hash_free (sx->hash_pdr_id_by_flow_id[FT_ORIGIN]);
  hash_free (sx->hash_pdr_id_by_flow_id[FT_REVERSE]);
  vec_foreach (pdr, rules->pdr)
  {
    upf_debug ("@@@@@@pdr id:0x%x", pdr->id);
    vec_foreach (flow_table_id, pdr->flow_table_ids)
    {
      upf_debug ("@@@@@@flow_table_id:0x%x", *flow_table_id);
      // clib_spinlock_lock (&fm->flows_lock);
      if (!pool_is_free_index (fm->flows, *flow_table_id))
        {
          flow = pool_elt_at_index (fm->flows, *flow_table_id);
          upf_flowtable_reset (flow);
          flow->is_update = 1;
          flow->update_timestamp = (u32)vlib_time_now (vm);
          upf_debug ("set the flow table: 0x%x pdr_id 0x%x to default!\n",
                     *flow_table_id, pdr->id);
        }
      // clib_spinlock_unlock (&fm->flows_lock);
    }
    vec_free (pdr->flow_table_ids);
  }
  clib_spinlock_unlock (&sx->lock);
}

static int upf_handle_modification_rules(pfcp_session_modification_request_t *msg, upf_session_t *sess,
                                         pfcp_session_modification_response_t *resp)
{
    int r;
    int id_field = SESSION_MODIFICATION_RESPONSE_FAILED_RULE_ID;
    sx_server_main_t *sxsm = &sx_server_main;

    if (!(msg->grp.fields &
        (BIT (SESSION_MODIFICATION_REQUEST_REMOVE_PDR) |
         BIT (SESSION_MODIFICATION_REQUEST_REMOVE_FAR) |
         BIT (SESSION_MODIFICATION_REQUEST_REMOVE_URR) |
         BIT (SESSION_MODIFICATION_REQUEST_REMOVE_QER) |
         BIT (SESSION_MODIFICATION_REQUEST_REMOVE_BAR) |
         BIT (SESSION_MODIFICATION_REQUEST_REMOVE_SRR) |
         BIT (SESSION_MODIFICATION_REQUEST_CREATE_PDR) |
         BIT (SESSION_MODIFICATION_REQUEST_CREATE_FAR) |
         BIT (SESSION_MODIFICATION_REQUEST_CREATE_URR) |
         BIT (SESSION_MODIFICATION_REQUEST_CREATE_QER) |
         BIT (SESSION_MODIFICATION_REQUEST_CREATE_BAR) |
         BIT (SESSION_MODIFICATION_REQUEST_CREATE_SRR) |
         BIT (SESSION_MODIFICATION_REQUEST_UPDATE_PDR) |
         BIT (SESSION_MODIFICATION_REQUEST_UPDATE_FAR) |
         BIT (SESSION_MODIFICATION_REQUEST_UPDATE_URR) |
         BIT (SESSION_MODIFICATION_REQUEST_UPDATE_QER) |
         BIT (SESSION_MODIFICATION_REQUEST_UPDATE_BAR) |
         BIT (SESSION_MODIFICATION_REQUEST_UPDATE_SRR) |
         BIT (SESSION_MODIFICATION_REQUEST_USER_PLANE_INACTIVITY_TIMER))))
    {
        return 0;
    }

    /* invoke the update process only if a update is include */
    upf_pfcp_session_update (sess);
    if ((r = upf_create_pdr_handle (sess, msg->create_pdr, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_pdr_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_update_pdr_handle (sess, msg->update_pdr, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_update_pdr_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_remove_pdr_handle (sess, msg->remove_pdr, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_remove_pdr_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_create_far_handle (sess, msg->create_far, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_far_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_update_far_handle (sess, msg->update_far, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_update_far_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_remove_far_handle (sess, msg->remove_far, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_remove_far_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_create_urr_handle (sess, msg->create_urr, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_urr_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_update_urr_handle (sess, msg->update_urr, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_update_urr_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_remove_urr_handle (sess, msg->remove_urr, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_remove_urr_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_create_qer_handle (sess, msg->create_qer, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_qer_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_update_qer_handle (sess, msg->update_qer, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_update_qer_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_remove_qer_handle (sess, msg->remove_qer, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_remove_qer_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_create_bar_handle (sess, msg->create_bar, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_bar_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_update_bar_handle (sess, msg->update_bar, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_update_bar_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_remove_bar_handle (sess, msg->remove_bar, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_remove_bar_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_create_srr_handle (sess, msg->create_srr, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_create_srr_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_update_srr_handle (sess, msg->update_srr, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_update_srr_handle failed %d\n", r);
        return r;
    }

    if ((r = upf_remove_srr_handle (sess, msg->remove_srr, sxsm->now, &resp->grp, id_field, &resp->failed_rule_id)) != 0)
    {
        upf_err ("upf_remove_srr_handle failed %d\n", r);
        return r;
    }

    if (msg->user_plane_inactivity_timer)
    {
        sess->up_inactive_timer.timer.base = sxsm->now;
        sess->up_inactive_timer.timer.period = PFCP_HB_INTERVAL;
        sess->up_inactive_timer.period = msg->user_plane_inactivity_timer;
    }

    if ((r = upf_session_update_apply (sess)) != 0)
    {
        upf_err ("upf_session_update_apply failed %d\n", r);
        return r;
    }

    return 0;
}

static int upf_handle_modification_post_process(pfcp_session_modification_request_t *msg, upf_session_t *sess,
                                                pfcp_session_modification_response_t *resp)
{
    upf_node_assoc_t *assoc = NULL;
    sx_server_main_t *sxsm = &sx_server_main;
    pfcp_query_urr_t *qry;

    if (!pool_is_free_index (g_upf_main.nodes, sess->assoc.node))
        assoc = pool_elt_at_index (g_upf_main.nodes, sess->assoc.node);

    if (assoc)
    {
         if (assoc->cp_feature & F_CPFF_LOAD)
         {
             if (upf_pfcp_load_info_report (&resp->load_control_information))
             SET_BIT (resp->grp.fields, SESSION_MODIFICATION_RESPONSE_LOAD_CONTROL_INFORMATION);
         }

         if (assoc->cp_feature & F_CPFF_OVRL)
         {
             if (upf_pfcp_overload_report (&resp->overload_control_information))
                 SET_BIT (resp->grp.fields, SESSION_MODIFICATION_RESPONSE_OVERLOAD_CONTROL_INFORMATION);
         }
    }

    if (ISSET_BIT (msg->grp.fields, SESSION_MODIFICATION_REQUEST_QUERY_URR) && vec_len (msg->query_urr) != 0)
    {
        SET_BIT (resp->grp.fields, SESSION_MODIFICATION_RESPONSE_USAGE_REPORT);
        vec_foreach (qry, msg->query_urr)
        {
            upf_urr_t *urr;
            if (!(urr = upf_get_urr (sess, SX_ACTIVE, qry->urr_id)))
                continue;

            upf_usage_report_build (sess, urr, USAGE_REPORT_TRIGGER_IMMEDIATE_REPORT, sxsm->now, &resp->usage_report, UPF_INVALID_PDR);
        }
    }

    if (ISSET_BIT (msg->grp.fields, SESSION_MODIFICATION_REQUEST_PFCPSMREQ_FLAGS))
    {
        struct rules *active = upf_get_rules (sess, SX_ACTIVE);
        if (msg->pfcpsmreq_flags & PFCPSMREQ_QAURR)
        {
            if (vec_len (active->urr) != 0)
            {
                upf_urr_t *urr;
                SET_BIT (resp->grp.fields, SESSION_MODIFICATION_RESPONSE_USAGE_REPORT);
                vec_foreach (urr, active->urr)
                {
                    upf_usage_report_build (sess, urr, USAGE_REPORT_TRIGGER_IMMEDIATE_REPORT, sxsm->now, &resp->usage_report, UPF_INVALID_PDR);
                }
            }
        }

        if (msg->pfcpsmreq_flags & PFCPSMREQ_DROBU)
        {
            upf_far_t *far0;
            vec_foreach (far0, active->far)
            {
                if ((far0->forward.dst_intf == DST_INTF_ACCESS) && far0->buffer_bi[UPF_DL] && (far0->apply_action & F_APPLY_BUFF))
                {
                    upf_pfcp_clean_far_buffering_list(sess, far0);
                    UPF_PDU_SESS_STATISTICS_ADD(sess, CP_TO_UP_DEL_BUFFER);
                }

            }
        }
    }
    return 0;
}

static int upf_session_modification_request_handle (sx_msg_t *req, pfcp_session_modification_request_t *msg)
{
    pfcp_session_modification_response_t resp = {0};
    upf_session_t *sess;
    int r = -1;
    u64 t1 = unix_time_now_nsec();

    /*u32 cpu_index = os_get_thread_index ();
    upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[cpu_index].upf_stat.n4_statistics;
    n4_statistics->sess_modify.req_times++;*/

    SET_BIT (resp.grp.fields, SESSION_ESTABLISHMENT_RESPONSE_CAUSE);
    resp.response.cause = PFCP_CAUSE_REQUEST_REJECTED;

    u64 cp_seid = 0;
    u64 up_seid = be64toh (req->hdr->session_hdr.seid);
    if (!(sess = upf_session_lookup (up_seid)))
    {
        upf_err ("Sx Session 0x%016lx not found.\n", up_seid);
        resp.response.cause = PFCP_CAUSE_SESSION_CONTEXT_NOT_FOUND;
        UPF_STATISTICS_ADD(SESS_NOT_FOUND_MOD);
        goto out_send_resp;
    }

    upf_node_assoc_t *assoc = NULL;
    if (!pool_is_free_index (g_upf_main.nodes, sess->assoc.node))
      assoc = pool_elt_at_index (g_upf_main.nodes, sess->assoc.node);
    if (upf_cp_cheating_prevention(&req->rmt.address, &sess->cp_address, assoc))
    {
        upf_err ("Sx Session 0x%016lx cheating, reject.\n", up_seid);
        UPF_STATISTICS_ADD(CHEATING_FAILED);
        goto out_send_resp;
    }

    if (sess->flags & SX_UPDATING)
    {
        upf_err ("Sx Session 0x%016lx updating.\n", up_seid);
        /* neil.fan@20230324 modify for "... a temporary unavailability of resources to process the received request" */
        resp.response.cause = PFCP_CAUSE_NO_RESOURCES_AVAILABLE;
        goto out_send_resp;
    }
    cp_seid = sess->cp_seid;
    sess->update_session_time = t1;// record create session succsessed time
    if ((r = upf_handle_modification_rules(msg, sess, &resp)) != 0)
    {
        goto out_update_finished;
    }

    /* asso, load, overload, urr report, ... */
    upf_handle_modification_post_process(msg, sess, &resp);

out_update_finished:
    for (int i = 0; i < ARRAY_LEN (sess->rules); i++)
        upf_session_cleaning_flow_table (sess, i);

    upf_session_update_finish (sess);

    upf_trace ("%U", upf_format_sx_session, sess, SX_ACTIVE, 0);

out_send_resp:
    if (r == 0)
    {
        //n4_statistics->sess_modify.handle_succ_times++;
        resp.response.cause = PFCP_CAUSE_REQUEST_ACCEPTED;
        upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_ADD_SESS, sess, msg);
    }
    else
    {
        //n4_statistics->sess_modify.handle_fail_times++;
        //n4_statistics->sess_modify.cause_times[resp.response.cause]++;
    }

    //n4_statistics->sess_modify.rsp_times++;

    upf_pfcp_response_send (req, cp_seid, PFCP_SESSION_MODIFICATION_RESPONSE, &resp.grp);

    ulog_level_t lv = (g_upf_pfcp_log_switch == SWITCH_ON) ? ULOG_LEVEL_ERR : ULOG_LEVEL_DEBUG;
    ulog_log (lv, __FUNCTION__, __LINE__, "PFCP Modification Req cp_seid:0x%016lx up_seid:0x%016lx cause:%u "
         "Usecond delay:%lu UE:%U\n", cp_seid, up_seid, resp.response.cause,
        (unix_time_now_nsec() - t1) / 1000, format_ip46_address, sess ? &sess->ue_address : &g_invalid_ip, IP46_TYPE_ANY);

    if (resp.usage_report)
        vec_free (resp.usage_report);

    return r;
}

u32 pfcp_build_one_qos_monitor_report (pfcp_qos_monitoring_report_t *p, upf_per_qos_monitor_flow_control_t *flow_ctrl,
                                       u8 reporting_frequency, u32 now, pfcp_qos_monitoring_measurement_t *measurement)
{
    SET_BIT (p->grp.fields, QOS_MONITORING_REPORT_QFI);
    p->qfi = flow_ctrl->qfi;

    SET_BIT (p->grp.fields, QOS_MONITORING_REPORT_MEASUREMENT);
    p->qos_monitor_measure.flags = IE_QOS_MONITOR_MEASURE_PLMF;

    if ((reporting_frequency == IE_REPORTING_FREQUENCYE_SESRL) && flow_ctrl->last_measure_start_time)
    {
        clib_memcpy (&p->qos_monitor_measure, &flow_ctrl->last_measurement, sizeof(pfcp_qos_monitoring_measurement_t));
        SET_BIT (p->grp.fields, QOS_MONITORING_REPORT_START_TIME);
        p->start_time = flow_ctrl->last_measure_start_time;
    }
    else if ((reporting_frequency == IE_REPORTING_FREQUENCYE_EVENT) || (reporting_frequency == IE_REPORTING_FREQUENCYE_PERIOD))
    {
        if (measurement)
            clib_memcpy (&p->qos_monitor_measure, measurement, sizeof(pfcp_qos_monitoring_measurement_t));

        SET_BIT (p->grp.fields, QOS_MONITORING_REPORT_START_TIME);
        if (reporting_frequency == IE_REPORTING_FREQUENCYE_PERIOD)
            p->start_time = flow_ctrl->periodic_report.start_time;
        else
            p->start_time = flow_ctrl->default_report.start_time;
    }

    SET_BIT (p->grp.fields, QOS_MONITORING_REPORT_TIME_STAMP);
    p->time_stamp = now;

    return 0;
}

u32 upf_pfcp_deletion_srr_report_build (upf_srr_t *v_srr, f64 now, pfcp_session_report_t **report)
{
    upf_srr_t *srr;
    vec_foreach (srr, v_srr)
    {
        pfcp_session_report_t n = {0};
        upf_per_qos_flow_control_t *per_qos_flow_ctrl;
        vec_foreach (per_qos_flow_ctrl, srr->per_qos_flow_ctrl)
        {
            if (!(per_qos_flow_ctrl->reporting_frequency.flags & IE_REPORTING_FREQUENCYE_SESRL))
                continue;

            upf_per_qos_monitor_flow_control_t *flow_ctrl;
            vec_foreach (flow_ctrl, per_qos_flow_ctrl->flow_ctrl)
            {
                pfcp_qos_monitoring_report_t qos_monitor_report = {0};
                pfcp_build_one_qos_monitor_report (&qos_monitor_report, flow_ctrl, IE_REPORTING_FREQUENCYE_SESRL, now, NULL);
                vec_add1 (n.qos_monitor_report, qos_monitor_report);
            }
        }

        if (n.qos_monitor_report)
        {
            SET_BIT (n.grp.fields, SESSION_REPORT_SRR_ID);
            SET_BIT (n.grp.fields, SESSION_REPORT_QOS_MONITORING);
            n.srr_id = srr->id;
            vec_add1 (*report, n);
        }
    }

    if (vec_len(*report))
        return 0;

    return 1;
}

static int upf_session_deletion_request_handle (sx_msg_t *req, pfcp_session_deletion_request_t *msg)
{
  sx_server_main_t *sxsm = &sx_server_main;
  pfcp_session_deletion_response_t resp;
  f64 now = sxsm->now;
  upf_session_t *sess;
  upf_node_assoc_t *assoc = NULL;
  struct rules *active;
  u64 cp_seid = 0;
  int r = -1;
  upf_main_t *gtm = &g_upf_main;

  upf_debug ("Session Deletion Request message received");

  vlib_node_increment_counter (vlib_get_main (), sx_api_process_node.index,
                               SX_PROCESS_SESSION_DELETION_REQUEST, 1);

  memset (&resp, 0, sizeof (resp));
  SET_BIT (resp.grp.fields, SESSION_DELETION_RESPONSE_CAUSE);
  resp.response.cause = PFCP_CAUSE_REQUEST_REJECTED;

  u64 up_seid = be64toh (req->hdr->session_hdr.seid);
  if (!(sess = upf_session_lookup (up_seid)))
    {
      upf_err ("PFCP Deletion Req Fail, up_seid:0x%016lx not found.\n", up_seid);
      resp.response.cause = PFCP_CAUSE_SESSION_CONTEXT_NOT_FOUND;
      UPF_STATISTICS_ADD(SESS_NOT_FOUND_DEL);
      goto out_send_resp;
    }

  cp_seid = sess->cp_seid;

  ulog_level_t lv = ULOG_LEVEL_ERR;
  ulog_log (lv, __FUNCTION__, __LINE__, "PFCP Deletion Req cp_seid:0x%016lx up_seid:0x%016lx cause:%u UE:%U IMSI:%s\n",
      cp_seid, up_seid, resp.response.cause, format_ip46_address, sess ? &sess->ue_address : &g_invalid_ip, IP46_TYPE_ANY,
      sess ? sess->user_id.imsi_str : NULL);

  if (!pool_is_free_index (g_upf_main.nodes, sess->assoc.node))
    assoc = pool_elt_at_index (g_upf_main.nodes, sess->assoc.node);

  if (upf_cp_cheating_prevention(&req->rmt.address, &sess->cp_address, assoc))
    {
      upf_err ("Sx Session 0x%016lx cheating, reject.\n", up_seid);
      UPF_STATISTICS_ADD(CHEATING_FAILED);
      goto out_send_resp;
    }

  if (assoc && assoc->cp_feature & F_CPFF_LOAD)
    {
      if (upf_pfcp_load_info_report (&resp.load_control_information))
        SET_BIT (resp.grp.fields, SESSION_DELETION_RESPONSE_LOAD_CONTROL_INFORMATION);
    }

  r = 0;
  /* add by liudong in 20210510 for pfcp消息过载 begin */
  if (assoc->cp_feature & F_CPFF_OVRL)
  {
      if (!r && gtm->session_num)
        gtm->session_num--;
      if (upf_pfcp_overload_report (&resp.overload_control_information))
          SET_BIT (resp.grp.fields, SESSION_DELETION_RESPONSE_OVERLOAD_CONTROL_INFORMATION);
  }
  /* add by liudong in 20210510 for pfcp消息过载 end */
  if ((r = upf_pfcp_session_disable (sess, true)) != 0)
    {
      upf_err ("Sx Session 0x%016lx could no be disabled.\n", be64toh (req->hdr->session_hdr.seid));
      goto out_send_resp;
    }

  active = upf_get_rules (sess, SX_ACTIVE);
  if (vec_len (active->urr) != 0)
    {
      upf_urr_t *urr;

      SET_BIT (resp.grp.fields, SESSION_DELETION_RESPONSE_USAGE_REPORT);

      vec_foreach (urr, active->urr)
        {
          upf_urr_t **linked_list = NULL;
          if (upf_pfcp_linked_urr_list_found (active, urr->id, &linked_list))
            {
              upf_pfcp_linked_urr_usage_accumulated(urr, linked_list);
              vec_free (linked_list);
            }
        }

      vec_foreach (urr, active->urr)
        {
          if ((urr->methods & SX_URR_VOLUME) &&
              (urr->volume.measure.consumed.total == 0))
            continue;

          u32 trigger = USAGE_REPORT_TRIGGER_TERMINATION_REPORT;
          if (vec_len(urr->linked_urr_ids))
              trigger |= USAGE_REPORT_TRIGGER_LINKED_USAGE_REPORTING;
          upf_usage_report_build (sess, urr, trigger, now, &resp.usage_report, UPF_INVALID_PDR);
        }
    }

  if (vec_len (active->srr))
    {
      if (!upf_pfcp_deletion_srr_report_build (active->srr, now, &resp.session_report))
        SET_BIT (resp.grp.fields, SESSION_DELETION_RESPONSE_SESSION_REPORT);
    }

out_send_resp:
  if (r == 0)
    {
      //n4_statistics->sess_delete.handle_succ_times++;
      upf_pfcp_events_publish (PFCP_RPC_PUBLISH_REDIS_DEL_SESS, sess, msg);
      memcpy(&req->user_id, &sess->user_id, sizeof(req->user_id));
      req->user_id.nai = NULL;

      upf_pfcp_session_free (sess);
      resp.response.cause = PFCP_CAUSE_REQUEST_ACCEPTED;
    }

  upf_pfcp_response_send (req, cp_seid, PFCP_SESSION_DELETION_RESPONSE,
                          &resp.grp);

  if (resp.usage_report)
    vec_free (resp.usage_report);

  return r;
}

static int upf_session_report_response_handle (sx_msg_t *req, pfcp_session_report_response_t *msg)
{
  upf_session_t *sess;
  int r = 0;

  //upf_pfcp_msg_count_t *n4_statistics = &g_flowtable_main.per_cpu[os_get_thread_index ()].upf_stat.n4_statistics;
  //n4_statistics->sess_report.rsp_times++;

  if (!(sess = upf_session_lookup (be64toh (req->hdr->session_hdr.seid))))
    {
      upf_err ("Sx Session %" PRIu64 " not found.\n",
               be64toh (req->hdr->session_hdr.seid));

      r = -1;
      //n4_statistics->sess_report.handle_fail_times++;
      return r;
    }

  if (msg->grp.fields & BIT (SESSION_REPORT_RESPONSE_UPDATE_BAR))
    {
      if ((r = upf_update_bar_response_handle (sess, msg->update_bar)) != 0)
        {
          upf_err ("update bar failed");
          goto rtn;
        }

      r = upf_session_update_apply (sess);
      upf_debug ("upf_session_update_apply result: %d\n", r);
    }

rtn:
  upf_session_update_finish (sess);

  /*if (!r)
    n4_statistics->sess_report.handle_succ_times++;
  else
    n4_statistics->sess_report.handle_fail_times++;*/

  return r;
}

static int upf_pfcp_session_msg (sx_msg_t *msg)
{
  union
  {
    struct pfcp_group grp;
    pfcp_simple_response_t simple_response;
    pfcp_session_set_deletion_request_t session_set_deletion_request;
    pfcp_session_establishment_request_t session_establishment_request;
    pfcp_session_establishment_response_t session_establishment_response;
    pfcp_session_modification_request_t session_modification_request;
    pfcp_session_modification_response_t session_modification_response;
    pfcp_session_deletion_request_t session_deletion_request;
    pfcp_session_deletion_response_t session_deletion_response;
    pfcp_session_report_request_t session_report_request;
    pfcp_session_report_response_t session_report_response;
  } m;
  pfcp_offending_ie_t *err = NULL;

  if (!msg->hdr->s_flag)
    {
      upf_err ("PFCP: session msg without SEID.");
      UPF_STATISTICS_ADD(INVALID_SESS_FLAG);
      return -1;
    }

  memset (&m, 0, sizeof (m));
  int r = upf_pfcp_decode_msg (msg->hdr->type, &msg->hdr->session_hdr.ies[0],
                       clib_net_to_host_u16 (msg->hdr->length) - sizeof (msg->hdr->session_hdr), &m.grp, &err);
  if (r != 0)
    {
      upf_info ("PFCP: Decode pfcp message fail! message type: %d, cause=%d", msg->hdr->type, r);
      UPF_STATISTICS_ADD(DOCODE_PFCP_SESSION_MSG_FAILURE);
      switch (msg->hdr->type)
        {
        case PFCP_SESSION_SET_DELETION_REQUEST:
        case PFCP_SESSION_ESTABLISHMENT_REQUEST:
        case PFCP_SESSION_MODIFICATION_REQUEST:
        case PFCP_SESSION_DELETION_REQUEST:
        case PFCP_SESSION_REPORT_REQUEST:
          upf_pfcp_simple_repsonse_send (msg, 0, msg->hdr->type + 1, r, err);
          break;

        default:
          break;
        }

      upf_pfcp_free_msg (msg->hdr->type, &m.grp);
      vec_free (err);
      return r;
    }

  switch (msg->hdr->type)
    {
    case PFCP_SESSION_ESTABLISHMENT_REQUEST:
      r = upf_session_establishment_request_handle (msg, &m.session_establishment_request);
      break;

    case PFCP_SESSION_MODIFICATION_REQUEST:
      r = upf_session_modification_request_handle (msg, &m.session_modification_request);
      break;

    case PFCP_SESSION_DELETION_REQUEST:
      r = upf_session_deletion_request_handle (msg, &m.session_deletion_request);
      break;

    case PFCP_SESSION_REPORT_RESPONSE:
      r = upf_session_report_response_handle (msg, &m.session_report_response);
      break;

    default:
      break;
    }

  upf_pfcp_free_msg (msg->hdr->type, &m.grp);
  return 0;
}

void upf_pfcp_error_indication_report (upf_session_t *sx, gtp_error_ind_t *error)
{
  pfcp_session_report_request_t req;
  pfcp_f_teid_t f_teid = {0};

  memset (&req, 0, sizeof (req));
  SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_REPORT_TYPE);
  req.report_type = REPORT_TYPE_ERIR;

  SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_ERROR_INDICATION_REPORT);
  SET_BIT (req.error_indication_report.grp.fields,
           ERROR_INDICATION_REPORT_F_TEID);

  f_teid.teid = error->teid;
  if (ip46_address_is_ip4 (&error->addr))
    {
      f_teid.flags = F_TEID_V4;
      f_teid.ip4 = error->addr.ip4;
    }
  else
    {
      f_teid.flags = F_TEID_V6;
      f_teid.ip6 = error->addr.ip6;
    }

  vec_add1 (req.error_indication_report.f_teid, f_teid);

  g_flowtable_main.per_cpu[os_get_thread_index ()].upf_stat.n4_statistics.report_type.erir++;
  upf_pfcp_request_send (sx, PFCP_SESSION_REPORT_REQUEST, &req.grp, EVENT_TX);
}

void upf_pfcp_downlink_data_report (upf_session_t *sx, upf_pdr_t *pdr)
{
    pfcp_session_report_request_t req;
    pfcp_downlink_data_service_information_t downlink_data_service_information;

    memset (&req, 0, sizeof (req));
    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_REPORT_TYPE);

    req.report_type |= REPORT_TYPE_DLDR;

    SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_DOWNLINK_DATA_REPORT);
    SET_BIT (req.downlink_data_report.grp.fields, DOWNLINK_DATA_REPORT_PDR_ID);
    vec_add1 (req.downlink_data_report.pdr_id, pdr->id);

    sx->session_report_time = unix_time_now_nsec();
    if ((PDN_TYPE_IPv4 <= sx->pdn_type) && (sx->pdn_type <= PDN_TYPE_IPv4v6))
    {
       clib_memcpy (&downlink_data_service_information, &pdr->downlink_data_service_information,
                    sizeof (pfcp_downlink_data_service_information_t));
       SET_BIT (req.downlink_data_report.grp.fields, DOWNLINK_DATA_REPORT_DOWNLINK_DATA_SERVICE_INFORMATION);
       vec_add1 (req.downlink_data_report.downlink_data_service_information, downlink_data_service_information);
    }
    g_flowtable_main.per_cpu[os_get_thread_index ()].upf_stat.n4_statistics.report_type.dldr++;
    upf_pfcp_request_send (sx, PFCP_SESSION_REPORT_REQUEST, &req.grp, EVENT_TX);
    upf_debug(" upf_pfcp_downlink_data_report ueip: %U", format_ip46_address, &sx->ue_address, IP46_TYPE_ANY);
}

void upf_pfcp_user_plane_inactive_report (upf_session_t *sx)
{
  pfcp_session_report_request_t req;
  memset (&req, 0, sizeof (req));
  SET_BIT (req.grp.fields, SESSION_REPORT_REQUEST_REPORT_TYPE);
  req.report_type = REPORT_TYPE_UPIR;

  g_flowtable_main.per_cpu[os_get_thread_index ()].upf_stat.n4_statistics.report_type.upir++;
  upf_pfcp_request_send (sx, PFCP_SESSION_REPORT_REQUEST, &req.grp, EVENT_TX);
}

u8 *upf_format_acl_rule(u8 *s, va_list *args)
{
    acl_rule_t *acl = va_arg (*args, acl_rule_t *);
    s = format (s, "%U", upf_format_ipfilter, acl);
    if (acl->tos != 0)
        s = format (s, ", tos = %d", acl->tos);
    if (acl->id != 0)
        s = format (s, ", sdf id = %d", acl->id);
    s = format (s, "\n");
    return s;
}

u8 *upf_format_ethernet_packet_filter(u8 *s, va_list *args)
{
    pfcp_ethernet_packet_filter_t *v = va_arg (*args, pfcp_ethernet_packet_filter_t *);
    int is_pfcp = va_arg (*args, int);
    u32 fields = v->grp.fields;

    if (ISSET_BIT(fields, ETHERNET_PACKET_FILTER_ETHERNET_FILTER_ID))
        s = format (s, "      Ethernet filter id: %u\n", v->ethernet_filter_id);

    if (ISSET_BIT(fields, ETHERNET_PACKET_FILTER_ETHERNET_FILTER_PROPERTIES))
        s = format (s, "      Filter Properties : %U\n", upf_format_ethernet_filter_properties, &v->ethernet_filter_properties);

    if (ISSET_BIT(fields, ETHERNET_PACKET_FILTER_MAC_ADDRESS))
    {
        pfcp_mac_address_t *mac;
        vec_foreach (mac, v->mac_address)
        {
            s = format (s, "      Mac address       : %U\n", upf_format_pfcp_mac_address, mac);
        }
    }

    if (ISSET_BIT(fields, ETHERNET_PACKET_FILTER_ETHERTYPE))
        s = format (s, "      Ethernet type     : 0x%x\n", v->ethertype);

    if (ISSET_BIT(fields, ETHERNET_PACKET_FILTER_C_TAG))
        s = format (s, "      C-TAG             : %U\n", upf_format_vlan_tag, &v->c_tag);

    if (ISSET_BIT(fields, ETHERNET_PACKET_FILTER_S_TAG))
        s = format (s, "      S-TAG             : %U\n", upf_format_vlan_tag, &v->s_tag);

    if (ISSET_BIT(fields, ETHERNET_PACKET_FILTER_SDF_FILTER))
    {
        if (is_pfcp)
        {
            pfcp_sdf_filter_t *sdf;
            vec_foreach (sdf, v->sdf_filter)
            {
                s = format (s, "      Sdf Filter        : %U\n", upf_format_sdf_filter, sdf);
            }
        }
        else
        {
            upf_ethernet_packet_filter_t *p = (upf_ethernet_packet_filter_t *)v;
            acl_rule_t *acl;
            vec_foreach (acl, p->acl)
            {
                s = format (s, "      Sdf Filter        : %U", upf_format_acl_rule, acl);
            }
        }
    }
    return s;
}

static clib_error_t *
iupf_load_control_switch_command_fn(vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
  while (unformat_check_input (input) != UNFORMAT_END_OF_INPUT)
  {
    if (unformat (input, "on"))
      g_upf_load_control_swtich = SWITCH_ON;
    else if (unformat (input, "off"))
      g_upf_load_control_swtich = SWITCH_OFF;
    else if (unformat (input, "show"))
      {
        vlib_cli_output (vm, "switch is [%s]\n", g_upf_load_control_swtich == SWITCH_ON ? "on" : "off");
        return 0;
      }
    else
      return 0;
  }
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (iupf_load_control_switch_command, static) = {
    .path = "upf load control switch",
    .short_help = "upf load control switch [on | off | show]",
    .function = iupf_load_control_switch_command_fn,
};
/* *INDENT-OFF* */

static clib_error_t *
iupf_load_control_param_command_fn(vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  u32 interval = 0;
  u32 metric_threshold = 0;
  u32 pps = 0;
  u8 is_shown = 0;
  u8 interval_set = 0;
  u8 threshold_set = 0;
  u8 pps_set = 0;

  upf_load_control_param_t *p = &g_upf_load_ctrl_param;

  /* Get a line of input. */
  if (!unformat_user (input, unformat_line_input, line_input))
    {
      vlib_cli_output (vm, "     input none\n");
      return 0;
    }

  while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
    {
      if (unformat (line_input, "interval %u", &interval))
        interval_set = 1;
      else if (unformat (line_input, "metric-threshold %u", &metric_threshold))
        threshold_set = 1;
      else if (unformat (line_input, "max-pps %u", &pps))
        pps_set = 1;
      else if (unformat (line_input, "show"))
        is_shown = 1;
      else
        {
          vlib_cli_output (vm, "     input error\n");
          unformat_free (line_input);
          return 0;
        }
    }
  unformat_free (line_input);

  if (interval_set)
    p->interval = interval;

  if (threshold_set && (metric_threshold <= 100))
    p->metric_threshold = metric_threshold;

  if (pps_set)
    g_upf_main.lc_max_pps = pps;

  if (is_shown)
    {
      vlib_cli_output (vm, " interval (minimum interval between two reports): %us", p->interval);
      vlib_cli_output (vm, " max-pps                                        : %um", g_upf_main.lc_max_pps);
      vlib_cli_output (vm, " metric-threshold                               : %u%%", p->metric_threshold);
      vlib_cli_output (vm, " last-metric-reported                           : %u%%", p->last_metric_reported);
    }
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (iupf_load_control_param_command, static) = {
    .path = "upf load control param",
    .short_help = "upf load control param [interval <>] [max-pps <>] [metric-threshold <>] [show]",
    .function = iupf_load_control_param_command_fn,
};
/* *INDENT-OFF* */

static clib_error_t *
iupf_overload_control_param_command_fn(vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  u32 interval = 0;
  u8 is_shown = 0;
  u8 interval_set = 0;
  upf_overload_control_param_t *p = &g_upf_overload_ctrl_param;

  /* Get a line of input. */
  if (!unformat_user (input, unformat_line_input, line_input))
    {
      vlib_cli_output (vm, "     input none\n");
      return 0;
    }

  while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
    {
      if (unformat (line_input, "interval %u", &interval))
        interval_set = 1;
      else if (unformat (line_input, "show"))
        is_shown = 1;
      else
        {
          vlib_cli_output (vm, "     input error\n");
          unformat_free (line_input);
          return 0;
        }
    }
  unformat_free (line_input);

  if (interval_set)
    p->interval = interval;

  if (is_shown)
    {
      vlib_cli_output (vm, " interval (minimum interval between two reports): %us", p->interval);
      vlib_cli_output (vm, " last-metric-reported                           : %u%%", p->last_metric_reported);
    }
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (iupf_overload_control_param_command, static) = {
    .path = "upf overload control param",
    .short_help = "upf overload control param [interval <>] [show]",
    .function = iupf_overload_control_param_command_fn,
};
/* *INDENT-OFF* */

static clib_error_t *
iupf_n9_tunnel_inactive_timer_command_fn(vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  u32 value_set = 0;
  u32 value = 0;
  u32 is_shown = 0;

  /* Get a line of input. */
  if (!unformat_user (input, unformat_line_input, line_input))
    {
      vlib_cli_output (vm, "     input none\n");
      return 0;
    }

  while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
    {
      if (unformat (line_input, "set %u", &value))
        value_set = 1;
      else if (unformat (line_input, "show"))
        is_shown = 1;
      else
        {
          vlib_cli_output (vm, "     input error\n");
          unformat_free (line_input);
          return 0;
        }
    }
  unformat_free (line_input);

  if (value_set)
    g_upf_n9_tunnel_inactive_timer = value;

  if (is_shown)
    {
      if (!g_upf_n9_tunnel_inactive_timer)
          vlib_cli_output (vm, " timer is invalid");
      else
          vlib_cli_output (vm, " timer is %us", g_upf_n9_tunnel_inactive_timer);
    }
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (iupf_n9_tunnel_inactive_timer_command, static) = {
    .path = "upf n9 tunnel inactive timer",
    .short_help = "upf n9 tunnel inactive timer [set <value> | show]",
    .function = iupf_n9_tunnel_inactive_timer_command_fn,
};
/* *INDENT-ON* */

int upf_monitor_send_timer_init(upf_monitor_send_timer_t *timer, u32 trigger_period, f64 now)
{
    timer->tx_trigger.handle = timer->dummy_gtp.handle = timer->fail_report.handle = ~0;

    if (!trigger_period)
        return 1;

    timer->wait_timestamp = 0;

    timer->tx_trigger.period = trigger_period;
    timer->tx_trigger.base = now;

    timer->dummy_gtp.period = g_upf_per_qos_timer_config.t2;
    timer->dummy_gtp.base = now;

    timer->fail_report.period = g_upf_per_qos_timer_config.t3;
    timer->fail_report.base = now;

    if (g_upf_per_qos_timer_config.t3 > trigger_period)
    {
        upf_info ("error, t3:%u should less than trigger_period:%u ", g_upf_per_qos_timer_config.t3, trigger_period);
        return 1;
    }

    return 0;
}

void upf_pfcp_monitor_send_timer_reset (upf_monitor_send_timer_t *timer, f64 now, u32 sx_idx, u8 status)
{
    timer->measuring = 1; /* measure begin */
    timer->send_cnt = 0;
    timer->recv_cnt = 0;
    timer->is_report = 0;

    upf_pfcp_timer_reset (sx_idx, UPF_SRR_TIMER, &timer->tx_trigger, status);
    f64 base = timer->tx_trigger.base;

    if (status & TIMER_ADVANCE)
    {
        timer->dummy_gtp.base = base;
        timer->fail_report.base = base;
        status &= ~TIMER_ADVANCE;
    }
    upf_pfcp_timer_reset (sx_idx, UPF_SRR_TIMER, &timer->dummy_gtp, status);
    upf_pfcp_timer_reset (sx_idx, UPF_SRR_TIMER, &timer->fail_report, status);

    timer->start_time = (u32)now;
}

void upf_pfcp_monitor_send_timer_stop (upf_monitor_send_timer_t *timer)
{
    timer->measuring = 0;
    upf_pfcp_session_urr_time_stop (&timer->tx_trigger);
    upf_pfcp_session_urr_time_stop (&timer->dummy_gtp);
    upf_pfcp_session_urr_time_stop (&timer->fail_report);
}

static clib_error_t *
upf_per_qos_monitor_detect_timer_command_fn(vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  u32 is_set = 0;
  u32 is_shown = 0;
  struct upf_per_qos_timer_t cfg = g_upf_per_qos_timer_config;

  /* Get a line of input. */
  if (!unformat_user (input, unformat_line_input, line_input))
    {
      vlib_cli_output (vm, "     input none\n");
      return 0;
    }

  while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
    {
      if (unformat (line_input, "set"))
        is_set = 1;
      else if (unformat (line_input, "t1 %u", &cfg.t1))
        ;
      else if (unformat (line_input, "t2 %u", &cfg.t2))
        ;
      else if (unformat (line_input, "t3 %u", &cfg.t3))
        ;
      else if (unformat (line_input, "show"))
        is_shown = 1;
      else
        {
          vlib_cli_output (vm, "     input error\n");
          unformat_free (line_input);
          return 0;
        }
    }
  unformat_free (line_input);

  if (is_set)
    {
      if (cfg.t3 >= cfg.t1)
        {
          vlib_cli_output (vm, "     error occur, t3 should less than t1\n");
          return 0;
        }

      if (cfg.t2 >= cfg.t3)
        {
          vlib_cli_output (vm, "     error occur, t2 should less than t3\n");
          return 0;
        }
      g_upf_per_qos_timer_config = cfg;
    }

  if (is_shown)
    {
      vlib_cli_output (vm, "  t1: %3us  #measurement timer\n"
                           "  t2: %3us  #wait for DL packet, timeout to send dummy-GTP\n"
                           "  t3: %3us  #wait for UL packet with QMP, timeout to send measurement failure\n",
      g_upf_per_qos_timer_config.t1, g_upf_per_qos_timer_config.t2, g_upf_per_qos_timer_config.t3);
    }
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_per_qos_monitor_detect_timer_command, static) = {
    .path = "upf per qos monitor detect timer",
    .short_help = "upf per qos monitor detect timer [set t1<value> t2<value> t3<value>] | show]",
    .function = upf_per_qos_monitor_detect_timer_command_fn,
};
/* *INDENT-ON* */

static clib_error_t *
iupf_pfcp_snssai_check_switch_command_fn(vlib_main_t *vm, unformat_input_t *input, vlib_cli_command_t *cmd)
{
  while (unformat_check_input (input) != UNFORMAT_END_OF_INPUT)
  {
    if (unformat (input, "on"))
      g_upf_pfcp_snssai_check_switch = SWITCH_ON;
    else if (unformat (input, "off"))
      g_upf_pfcp_snssai_check_switch = SWITCH_OFF;
    else if (unformat (input, "show"))
      {
        vlib_cli_output (vm, "upf pfcp snssai check switch [%s]\n", g_upf_pfcp_snssai_check_switch == 1 ? "on" : "off");
        return 0;
      }
    else
      return 0;
  }
  return 0;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (iupf_pfcp_snssai_check_switch_command, static) = {
    .path = "upf pfcp snssai check switch",
    .short_help = "upf pfcp snssai check switch [on | off | show]",
    .function = iupf_pfcp_snssai_check_switch_command_fn,
};

/*
 * fd.io coding-style-patch-verification: ON
 *
 * Local Variables:
 * eval: (c-set-style "gnu")
 * End:
 */
