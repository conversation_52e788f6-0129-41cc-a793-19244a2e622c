import unittest
from framework import VppTestCase

class TestN4PersistCli(VppTestCase):
    def test_show_and_persist_window(self):
        # Ensure defaults and enable
        self.vapi.cli("set upf n4-persist enable")
        self.vapi.cli("set upf n4-persist time 10")
        self.vapi.cli("set upf n4-persist max-missed-heartbeats 3")

        out = self.vapi.cli("show upf n4-persist")
        self.assertIn("Status: enabled", out)
        self.assertIn("Persistence time: 10 seconds", out)

        # Simulate link anomaly and check remaining window is shown
        self.vapi.cli("test upf n4-persist link anomaly persist_time 5")
        out = self.vapi.cli("show upf n4-persist")
        self.assertIn("Current link status: anomaly", out)
        self.assertIn("Session persist remaining time", out)

        # Simulate link normal and ensure recovered
        self.vapi.cli("test upf n4-persist link normal")
        out = self.vapi.cli("show upf n4-persist")
        self.assertIn("Current link status: normal", out)
        self.assertNotIn("Session persist remaining time", out)

