


/*
 * Copyright (c) 2016 Qosmos and/or its affiliates.
 * Copyright (c) 2018 Travelping GmbH
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at:
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <vppinfra/dlist.h>
#include <vppinfra/types.h>
#include <vppinfra/vec.h>
#include <vnet/ip/ip4.h>
#include <vnet/ip/ip6.h>
#include <vnet/ip/ip4_packet.h>
#include <vnet/udp/udp_packet.h>
#include <vnet/interface_output.h>

#include <netinet/ip.h>
#include <netinet/tcp.h>
#include <netinet/ip_icmp.h>

#include <arpa/inet.h>
#include <netinet/ip6.h>
#include <igmp/igmp_pkt.h>

#include "pdr_detect.h"
#include "upf.h"
#include "upf_pfcp_api.h"

/*add for dns sniffer begin by lixiao*/
#include "dns/dns_packet.h"
//#include <rte_hash.h>
#include <rte_cycles.h>
//#include <generic/rte_cycles.h>
int rte_cycles_vmware_tsc_map = 0;
/*add for dns sniffer end by lixiao*/

/*begin add by huqingyang 2021.4.30 for dns Sniffer  */
#include <sys/time.h>
/*end add by huqingyang 2021.4.30 for dns Sniffe */
#include "upf_5glan.h"

extern u8 g_urr_tcp_hand_switch;
extern u8 g_upf_qfi_check_switch;

void
upf_buffer_hex_print (void *buffer, int length)
{
  uint8_t *ptr = (uint8_t *)buffer;
  int offset = 0;

  while (offset < length)
    {
      int i;
      for (i = 0; i < 32; i++)
        {
          if ((i & 3) == 0)
            printf (" ");
          if (offset + i < length)
            printf ("%02x", 0xff & ptr[offset + i]);
          else
            printf ("  ");
        }
      printf ("\n");
      offset += 32;
    }
}

static inline int
upf_filter_compare (void *t, void *r)
{
#if 1
  upf_trace ("r->sipaddr=%u, t->sipaddr=%u \n", ((filter_pdr_bdt *)r)->sipaddr,
             ((filter_tuple_t *)t)->sipaddr);
  upf_trace ("r->smask=%u\n", ((filter_pdr_bdt *)r)->smask);
  upf_trace ("r->sport_low=%d, t->sport=%d \n",
             ((filter_pdr_bdt *)r)->sport_low, ((filter_tuple_t *)t)->sport);
  upf_trace ("r->sport_high=%d, t->sport=%d \n",
             ((filter_pdr_bdt *)r)->sport_high, ((filter_tuple_t *)t)->sport);
  upf_trace ("r->dipaddr=%u, t->dipaddr=%u \n", ((filter_pdr_bdt *)r)->dipaddr,
             ((filter_tuple_t *)t)->dipaddr);
  upf_trace ("r->dmask=%u\n", ((filter_pdr_bdt *)r)->dmask);
  upf_trace ("r->dport_low=%d, t->dport=%d \n",
             ((filter_pdr_bdt *)r)->dport_low, ((filter_tuple_t *)t)->dport);
  upf_trace ("r->dport_high=%d, t->dport=%d \n",
             ((filter_pdr_bdt *)r)->dport_high, ((filter_tuple_t *)t)->dport);
  upf_trace ("r->protocol=%d, t->protocol=%d \n",
             ((filter_pdr_bdt *)r)->protocol, ((filter_tuple_t *)t)->proto);
  upf_trace ("r->src_int=%d, t->source_if=%d \n",
             ((filter_pdr_bdt *)r)->src_int, ((filter_tuple_t *)t)->source_if);
  upf_trace ("r->qfi=%d, t->qfi=%d \n", ((filter_pdr_bdt *)r)->qfi,
             ((filter_tuple_t *)t)->qfi);
  // upf_trace ("r->tos=%d, t->tos=%d, r->tos_mask=%d \n",
  //         ((filter_pdr_bdt *)r)->tos, ((filter_tuple_t *)t)->tos,
  //        ((filter_pdr_bdt *)r)->tos_mask);

#endif

  return (
      (0 == ((filter_pdr_bdt *)r)->sipaddr ||
       (((filter_pdr_bdt *)r)->sipaddr & ((filter_pdr_bdt *)r)->smask) ==
           (((filter_tuple_t *)t)->sipaddr & ((filter_pdr_bdt *)r)->smask)) &&
      ((filter_pdr_bdt *)r)->sport_low <= ((filter_tuple_t *)t)->sport &&
      ((filter_pdr_bdt *)r)->sport_high >= ((filter_tuple_t *)t)->sport &&
      (0 == ((filter_pdr_bdt *)r)->dipaddr ||
       (((filter_pdr_bdt *)r)->dipaddr & ((filter_pdr_bdt *)r)->dmask) ==
           (((filter_tuple_t *)t)->dipaddr & ((filter_pdr_bdt *)r)->dmask)) &&
      ((filter_pdr_bdt *)r)->dport_low <= ((filter_tuple_t *)t)->dport &&
      ((filter_pdr_bdt *)r)->dport_high >= ((filter_tuple_t *)t)->dport &&
      (((filter_pdr_bdt *)r)->protocol == 0 ||
       ((filter_pdr_bdt *)r)->protocol == ((filter_tuple_t *)t)->proto) &&
      (((filter_pdr_bdt *)r)->src_int == ((filter_tuple_t *)t)->source_if) &&
      (((filter_pdr_bdt *)r)->qfi == 0 ||
       ((filter_pdr_bdt *)r)->qfi == ((filter_tuple_t *)t)->qfi));
}

static inline int
upf_filter6_compare_addr (filter_pdr_bdt6 *r, filter6_tuple_t *t)
{
  return ((r->sipaddr[0] & r->smask[0]) == (t->sipaddr[0] & r->smask[0]) &&
          (r->sipaddr[1] & r->smask[1]) == (t->sipaddr[1] & r->smask[1]) &&
          (r->sipaddr[2] & r->smask[2]) == (t->sipaddr[2] & r->smask[2]) &&
          (r->sipaddr[3] & r->smask[3]) == (t->sipaddr[3] & r->smask[3]) &&
          (r->dipaddr[0] & r->dmask[0]) == (t->dipaddr[0] & r->dmask[0]) &&
          (r->dipaddr[1] & r->dmask[1]) == (t->dipaddr[1] & r->dmask[1]) &&
          (r->dipaddr[2] & r->dmask[2]) == (t->dipaddr[2] & r->dmask[2]) &&
          (r->dipaddr[3] & r->dmask[3]) == (t->dipaddr[3] & r->dmask[3]));
}

static inline int
upf_filter6_compare (void *t, void *r)
{
#if 1

  upf_trace ("r->sipaddr6=%U, t->sipaddr6=%U \n", format_ip6_address,
             ((filter_pdr_bdt6 *)r)->sipaddr, format_ip6_address,
             ((filter6_tuple_t *)t)->sipaddr);
  upf_trace ("r->dipaddr6=%U, t->dipaddr6=%U \n", format_ip6_address,
             ((filter_pdr_bdt6 *)r)->dipaddr, format_ip6_address,
             ((filter6_tuple_t *)t)->dipaddr);

  upf_trace ("r->smask6=%U\n", format_ip6_address,
             ((filter_pdr_bdt6 *)r)->smask);
  upf_trace ("r->dmask6=%U\n", format_ip6_address,
             ((filter_pdr_bdt6 *)r)->dmask);

  upf_trace ("r->sport_low=%d, t->sport=%d \n",
             ((filter_pdr_bdt6 *)r)->sport_low, ((filter6_tuple_t *)t)->sport);
  upf_trace ("r->sport_high=%d, t->sport=%d \n",
             ((filter_pdr_bdt6 *)r)->sport_high,
             ((filter6_tuple_t *)t)->sport);
  upf_trace ("r->protocol=%d, t->protocol=%d \n",
             ((filter_pdr_bdt6 *)r)->protocol, ((filter6_tuple_t *)t)->proto);
  upf_trace ("r->src_int=%d, t->source_if=%d \n",
             ((filter_pdr_bdt6 *)r)->src_int,
             ((filter6_tuple_t *)t)->source_if);

  upf_trace ("r->qfi=%d, t->qfi=%d \n", ((filter_pdr_bdt6 *)r)->qfi,
             ((filter6_tuple_t *)t)->qfi);

#endif

  return (
      upf_filter6_compare_addr ((filter_pdr_bdt6 *)r, (filter6_tuple_t *)t) &&
      ((filter_pdr_bdt6 *)r)->sport_low <= ((filter6_tuple_t *)t)->sport &&
      ((filter_pdr_bdt6 *)r)->sport_high >= ((filter6_tuple_t *)t)->sport &&
      ((filter_pdr_bdt6 *)r)->dport_low <= ((filter6_tuple_t *)t)->dport &&
      ((filter_pdr_bdt6 *)r)->dport_high >= ((filter6_tuple_t *)t)->dport &&
      (((filter_pdr_bdt6 *)r)->protocol == 0 ||
       ((filter_pdr_bdt6 *)r)->protocol == ((filter6_tuple_t *)t)->proto) &&
      (((filter_pdr_bdt6 *)r)->src_int == ((filter6_tuple_t *)t)->source_if) &&
      (((filter_pdr_bdt6 *)r)->qfi == 0 ||
       ((filter_pdr_bdt6 *)r)->qfi == ((filter6_tuple_t *)t)->qfi));
}

/*The id is combined by pdr_id|precedence*/
int
upf_hs_handler (unsigned int id, unsigned long long from, unsigned long long to,
            unsigned int flags, void *ctx)
{
  upf_debug ("match offset %llu, ctx=%u, precedence=%d, pdr_idx=%d\n", to,
             *(u32 *)ctx, id & 0x0000ffff, id >> 16);
  *(u32 *)ctx =
      (*(u32 *)ctx & 0x0000ffff) < (id & 0x0000ffff) ? *(u32 *)ctx : id;
  return 0;
}

u32
upf_search_pdr_ip4 (struct upf_acl_ctx *acl, vlib_buffer_t *b0, u32 is_eth)
{
  u32 result;
  ethernet_header_t *eth;
  ethernet_arp_header_t *arph = NULL;
  tcp_header_t *tcph;
  udp_header_t *udph;
  ip4_header_t *ip4 = NULL;
  struct acl_ipv4_key key = {0};
  const u8 *data[1];

  if (acl == NULL)
    {
      upf_debug ("sess acl is null.\n");
      UPF_STATISTICS_ADD(NULL_ACL4);
      return ~0;
    }

  if (is_eth)
    {
      eth = vlib_buffer_get_current (b0) +
            upf_buffer_opaque (b0)->upf.data_offset -
            vnet_buffer (b0)->l2.l2_len;
      memcpy (&key.eth_src, eth->src_address, 6);
      memcpy (&key.eth_dst, eth->dst_address, 6);
      key.ether_type = *(u16 *)(vlib_buffer_get_current (b0) - 2);
      if (clib_net_to_host_u16 (eth->type) == ETHERNET_TYPE_ARP)
        {
          arph = (ethernet_arp_header_t *)(eth + 1);
        }
      else
        {
          ip4 = (ip4_header_t *)(eth + 1);
        }
    }
  else
    {
      ip4 = vlib_buffer_get_current (b0) +
            upf_buffer_opaque (b0)->upf.data_offset;
    }

  if (arph)
    {
      key.ip_dst = arph->ip4_over_ethernet[1].ip4;
      key.source_interface = upf_buffer_opaque (b0)->upf.src_intf;
    }
  else
    {
      if (ip4->protocol == IP_PROTOCOL_TCP)
        {
          tcph = vlib_buffer_get_current (b0) +
                 upf_buffer_opaque (b0)->upf.data_offset +
                 sizeof (ip4_header_t);
          key.port_src = tcph->src_port;
          key.port_dst = tcph->dst_port;
        }
      else if (ip4->protocol == IP_PROTOCOL_UDP)
        {
          udph = vlib_buffer_get_current (b0) +
                 upf_buffer_opaque (b0)->upf.data_offset +
                 sizeof (ip4_header_t);
          key.port_src = udph->src_port;
          key.port_dst = udph->dst_port;
        }
      else
        {
          key.port_src = 0;
          key.port_dst = 0;
        }

      key.proto = ip4->protocol;
      key.ip_src = ip4->src_address;
      key.ip_dst = ip4->dst_address;

      key.qfi = upf_buffer_opaque (b0)->upf.gtp_flags.qfi;
      key.source_interface = upf_buffer_opaque (b0)->upf.src_intf;
    }

  upf_debug ("%U ==> %U 0x%04x %u %U:%u ==> %U:%u src int %u qfi %u",
             format_mac_address, &key.eth_src, format_mac_address,
             &key.eth_dst, clib_net_to_host_u16 (key.ether_type), key.proto,
             format_ip4_address, &key.ip_src,
             clib_net_to_host_u16 (key.port_src), format_ip4_address,
             &key.ip_dst, clib_net_to_host_u16 (key.port_dst),
             key.source_interface, key.qfi);

  data[0] = (u8 *)&key;
  upf_acl_classify (acl, data, &result, 1, ACL_CATEGORY_MASK);
  if (result == 0)
  {
    UPF_STATISTICS_ADD(ACL_CHECK_FAILED);
    return ~0;
  }
  else
    return (result - ACL_OFFSET);
}

u32
upf_search_pdr_ip6 (struct upf_acl_ctx *acl, vlib_buffer_t *b0, u32 is_eth)
{
  u32 result;
  ethernet_header_t *eth;
  tcp_header_t *tcph;
  udp_header_t *udph;
  ip6_header_t *ip6;
  struct acl_ipv6_key key = {0};
  const u8 *data[1];

  if (acl == NULL)
    {
      upf_debug ("sess acl is null.\n");
      UPF_STATISTICS_ADD(NULL_ACL6);
      return ~0;
    }

  if (is_eth)
    {
      eth = vlib_buffer_get_current (b0) +
            upf_buffer_opaque (b0)->upf.data_offset;
      memcpy (&key.eth_src, eth->src_address, 6);
      memcpy (&key.eth_dst, eth->dst_address, 6);
      key.ether_type = eth->type;
      ip6 = (ip6_header_t *)(eth + 1);
    }
  else
    {
      ip6 = vlib_buffer_get_current (b0) +
            upf_buffer_opaque (b0)->upf.data_offset;
    }

  if (ip6->protocol == IP_PROTOCOL_TCP)
    {
      tcph = vlib_buffer_get_current (b0) +
             upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip6_header_t);
      key.port_src = tcph->src_port;
      key.port_dst = tcph->dst_port;
    }
  else if (ip6->protocol == IP_PROTOCOL_UDP)
    {
      udph = vlib_buffer_get_current (b0) +
             upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip6_header_t);
      key.port_src = udph->src_port;
      key.port_dst = udph->dst_port;
    }
  else
    {
      key.port_src = 0;
      key.port_dst = 0;
    }

  key.proto = ip6->protocol;
  ip6_address_copy ((ip6_address_t *)&key.ip_src[0], &ip6->src_address);
  ip6_address_copy ((ip6_address_t *)&key.ip_dst[0], &ip6->dst_address);

  key.qfi = upf_buffer_opaque (b0)->upf.gtp_flags.qfi;
  key.source_interface = upf_buffer_opaque (b0)->upf.src_intf;

  upf_debug ("%U ==> %U 0x%04x %u %U:%u ==> %U:%u src int %u qfi %u",
             format_mac_address, &key.eth_src, format_mac_address,
             &key.eth_dst, clib_net_to_host_u16 (key.ether_type), key.proto,
             format_ip6_address, &key.ip_src[0], key.port_src,
             format_ip6_address, &key.ip_dst[0], key.port_dst,
             key.source_interface, key.qfi);

  data[0] = (u8 *)&key;
  upf_acl_classify (acl, data, &result, 1, ACL_CATEGORY_MASK);
  if (result == 0)
  {
    UPF_STATISTICS_ADD(ACL_CHECK_FAILED);
    return ~0;
  }
  else
    return (result - ACL_OFFSET);
}

u32
upf_bdt_search_pdr (upf_bdtree_t *bdt, vlib_buffer_t *b0)
{
  u16 sport = 0, dport = 0;
  u8 source_if = ~0;
  filter_pdr_bdt *r = NULL;
  tcp_header_t *tcph;
  udp_header_t *udph;
  ip4_header_t *ip4;

  filter_tuple_t t;
  if (bdt == NULL)
    {
      upf_debug ("sess bdt is null.\n");
      return ~0;
    }

  /* udp leaves current_data pointing at the gtpu header */
  // gtpu0 = vlib_buffer_get_current (b0);

  source_if = upf_buffer_opaque (b0)->upf.src_intf;

  ip4 = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset;
  if (ip4->protocol == IP_PROTOCOL_TCP)
    {
      tcph = vlib_buffer_get_current (b0) +
             upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip4_header_t);
      sport = tcph->src_port;
      dport = tcph->dst_port;
    }
  else if (ip4->protocol == IP_PROTOCOL_UDP)
    {
      udph = vlib_buffer_get_current (b0) +
             upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip4_header_t);
      sport = udph->src_port;
      dport = udph->dst_port;
    }

  t.proto = ip4->protocol;

  t.sipaddr = ip4->src_address.as_u32;
  t.sport = clib_net_to_host_u16 (sport);

  t.dipaddr = ip4->dst_address.as_u32;
  t.dport = clib_net_to_host_u16 (dport);

  // t.tos = ip4->tos;
  t.qfi = upf_buffer_opaque (b0)->upf.gtp_flags.qfi;
  t.source_if = source_if;

  // os_dp_log_info("sdf_detect: "OS_NIP_FMT" 0x%02x %d %d %d %d\n",
  //  OS_NIP(&t.ipaddr),t.proto,t.dport,t.tos,t.dir);

  r = (filter_pdr_bdt *)search_bdt (bdt, &t, upf_filter_compare);
  if (NULL != r)
    {
      upf_debug ("pdr index %d.\n", r->offset);
      return r->offset;
    }

  return ~0;
}
u32
upf_bdt6_search_pdr (upf_bdtree_t *bdt, vlib_buffer_t *b0)
{
  u32 i;
  u16 sport = 0, dport = 0;

  filter_pdr_bdt6 *r6 = NULL;
  tcp_header_t *tcph;
  udp_header_t *udph;
  ip6_header_t *ip6;
  u8 source_if = ~0;

  filter6_tuple_t t6;

  if (bdt == NULL)
    {
      upf_debug ("sess bdt6 is null.\n");
      return ~0;
    }

  /* udp leaves current_data pointing at the gtpu header */
  // gtpu0 = vlib_buffer_get_current (b0);
  source_if = upf_buffer_opaque (b0)->upf.src_intf;

  ip6 = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset;
  if (ip6->protocol == IP_PROTOCOL_TCP)
    {
      tcph = vlib_buffer_get_current (b0) +
             upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip6_header_t);
      sport = tcph->src_port;
      dport = tcph->dst_port;
    }
  else if (ip6->protocol == IP_PROTOCOL_UDP)
    {
      udph = vlib_buffer_get_current (b0) +
             upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip6_header_t);
      sport = udph->src_port;
      dport = udph->dst_port;
    }

  t6.source_if = source_if;
  t6.proto = ip6->protocol;

  for (i = 0; i < 4; i++)
    {
      t6.sipaddr[i] = ip6->src_address.as_u32[i];
    }
  t6.sport = clib_net_to_host_u16 (sport);

  for (i = 0; i < 4; i++)
    {
      t6.dipaddr[i] = ip6->dst_address.as_u32[i];
    }
  t6.dport = clib_net_to_host_u16 (dport);

  t6.qfi = upf_buffer_opaque (b0)->upf.gtp_flags.qfi;

  r6 = (filter_pdr_bdt6 *)search_bdt_x (bdt, &t6, upf_filter6_compare);
  if (NULL != r6)
    {
      upf_debug ("v6 pdr index %d.\n", r6->offset);
      return r6->offset;
    }

  return ~0;
}
static inline int
upf_acl_compare (acl_rule_t *acl, pkt_5tuple *pkt_info, u32 is_to_vn_group)
{
  u16 ue_field = 0;
  ip46_address_t *acl_ue, *acl_ue_mask;
  ip46_address_t *acl_dn, *acl_dn_mask;

  if ((acl->direction == ACL_OUT) && !is_to_vn_group)
    ue_field = UPF_ACL_FIELD_DST;
  else
    ue_field = UPF_ACL_FIELD_SRC;

  acl_ue = &acl->address[ue_field].address;
  acl_ue_mask = &acl->address[ue_field].mask_address;
  acl_dn = &acl->address[ue_field ^ 1].address;
  acl_dn_mask = &acl->address[ue_field ^ 1].mask_address;
  upf_trace ("filter UE: %U/%d, %d-%d, DN: %U/%d, %d-%d, PROTO: %d\n",
             format_ip46_address, &acl->address[ue_field].address,
             IP46_TYPE_ANY, acl->address[ue_field].mask,
             acl->port[ue_field].min, acl->port[ue_field].max,
             format_ip46_address, &acl->address[ue_field ^ 1].address,
             IP46_TYPE_ANY, acl->address[ue_field ^ 1].mask,
             acl->port[ue_field ^ 1].min, acl->port[ue_field ^ 1].max,
             acl->proto);

  if (ip46_address_is_ip4 (&pkt_info->ue_addr))
    {
      return (((acl->proto == (u8)~0) || acl->proto == pkt_info->proto) &&
              (ip46_address_is_zero (&acl->address[ue_field].address) ||
               ((acl_ue->ip4.as_u32 & acl_ue_mask->ip4.as_u32) ==
                (pkt_info->ue_addr.ip4.as_u32 & acl_ue_mask->ip4.as_u32))) &&
              (ip46_address_is_zero (&acl->address[ue_field ^ 1].address) ||
               ((acl_dn->ip4.as_u32 & acl_dn_mask->ip4.as_u32) ==
                (pkt_info->dn_addr.ip4.as_u32 & acl_dn_mask->ip4.as_u32))) &&
              ((acl->port[ue_field].min <= pkt_info->ue_port) &&
               (acl->port[ue_field].max >= pkt_info->ue_port)) &&
              ((acl->port[ue_field ^ 1].min <= pkt_info->dn_port) &&
               (acl->port[ue_field ^ 1].max >= pkt_info->dn_port)));
    }
  else
    {
      return (((acl->proto == (u8)~0) || acl->proto == pkt_info->proto) &&

              (ip46_address_is_zero (&acl->address[ue_field].address) ||
               ip6_address_is_equal_masked (
                   &acl_ue->ip6, &pkt_info->ue_addr.ip6, &acl_ue_mask->ip6)) &&
              (ip46_address_is_zero (&acl->address[ue_field ^ 1].address) ||
               ip6_address_is_equal_masked (
                   &acl_dn->ip6, &pkt_info->dn_addr.ip6, &acl_dn_mask->ip6)) &&
              ((acl->port[ue_field].min <= pkt_info->ue_port) &&
               (acl->port[ue_field].max >= pkt_info->ue_port)) &&
              ((acl->port[ue_field ^ 1].min <= pkt_info->dn_port) &&
               (acl->port[ue_field ^ 1].max >= pkt_info->dn_port)));
    }
  return 0;
}

void upf_get_ip_5tuple_info(u8 *data, pkt_5tuple *pkt_info, u8 src_intf)
{
    udp_header_t *tcph = NULL;
    ip6_header_t *ip6 = NULL;
    ip4_header_t *ip4 = NULL;

    if (!pkt_info || !data)
        return;

    u16 is_v6 = is_v6_packet (data);
    if (is_v6)
    {
        ip6 = (ip6_header_t *)data;
        if (ip6->protocol == IP_PROTOCOL_UDP || ip6->protocol == IP_PROTOCOL_TCP)
        {
            tcph = (udp_header_t *)(data + sizeof (ip6_header_t));
        }

        /* SRC_INTF_ACCESS(0) means data is uplink, that from ue to dn */
        if (src_intf == SRC_INTF_ACCESS) 
        {
            ip46_address_set_ip6 (&pkt_info->ue_addr, &ip6->src_address);
            ip46_address_set_ip6 (&pkt_info->dn_addr, &ip6->dst_address);
        }
        else
        {
            ip46_address_set_ip6 (&pkt_info->ue_addr, &ip6->dst_address);
            ip46_address_set_ip6 (&pkt_info->dn_addr, &ip6->src_address);
        }
        pkt_info->proto = ip6->protocol;
    }
    else
    {
        ip4 = (ip4_header_t *)data;
        if (ip4->protocol == IP_PROTOCOL_UDP || ip4->protocol == IP_PROTOCOL_TCP)
        {
            tcph = (udp_header_t *)(data + sizeof (ip4_header_t));
        }

        /* SRC_INTF_ACCESS(0) means data is uplink, that from ue to dn */
        if (src_intf == SRC_INTF_ACCESS) 
        {
            ip46_address_set_ip4 (&pkt_info->ue_addr, &ip4->src_address);
            ip46_address_set_ip4 (&pkt_info->dn_addr, &ip4->dst_address);
        }
        else
        {
            ip46_address_set_ip4 (&pkt_info->ue_addr, &ip4->dst_address);
            ip46_address_set_ip4 (&pkt_info->dn_addr, &ip4->src_address);
        }
        pkt_info->proto = ip4->protocol;
    }
    
    if (tcph)
    {
        if (!src_intf)
        {
            pkt_info->ue_port = clib_net_to_host_u16 (tcph->src_port);
            pkt_info->dn_port = clib_net_to_host_u16 (tcph->dst_port);
        }
        else
        {
            pkt_info->ue_port = clib_net_to_host_u16 (tcph->dst_port);
            pkt_info->dn_port = clib_net_to_host_u16 (tcph->src_port);
        }
    }
    upf_trace ("PKT UE: %U, : %d, DN: %U, : %d, PROTO: %d\n",
               format_ip46_address, &pkt_info->ue_addr, IP46_TYPE_ANY, pkt_info->ue_port,
               format_ip46_address, &pkt_info->dn_addr, IP46_TYPE_ANY, pkt_info->dn_port, pkt_info->proto);
}

void upf_get_ip_pkt_info(pkt_5tuple *pkt_info, vlib_buffer_t *b0)
{
    if (!pkt_info || !b0)
        return;

    clib_memset (pkt_info, 0, sizeof (pkt_5tuple));

    u8 *data = (u8 *)(vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset);
    upf_get_ip_5tuple_info(data, pkt_info, upf_buffer_opaque (b0)->upf.src_intf);
}

void upf_get_eth_pkt_info(eth_pkt_tuple *eth_info, vlib_buffer_t *b0)
{
    if (!eth_info || !b0)
        return;

    clib_memset (eth_info, 0, sizeof (eth_pkt_tuple));
    ethernet_header_t *eth = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset;

    if (upf_buffer_opaque (b0)->upf.src_intf == SRC_INTF_ACCESS)
    {
        mac_address_from_bytes(&eth_info->ue_eth, eth->src_address);
        mac_address_from_bytes(&eth_info->dn_eth, eth->dst_address);
    }
    else
    {
        mac_address_from_bytes(&eth_info->ue_eth, eth->dst_address);
        mac_address_from_bytes(&eth_info->dn_eth, eth->src_address);
    }

    upf_get_eth_info((u8 *)eth, &eth_info->c_vlan, &eth_info->s_vlan, &eth_info->eth_type);
    upf_trace ("ETH UE: %U : DN: %U c-tag:0x%x, s-tag:0x%x eth_type:0x%x\n",
               format_mac_address, &eth_info->ue_eth, format_mac_address, &eth_info->dn_eth,
               eth_info->c_vlan, eth_info->s_vlan, eth_info->eth_type);

    if ((eth_info->eth_type == ETHERNET_TYPE_IP4) || (eth_info->eth_type == ETHERNET_TYPE_IP6))
        upf_get_ip_5tuple_info(((u8 *)eth) + vnet_buffer (b0)->l2.l2_len, &eth_info->ip_5tuple,
                               upf_buffer_opaque (b0)->upf.src_intf);
    else
        clib_memset(&eth_info->ip_5tuple, 0, sizeof(pkt_5tuple));
}

u32 is_ip_multicast_addressing_match(u32 is_v6, const ip46_address_t *addr, pfcp_ip_multicast_address_t *ip_ma)
{
    if ((!addr) || (!ip_ma))
        return NOT_MATCH;

    if (ip_ma->flags & IE_IP_MULTICAST_ADDRESS_A)
        return MATCH;

    if (is_v6 && (ip_ma->flags & IE_IP_MULTICAST_ADDRESS_V6))
    {
        if (PREDICT_FALSE(ip_ma->flags & IE_IP_MULTICAST_ADDRESS_R))
        {
            if ((clib_net_to_host_u64(addr->ip6.as_u64[0]) > clib_net_to_host_u64(ip_ma->ip6_start.as_u64[0]) || 
                (clib_net_to_host_u64(addr->ip6.as_u64[0]) == clib_net_to_host_u64(ip_ma->ip6_start.as_u64[0]) && 
                clib_net_to_host_u64(addr->ip6.as_u64[1]) >= clib_net_to_host_u64(ip_ma->ip6_start.as_u64[1]))) && 
                (clib_net_to_host_u64(addr->ip6.as_u64[0]) < clib_net_to_host_u64(ip_ma->ip6_end.as_u64[0]) || 
                ((clib_net_to_host_u64(addr->ip6.as_u64[0]) == clib_net_to_host_u64(ip_ma->ip6_end.as_u64[0])) && 
                clib_net_to_host_u64(addr->ip6.as_u64[1]) <= clib_net_to_host_u64(ip_ma->ip6_end.as_u64[1]))))
            {
                return MATCH;
            }
            return NOT_MATCH;
        }
        else
        {
            return ip6_address_is_equal(&addr->ip6, &ip_ma->ip6_start);
        }
    }

    if (!is_v6 && (ip_ma->flags & IE_IP_MULTICAST_ADDRESS_V4))
    {
        if (PREDICT_FALSE(ip_ma->flags & IE_IP_MULTICAST_ADDRESS_R))
        {
            if ((clib_net_to_host_u32(addr->ip4.as_u32) >= clib_net_to_host_u32(ip_ma->ip4_start.as_u32)) && 
                (clib_net_to_host_u32(addr->ip4.as_u32) <= clib_net_to_host_u32(ip_ma->ip4_end.as_u32)))
            {
                return MATCH;
            }
            return NOT_MATCH;
        }
        else
        {
            return ip4_address_is_equal(&addr->ip4, &ip_ma->ip4_start);
        }
    }

    upf_debug ("error match, is_v6:%u ip_ma->flags:0x%x[bit0-v6 bit1-v4 bit2-range bit3-any]", is_v6, ip_ma->flags);
    return NOT_MATCH;
}

u32 is_source_ip_address_match(u32 is_v6, const ip46_address_t *addr, pfcp_source_ip_address_t *src)
{
    if ((!addr) || (!src))
        return NOT_MATCH;

    if (is_v6)
    {
        if (src->flags & IE_SOURCE_IP_ADDRESS_MPL)
        {
            return ip6_gateway_is_equal(&addr->ip6, &src->ip6, src->prefix);
        }
        else
        {
            return ip6_address_is_equal(&addr->ip6, &src->ip6);
        }
    }
    else
    {
        if (src->flags & IE_SOURCE_IP_ADDRESS_MPL)
        {
            return ip4_gateway_is_equal(&addr->ip4, &src->ip4, src->prefix);
        }
        else
        {
            return ip4_address_is_equal(&addr->ip4, &src->ip4);
        }
    }
}

void upf_ip_multicast_address_info_report(vlib_buffer_t *b0, upf_pdr_t *pdr, u32 pdr_idx)
{
    int i = 0, j = 0;
    igmp_membership_report_v3_t *igmp_header;
    upf_igmp_membership_report_v2_t *igmp2_header;
    upf_main_t *um = &g_upf_main;
    upf_report_ip_multicast_information_t upf_report_mip;
    pfcp_join_ip_multicast_information_t *join_info_tmp;
    pfcp_leave_ip_multicast_information_t *leave_info_tmp;
    pfcp_source_ip_address_t *mip_info_sip;
    pfcp_ip_multicast_addressing_info_t *ip_ma;
    ip46_address_t addr;

    memset(&upf_report_mip, 0, sizeof(upf_report_mip));

    u16 is_v4 = is_v4_packet ((u8 *)(vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset));

    if (is_v4)
    {
        //ip header >> options(4byte)
        igmp_header = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip4_header_t) + 4;
        upf_trace ("Is igmp packet, send to pfcp thread, groups_num: %d, mip: %U, type:0x%x\n", 
            igmp_header->n_groups, format_ip4_address, &igmp_header->groups[0].group_address, igmp_header->header.type);
        if (igmp_header->header.type == IGMP_TYPE_membership_report_v3)
        {
            for (i = 0; i < clib_net_to_host_u16(igmp_header->n_groups); i++)
            {
                if (igmp_header->groups[i].type == IGMP_MEMBERSHIP_GROUP_mode_is_include || 
                    igmp_header->groups[i].type == IGMP_MEMBERSHIP_GROUP_change_to_include)
                {
                    vec_foreach(ip_ma, pdr->ip_multicast_addressing_info)
                    {
                        addr.ip4 = igmp_header->groups[i].group_address;
                        if (is_ip_multicast_addressing_match(!is_v4, &addr, &ip_ma->ip_multicast_address))
                        {
                            upf_trace ("# multicast address match, %U\n", format_ip46_address, &ip_ma->ip_multicast_address, IP46_TYPE_ANY);
                            vec_alloc (upf_report_mip.join_info, 1);
                            join_info_tmp = vec_end (upf_report_mip.join_info);
                            _vec_len (upf_report_mip.join_info)++;
                            SET_BIT (join_info_tmp->grp.fields, IP_MULTICAST_ADDRESSING_INFO_IP_MULTICAST_ADDRESS);
                            join_info_tmp->mip.flags |= IE_IP_MULTICAST_ADDRESS_V4;
                            join_info_tmp->mip.ip4_start = igmp_header->groups[i].group_address;
                            if (!vec_len(ip_ma->source_ip_address))
                            {
                                for (j = 0; j < igmp_header->groups[i].n_src_addresses; j++)
                                {
                                    SET_BIT (join_info_tmp->grp.fields, IP_MULTICAST_ADDRESSING_INFO_SOURCE_IP_ADDRESS);
                                    vec_alloc (join_info_tmp->sip, 1);
                                    mip_info_sip = vec_end (join_info_tmp->sip);
                                    _vec_len (join_info_tmp->sip)++;
                                    mip_info_sip->flags |= IE_SOURCE_IP_ADDRESS_V4;
                                    mip_info_sip->ip4 = igmp_header->groups[i].src_addresses[j];
                                    upf_debug("# join sip:%U", format_ip4_address, &mip_info_sip->ip4);
                                }
                            }
                            else
                            {
                                for (j = 0; j < igmp_header->groups[i].n_src_addresses; j++)
                                {
                                    pfcp_source_ip_address_t *src_ip;
                                    vec_foreach(src_ip, ip_ma->source_ip_address)
                                    {
                                        addr.ip4 = igmp_header->groups[i].src_addresses[j];
                                        if (is_source_ip_address_match(!is_v4, &addr, src_ip))
                                        {
                                            SET_BIT (join_info_tmp->grp.fields, IP_MULTICAST_ADDRESSING_INFO_SOURCE_IP_ADDRESS);
                                            vec_alloc (join_info_tmp->sip, 1);
                                            mip_info_sip = vec_end (join_info_tmp->sip);
                                            _vec_len (join_info_tmp->sip)++;
                                            mip_info_sip->flags |= IE_SOURCE_IP_ADDRESS_V4;
                                            mip_info_sip->ip4 = igmp_header->groups[i].src_addresses[j];
                                            upf_debug("# join sip:%U", format_ip4_address, &mip_info_sip->ip4);
                                            break;
                                        }
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
                else if (igmp_header->groups[i].type == IGMP_MEMBERSHIP_GROUP_mode_is_exclude || 
                    igmp_header->groups[i].type == IGMP_MEMBERSHIP_GROUP_change_to_exclude)
                {
                    vec_foreach(ip_ma, pdr->ip_multicast_addressing_info)
                    {
                        addr.ip4 = igmp_header->groups[i].group_address;
                        if (is_ip_multicast_addressing_match(!is_v4, &addr, &ip_ma->ip_multicast_address))
                        {
                            upf_trace ("# multicast address match, %U\n", format_ip46_address, &ip_ma->ip_multicast_address, IP46_TYPE_ANY);
                            vec_alloc (upf_report_mip.leave_info, 1);
                            leave_info_tmp = vec_end (upf_report_mip.leave_info);
                            _vec_len (upf_report_mip.leave_info)++;
                            SET_BIT (leave_info_tmp->grp.fields, IP_MULTICAST_ADDRESSING_INFO_IP_MULTICAST_ADDRESS);
                            leave_info_tmp->mip.flags |= IE_IP_MULTICAST_ADDRESS_V4;
                            leave_info_tmp->mip.ip4_start = igmp_header->groups[i].group_address;
                            if (!vec_len(ip_ma->source_ip_address))
                            {
                                for (j = 0; j < igmp_header->groups[i].n_src_addresses; j++)
                                {
                                    SET_BIT (leave_info_tmp->grp.fields, IP_MULTICAST_ADDRESSING_INFO_SOURCE_IP_ADDRESS);
                                    vec_alloc (leave_info_tmp->sip, 1);
                                    mip_info_sip = vec_end (leave_info_tmp->sip);
                                    _vec_len (leave_info_tmp->sip)++;
                                    mip_info_sip->flags |= IE_SOURCE_IP_ADDRESS_V4;
                                    mip_info_sip->ip4 = igmp_header->groups[i].src_addresses[j];
                                    upf_debug("# join sip:%U", format_ip4_address, &mip_info_sip->ip4);
                                }
                            }
                            else
                            {
                                for (j = 0; j < igmp_header->groups[i].n_src_addresses; j++)
                                {
                                    pfcp_source_ip_address_t *src_ip;
                                    vec_foreach(src_ip, ip_ma->source_ip_address)
                                    {
                                        addr.ip4 = igmp_header->groups[i].src_addresses[j];
                                        if (is_source_ip_address_match(!is_v4, &addr, src_ip))
                                        {
                                            SET_BIT (leave_info_tmp->grp.fields, IP_MULTICAST_ADDRESSING_INFO_SOURCE_IP_ADDRESS);
                                            vec_alloc (leave_info_tmp->sip, 1);
                                            mip_info_sip = vec_end (leave_info_tmp->sip);
                                            _vec_len (leave_info_tmp->sip)++;
                                            mip_info_sip->flags |= IE_SOURCE_IP_ADDRESS_V4;
                                            mip_info_sip->ip4 = igmp_header->groups[i].src_addresses[j];
                                            upf_debug("# join sip:%U", format_ip4_address, &mip_info_sip->ip4);
                                            break;
                                        }
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
                else
                {
                      upf_debug("igmp_header->groups[i].type:%u", igmp_header->groups[i].type);
                }
            }
        }
        else if (igmp_header->header.type == IGMP_TYPE_membership_report_v2)
        {
            igmp2_header = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip4_header_t) + 4;
            vec_foreach(ip_ma, pdr->ip_multicast_addressing_info)
            {
                addr.ip4 = igmp2_header->group_address;
                if (is_ip_multicast_addressing_match(!is_v4, &addr, &ip_ma->ip_multicast_address))
                {
                    upf_trace ("# multicast address match, %U\n", format_ip46_address, &ip_ma->ip_multicast_address, IP46_TYPE_ANY);
                    vec_alloc (upf_report_mip.join_info, 1);
                    join_info_tmp = vec_end (upf_report_mip.join_info);
                    _vec_len (upf_report_mip.join_info)++;
                    SET_BIT (join_info_tmp->grp.fields, IP_MULTICAST_ADDRESSING_INFO_IP_MULTICAST_ADDRESS);
                    join_info_tmp->mip.flags |= IE_IP_MULTICAST_ADDRESS_V4;
                    join_info_tmp->mip.ip4_start = igmp2_header->group_address;
                    break;
                }
            }
        }
        else if (igmp_header->header.type == IGMP_TYPE_leave_group_v2)
        {
            igmp2_header = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip4_header_t) + 4;
            vec_foreach(ip_ma, pdr->ip_multicast_addressing_info)
            {
                addr.ip4 = igmp2_header->group_address;
                if (is_ip_multicast_addressing_match(!is_v4, &addr, &ip_ma->ip_multicast_address))
                {
                    upf_trace ("# multicast address match, %U\n", format_ip46_address, &ip_ma->ip_multicast_address, IP46_TYPE_ANY);
                    vec_alloc (upf_report_mip.leave_info, 1);
                    leave_info_tmp = vec_end (upf_report_mip.leave_info);
                    _vec_len (upf_report_mip.leave_info)++;
                    SET_BIT (leave_info_tmp->grp.fields, IP_MULTICAST_ADDRESSING_INFO_IP_MULTICAST_ADDRESS);
                    leave_info_tmp->mip.flags |= IE_IP_MULTICAST_ADDRESS_V4;
                    leave_info_tmp->mip.ip4_start = igmp2_header->group_address;
                    break;
                }
            }
        }
        else
        {
            upf_debug("Other igmp type! 0x%x", igmp_header->header.type);
        }
    }
    else
    {
        //to do ipv6 multicast
    }

    upf_report_mip.pdr_idx = pdr_idx;
    upf_report_mip.sess_idx = upf_buffer_opaque (b0)->upf.session_index;

    upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
    memset (msg, 0, sizeof (*msg));
    msg->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_report_mip), CLIB_CACHE_LINE_BYTES);
    memset (msg->data, 0, sizeof((upf_report_mip)));
    msg->msg_id = PFCP_RPC_PUBLISH_REPORT_IP_MULTICAST;
    memcpy(msg->data, &(upf_report_mip), sizeof((upf_report_mip)));
    
    for (int i = 0; i < um->num_pfcp_threads; i++)
    {
        upf_trace ("PFCP_RPC_PUBLISH_REPORT_IP_MULTICAST,\n");
        send_sx_msg_to_pfcp_thread(i, msg);
    }
}

void upf_alarm_ue_ul_ueip_check(upf_pdr_t *pdr, ip46_address_t *ue_ip, u32 is_v6)
{
    upf_ip_check_t tmp;

    if (!is_v6)
    {
        memcpy(&tmp.ue_ip, &pdr->pdi.ue_addr.ip4, sizeof(tmp.ue_ip));
        memcpy(&tmp.pkt_src_ip, &ue_ip->ip4, sizeof(tmp.pkt_src_ip));
        if (upf_alarm_search(UPF_ALARM_UL_UEIP_CHECK_FAIL, &tmp) == (u32)~0)
        {
            upf_alarm_msg_t *upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
            memset (upf_alarm, 0, sizeof (*upf_alarm));
            upf_alarm->alarm_id = UPF_ALARM_UL_UEIP_CHECK_FAIL;
            upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
            upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
            upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_ip_check_t), CLIB_CACHE_LINE_BYTES);
            memset (upf_alarm->data, 0, sizeof(upf_ip_check_t));
            memcpy(upf_alarm->data, &tmp, sizeof(tmp));
            pfcp_send_sx_alarm_to_thread(g_upf_main.first_pfcp_thread_index, upf_alarm);
            upf_add_del_alarm_key(UPF_ALARM_UL_UEIP_CHECK_FAIL, &tmp, 1);
        }
    }
}

upf_pdr_t * get_default_pdr(struct rules *rule, u8 src_intf)
{
    upf_pdr_t *default_pdr = NULL;

    default_pdr = rule->pdr + rule->pdr_edge[src_intf].end - 1;

    if (default_pdr->precedence != UPF_PDR_DEFAULT_PRECEDENCE)
    {
        upf_trace ("default pdr id: %d, it is not %d\n", default_pdr->id, UPF_PDR_DEFAULT_PRECEDENCE);
    }

    return default_pdr;
}

always_inline int upf_pdr_is_tcp_hand_msg (vlib_buffer_t *b)
{
	u32 offs;
	u8 *data = NULL;
	u32 is_payload_v4;
	ip4_header_t *ip4 = NULL;
	ip6_header_t *ip6 = NULL;
	u8 *proto_hdr = NULL;
    u8 protocol;
	word len;
	
    offs = upf_buffer_opaque (b)->upf.data_offset;
	data = (u8 *)(vlib_buffer_get_current (b) + offs);
    is_payload_v4 = is_v4_packet (data);
	
    if (is_payload_v4)
	{
	  ip4 = (ip4_header_t *)data;
	  proto_hdr = ip4_next_header (ip4);
	  protocol = ip4->protocol;
	  len = clib_net_to_host_u16 (ip4->length) - sizeof (ip4_header_t);
	}
    else
	{
	  ip6 = (ip6_header_t *)data;
	  proto_hdr = ip6_next_header (ip6);
	  protocol = ip6->protocol;
	  len = clib_net_to_host_u16 (ip6->payload_length);
	}

    if (protocol == IP_PROTOCOL_TCP)
	{
	  len -= tcp_header_bytes ((tcp_header_t *)proto_hdr);
	  
	  if (len <= 0 && (PREDICT_TRUE(tcp_syn((tcp_header_t *)proto_hdr)) || PREDICT_TRUE(tcp_ack((tcp_header_t *)proto_hdr))))  //tcp_header_t
      {
          upf_debug ("it is tcp hand msg\n");
          return 1;
      }
	}

	return 0;
}

u32 pdi_check_ueip_frame_route(upf_pdr_t *pdr, ip46_address_t *ue_ip, u32 is_v6)
{
    upf_pdi_t *pdi = &pdr->pdi;
    int cmp = 1;
    if (is_v6)
    {
        if (pdi->ue_addr.flags & IE_UE_IP_ADDRESS_V6)
        {
            u16 prefix = 64;
            if (pdi->ue_addr.flags & IE_UE_IP_ADDRESS_IPv6D)
                prefix = 64 - pdi->ue_addr.prefix_delegation_length;
            else if (pdi->ue_addr.flags & IE_UE_IP_ADDRESS_IP6PL)
                prefix = pdi->ue_addr.prefix_length;
            cmp = ip6_gateway_is_equal (&ue_ip->ip6, &pdi->ue_addr.ip6, prefix);
        }
        if (!cmp)
        {
            fib_prefix_t *fr6;
            vec_foreach(fr6, pdi->framed_ipv6_route)
            {
                cmp = ip6_gateway_is_equal(&ue_ip->ip6, &fr6->fp_addr.ip6, fr6->fp_len);
                if (cmp)
                    break;
            }
        }
    }
    else
    {
        if (pdi->ue_addr.flags & IE_UE_IP_ADDRESS_V4)
            cmp = ip4_address_is_equal (&ue_ip->ip4, &pdi->ue_addr.ip4);
        if (!cmp)
        {
            fib_prefix_t *fr4;
            vec_foreach(fr4, pdi->framed_route)
            {
                cmp = ip4_gateway_is_equal(&ue_ip->ip4, &fr4->fp_addr.ip4, fr4->fp_len);
                if (cmp)
                    break;
            }
        }
    }

    if (!cmp)
    {
        if (is_v6)
            upf_err ("dismatch ueip or frame route v6, pdi:%U pkt:%U\n", format_ip6_address, &pdi->ue_addr.ip6,
                format_ip6_address, &ue_ip->ip6);
        else
            upf_err ("dismatch ueip or frame route, pdi:%U pkt:%U\n", format_ip4_address, &pdi->ue_addr.ip4,
                format_ip4_address, &ue_ip->ip4);

        return NOT_MATCH;
    }

    return MATCH;
}

u32 match_pdi_misc(upf_pdr_t *pdr, vlib_buffer_t *b0)
{
    if (!pdr->is_active)
    {
        upf_trace ("dismatch for pdr inactive");
        return NOT_MATCH;
    }

    /* neil.fan@20230131 mask src_intf compare because it has checked in vec_pdr_foreach, 
     * the exceptant like L3 5GLAN(not using vec_pdr_foreach) will be compared individually.
     */
#if 0
    if (upf_buffer_opaque (b0)->upf.src_intf != pdr->pdi.src_intf)
    {
        upf_trace ("dismatch src_intf, pdi:%d, pkt:%d\n", pdr->pdi.src_intf, upf_buffer_opaque (b0)->upf.src_intf);
        return NOT_MATCH;
    }
#endif
    if (g_upf_qfi_check_switch && pdr->pdi.qfi && upf_buffer_opaque (b0)->upf.gtp_flags.qfi != pdr->pdi.qfi)
    {
        upf_err ("dismatch qfi, pdi:%d, pkt:%d\n", pdr->pdi.qfi, upf_buffer_opaque (b0)->upf.gtp_flags.qfi);
        UPF_STATISTICS_ADD(QFI_MISMATCH);
        return NOT_MATCH;
    }

    if (pdr->pdi.teid.teid && upf_buffer_opaque (b0)->upf.teid != pdr->pdi.teid.teid)
    {
        upf_err ("dismatch teid, pdi:%d, pkt:%d\n", pdr->pdi.teid.teid, upf_buffer_opaque (b0)->upf.teid);
        UPF_STATISTICS_ADD(TEID_MISMATCH)
        return NOT_MATCH;
    }
    else
    {
        if (!pdr->pdi.teid.teid && upf_buffer_opaque (b0)->upf.teid)
        {
            upf_err("dismatch teid, pdi has not, but flow has.");
            UPF_STATISTICS_ADD(TEID_NULL_IN_PDI);
            return NOT_MATCH;
        }
    }

    return MATCH;
}

acl_rule_t *match_ip_acl (upf_pdr_t *pdr, pkt_5tuple *pkt_info)
{
    acl_rule_t *acl;
    vec_foreach (acl, pdr->pdi.acl)
    {
        if (upf_acl_compare (acl, pkt_info, pdr->is_to_vn_group))
        {
            return acl;
        }
    }

    return NULL;
}

u32 match_ip_pdi(upf_session_t *sx, upf_pdr_t *pdr, vlib_buffer_t *b0, flow_entry_t *flow, pkt_5tuple *pkt_info)
{
    CHECK_THREE_PTR_RET_VALID(pdr, b0, flow, NOT_MATCH);
    upf_trace ("try to match pdr id: %d\n", pdr->id);

    if (NOT_MATCH == match_pdi_misc(pdr, b0))
    {
        UPF_STATISTICS_ADD(PDR_COMMON_CHECK_FAILED);
        return NOT_MATCH;
    }

    u16 is_v6 = is_v6_packet ((u8 *)(vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset));
    u32 is_mulicast = IS_MULTICAST(&pkt_info->ue_addr, !is_v6);

    if ((pdr->pdi.fields & F_PDI_UE_IP_ADDR)
        && (!sx->handover_ongoing)
        && (!is_mulicast)
        && (!IS_BROADCAST(&pkt_info->ue_addr, !is_v6))
        && ((SRC_INTF_ACCESS == pdr->pdi.src_intf) || (SRC_INTF_5G_VN == pdr->pdi.src_intf)))
    {
        if (PREDICT_FALSE(NOT_MATCH == pdi_check_ueip_frame_route(pdr, &pkt_info->ue_addr, is_v6)))
        {
            if (g_upf_alarm_switch && (SRC_INTF_ACCESS == pdr->pdi.src_intf) && !is_v6)
                upf_alarm_ue_ul_ueip_check(pdr, &pkt_info->ue_addr, is_v6);
            UPF_STATISTICS_ADD(UEIP_CHECK_FAILED);
            UPF_PDU_SESS_STATISTICS_ADD(sx,UEIP_CHECK_FAILED);
            return NOT_MATCH;
        }
    }

    if ((SRC_INTF_5G_VN == upf_buffer_opaque (b0)->upf.src_intf)
        && is_mulicast
        && (vec_len(pdr->pdi.ip_multicast_addressing_info)))
    {
        pfcp_ip_multicast_addressing_info_t *ip_ma;
        vec_foreach(ip_ma, pdr->pdi.ip_multicast_addressing_info)
        {
            if (is_ip_multicast_addressing_match(is_v6, &pkt_info->ue_addr, &ip_ma->ip_multicast_address))
            {
                upf_trace ("multicast address match, %U\n", format_ip46_address, &pkt_info->ue_addr, IP46_TYPE_ANY);
                if (!vec_len(ip_ma->source_ip_address))
                    goto next_cmp; /* MATCH */

                pfcp_source_ip_address_t *src_ip;
                vec_foreach(src_ip, ip_ma->source_ip_address)
                {
                    if (is_source_ip_address_match(is_v6, &pkt_info->dn_addr, src_ip))
                        goto next_cmp; /* MATCH */
                }
            }
        }
        upf_err("dismatch source ip address, pkt src:%U", format_ip46_address, &pkt_info->dn_addr, IP46_TYPE_ANY);
        UPF_STATISTICS_ADD(MUILTCAST_IP_CHECK_FAILED);
        UPF_PDU_SESS_STATISTICS_ADD(sx, MUILTCAST_IP_CHECK_FAILED);
        return NOT_MATCH;
    }

next_cmp:
    if (!vec_len (pdr->pdi.acl) && !vec_len (pdr->pdi.app_name))
    {
        return MATCH;
    }

    /* neil.fan@20230202 add to support L3+L4+L7 rule:
     * 1) precondition: only SDF Filter or APPID exist, not both.
     * 2) APPID exist: if L7 rule exist (with or without ACL), regard as not match, i.e.:
     *    a) L7 only, NOT MATCH.
     *    b) L3/L4 only, try to match...
     *    c) L3/4 + L7, NOT MATCH, the L3/L4 will be matched in upf_pdr_application_detection...
     * 3) SDF Fliter exist: try to match...
     */
    if (pdr->pdi.app_l7_rule)
    {
        upf_trace ("dismatch appid acl because L7 rule exist");
        UPF_STATISTICS_ADD(L7_RULE_EXISTED);
        UPF_PDU_SESS_STATISTICS_ADD(sx, L7_RULE_EXISTED);
        return NOT_MATCH;
    }

    acl_rule_t *acl = match_ip_acl (pdr, pkt_info);
    if (acl)
    {
        flow->application_id = acl->app_index; /* note: for APPID L3/4 ACL only; unused for SDF Filter. */
        return MATCH;
    }
    UPF_STATISTICS_ADD(ACL_CHECK_FAILED);
    UPF_PDU_SESS_STATISTICS_ADD(sx, ACL_CHECK_FAILED);
    upf_trace ("dismatch at the end of match_pdi");
    return NOT_MATCH;
}

u32 mac_addr_compare(mac_address_t *target, mac_address_t *lower, mac_address_t *upper, u32 is_range)
{
    u32 r;
    if (!is_range)
    {
        r = ethernet_mac_address_equal(lower->bytes, target->bytes);
        if (!r)
            upf_debug ("[%U]  pkt: %U\n", format_mac_address, lower, format_mac_address, target);
    }
    else
    {
        u64 lo = ethernet_mac_address_u64(lower->bytes);
        u64 up = ethernet_mac_address_u64(upper->bytes);
        u64 t = ethernet_mac_address_u64(target->bytes);
        r = ((lo <= t) && (t <= up));
        if (!r)
            upf_debug ("[%U - %U]  pkt: %U\n", format_mac_address, lower, format_mac_address, upper,
                format_mac_address, target);
    }
    return r;
}

/* neil.fan@20220523 add: the source mac is for dn, and the destination mac is ue, refer to:29244 5.13.3A, 5.13.4 */
u32 match_mac_addr(eth_pkt_tuple *eth_info, pfcp_mac_address_t *m_addr)
{
    if (m_addr->flags & F_DESTINATION_MAC)
    {
        if (!mac_addr_compare(&eth_info->ue_eth, &m_addr->dst_mac, &m_addr->upper_dst_mac, 
                (m_addr->flags & F_UPPER_DESTINATION_MAC)))
        {
            upf_debug ("packet dst mac match fail!\n");
            return NOT_MATCH;
        }
    }

    if (m_addr->flags & F_SOURCE_MAC)
    {
        if (!mac_addr_compare(&eth_info->dn_eth, &m_addr->src_mac, &m_addr->upper_src_mac, 
                (m_addr->flags & F_UPPER_DESTINATION_MAC)))
        {
            upf_debug ("packet src mac match fail!\n");
            return NOT_MATCH;
        }
    }

    return MATCH;
}

/* neil.fan@20220801 add for vn group level pdr: the dst mac(i.e. dn mac) of the packet need to match the src mac
 * of pfcp_match_address_t
 */
u32 match_vn_group_mac_addr(eth_pkt_tuple *eth_info, pfcp_mac_address_t *m_addr)
{
    if (m_addr->flags & F_DESTINATION_MAC)
    {
        if (!mac_addr_compare(&eth_info->dn_eth, &m_addr->dst_mac, &m_addr->upper_dst_mac,
                (m_addr->flags & F_UPPER_DESTINATION_MAC)))
        {
            upf_debug ("packet src mac match fail!\n");
            return NOT_MATCH;
        }
    }

    if (m_addr->flags & F_SOURCE_MAC)
    {
        if (!mac_addr_compare(&eth_info->ue_eth, &m_addr->src_mac, &m_addr->upper_src_mac,
                (m_addr->flags & F_UPPER_SOURCE_MAC)))
        {
            upf_debug ("packet dst mac match fail!\n");
            return NOT_MATCH;
        }
    }

    return MATCH;
}

static u32 match_ether_packet_filter(upf_ethernet_packet_filter_t *f, eth_pkt_tuple *eth_info, u8 is_to_vn_group, u8 mac_pass)
{
    u32 fields = f->grp.fields;

    if (vec_len(f->mac_address) && !mac_pass)
    {
        pfcp_mac_address_t *mac;
        vec_foreach(mac, f->mac_address)
        {
            if (PREDICT_TRUE(!is_to_vn_group))
            {
                if (MATCH == match_mac_addr(eth_info, mac))
                    goto next_match;
            }
            else
            {
                if (MATCH == match_vn_group_mac_addr(eth_info, mac))
                    goto next_match;
            }
        }
        return NOT_MATCH;
    }

next_match:

    if ((ISSET_BIT(fields, ETHERNET_PACKET_FILTER_ETHERTYPE)) && (f->ethertype != eth_info->eth_type))
    {
        upf_debug ("eth_type dismatch, eth_type:0x%x pkt:0x%x\n", f->ethertype, eth_info->eth_type);
        return NOT_MATCH;
    }

    if ((ISSET_BIT(fields, ETHERNET_PACKET_FILTER_S_TAG)) && !VLAN_MATCH_WITH_MASK(&f->s_tag, eth_info->s_vlan))
    {
        upf_debug ("s_vlan dismatch, s_tag:%U pkt:0x%x\n", upf_format_vlan_tag, &f->s_tag, eth_info->s_vlan);
        return NOT_MATCH;
    }

    if ((ISSET_BIT(fields, ETHERNET_PACKET_FILTER_C_TAG)) && !VLAN_MATCH_WITH_MASK(&f->c_tag, eth_info->c_vlan))
    {
        upf_debug ("c_vlan dismatch, c_tag:%U pkt:0x%x\n", upf_format_vlan_tag, &f->c_tag, eth_info->c_vlan);
        return NOT_MATCH;
    }

    if (vec_len (f->acl) && ((eth_info->eth_type == ETHERNET_TYPE_IP4) || (eth_info->eth_type == ETHERNET_TYPE_IP6)))
    {
        acl_rule_t *acl;
        vec_foreach (acl, f->acl)
        {
            if (upf_acl_compare (acl, &eth_info->ip_5tuple, is_to_vn_group))
            {
                return MATCH;
            }
        }
        upf_debug (" eth sdf filter not match");
        return NOT_MATCH;
    }
    else
    {
        return MATCH;
    }
}

static u32 match_ether_multicast(pfcp_ip_multicast_addressing_info_t *m, mac_address_t *dst)
{
    pfcp_ip_multicast_addressing_info_t *info;

    vec_foreach (info, m)
    {
        pfcp_ip_multicast_address_t *ip_m = &info->ip_multicast_address;
        if (ip_m->flags & IE_IP_MULTICAST_ADDRESS_A)
            goto src_ip_match;

        mac_address_t mac = {0};
        if (ip_m->flags & IE_IP_MULTICAST_ADDRESS_V4)
            ip4_multicast_ethernet_address(mac.bytes, &ip_m->ip4_start);
        else if (ip_m->flags & IE_IP_MULTICAST_ADDRESS_V6)
            ip6_multicast_ethernet_address(mac.bytes, clib_net_to_host_u32(ip_m->ip6_start.as_u32[3]));
        else
            continue; /* temporarial not support mac range */

        if (ethernet_mac_address_equal(dst->bytes, mac.bytes))
            goto src_ip_match;

        upf_err("mac address dismatch, %U, pkt:%U", format_mac_address, &mac, format_mac_address, &dst->bytes);
        continue;

src_ip_match:
        /* temporarial not ignore src ip match */
        return MATCH;
    }

    return NOT_MATCH;
}

u32 match_eth_pdi(upf_session_t *sx, upf_pdr_t *pdr, vlib_buffer_t *b0, flow_entry_t *flow, eth_pkt_tuple *eth_info)
{
    if (PREDICT_FALSE(!pdr || !b0))
        return NOT_MATCH;

    upf_debug ("try to match pdr id: %d\n", pdr->id);

    if (NOT_MATCH == match_pdi_misc(pdr, b0))
    {
        UPF_STATISTICS_ADD(PDR_COMMON_CHECK_FAILED);
        return NOT_MATCH;
    }

    u8 mac_pass = 0;
    upf_pdi_t *pdi = &pdr->pdi;
    ethernet_header_t *eth = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset;
    if (PREDICT_FALSE(pdi->fields & F_PDI_IP_MULTICAST_ADDRESSING_INFO))
    {
        if (ethernet_address_cast(eth->dst_address))
        {
            if (NOT_MATCH ==  match_ether_multicast(pdi->ip_multicast_addressing_info, (mac_address_t *)eth->dst_address))
            {
                upf_err ("multicast/boardcast mac cannot match multicast pdr\n");
                UPF_STATISTICS_ADD(MULTICAST_ADDR_CHECK_FAILED);
                UPF_PDU_SESS_STATISTICS_ADD(sx, MULTICAST_ADDR_CHECK_FAILED);
                return NOT_MATCH;
            }
        }
        else
        {
            upf_err ("unicast mac cannot match multicast pdr\n");
            UPF_STATISTICS_ADD(UNICAST_ADDR_CHECK_FAILED);
            UPF_PDU_SESS_STATISTICS_ADD(sx, UNICAST_ADDR_CHECK_FAILED);
            return NOT_MATCH;
        }
        mac_pass = 1;
    }

    if (!(pdi->fields & F_PDI_ETHERNET_PACKET_FILTER))
        return MATCH;

    upf_ethernet_packet_filter_t *f;
    vec_foreach(f, pdi->eth_rule)
    {
        if (MATCH == match_ether_packet_filter(f, eth_info, pdr->is_to_vn_group, mac_pass))
            return MATCH;
    }
    UPF_STATISTICS_ADD(ETH_FILTER_MATCH_FAILED);
    UPF_PDU_SESS_STATISTICS_ADD(sx, ETH_FILTER_MATCH_FAILED);
    upf_err ("all ethernet packet filters of this pdi not matched");

    return NOT_MATCH;
}

u32 search_ip_pdr (upf_session_t *sx, struct rules *rule, vlib_buffer_t *b0, flow_entry_t *flow)
{
    CHECK_THREE_PTR_RET_VALID(rule, b0, flow, UPF_INVALID_INDEX);

    if (g_upf_main.pdr_search_perf)
    {
        u8 *data = (u8 *)(vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset);
        if (is_v6_packet (data))
            return upf_search_pdr_ip6 (rule->acl6, b0, 0);
        else
            return upf_search_pdr_ip4 (rule->acl, b0, 0);
    }

    u8 src_intf = upf_buffer_opaque (b0)->upf.src_intf;
    if (PREDICT_FALSE(src_intf >= SRC_INTF_NUM))
    {
        upf_err("src_intf:%u is larger than max", src_intf);
        return UPF_INVALID_INDEX;
    }

    pkt_5tuple pkt_info;
    upf_get_ip_pkt_info(&pkt_info, b0);

    if (PREDICT_FALSE(rule->pdr_edge[src_intf].end > vec_len(rule->pdr)))
    {
        upf_err("pdr_edge:%u is larger than rule->pdr len:%u src_intf:%u",
            rule->pdr_edge[src_intf].end, vec_len(rule->pdr), src_intf);
        return UPF_INVALID_INDEX;
    }

    if (src_intf == SRC_INTF_ACCESS)
    {
        upf_pdr_t *pdr;
        vec_pdr_foreach(pdr, &rule->pdr_edge[SRC_INTF_ACCESS], rule->pdr)
        {
            if (pdr->ip_multicast_addressing_info && pkt_info.proto == 2)
                upf_ip_multicast_address_info_report(b0, pdr, pdr - rule->pdr);
        }
    }

    upf_pdr_t *pdr;
    vec_pdr_foreach(pdr, &rule->pdr_edge[src_intf], rule->pdr)
    {
        if (g_urr_tcp_hand_switch && upf_pdr_is_tcp_hand_msg(b0))
        {
            upf_pdr_t *default_pdr = get_default_pdr(rule, src_intf);

			if (pdr->id != default_pdr->id)
			{
			    upf_err ("default pdr id: %u, pdr id: %u, it is misatch\n", default_pdr->id, pdr->id);
				continue;
			}
        }
		
        if (MATCH == match_ip_pdi(sx, pdr, b0, flow, &pkt_info))
        {
            upf_debug ("match pdr id: %d\n", pdr->id);
            return pdr - rule->pdr;
        }
    }
	
    return UPF_INVALID_INDEX;
}

u32 search_eth_pdr (upf_session_t *sx, struct rules *rule, vlib_buffer_t *b0, flow_entry_t *flow)
{
    CHECK_TWO_PTR_RET_VALID(rule, b0, UPF_INVALID_INDEX);

    u8 src_intf = upf_buffer_opaque (b0)->upf.src_intf;
    if (PREDICT_FALSE(src_intf >= SRC_INTF_NUM))
    {
        upf_err("src_intf:%u is larger than max", src_intf);
        return UPF_INVALID_INDEX;
    }

    eth_pkt_tuple eth_info;
    upf_get_eth_pkt_info(&eth_info, b0);

    if (PREDICT_FALSE(rule->pdr_edge[src_intf].end > vec_len(rule->pdr)))
    {
        upf_err("pdr_edge:%u is larger than rule->pdr len:%u src_intf:%u",
            rule->pdr_edge[src_intf].end, vec_len(rule->pdr), src_intf);
        return UPF_INVALID_INDEX;
    }

    upf_pdr_t *pdr;
    vec_pdr_foreach(pdr, &rule->pdr_edge[src_intf], rule->pdr)
    {
        if (MATCH == match_eth_pdi(sx, pdr, b0, flow, &eth_info))
        {
            upf_trace ("match pdr id: %d\n", pdr->id);
            return pdr - rule->pdr;
        }
    }
    return UPF_INVALID_INDEX;
}

/* perfect hash over the HTTP keywords:
 *   GET
 *   PUT
 *   HEAD
 *   POST
 *   COPY
 *   MOVE
 *   LOCK
 *   MKCOL
 *   TRACE
 *   PATCH
 *   DELETE
 *   UNLOCK
 *   CONNECT
 *   OPTIONS
 *   PROPPATCH
 */
#if CLIB_ARCH_IS_BIG_ENDIAN
#define upf_char_to_u32(A, B, C, D) (((A) << 24) | ((B) << 16) | ((C) << 8) | (D))
#define upf_char_to_u64(A, B, C, D, E, F, G, H)                    \
  (((u64) (A) << 56) | ((u64) (B) << 48) | ((u64) (C) << 40) | \
   ((u64) (D) << 32) | ((u64) (E) << 24) | ((u64) (F) << 16) | \
   ((u64) (G) << 8) | (u64) (H))
#else
#define upf_char_to_u32(A, B, C, D) (((D) << 24) | ((C) << 16) | ((B) << 8) | (A))
#define upf_char_to_u64(A, B, C, D, E, F, G, H)                    \
  (((u64) (H) << 56) | ((u64) (G) << 48) | ((u64) (F) << 40) | \
   ((u64) (E) << 32) | ((u64) (D) << 24) | ((u64) (C) << 16) | \
   ((u64) (B) << 8) | (u64) (A))
#endif

#define upf_char_mask_64_40 upf_char_to_u64 (0xff, 0xff, 0xff, 0xff, 0xff, 0, 0, 0)
#define upf_char_mask_64_48 upf_char_to_u64 (0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0, 0)
#define upf_char_mask_64_56 \
  upf_char_to_u64 (0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0)

int upf_is_https_client_hello (u8 **payload, word *len)
{
  u16 c0 = *(u16 *)*payload;

  if (*len < 10)
    return 0;
  /*handshake :0x16, tls:0x03*/
  if (c0 == 0x0316)
    {
      *payload += 5;
      *len -= 5;
      /*client hello : 0x01*/
      if (*(u8 *)*payload == 0x01)
        {
          upf_debug ("hit ssl client hello!");
          return 1;
        }
    }
  return 0;
}

int upf_is_http_request (u8 **payload, word *len)
{
  u32 c0 = *(u32 *)*payload;
  u64 d0 = *(u64 *)*payload;

  if (*len < 10)
    return 0;

  if (g_upf_dpi_switch)
  {
      if (c0 == upf_char_to_u32 ('G', 'E', 'T', ' ') ||
          c0 == upf_char_to_u32 ('P', 'U', 'T', ' '))
        {
          *payload += 4;
          *len -= 4;
          return 1;
        }
  }
  else
  {
      if (c0 == upf_char_to_u32 ('G', 'E', 'T', ' ') ||
          c0 == upf_char_to_u32 ('P', 'U', 'T', ' '))
        {
          *payload += 4;
          *len -= 4;
          return 1;
        }
      else if ((c0 == upf_char_to_u32 ('H', 'E', 'A', 'D') ||
                c0 == upf_char_to_u32 ('P', 'O', 'S', 'T') ||
                c0 == upf_char_to_u32 ('C', 'O', 'P', 'Y') ||
                c0 == upf_char_to_u32 ('M', 'O', 'V', 'E') ||
                c0 == upf_char_to_u32 ('L', 'O', 'C', 'K')) &&
               (*payload)[4] == ' ')
        {
          *payload += 5;
          *len -= 5;
          return 1;
        }
      else if (((d0 & upf_char_mask_64_48) ==
                upf_char_to_u64 ('M', 'K', 'C', 'O', 'L', ' ', 0, 0)) ||
               ((d0 & upf_char_mask_64_48) ==
                upf_char_to_u64 ('T', 'R', 'A', 'C', 'E', ' ', 0, 0)) ||
               ((d0 & upf_char_mask_64_48) ==
                upf_char_to_u64 ('P', 'A', 'T', 'C', 'H', ' ', 0, 0)))
        {
          *payload += 6;
          *len -= 6;
          return 1;
        }
      else if (((d0 & upf_char_mask_64_56) ==
                upf_char_to_u64 ('D', 'E', 'L', 'E', 'T', 'E', ' ', 0)) ||
               ((d0 & upf_char_mask_64_56) ==
                upf_char_to_u64 ('U', 'N', 'L', 'O', 'C', 'K', ' ', 0)))
        {
          *payload += 7;
          *len -= 7;
          return 1;
        }
      else if ((d0 == upf_char_to_u64 ('C', 'O', 'N', 'N', 'E', 'C', 'T', ' ')) ||
               (d0 == upf_char_to_u64 ('O', 'P', 'T', 'I', 'O', 'N', 'S', ' ')))
        {
          *payload += 8;
          *len -= 8;
          return 1;
        }
      if (c0 == upf_char_to_u32 ('P', 'R', 'O', 'P'))
        {
          u64 d1 = *(u64 *)(*payload + 4);

          if ((d1 & upf_char_mask_64_40) ==
              upf_char_to_u64 ('F', 'I', 'N', 'D', ' ', 0, 0, 0))
            {
              *payload += 9;
              *len -= 9;
              return 1;
            }
          else if ((d1 & upf_char_mask_64_48) ==
                   upf_char_to_u64 ('P', 'A', 'T', 'C', 'H', ' ', 0, 0))
            {
              *payload += 10;
              *len -= 10;
              return 1;
            }
        }
  }

  return 0;
}

int
upf_is_host_header (u8 **s, word *len)
{
  u8 *eol;
  u8 *c;

  eol = memchr (*s, '\n', *len);
  if (!eol)
    {
      *s += *len;
      *len = 0;
      return 0;
    }

  if ((eol - *s) < 5)
    goto out_skip;

  u64 d0 = *(u64 *)(*s);

  /* upper case 1st 4 characters of header */
  d0 &= upf_char_to_u64 (0xdf, 0xdf, 0xdf, 0xdf, 0xff, 0, 0, 0);
  if (d0 != upf_char_to_u64 ('H', 'O', 'S', 'T', ':', 0, 0, 0))
    goto out_skip;

  *s += 5;
  *len -= 5;

  /* find first non OWS */
  for (; *len > 0 && **s <= ' '; (*len)--, (*s)++)
    ;
  /* find last non OWS */
  for (c = *s; *len > 0 && *c > ' '; (*len)--, c++)
    ;

  if (len <= 0)
    return 0;

  *len = c - *s;
  return 1;

out_skip:
  eol++;
  *len -= eol - *s;
  *s = eol;

  return 0;
}

always_inline int
upf_pdr_is_http_request (vlib_main_t *vm, vlib_buffer_t *b, flow_entry_t *flow,
                    struct rules *active, u16 is_eth, u8 is_ip4)
{
  u32 offs = upf_buffer_opaque (b)->upf.data_offset;
  ip4_header_t *ip4 = NULL;
  ip6_header_t *ip6 = NULL;
  u8 *proto_hdr;
  u8 *tcp_payload;
  word len;

  if (!KEY_LOG_SWITCH(KL_SWITCH_PDR_DETECT_HTTP))
    return 0;

  // known PDR.....
  // scan for Application Rules
  if (!g_upf_main.database)
    {
      upf_debug ("g_upf_main.database is NULL");
      return 0;
    }
  if (!(active->flags & SX_APP))
    return 0;

  if (is_eth)
    return 0; /* temporary not support */

  if (is_ip4)
    {
      ip4 = (ip4_header_t *)(vlib_buffer_get_current (b) + offs);
      proto_hdr = ip4_next_header (ip4);
      len = clib_net_to_host_u16 (ip4->length) - sizeof (ip4_header_t);
    }
  else
    {
      ip6 = (ip6_header_t *)(vlib_buffer_get_current (b) + offs);
      proto_hdr = ip6_next_header (ip6);
      len = clib_net_to_host_u16 (ip6->payload_length);
    }

  if (flow->key.proto == IP_PROTOCOL_TCP)
    {
      //&&flow->tcp_state == TCP_STATE_ESTABLISHED) {
      len -= tcp_header_bytes ((tcp_header_t *)proto_hdr);
      offs = proto_hdr - (u8 *)vlib_buffer_get_current (b) +
             tcp_header_bytes ((tcp_header_t *)proto_hdr);
    }
  else if (flow->key.proto == IP_PROTOCOL_ICMP)
  {
      return 1;
  }
  else
    return 0;

  if (len < vlib_buffer_length_in_chain (vm, b) - offs || len <= 0)
    /* no or invalid payload */
    return 0;

  tcp_payload = vlib_buffer_get_current (b) + offs;
  if (upf_is_https_client_hello (&tcp_payload, &len))
  	{
  	  upf_buffer_opaque (b)->upf.https = 1;
      return 1;
  	}
  else if (upf_is_http_request (&tcp_payload, &len))
    {
      upf_buffer_opaque (b)->upf.http = 1;
      return 1;
    }
  else
    return 0;
}

always_inline int
upf_pdr_detect_dns (vlib_buffer_t *b, struct rules *active, u8 is_eth, u8 is_ip4)
{
  u32 offs = upf_buffer_opaque (b)->upf.data_offset;
  ip4_header_t *ip4 = NULL;
  ip6_header_t *ip6 = NULL;
  udp_header_t *udp;

  if (!(active->flags & SX_APP))
    return 0;
  
  if(is_eth)
	{
	   offs += sizeof (ethernet_header_t);
	}

  if (is_ip4)
    {
      ip4 = (ip4_header_t *)(vlib_buffer_get_current (b) + offs);
      udp = ip4_next_header (ip4);
    }
  else
    {
      ip6 = (ip6_header_t *)(vlib_buffer_get_current (b) + offs);
      udp = ip6_next_header (ip6);
    }
  if (udp->dst_port == 0x3500 || udp->src_port == 0x3500) // dns port 53
    return 1;
  else
    return 0;
}
static inline int
upf_hs_handler_global (unsigned int id, unsigned long long from,
                   unsigned long long to, unsigned int flags, void *ctx)
{
  upf_app_id_pfd_ctx_t *app;
  upf_pfd_ctx_t *pfd = NULL;
  upf_main_t *um = &g_upf_main;
  upf_debug ("match offset %llu,appid_idx=%u\n", to, id);
  /*if this rule is for fd+custormer,but dpi is null,then next*/
  if (id & CT_FLAG)
    {
      if (((upf_appid_t *)ctx)->customer == NULL)
        return 0;
      else
        id &= ~CT_FLAG;
    }

  CHECK_POOL_IS_VALID_RET(g_upf_main.pfd_list, id, 0);
  app = pool_elt_at_index (g_upf_main.pfd_list, id);
  if (((upf_appid_t *)ctx)->customer)
    {
      vec_foreach (pfd, app->pfd_contents)
      {
        if (!(pfd->flags & F_PFD_C_CP))
          goto hit_ct;
        if ((pfd->flags & F_PFD_C_CP) &&
            vec_is_equal (((upf_appid_t *)ctx)->customer, pfd->ct))
          goto hit_ct;
      }
      upf_debug ("can't find %v in customer of appid %v\n",
                 ((upf_appid_t *)ctx)->customer, app->app_name);
      return 0;
    }

  hit_ct:
  if (vec_is_equal (((upf_appid_t *)ctx)->pdr->pdi.app_name, app->app_name))
  {
    ((upf_appid_t *)ctx)->appid_index = id;
    return 1;
  }
  else if (((upf_appid_t *)ctx)->pdr->activate_predefined_rules)
  {
      #if 0
      predef_rule_t *pr;
      predef_appid_t *pr_appid;
      uword *p = hash_get_mem (g_upf_main.hash_rule_id_by_rule_name,
                               ((upf_appid_t *)ctx)->pdr->activate_predefined_rules);
      if (p)
      {
        pr = pool_elt_at_index (g_upf_main.pre_rule, p[0]);
        pool_foreach(pr_appid, pr->app_ids, ({
            if (vec_is_equal (pr_appid->app_id, app->app_name))
            {
                ((upf_appid_t *)ctx)->appid_index = id;
                pr_appid->appid_index = id;
                if (pr_appid->urr_id)
                    ((upf_appid_t *)ctx)->urr_id = *pr_appid->urr_id;
                return 1;
            }
        }));
      }
      #endif
      predef_rule_t *pr;
      predef_appid_t *pr_appid;
      if(g_upf_rg_switch)
      {
          predef_rule_group_t *pre_rule_group;
          predef_rule_name_t *rule_name;
          uword *q = hash_get_mem (um->hash_rule_id_by_rule_group, ((upf_appid_t *)ctx)->pdr->activate_predefined_rules);
          if (q)
          {
              CHECK_POOL_IS_VALID_RET(um->pre_rule_group, q[0], 0);
              pre_rule_group = pool_elt_at_index (um->pre_rule_group, q[0]);
              if (pre_rule_group)
              {
                  pool_foreach(rule_name, pre_rule_group->rule_names, ({
                      uword *q1 = hash_get_mem (um->hash_rule_id_by_rule_name, rule_name->rule_name);
                      CHECK_POOL_IS_VALID_RET(um->pre_rule, q1[0], 0);
                      pr = pool_elt_at_index (um->pre_rule, q1[0]);
                      pool_foreach(pr_appid, pr->app_ids, ({ 
                            if (vec_is_equal (pr_appid->app_id, app->app_name))
                            {
                                ((upf_appid_t *)ctx)->appid_index = id;
                                pr_appid->appid_index = id;
                                if (pr_appid->urr_id)
                                    ((upf_appid_t *)ctx)->urr_id = *pr_appid->urr_id;
                                return 1;
                            }
                         }));
                  }));
              }
          }
      }
      else
      {
          uword *q = hash_get_mem (um->hash_rule_id_by_rule_name, ((upf_appid_t *)ctx)->pdr->activate_predefined_rules);
          CHECK_POOL_IS_VALID_RET(um->pre_rule, q[0], 0);
          pr = pool_elt_at_index (um->pre_rule, q[0]);
          pool_foreach(pr_appid, pr->app_ids, ({ 
                if (vec_is_equal (pr_appid->app_id, app->app_name))
                {
                    ((upf_appid_t *)ctx)->appid_index = id;
                    pr_appid->appid_index = id;
                    if (pr_appid->urr_id)
                        ((upf_appid_t *)ctx)->urr_id = *pr_appid->urr_id;
                    return 1;
                }
                }));
  
      }
  }
  return 0;
}

static inline u32
upf_pdr_appid_lookup (upf_pdr_t *pdr, u8 *str, uint16_t length, u32 *urr_id, u8 *appid_customer)
{
    upf_appid_t args = {};
    args.pdr = pdr;
    args.appid_index = ~0;
    args.urr_id = ~0;
	
    if (!g_upf_main.database)
    {
      upf_debug ("g_upf_main.database is NULL");
      return args.appid_index;
    }
	
    args.customer = appid_customer;

    int ret = hs_scan (g_upf_main.database, (const char *)str, length, 0, g_upf_main.scratch[vlib_get_thread_index () - g_upf_main.first_worker_thread_index], upf_hs_handler_global, &args);
  
    if (ret == HS_SCAN_TERMINATED)
    {
      upf_debug ("upf_pdr_appid_lookup pdr %u for appid match\n", pdr->id);
      if (urr_id)
      {
          *urr_id = args.urr_id;
      }
    }
    else
    {
        upf_debug ("upf_pdr_appid_lookup pdr %u for appid fail, ret:%u, length:%u \n%U",
            pdr->id, ret, length, format_hex, str, length > 200 ? 200 : length);
    }

  return args.appid_index;
}

u8 dns_serverlist_num = 0;

int upf_dns_server_ip_add_del(ip4_address_t *ip, u8 add)
{
  upf_main_t *gtm = &g_upf_main;
  dns_server_s *dns_server;
  dns_server_key_s dns_server_key = {0};
  uword *p;
 
  dns_server_key.server_ip = ip->data_u32;
   
  p = hash_get_mem (gtm->dns_server_index, &dns_server_key);

  if (add)
  {
	  if (p)
	  {
		clib_error_return (0, "dns server already exists...");
		return 0;
	  }

	  pool_get (gtm->dns_server_list, dns_server);
	  dns_server->dns_server_key.server_ip = ip->data_u32;
	  dns_server->ipaddress = ip->data_u32;

	  hash_set_mem_alloc (&gtm->dns_server_index, &dns_server->dns_server_key, dns_server - gtm->dns_server_list);

	  dns_serverlist_num++;
  }
  else
  {
	  if (!p)
	  {
		  return -1;
	  }

      CHECK_POOL_IS_VALID_RET(gtm->dns_server_list, p[0], -1);
	  dns_server = pool_elt_at_index (gtm->dns_server_list, p[0]);
	  hash_unset_mem_free (&gtm->dns_server_index, &dns_server->dns_server_key);
	  pool_put (gtm->dns_server_list, dns_server);

	  dns_serverlist_num--;
  }

  return 0;
}


static clib_error_t *
upf_set_dns_server_command_fn (vlib_main_t *vm, unformat_input_t *input,
								   vlib_cli_command_t *cmd)
{
   unformat_input_t _line_input, *line_input = &_line_input;
   clib_error_t *error = 0;
   u8 add = 1;
   ip4_address_t ip = {0};
   u8 ip_set = 0;
   
   /* Get a line of input. */
   if (unformat_user (input, unformat_line_input, line_input))
   {
	   while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
	   {
		   if (unformat (line_input, "del"))
		   {
			   add = 0;
		   }  
		   else if (unformat (line_input, "ip %U", unformat_ip4_address, &ip,
							IP46_TYPE_IP4))
		   {
			   ip_set = 1;
		   }
		   else
		   {
			 error = unformat_parse_error (line_input);
			 goto done;
		   }
	   }
   }
   else
   {
	   return clib_error_return (0, "need input paramter");
   }
   
   if (!ip_set)
   {
	 error = clib_error_return (0, "ip need to be set");
	 goto done;
   }

   upf_dns_server_ip_add_del (&ip, add);

done:
   unformat_free (line_input);
   return error;
}


VLIB_CLI_COMMAND (upf_dns_server_command, static) = {
  .path = "upf dns server",
  .short_help = "upf dns server [ip <ip-address>] [del]",
  .function = upf_set_dns_server_command_fn,
};

static clib_error_t *
upf_show_dns_server_command_fn (vlib_main_t *vm, unformat_input_t *input,
									vlib_cli_command_t *cmd)
{
      clib_error_t *error = 0;
	  upf_main_t *gtm = &g_upf_main;
	  dns_server_s *dns_server = NULL;
      
      
	  vlib_cli_output (vm, "upf trusted dns server:");
	  
	  /* *INDENT-OFF* */
	  pool_foreach (dns_server, gtm->dns_server_list, ({
	        
			vlib_cli_output (vm, "%U", format_ip4_address, &dns_server->ipaddress);
				
		  }));
	  /* *INDENT-ON* */ 

      return error;
}


/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_show_dns_server_command, static) = {
	.path = "show upf dns server",
	.short_help = "show upf dns server",
	.function = upf_show_dns_server_command_fn,
};
/* *INDENT-ON* */

//begin liukang add for v6 fd_ct 2021/07/22

int upf_v6_fd_get_url(u8 *data,int data_len,char *url)
{
	
    char http_buf[2048] = {0};
	strncpy(http_buf,(char *)data,data_len);
	char *p = NULL;
	char *q = NULL;
	int num = 0;
	p = strstr(http_buf,"Host");

	if(p == NULL)
	{
		p = strstr(http_buf,"host");
	}


	p = p+4;
	while(*p == ':' || *p == ' ')
	{
		p++;
	}
	
	q = p;
	while(*p != '\015' && *p !='\0')
	{
		p++;
	}
	
	if(*p == '\015' && *(++p) == '\012')
	{
		num = p - q -1;
		strncpy(url,q,num);
		return num;
	}
	else
	{
		return 0;
	}
}

extern int tcp_reass_of_http_request (char *payload, u32 len);

static inline int upf_v6_fd_com(upf_pfd_ctx_t *tmp_filter_node, pkt_5tuple *pkt_info,vlib_buffer_t *b)
{
  
  u16 ue_field = 0;
  ip46_address_t *acl_ue, *acl_ue_mask;
  ip46_address_t *acl_dn, *acl_dn_mask;

  //struct ip6_hdr *ip6h = NULL;

  
  acl_rule_t *acl = &(tmp_filter_node->fd_filter);

  if (acl->direction == ACL_OUT)
    ue_field = UPF_ACL_FIELD_DST;
  else
    ue_field = UPF_ACL_FIELD_SRC;
  acl_ue = &acl->address[ue_field].address;
  acl_ue_mask = &acl->address[ue_field].mask_address;
  acl_dn = &acl->address[ue_field ^ 1].address;
  acl_dn_mask = &acl->address[ue_field ^ 1].mask_address;

  upf_trace ("upf_v6_fd_com acl filter UE: %U/%d, %d-%d, DN: %U/%d, %d-%d, PROTO: %d\n",
             format_ip46_address, &acl->address[ue_field].address,
             IP46_TYPE_ANY, acl->address[ue_field].mask,
             acl->port[ue_field].min, acl->port[ue_field].max,
             format_ip46_address, &acl->address[ue_field ^ 1].address,
             IP46_TYPE_ANY, acl->address[ue_field ^ 1].mask,
             acl->port[ue_field ^ 1].min, acl->port[ue_field ^ 1].max,
             acl->proto);

  upf_trace ("upf_v6_fd_com pkt filter UE: %U/%d, %d-%d, DN: %U/%d, %d-%d, PROTO: %d\n",
             format_ip46_address, &pkt_info->ue_addr,
             IP46_TYPE_ANY, acl->address[ue_field].mask,
             pkt_info->ue_port, pkt_info->ue_port,
             format_ip46_address, &pkt_info->dn_addr,
             IP46_TYPE_ANY, acl->address[ue_field ^ 1].mask,
             pkt_info->dn_port, pkt_info->dn_port,
             pkt_info->proto);

  if(tmp_filter_node->flags & F_PFD_C_FD)
  {
      if(!(((acl->proto == (u8)~0) || acl->proto == pkt_info->proto) &&

                  (ip46_address_is_zero (&acl->address[ue_field].address) ||
                   ip6_address_is_equal_masked (
                       &acl_ue->ip6, &pkt_info->ue_addr.ip6, &acl_ue_mask->ip6)) &&
                  (ip46_address_is_zero (&acl->address[ue_field ^ 1].address) ||
                   ip6_address_is_equal_masked (
                       &acl_dn->ip6, &pkt_info->dn_addr.ip6, &acl_dn_mask->ip6)) &&
                  ((acl->port[ue_field].min <= pkt_info->ue_port) &&
                   (acl->port[ue_field].max >= pkt_info->ue_port)) &&
                  ((acl->port[ue_field ^ 1].min <= pkt_info->dn_port) &&
                   (acl->port[ue_field ^ 1].max >= pkt_info->dn_port))))
      	{
      		return 0;
        }
    }

    #if 0
	if((tmp_filter_node->flags & F_PFD_C_URL) == F_PFD_C_URL)
	{
		ip6_header_t *ip6;
		ip6 = vlib_buffer_get_current (b) + upf_buffer_opaque (b)->upf.data_offset;

		ip6h = (struct ip6_hdr *) ip6;
		
		struct tcphdr *this_tcphdr = (struct tcphdr *)((u_char *)ip6 +sizeof(struct ip6_hdr));
		int datalen;
		datalen = ntohs(ip6h->ip6_plen) - 4 * this_tcphdr->th_off;
		u_char *data = (u_char *)(this_tcphdr)+4 * this_tcphdr->th_off;

		char url[256] = {0};
		int url_len = 0;
		if(tcp_reass_of_http_request((char *)data,datalen))
		{
			url_len = upf_v6_fd_get_url(data,datalen,url);
		}
		upf_trace ("upf_v6_fd_com pkt url: %s\n",url);
		upf_trace ("upf_v6_fd_com pkt tmp_filter_node->url: %s\n",tmp_filter_node->url);

		if(url_len == 0)
		{
			return 0;
		}
		if(strncmp ((char *)tmp_filter_node->url, url,url_len) == 0)
		{
			return 1;
		}
		else
		{
			return 0;
		}
	}
    #endif
	
    return 1;
}


static inline u32
upf_appid_lookup_v6 (upf_pdr_t *pdr, vlib_buffer_t *b, u32 *urr_id, u8 *appid_customer)
{
	ip6_header_t *ip6 = (ip6_header_t *)(vlib_buffer_get_current (b) +
                                       upf_buffer_opaque (b)->upf.data_offset);
	pkt_5tuple pkt_info;
	udp_header_t *tcph = NULL;
	
	if (ip6->protocol == IP_PROTOCOL_UDP || ip6->protocol == IP_PROTOCOL_TCP)
        tcph = vlib_buffer_get_current (b) +
               upf_buffer_opaque (b)->upf.data_offset + sizeof (ip6_header_t);
      /*SRC_INTF_ACCESS(0) means data is uplink,that from ue to dn*/
    if (!upf_buffer_opaque (b)->upf.src_intf)
    {
          ip46_address_set_ip6 (&pkt_info.ue_addr, &ip6->src_address);
          ip46_address_set_ip6 (&pkt_info.dn_addr, &ip6->dst_address);

          pkt_info.proto = ip6->protocol;
    }
    else
    {
          ip46_address_set_ip6 (&pkt_info.ue_addr, &ip6->dst_address);
          ip46_address_set_ip6 (&pkt_info.dn_addr, &ip6->src_address);

          pkt_info.proto = ip6->protocol;
    }

	if (tcph)
    {
      if (!upf_buffer_opaque (b)->upf.src_intf)
        {
          pkt_info.ue_port = clib_net_to_host_u16 (tcph->src_port);
          pkt_info.dn_port = clib_net_to_host_u16 (tcph->dst_port);
        }
      else
        {
          pkt_info.ue_port = clib_net_to_host_u16 (tcph->dst_port);
          pkt_info.dn_port = clib_net_to_host_u16 (tcph->src_port);
        }
    }
	  
	upf_main_t *gtm = &g_upf_main;
	upf_pfd_ctx_t *tmp_filter_node;
	upf_app_id_pfd_ctx_t *tmp_filter_list = NULL;
	int index = 0;
	
	
	upf_appid_t args = {};
	pool_foreach (tmp_filter_list, gtm->pfd_list, ({
                  pool_foreach (tmp_filter_node, tmp_filter_list->pfd_contents,
                                ({     
									if(upf_v6_fd_com(tmp_filter_node,&pkt_info,b))
									{
										index = tmp_filter_list - gtm->pfd_list;
										goto FOUND;
									}      
                                }));
                }));
    return 0;

FOUND:

    return 1;
    args.pdr = pdr;
    args.appid_index = ~0;
    args.urr_id = ~0;
    if (!g_upf_main.database)
    {
      upf_debug ("g_upf_main.database is NULL");
      return args.appid_index;
    }
    args.customer = appid_customer;

	upf_hs_handler_global (index, 0,0, 0, (void *)(&args));
    return args.appid_index;
}

//end liukang add for v6 fd_ct 2021/07/22

static u32 upf_app_pdr_and_srcintf_get(struct rules *active, u32 pdr_index, upf_pdr_t **v_pdr, u8 *src_intf)
{
    upf_pdr_t *pdr = upf_get_pdr_by_index_r (active, pdr_index);
    if (!pdr)
      {
          upf_warn ("Error pdr_index: %d", pdr_index);
          return 1;
      }

    if (PREDICT_FALSE (pdr->pdi.src_intf > SRC_INTF_CP))
      {
        upf_warn ("src_intf:% is larger than SRC_INTF_CP\n", pdr->pdi.src_intf);
        return 1;
      }

    *src_intf = pdr->pdi.src_intf;
    *v_pdr = pdr;

    return 0;
}

static u32 upf_app_ueip_check (upf_pdr_t *pdr, u8 is_ip4, u8 *ip)
{
  if (is_ip4)
    {
      ip4_header_t *ip4 = (ip4_header_t *)ip;
      const ip4_address_t *addr;

      if (!(pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_V4))
        {
          return 1;
        }
      addr = (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_SD)
                 ? &ip4->dst_address
                 : &ip4->src_address;

      if (!ip4_address_is_equal (&pdr->pdi.ue_addr.ip4, addr))
        {
          return 2;
        }
    }
  else
    {
      ip6_header_t *ip6 = (ip6_header_t *)ip;
      const ip6_address_t *addr;

      if (!(pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_V6))
        {
          return 3;
        }
      addr = (pdr->pdi.ue_addr.flags & IE_UE_IP_ADDRESS_SD)
                 ? &ip6->dst_address
                 : &ip6->src_address;

        if (!upf_ip6_address_is_equal (&pdr->pdi.ue_addr.ip6, addr, pdr->pdi.ue_addr.prefix_delegation_length))
          {
            return 4;
          }
      }
  return 0;
}

always_inline void
upf_pdr_application_detection (vlib_main_t *vm, vlib_buffer_t *b,
                                      flow_entry_t *flow, struct rules *active,
                                      u8 is_ip4,
                                      u8 *flowcache_abort_learning_flag,
                                      u8 *http2_header)
{
  ip4_header_t *ip4 = (ip4_header_t *)(vlib_buffer_get_current (b) + upf_buffer_opaque (b)->upf.data_offset);
  upf_pdr_t *o_pdr;
  upf_pdr_t *pdr;

  u32 urr_id = ~0;
  u8 src_intf;
  u32 dpi_mix_id = 0;
  u16 master_id = 0;
  u16 sub_id = 0;
  upf_dpi_t *dpi = NULL;
  uword *p1;
  uword *p2;
  u32 appid_index = ~0;
  upf_app_id_pfd_ctx_t *pfd_list;
  upf_pfd_ctx_t *pfd;
  pkt_5tuple *pkt_info = NULL;
  pkt_5tuple t;

  u32 ret = 0;

  if (!(active->flags & SX_APP))
    return;

  if (upf_app_pdr_and_srcintf_get(active, upf_buffer_opaque (b)->upf.pdr_index, &o_pdr, &src_intf))
    {
      upf_debug ("upf_app_pdr_and_srcintf_get fail, pdr_index: %u\n", upf_buffer_opaque (b)->upf.pdr_index);
      return;
    }
  upf_debug ("Old PDR: %u (idx %u) src_intf:%u\n", o_pdr->id, upf_buffer_opaque (b)->upf.pdr_index, src_intf);

  if (vnet_buffer2 (b)->__unused2[0])
    {
      dpi_mix_id = vnet_buffer2 (b)->__unused2[0];
      master_id = dpi_mix_id >> 16;
      sub_id = dpi_mix_id & 0xff;
      upf_debug ("dpi master_id=%d, sub_id=%d\n", master_id, sub_id);
      p1 = hash_get (g_upf_main.dpi_index_by_id, master_id);
      p2 = hash_get (g_upf_main.dpi_index_by_id, sub_id);

      if (p1)
        {
          if (PREDICT_TRUE(!pool_is_free_index (g_upf_main.dpi, p1[0])))
            {
              dpi = pool_elt_at_index (g_upf_main.dpi, p1[0]);
              upf_debug ("hit the dpi_name %v\n", dpi->name);
            }
        }
      else if (p2)
        {
          if (PREDICT_TRUE(!pool_is_free_index (g_upf_main.dpi, p2[0])))
            {
              dpi = pool_elt_at_index (g_upf_main.dpi, p2[0]);
              upf_debug ("hit the dpi_name %v\n", dpi->name);
            }
        }
    }

  /*
   * see 3GPP TS 23.214 Table 5.2.2-1 for valid ADR combinations
   */
  vec_pdr_foreach(pdr, &active->pdr_edge[src_intf], active->pdr)
  {
    if (pdr->id == o_pdr->id)
        break;

    upf_debug ("try match PDR %u for URL detect\n", pdr->id);
    if (pdr->precedence > o_pdr->precedence)
      {
        upf_debug ("break because of lower precedence in PDR %u\n", pdr->id);
        break;
      }
    if (!(pdr->pdi.fields & F_PDI_APPLICATION_ID))
      {
        upf_debug ("skip PDR %u for no appid\n", pdr->id);
        continue;
      }

    if ((pdr->pdi.fields & F_PDI_UE_IP_ADDR))
      {
        u32 r = upf_app_ueip_check(pdr, is_ip4, (u8 *)ip4);
        if (r)
          {
            upf_debug ("skip PDR %u for UE IP check, r:%u\n", pdr->id, r);
            continue;
          }
      }

    if (NOT_MATCH == match_pdi_misc(pdr, b))
      {
        upf_debug ("skip PDR %u for pdi misc match fail\n", pdr->id);
        continue;
      }

    if (vec_len(pdr->pdi.acl))
      {
        if (!pkt_info)
          {
            upf_get_ip_pkt_info(&t, b);
            pkt_info = &t;
          }

        if (!match_ip_acl(pdr, pkt_info))
          {
            upf_debug ("skip PDR %u for acl match fail\n", pdr->id);
            continue;
          }
      }

    if (dpi && (pdr->pdi.app_index != ~0))
      {
        CHECK_POOL_IS_VALID_CONTINUE(g_upf_main.pfd_list, pdr->pdi.app_index);
        pfd_list = pool_elt_at_index (g_upf_main.pfd_list, pdr->pdi.app_index);
        vec_foreach (pfd, pfd_list->pfd_contents)
        {
          if ((pfd->flags == F_PFD_C_CP) && vec_len (pfd->ct) &&
              vec_is_equal (dpi->name, pfd->ct))
            {
              appid_index = pdr->pdi.app_index;
              upf_debug ("hit pure dpi in appid, idx:%d", appid_index);
              goto HIT;
            }
        }
      }

	/*	 liukang change 2021/07/22
    if (flow->key.proto == IP_PROTOCOL_ICMP && is_ip4)
    {
        char hostname[64], tmp[1024];
        memset(hostname, 0, sizeof(hostname));
        memset(tmp, 0, sizeof(tmp));
        upf_dns_sniffer_get_name_by_ip(ip4->dst_address.data_u32, hostname);
        sprintf(tmp, " /tsHost: %s", hostname);
        appid_index = upf_pdr_appid_lookup (pdr, (u8 *)tmp, strlen(tmp)+1, &urr_id, 
                                        dpi ? dpi->name : NULL);
    }
    else
    {
        if (http2_header)
          appid_index = upf_pdr_appid_lookup (pdr, http2_header,
                                          vlib_buffer_length_in_chain (vm, b), &urr_id, 
                                          dpi ? dpi->name : NULL);
        else
          appid_index = upf_pdr_appid_lookup (pdr, vlib_buffer_get_current (b),
                                          vlib_buffer_length_in_chain (vm, b), &urr_id, 
                                          dpi ? dpi->name : NULL);
    }
  */
	  //begin liukang add for v6 fd detect 2021/07/22
	  if (is_ip4)
	  {
		  if(flow->key.proto == IP_PROTOCOL_ICMP)
		  {
			  char hostname[64], tmp[1024];
			  memset(hostname, 0, sizeof(hostname));
			  memset(tmp, 0, sizeof(tmp));
			  upf_dns_sniffer_get_name_by_ip(ip4->dst_address.data_u32, hostname);
			  sprintf(tmp, " /tsHost: %s", hostname);
			  appid_index = upf_pdr_appid_lookup (pdr, (u8 *)tmp, strlen(tmp)+1, &urr_id, 
											  dpi ? dpi->name : NULL);
		  }
		  else
		  {
			  if (http2_header)
				appid_index = upf_pdr_appid_lookup (pdr, http2_header,
												vlib_buffer_length_in_chain (vm, b), &urr_id, 
												dpi ? dpi->name : NULL);
			  else
				appid_index = upf_pdr_appid_lookup (pdr, vlib_buffer_get_current (b),
												vlib_buffer_length_in_chain (vm, b), &urr_id, 
												dpi ? dpi->name : NULL);
		  }
	  }
	  else
	  {
		  ret = upf_appid_lookup_v6 (pdr, b, &urr_id, dpi ? dpi->name : NULL);

          if(ret > 0)
          {
              if (http2_header)
    				appid_index = upf_pdr_appid_lookup (pdr, http2_header,
    												vlib_buffer_length_in_chain (vm, b), &urr_id, 
    												dpi ? dpi->name : NULL);
    			  else
    				appid_index = upf_pdr_appid_lookup (pdr, vlib_buffer_get_current (b),
    												vlib_buffer_length_in_chain (vm, b), &urr_id, 
    												dpi ? dpi->name : NULL);
          }
	  }
	  //end liukang add for v6 fd detect 2021/07/22


  HIT:
    if (appid_index != ~0)
      {
        upf_app_id_pfd_ctx_t *app;
        CHECK_POOL_IS_VALID_CONTINUE(g_upf_main.pfd_list, appid_index);
        app = pool_elt_at_index (g_upf_main.pfd_list, appid_index);
        u32 dns_sniffer = 1;
        /*begin add by huqingyang 2021.5.26 for https dns-sniffer*/
        char acHosName[256] = {0};
        u32 uiRet  = 0;
        if (app && app->dns_sniffer_switch)
        {
            upf_pfd_ctx_t *upc = app->pfd_contents;
            if (upc)
            {
                /*begin add by huqingyang 2021.4.30 for dns Sniffer  */
                if (is_ip4)
                {
                    uiRet = upf_dns_sniffer_get_name_by_url((char *)upc->url,acHosName);
                    if (uiRet != 0)
                    {
                        upf_err("upf_dns_sniffer_get_name_by_url is err ");
                    }
                
                    uiRet = upf_dns_sniffer_if_catch_by_name_addr(acHosName, ip4->dst_address.data_u32, &dns_sniffer);
                    if (uiRet != 0)
                    {
                        upf_err("upf_dns_sniffer_if_catch_by_name_addr is err ");
                    }
                }
                /*end add by huqingyang 2021.4.30 for dns Sniffe */
            }
            if (dns_sniffer)
            {
                *flowcache_abort_learning_flag = 0;
                o_pdr = pdr;
                break;
            }
            continue;
        }
        /*end add by huqingyang 2021.5.26 for https dns-sniffer  */
        *flowcache_abort_learning_flag = 0;
        o_pdr = pdr;
        break;
      }
	  else
	  {
	      upf_err("appid index is null");
	  }
  }
  upf_buffer_opaque (b)->upf.pdr_index = o_pdr - active->pdr;
  if ((o_pdr->pdi.fields & F_PDI_APPLICATION_ID))
  {
      flow->application_id = appid_index;
      flow->urr_id = urr_id;
  }

  upf_debug ("New PDR: %u (idx %u)\n", o_pdr->id,
             upf_buffer_opaque (b)->upf.pdr_index);

  return;
}


always_inline void
upf_ethernet_pdr_application_detection (vlib_main_t *vm, vlib_buffer_t *b,
                                    flow_entry_t *flow, struct rules *active,
                                    u8 is_ip_pkt,
                                    u8 is_ip4,
                                    u8 *flowcache_abort_learning_flag,
                                    u8 *http2_header)
{
    ip4_header_t *ip4 = (ip4_header_t *)(vlib_buffer_get_current (b) +
                                     upf_buffer_opaque (b)->upf.data_offset + vnet_buffer (b)->l2.l2_len);
    ip6_header_t *ip6 = (ip6_header_t *)ip4;
    upf_pdr_t *o_pdr;
    upf_pdr_t *pdr;

    u32 urr_id = ~0;
    u8 src_intf;
    u32 dpi_mix_id = 0;
    u16 master_id = 0;
    u16 sub_id = 0;
    upf_dpi_t *dpi = NULL;
    uword *p1;
    uword *p2;
    u32 appid_index = ~0;
    upf_app_id_pfd_ctx_t *pfd_list;
    upf_pfd_ctx_t *pfd;

    if (!(active->flags & SX_APP))
        return;

    if (upf_app_pdr_and_srcintf_get(active, upf_buffer_opaque (b)->upf.pdr_index, &o_pdr, &src_intf))
      {
        upf_debug ("upf_app_pdr_and_srcintf_get fail, pdr_index: %u\n", upf_buffer_opaque (b)->upf.pdr_index);
        return;
      }
    upf_debug ("Old PDR: %u (idx %u) src_intf:%u\n", o_pdr->id, upf_buffer_opaque (b)->upf.pdr_index, src_intf);

    if (vnet_buffer2 (b)->__unused2[0])
    {
        dpi_mix_id = vnet_buffer2 (b)->__unused2[0];
        master_id = dpi_mix_id >> 16;
        sub_id = dpi_mix_id & 0xff;
        upf_debug ("dpi master_id=%d, sub_id=%d\n", master_id, sub_id);
        p1 = hash_get (g_upf_main.dpi_index_by_id, master_id);
        p2 = hash_get (g_upf_main.dpi_index_by_id, sub_id);

        if (p1)
        {
            if (PREDICT_TRUE(!pool_is_free_index (g_upf_main.dpi, p1[0])))
            {
                dpi = pool_elt_at_index (g_upf_main.dpi, p1[0]);
                upf_debug ("hit the dpi_name %v\n", dpi->name);
            }
        }
        else if (p2)
        {
            if (PREDICT_TRUE(!pool_is_free_index (g_upf_main.dpi, p2[0])))
            {
                dpi = pool_elt_at_index (g_upf_main.dpi, p2[0]);
                upf_debug ("hit the dpi_name %v\n", dpi->name);
            }
        }
    }

    /*
     * see 3GPP TS 23.214 Table 5.2.2-1 for valid ADR combinations
     */
    vec_pdr_foreach(pdr, &active->pdr_edge[src_intf], active->pdr)
    {
        if (pdr->id == o_pdr->id)
            break;

        upf_debug ("try match PDR %u for URL detect\n", pdr->id);
        if (pdr->precedence > o_pdr->precedence)
        {
            upf_debug ("break because of lower precedence in PDR %u\n", pdr->id);
            break;
        }
        if (!(pdr->pdi.fields & F_PDI_APPLICATION_ID))
        {
            upf_debug ("skip PDR %u for no appid\n", pdr->id);
            continue;
        }

        if (NOT_MATCH == match_pdi_misc(pdr, b))
          {
            upf_debug ("skip PDR %u for pdi misc match fail\n", pdr->id);
            continue;
          }

        if (dpi && (pdr->pdi.app_index != ~0))
        {
            CHECK_POOL_IS_VALID_CONTINUE(g_upf_main.pfd_list, pdr->pdi.app_index);
            pfd_list = pool_elt_at_index (g_upf_main.pfd_list, pdr->pdi.app_index);
            vec_foreach (pfd, pfd_list->pfd_contents)
            {
                if ((pfd->flags == F_PFD_C_CP) && vec_len (pfd->ct) && vec_is_equal (dpi->name, pfd->ct))
                {
                    appid_index = pdr->pdi.app_index;
                    upf_debug ("hit pure dpi in appid, idx:%d", appid_index);
                    goto HIT;
                }
                if((pfd->flags == F_PFD_C_CP) && vec_len (pfd->ct) && (strncmp((char *)pfd->ct,"icmpv6-135",10) == 0))
                {
                    if(!is_ip4)
                    {
                        if(ip6->protocol == 58)
                        {
                            icmp46_header_t *icmp0;
                            icmp6_type_t type0;
                            icmp0 = ip6_next_header (ip6);
                            type0 = icmp0->type;
                            if(type0 == ICMP6_neighbor_solicitation)
                            {
                                appid_index = pdr->pdi.app_index;

                                if(pdr->pdi.src_intf == SRC_INTF_ACCESS)
                                {
                                    u32 nwi = upf_get_n6_nwi_by_sess_idx(upf_buffer_opaque (b)->upf.session_index);
                                    make_ip6_mac_address(b,nwi);
                                    
                                }
                                upf_debug ("hit pure dpi in appid, idx:%d", appid_index);
                                goto HIT;
                            }
                        }
                    }
                    
                }
            }
        }

        if (is_ip4)
        {
            if(flow->key.proto == IP_PROTOCOL_ICMP)
            {
                char hostname[64], tmp[1024];
                memset(hostname, 0, sizeof(hostname));
                memset(tmp, 0, sizeof(tmp));
                upf_dns_sniffer_get_name_by_ip(ip4->dst_address.data_u32, hostname);
                sprintf(tmp, " /tsHost: %s", hostname);
                appid_index = upf_pdr_appid_lookup (pdr, (u8 *)tmp, strlen(tmp)+1, &urr_id, 
                                            dpi ? dpi->name : NULL);
            }
            else
            {
                if (http2_header)
                    appid_index = upf_pdr_appid_lookup (pdr, http2_header,
                                              vlib_buffer_length_in_chain (vm, b), &urr_id, 
                                              dpi ? dpi->name : NULL);
                else
                    appid_index = upf_pdr_appid_lookup (pdr, vlib_buffer_get_current (b),
                                              vlib_buffer_length_in_chain (vm, b), &urr_id, 
                                              dpi ? dpi->name : NULL);
            }
        }
        else
        {
            appid_index = upf_appid_lookup_v6 (pdr, b, &urr_id, dpi ? dpi->name : NULL);
        }
        //end liukang add for v6 fd detect 2021/07/22


HIT:
        if (appid_index != ~0)
        {
            o_pdr = pdr;
            break;
        }
    }
    upf_buffer_opaque (b)->upf.pdr_index = o_pdr - active->pdr;
    if ((o_pdr->pdi.fields & F_PDI_APPLICATION_ID))
    {
        flow->application_id = appid_index;
        flow->urr_id = urr_id;
    }

    upf_debug ("New PDR: %u (idx %u)\n", o_pdr->id,
           upf_buffer_opaque (b)->upf.pdr_index);

    return;
}


always_inline void
upf_pdr_application_detection_dns (vlib_main_t *vm, vlib_buffer_t *b,
                                          struct rules *active, u8 is_eth, u8 is_ip4)
{
  u32 offs = upf_buffer_opaque (b)->upf.data_offset;
  upf_pdr_t *adr;
  upf_pdr_t *pdr;
  u8 src_intf;
  u32 urr_id = ~0;
  pkt_5tuple *pkt_info = NULL;
  pkt_5tuple t;

  if (!(active->flags & SX_APP))
    return;

  if (is_eth)
    {
      offs += vnet_buffer (b)->l2.l2_len;
    }

  if (upf_app_pdr_and_srcintf_get(active, upf_buffer_opaque (b)->upf.pdr_index, &adr, &src_intf))
    {
      upf_debug ("upf_app_pdr_and_srcintf_get fail, pdr_index: %u\n", upf_buffer_opaque (b)->upf.pdr_index);
      return;
    }
  upf_debug ("Old PDR: %u (idx %u) src_intf:%u\n", adr->id, upf_buffer_opaque (b)->upf.pdr_index, src_intf);

  /*
   * see 3GPP TS 23.214 Table 5.2.2-1 for valid ADR combinations
   */
  vec_pdr_foreach(pdr, &active->pdr_edge[src_intf], active->pdr)
  {
    if (!(pdr->pdi.fields & F_PDI_APPLICATION_ID))
      {
        upf_debug ("skip PDR %u for no ADR\n", pdr->id);
        continue;
      }

    if ((pdr->pdi.fields & F_PDI_UE_IP_ADDR))
    {
        u32 r = upf_app_ueip_check(pdr, is_ip4, (u8 *)(vlib_buffer_get_current (b) + offs));
        if (r)
        {
            upf_debug ("skip PDR %u for UE IP check, r:%u\n", pdr->id, r);
            continue;
        }
    }

    if (NOT_MATCH == match_pdi_misc(pdr, b))
      {
        upf_debug ("skip PDR %u for pdi misc match fail\n", pdr->id);
        continue;
      }

    if (vec_len(pdr->pdi.acl))
      {
        if (!pkt_info)
          {
            upf_get_ip_pkt_info(&t, b);
            pkt_info = &t;
          }

        if (!match_ip_acl(pdr, pkt_info))
          {
            upf_debug ("skip PDR %u for acl match fail\n", pdr->id);
            continue;
          }
      }

    // u32 appid_index = upf_pdr_appid_lookup (pdr,url, vec_len (url));
    u32 appid_index = upf_pdr_appid_lookup (pdr, vlib_buffer_get_current (b),
                                        vlib_buffer_length_in_chain (vm, b), &urr_id, NULL);

    if (appid_index != ~0)
      {
        /*begin add by huqingyang 2021.7.1 for dns appid*/
        upf_app_id_pfd_ctx_t *app;
        CHECK_POOL_IS_VALID_CONTINUE(g_upf_main.pfd_list, appid_index);
        app = pool_elt_at_index (g_upf_main.pfd_list, appid_index);
        if (app && app->dns_sniffer_switch)
        {
            continue;
        }
        /*end add by huqingyang 2021.7.1 for dns appid  */
        adr = pdr;
        break;
      }
  }
  upf_buffer_opaque (b)->upf.pdr_index = adr - active->pdr;

  upf_debug ("New PDR: %u (idx %u)\n", adr->id,
             upf_buffer_opaque (b)->upf.pdr_index);

  // vec_free (url);

  return;
}

always_inline void
upf_pdr_get_application_rule (vlib_main_t *vm, vlib_buffer_t *b,
                                     flow_entry_t *flow, struct rules *active,
                                     u8 is_eth,
                                     u8 is_ip4,
                                     u8 *flowcache_abort_learning_flag)
{
  upf_pdr_t *adr;
  upf_pdr_t *pdr;
  u32 offs = upf_buffer_opaque (b)->upf.data_offset;

  u8 src_intf;
  if (upf_app_pdr_and_srcintf_get(active, upf_buffer_opaque (b)->upf.pdr_index, &adr, &src_intf))
    {
      upf_debug ("upf_app_pdr_and_srcintf_get fail, pdr_index: %u\n", upf_buffer_opaque (b)->upf.pdr_index);
      return;
    }
  upf_debug ("Old PDR: %u (idx %u) src_intf:%u\n", adr->id, upf_buffer_opaque (b)->upf.pdr_index, src_intf);

  pkt_5tuple *pkt_info = NULL;
  pkt_5tuple t;

  vec_pdr_foreach(pdr, &active->pdr_edge[src_intf], active->pdr)
  {
    if (pdr->id == adr->id)
        break;

    if (!is_eth && (pdr->pdi.fields & F_PDI_UE_IP_ADDR))
    {
        u32 r = upf_app_ueip_check(pdr, is_ip4, (u8 *)(vlib_buffer_get_current (b) + offs));
        if (r)
        {
            upf_debug ("skip PDR %u for UE IP check, r:%u\n", pdr->id, r);
            continue;
        }
    }

    if (NOT_MATCH == match_pdi_misc(pdr, b))
      {
        upf_debug ("skip PDR %u for pdi misc match fail\n", pdr->id);
        continue;
      }

    if (vec_len(pdr->pdi.acl))
      {
        if (!pkt_info)
          {
            upf_get_ip_pkt_info(&t, b);
            pkt_info = &t;
          }

        if (!match_ip_acl(pdr, pkt_info))
          {
            upf_debug ("skip PDR %u for acl match fail\n", pdr->id);
            continue;
          }
      }

    if ((pdr->pdi.fields & F_PDI_APPLICATION_ID) &&
        (pdr->precedence < adr->precedence) &&
        (pdr->pdi.app_index == flow->application_id))
      adr = pdr;

    if (pdr->activate_predefined_rules)
    {
        predef_rule_t *pr;
        predef_appid_t *pr_appid;    
        if(g_upf_rg_switch)
        {
            predef_rule_group_t *pre_rule_group;
            predef_rule_name_t *rule_name;
            uword *p = hash_get_mem (g_upf_main.hash_rule_id_by_rule_group, pdr->activate_predefined_rules);
            if (p)
            {
                CHECK_POOL_IS_VALID_CONTINUE(g_upf_main.pre_rule_group, p[0]);
                pre_rule_group = pool_elt_at_index (g_upf_main.pre_rule_group, p[0]);
                pool_foreach(rule_name, pre_rule_group->rule_names, ({
                    uword *p1 = hash_get_mem (g_upf_main.hash_rule_id_by_rule_name, rule_name->rule_name);
                    if (p1)
                    {
                      CHECK_POOL_IS_VALID_CONTINUE(g_upf_main.pre_rule, p1[0]);
                      pr = pool_elt_at_index (g_upf_main.pre_rule, p1[0]);
                      pool_foreach(pr_appid, pr->app_ids, ({
                          if (pr_appid->appid_index == flow->application_id && (pdr->precedence < adr->precedence))
                          {
                              adr = pdr;
                              break;
                          }
                      }));
                    }
                }));
            }
        }
        else
        {
            uword *p = hash_get_mem (g_upf_main.hash_rule_id_by_rule_name, pdr->activate_predefined_rules);
            if (p)
            {
                CHECK_POOL_IS_VALID_CONTINUE(g_upf_main.pre_rule, p[0]);
                pr = pool_elt_at_index (g_upf_main.pre_rule, p[0]);
                pool_foreach(pr_appid, pr->app_ids, ({
                    if (pr_appid->appid_index == flow->application_id && (pdr->precedence < adr->precedence))
                    {
                        adr = pdr;
                        break;
                    }
                  }));
            }

        }
    }
  }
  upf_buffer_opaque (b)->upf.pdr_index = adr - active->pdr;
  if ((adr->pdi.fields & F_PDI_APPLICATION_ID))
    flow->application_id = adr->pdi.app_index;

  upf_debug ("New PDR: %u (idx %u)\n", adr->id,
             upf_buffer_opaque (b)->upf.pdr_index);

  /* switch return traffic to processing node */
  *flowcache_abort_learning_flag = 0;
}

u8
upf_flowtable_update_timestamp_check (flow_entry_t *flow, u32 current_time)
{
  if ((1 == flow->is_update) && (current_time - flow->update_timestamp < 5))
    {
      upf_trace ("flow reset");
      upf_flowtable_reset (flow);
      return 1;
    }
  flow->is_update = 0;
  return 0;
}

int
upf_flow_id_related_to_pdr (upf_session_t *sess, u32 pdr_index, u32 flow_index,
                        vlib_buffer_t *b0, u8 is_reverse)
{
  upf_pdr_t *pdr = NULL, *hash_pdr = NULL;
  uword *p;
  struct rules *active = upf_get_rules (sess, SX_ACTIVE);

  if (pdr_index >= vec_len (active->pdr))
    {
      upf_err ("pdr index: 0x%x in session: 0x%lx(up_seid) error.\n",
               pdr_index, sess->up_seid);
      return -1;
    }
  pdr = active->pdr + pdr_index;
  clib_spinlock_lock (&sess->lock);
  p = hash_get (sess->hash_pdr_id_by_flow_id[is_reverse], flow_index);

  if (p)
    {
      if (pdr_index != p[0] && ((p[0]) < vec_len (active->pdr)))
        {
          hash_pdr = active->pdr + p[0];
          upf_debug ("Move the flow table index: 0x%x "
                     "from pdr:0x%x to pdr:0x%x in session: 0x%lx(up_seid)\n",
                     flow_index, hash_pdr->id, pdr->id, sess->up_seid);
          u32 index;
          if (~0 !=
              (index = vec_search (hash_pdr->flow_table_ids, flow_index)))
            vec_del1 (hash_pdr->flow_table_ids, index);

          if (vec_len (pdr->flow_table_ids) > UPF_MAX_FLOWS_PER_PDR)
            {
              upf_debug ("Number of flows per PDR: 0x%x in session: "
                         "0x%lx(up_seid) is more than "
                         "maximum: %d, abort flowcache learning!",
                         pdr->id, sess->up_seid, UPF_MAX_FLOWS_PER_PDR);
              upf_flowcache_abort_learning (b0);
            }
          if (~0 == (index = vec_search (pdr->flow_table_ids, flow_index)))
              vec_add1 (pdr->flow_table_ids, flow_index);
        }
    }
  else
    {
     upf_debug ("pdr_index:%u "
                "0x%lx(up_seid) "
                "flow_index %u",
                pdr_index, sess->up_seid, flow_index);

      hash_set (sess->hash_pdr_id_by_flow_id[is_reverse], flow_index,
                pdr_index);
      if (vec_len (pdr->flow_table_ids) > UPF_MAX_FLOWS_PER_PDR)
        {
          upf_debug ("Number of flows per PDR: 0x%x in session: "
                     "0x%lx(up_seid) is more than "
                     "maximum: %d, abort flowcache learning!",
                     pdr->id, sess->up_seid, UPF_MAX_FLOWS_PER_PDR);
          upf_flowcache_abort_learning (b0);
        }
      vec_add1 (pdr->flow_table_ids, flow_index);
    }

  clib_spinlock_unlock (&sess->lock);
  return 0;
}

extern int upf_tcp_reassemble;

/*add for dns sniffer begin by lixiao*/

//dns_rule_key g_rule[50]={0};
always_inline int
upf_pkt_is_dns_resp (vlib_buffer_t *b, struct rules *active, u8 is_ip4)
{
  u32 offs = upf_buffer_opaque (b)->upf.data_offset;
  ip4_header_t *ip4 = NULL;
  ip6_header_t *ip6 = NULL;
  udp_header_t *udp;

  /*if (!(active->flags & SX_APP))
    return 0;*/

  if (is_ip4)
    {
      ip4 = (ip4_header_t *)(vlib_buffer_get_current (b) + offs);
      udp = ip4_next_header (ip4);
    }
  else
    {
      ip6 = (ip6_header_t *)(vlib_buffer_get_current (b) + offs);
      udp = ip6_next_header (ip6);
    }
  if (udp->src_port == 0x3500) // dns port 53
    return 1;
  else
    return 0;
}

static inline u16 dns_labels_to_name(char* pLables)
{
    u8 ucLen;
    u8 ucLableCnt = 0;
    u16 usLen = 0;
    char *pPos = pLables;

    ucLen = *pPos++;
    while(ucLen)
    {
        *(pPos - 1) = '.';
        pPos += ucLen;
        usLen += ucLen;
        ucLen = *pPos++;
        ucLableCnt++;
    }
    usLen += ucLableCnt - 1;
    memmove(pLables, pLables+1, usLen + 1);
    return usLen;
}

static inline void dns_rr_to_whitelist(const dns_rr_t *pstRR, dns_rule_rr_t *dns_sniffer_rule, int cnt)
{
    u32 uip;
    ip46_address_t ip = {0};
    memcpy(&uip,pstRR->rdata,clib_net_to_host_u16(pstRR->rdlength));
    if(dns_sniffer_rule && uip)
    {
        dns_sniffer_rule->uip[cnt] = uip;
        upf_debug ("dns sniffer dns_sniffer_rule->uip[%d]=%d,dns_sniffer_rule->ulExpire=%lld\n", cnt,dns_sniffer_rule->uip[cnt],dns_sniffer_rule->ulExpire);
        //dns_rr_write_to_whitelist(uip);
        ip.ip4.data_u32=uip;
        //upf_whitelist_upf_ip_add_del(&ip,F_WHITELIST_IP,NULL,NULL,NULL,0,NULL,1);
    }
}

int dns_resp_pkt_parse(vlib_buffer_t *b, u8 is_ip4)
{
    u32 offs = upf_buffer_opaque (b)->upf.data_offset;
    ip4_header_t *ip4 = NULL;
    ip6_header_t *ip6 = NULL;
    dns_header_t  *pstDnsHdr;
    int i;
    dns_rule_key_t stKey;
    dns_rr_t *pstRR;
    uword *p=NULL;
    dns_rule_rr_t *dns_sniffer_rule;
    /*if (!(active->flags & SX_APP))
    return 0;*/

    if (is_ip4)
    {
        ip4 = (ip4_header_t *)(vlib_buffer_get_current (b) + offs);
        pstDnsHdr = ip4_next_header (ip4);

        //begin liukang add for trusted dns server 2021/04/01
        upf_main_t *gtm = &g_upf_main;
        //dns_server_s *dns_server;
        dns_server_key_s dns_server_key = {0};
        dns_server_key.server_ip = ip4->src_address.as_u32;
        p = hash_get_mem (gtm->dns_server_index, &dns_server_key);
        if (!p)
        {
            return 0;
        }
        //end liukang add for trusted dns server 2021/04/01
    }
    else
    {
        ip6 = (ip6_header_t *)(vlib_buffer_get_current (b) + offs);
        pstDnsHdr = ip6_next_header (ip6);
    }

    pstDnsHdr = (dns_header_t *)((u8 *)pstDnsHdr +sizeof(udp_header_t));
    u8 *pPos = (u8 *)(pstDnsHdr +1);
    u8 *pPosTmp;
    //for(i=0;i<clib_net_to_host_u16(pstDnsHdr->qdcount);i++)
    //{
        pPosTmp = pPos;

        while(*pPos) pPos++;

        if(PREDICT_FALSE(pPos - pPosTmp > HOSTNAME_LEN))
        {
            upf_err ("dns sniffer stKey.acHost too long\n");
            return -1;
        }
        memset(&stKey, 0, sizeof(stKey));
        memcpy(stKey.acHost, pPosTmp, pPos - pPosTmp);
        dns_labels_to_name(stKey.acHost);
        p = hash_get_mem (g_dns_sniffer_rule_hash, &stKey);
        if(!p)
        {
            return 0;
        }
        else 
        {
            CHECK_POOL_IS_VALID_RET(g_dns_sniffer_rule, p[0], 0);
            dns_sniffer_rule = pool_elt_at_index (g_dns_sniffer_rule, p[0]);
            if(dns_sniffer_rule->uip[0])
            {
                return 0;
            }
            dns_sniffer_rule->ulExpire = rte_get_timer_cycles() + rte_get_timer_hz() * 60 * g_dns_ageing_ttl;
            for(i=0;i<clib_net_to_host_u16(pstDnsHdr->anscount);i++)
            {
                if((pPos[0] & 0xC0) != 0xC0)
                {
                    return 0;
                }
                pPos += 2;

                pstRR = (dns_rr_t *)pPos;
                if(clib_net_to_host_u16(pstRR->type) == DNS_TYPE_A && clib_net_to_host_u16(pstRR->class)==DNS_CLASS_IN)
                {
                    dns_rr_to_whitelist(pstRR,dns_sniffer_rule,i);
                }
                pPos += (sizeof(dns_rr_t)+clib_net_to_host_u16(pstRR->rdlength));
            }
            #if 0
            u32 sidx = upf_buffer_opaque (b)->upf.session_index;
            upf_session_t *sess = pool_elt_at_index (g_upf_main.sessions, sidx);
            upf_pfcp_events_publish (PFCP_RPC_PUBLISH_DNS_SNIFFER, sess,
                                     &dns_sniffer_rule);
            #endif
        }
    //    pPos += (sizeof(dns_query_t)+1);
    //}
    upf_debug ("dns sniffer stKey.acHost=%s\n", stKey.acHost);
    //rte_hash_del_key(url_h,(void *)stKey.acHost);
    return 0;
}

static clib_error_t *
dns_sniffer_cmd (vlib_main_t * vm,
		unformat_input_t * main_input, vlib_cli_command_t * cmd)
{
    unformat_input_t _line_input, *line_input = &_line_input;
    int j;
    char url[256]={0};
    uword *p = NULL;
    dns_rule_rr_t *dns_sniffer_rule;
    dns_rule_key_t dns_sniffer_key={0};

    /* Get a line of input. */
    if (!unformat_user (main_input, unformat_line_input, line_input))
      return 0;

    while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
    {
        if (unformat (line_input, "url %s", url))
        {
            strncpy(dns_sniffer_key.acHost,url,HOSTNAME_LEN);
            p = hash_get_mem (g_dns_sniffer_rule_hash, &dns_sniffer_key);
            if(!p)
            {
                pool_get(g_dns_sniffer_rule, dns_sniffer_rule);
                strncpy(dns_sniffer_rule->dns_rule_key.acHost, url, HOSTNAME_LEN);
                hash_set_mem_alloc (&g_dns_sniffer_rule_hash, &dns_sniffer_rule->dns_rule_key, dns_sniffer_rule - g_dns_sniffer_rule);
            }
        }
        else if (unformat (line_input, "ttl %d", &g_dns_ageing_ttl))
	    ;
        else if (unformat (line_input, "s"))
        {
            pool_foreach (dns_sniffer_rule, g_dns_sniffer_rule, ({
              if (dns_sniffer_rule->dns_rule_key.acHost[0] )
              {
                  vlib_cli_output (vm, "Hostname:%s,AGING TIME is %s minus",dns_sniffer_rule->dns_rule_key.acHost,g_dns_ageing_ttl);
                  for(j=0;j<MAX_IP_CNT;j++)
                  {
                     if(dns_sniffer_rule->uip[j])
                     {
                         vlib_cli_output (vm, "ip:%d ",j,dns_sniffer_rule->uip[j]);
                     }
                  }
                  vlib_cli_output(vm,"\n");
              }
            }));

        }
        /*else if (unformat (line_input, "add"))
	        is_del = 0;*/
        else
	        return (clib_error_return (0, "unknown input '%U'",
				   format_unformat_error, line_input));
    }
    unformat_free (line_input);
    return (NULL);
}

VLIB_CLI_COMMAND (dns_sniffer, static) = {
  .path = "dns sniffer",
  .function = dns_sniffer_cmd,
  .short_help = "dns sniffer [add|del] url <url> ttl <ttl>",
  .is_mp_safe = 1,
};
/*add for dns sniffer end by lixiao*/

static uword
upf_pdr_detect_ip (vlib_main_t *vm, vlib_node_runtime_t *node,
                vlib_frame_t *frame, int is_ip4)
{
    u32 *to_next, n_left_to_next;
    u32 *from = vlib_frame_vector_args (frame);
    u32 n_left_from = frame->n_vectors;
    u32 next_index = node->cached_next_index;
    u32 current_time = (u32)vlib_time_now (vm);

#define _(sym, str) u64 PKTS_##sym = 0;
      foreach_pdr_detect_error
#undef _

    while (n_left_from > 0)
    {
        vlib_get_next_frame (vm, node, next_index, to_next, n_left_to_next);
        while (n_left_from > 0 && n_left_to_next > 0)
        {
            u32 next0 = PD_NEXT_DROP;
            upf_session_t *sess = NULL;
            u32 pdr_idx = ~0;
            struct rules *active = NULL;
            u8 is_forward = 0, is_reverse = 1, flowcache_abort_learning_flag = 1;
            u8 *headers = NULL;
            u32 bi0 = to_next[0] = from[0];
            vlib_buffer_t *b0 = vlib_get_buffer (vm, bi0);
            upf_buffer_opaque_t *opaque = upf_buffer_opaque (b0);
            u8 *data = (u8 *)(vlib_buffer_get_current (b0) + opaque->upf.data_offset);

            flow_entry_t *flow = flow_entry_get_by_index(g_flowtable_main.flows, opaque->upf.flow_index);
            if (!flow)
            {
                upf_err ("flow is null, upf.flow_index:%u!", opaque->upf.flow_index);
                UPF_STATISTICS_ADD(GET_NULL_FLOW);
                goto trace;
            }

            sess = sx_get_by_index(opaque->upf.session_index);
            if (PREDICT_FALSE(!sess) || PREDICT_FALSE (sess->flags & SX_DELETING))
            {
                upf_err ("session index %u may be invalid!", opaque->upf.session_index);
                UPF_STATISTICS_ADD(INVALID_SESS_INDEX);
                goto trace;
            }
            active = upf_get_rules (sess, SX_ACTIVE);

            if (g_urr_tcp_hand_switch && upf_pdr_is_tcp_hand_msg(b0))
            {
                upf_pdr_t *default_pdr = get_default_pdr(active, upf_buffer_opaque (b0)->upf.src_intf);

                if (default_pdr->pdi.qfi && upf_buffer_opaque (b0)->upf.gtp_flags.qfi != default_pdr->pdi.qfi)
                {
                    upf_trace ("default pdr qfi: %u, flow qfi: %u, set flow qfi by pdr qfi\n", default_pdr->pdi.qfi, upf_buffer_opaque (b0)->upf.gtp_flags.qfi);
                    upf_buffer_opaque (b0)->upf.gtp_flags.qfi = default_pdr->pdi.qfi;
                }
            }

            if (PREDICT_FALSE (b0->flags & VNET_BUFFER_F_LOCALLY_ORIGINATED))
            {
                /* comes from local */
                clib_memset (opaque, 0, sizeof (upf_buffer_opaque_t));
                opaque->upf.session_index = vnet_buffer (b0)->ip.adj_index[VLIB_TX];
                opaque->upf.pdr_index = ~0;
                opaque->upf.flow_index = ~0;
                opaque->upf.src_intf = SRC_INTF_CORE;
                opaque->upf.data_offset = 0;
                upf_debug ("this packet from local %p", b0);

                if (b0->flags & VNET_BUFFER_F_OFFLOAD_TCP_CKSUM)
                {
                    vnet_calc_checksums_inline (vm, b0, b0->flags & VNET_BUFFER_F_IS_IP4, b0->flags & VNET_BUFFER_F_IS_IP6);
                }

                sess = sx_get_by_index(opaque->upf.session_index);
                if (PREDICT_FALSE(!sess) || PREDICT_FALSE (sess->flags & SX_DELETING))
                {
                    upf_debug ("session index %u may be invalid!", opaque->upf.session_index);
                    goto trace;
                }
                active = upf_get_rules (sess, SX_ACTIVE);

                pdr_idx = search_ip_pdr (sess, active, b0, flow);

                if (pdr_idx != UPF_INVALID_INDEX)
                {
                    opaque->upf.pdr_index = pdr_idx;
                    next0 = PD_NEXT_PROCESS;
                }
                else
                {
                    upf_debug ("can not find the pdr\n");
                    UPF_STATISTICS_ADD(PDR_MATCH_FAILED);
                    UPF_PDU_SESS_STATISTICS_ADD(sess, PDR_MATCH_FAILED);
                }
                upf_flowcache_abort_learning (b0);
                goto trace;
            }

            u32 is_payload_v4 = is_v4_packet (data);
            if ((KEY_LOG_SWITCH (KL_SWITCH_PDR_DETECT_DNS))&& upf_pkt_is_dns_resp (b0, active, is_payload_v4))
            {
                dns_resp_pkt_parse(b0,is_payload_v4);
                if (0 != upf_dns_sniffer_hand_msg(b0,is_payload_v4))
                    upf_err ("upf_dns_sniffer_hand_msg  return err");
            }

            is_reverse = opaque->upf.is_reverse;
            is_forward = (is_reverse == flow->is_reverse) ? 1 : 0;
            opaque->upf.pdr_index = flow->pdr_index[is_reverse];
            upf_debug ("#0 is_rev %u, is_fwd %d, flow id: %u, pdr_idx: %u, flowcache_flag:%x, session index: %u, flow->app:%u\n",
                     is_reverse, is_forward, opaque->upf.flow_index, opaque->upf.pdr_index,
                     opaque->upf.flowcache_flag, opaque->upf.session_index, flow->application_id);

            if (PREDICT_FALSE (vnet_buffer (b0)->ip.reass.next_index) != 0)
            {
                /* comes from reassemble */
                upf_flowcache_abort_learning (b0);
                vlib_buffer_advance (b0, -opaque->upf.data_offset);
            }

            if(sess->handover_ongoing)
            {
                upf_trace("upf handover_ongoing! need to match pdr!");

                opaque->upf.pdr_index = ~0;
            }

            if ((opaque->upf.pdr_index == ~0) || (upf_flowtable_update_timestamp_check (flow, current_time)))
            {
                pdr_idx = search_ip_pdr (sess, active, b0, flow);
                if (pdr_idx == UPF_INVALID_INDEX)
                {
                    upf_err ("#1 can not find the pdr\n");
                    UPF_STATISTICS_ADD(PDR_MATCH_FAILED);
                    UPF_PDU_SESS_STATISTICS_ADD(sess, PDR_MATCH_FAILED);
                }
                else
                {
                    upf_debug ("#1 hit the pdr idx %d\n", pdr_idx);
                    opaque->upf.pdr_index = pdr_idx;
                    if (KEY_LOG_SWITCH (KL_SWITCH_PDR_DETECT_DNS) && upf_pdr_detect_dns (b0, active, is_payload_v4, IP_TRAFFIC))
                        upf_pdr_application_detection_dns (vm, b0, active, IP_TRAFFIC, is_payload_v4);
                }
            }
            else if (is_forward &&
                    (upf_pdr_is_http_request (vm, b0, flow, active, IP_TRAFFIC, is_payload_v4) ||
                    vnet_buffer2 (b0)->__unused2[0] ||
                    ((headers = upf_inflate_hd_func (flow, b0)) != NULL)))
            {
              upf_debug ("#2 Forward Flow app \n");
              upf_pdr_application_detection (vm, b0, flow, active, is_payload_v4,
                                                    &flowcache_abort_learning_flag, headers);
              if (headers)
                vec_free (headers);
            }
            else if (!is_forward && flow->application_id != ~0)
            {
                if (KEY_LOG_SWITCH (KL_SWITCH_PDR_DETECT_APPID))
                {
                    upf_debug ("#3 Reverse Flow and Appid_idx %u\n", flow->application_id);
                    upf_pdr_get_application_rule (vm, b0, flow, active, IP_TRAFFIC, is_payload_v4, &flowcache_abort_learning_flag);
                }
            }
            else if (flow->upf_learning_type == UPF_FLOW_LEARNING_TYPE_SINGLE && (flow->stats[0].bytes > 4096 || flow->stats[1].bytes > 4096))
            {
                /* stop flow classification after 4k in each direction */
                upf_debug ("####5glan Stopping PDR detect after 4k in each direction for flow id: 0x%x", opaque->upf.flow_index);
                flowcache_abort_learning_flag = 0;
            }
            else if (flow->stats[0].bytes > 4096 && flow->stats[1].bytes > 4096)
            {
                /* stop flow classification after 4k in each direction */
                upf_debug ("####Stopping PDR detect after 4k in each direction for flow id: 0x%x", opaque->upf.flow_index);
                flowcache_abort_learning_flag = 0;
            }

            if (1 == flowcache_abort_learning_flag)
                upf_flowcache_abort_learning (b0);

            if (opaque->upf.pdr_index != ~0)
            {
                next0 = PD_NEXT_PROCESS;
                PKTS_COUNTER++;
  
                if(!sess->handover_ongoing)
                {
                    flow->pdr_index[is_reverse] = opaque->upf.pdr_index;
                }
                else
                {
                    if(flow->pdr_index[0] != (u32)~0 || flow->pdr_index[1] != (u32)~0)
                    {
                        upf_trace("upf handover_ongoing! need to reset current flow!");
                        upf_flowtable_reset(flow);
                    }
                }

                upf_flow_id_related_to_pdr (sess, opaque->upf.pdr_index, opaque->upf.flow_index, b0, is_reverse);
            }
            else
            {
                PKTS_MISS++;
            }

trace:
            if (PREDICT_FALSE (b0->flags & VLIB_BUFFER_IS_TRACED))
            {
                upf_pdr_trace_t *tr = vlib_add_trace (vm, node, b0, sizeof (*tr));
                tr->up_seid = sess ? sess->up_seid : ~0;
                tr->pdr_idx = opaque->upf.pdr_index;
                tr->next = next0;
            }


          /* frame mgmt */
          from++;
          to_next++;
          n_left_from--;
          n_left_to_next--;
          vlib_validate_buffer_enqueue_x1 (vm, node, next_index, to_next,
                                           n_left_to_next, bi0, next0);
        }
      vlib_put_next_frame (vm, node, next_index, n_left_to_next);
    }

    u32 node_index = is_ip4 ? upf_pdr_detect_ip4_node.index : upf_pdr_detect_ip6_node.index;
#define _(sym, str)  vlib_node_increment_counter (vm, node_index, PDR_DETECT_ERROR_##sym, PKTS_##sym);
      foreach_pdr_detect_error
#undef _

  return frame->n_vectors;
}

static uword upf_pdr_detect_ethernet (vlib_main_t *vm, vlib_node_runtime_t *node, vlib_frame_t *frame)
{
    u32 n_left_from, *from, next_index, *to_next, n_left_to_next;
    from = vlib_frame_vector_args (frame);
    n_left_from = frame->n_vectors;
    next_index = node->cached_next_index;
    u32 pkts_counter = 0;
    u32 current_time = (u32)vlib_time_now (vm);

    while (n_left_from > 0)
    {
        vlib_get_next_frame (vm, node, next_index, to_next, n_left_to_next);
        while (n_left_from > 0 && n_left_to_next > 0)
        {
            u32 next0 = PD_NEXT_ETH_DROP;
            upf_session_t *sess = NULL;
            u32 pdr_idx = ~0;
            struct rules *active = NULL;
            u8 is_forward = 0, is_reverse = 1, flowcache_abort_learning_flag = 1;
            u8 *headers = NULL;
            u32 bi0 = to_next[0] = from[0];
            vlib_buffer_t *b0 = vlib_get_buffer (vm, bi0);
            upf_buffer_opaque_t *opaque = upf_buffer_opaque (b0);
            u8 *data = (u8 *)(vlib_buffer_get_current (b0) + opaque->upf.data_offset);

            flow_entry_t *flow = flow_entry_get_by_index(eth_flowtable_main.flows, opaque->upf.flow_index);
            if (!flow)
            {
                upf_debug ("flow is null, upf.flow_index:%u!", opaque->upf.flow_index);
                goto trace;
            }

            if (PREDICT_FALSE (b0->flags & VNET_BUFFER_F_LOCALLY_ORIGINATED))
            {
                /* comes from local */
                clib_memset (opaque, 0, sizeof (upf_buffer_opaque_t));
                opaque->upf.session_index = vnet_buffer (b0)->ip.adj_index[VLIB_TX];
                opaque->upf.pdr_index = ~0;
                opaque->upf.flow_index = ~0;
                opaque->upf.src_intf = SRC_INTF_CORE;
                opaque->upf.data_offset = 0;
                upf_debug ("this packet from local %p", b0);

                if (b0->flags & VNET_BUFFER_F_OFFLOAD_TCP_CKSUM)
                {
                    vnet_calc_checksums_inline (vm, b0, b0->flags & VNET_BUFFER_F_IS_IP4, b0->flags & VNET_BUFFER_F_IS_IP6);
                }

                sess = sx_get_by_index(opaque->upf.session_index);
                if (PREDICT_FALSE(!sess))
                {
                    upf_debug ("session index %u may be invalid!", opaque->upf.session_index);
                    goto trace;
                }
                active = upf_get_rules (sess, SX_ACTIVE);
                pdr_idx = search_eth_pdr (sess, active, b0, flow);
                if (pdr_idx != UPF_INVALID_INDEX)
                {
                    opaque->upf.pdr_index = pdr_idx;
                    next0 = PD_NEXT_ETH_PROCESS;
                }
                else
                {
                    upf_debug ("can not find the pdr\n");
                    UPF_STATISTICS_ADD(ETH_PDR_MATCH_FAILED);
                    UPF_PDU_SESS_STATISTICS_ADD(sess, ETH_PDR_MATCH_FAILED);
                }
                upf_flowcache_abort_learning (b0);
                goto trace;
            }

            sess = sx_get_by_index(opaque->upf.session_index);
            if (PREDICT_FALSE(!sess))
            {
                upf_debug ("session index %u may be invalid!", opaque->upf.session_index);
                goto trace;
            }
            active = upf_get_rules (sess, SX_ACTIVE);

            if (PREDICT_FALSE (sess->flags & SX_DELETING))
            {
                goto trace;
            }

            is_reverse = opaque->upf.is_reverse;
            is_forward = (is_reverse == flow->is_reverse) ? 1 : 0;
            if (flow->key.session_index == opaque->upf.session_index)
                opaque->upf.pdr_index = flow->pdr_index[is_reverse];
            else
                opaque->upf.pdr_index = flow->second_pdr_index[is_reverse];

            upf_debug ("is_rev %u, is_fwd %d, flow id: %u, pdr_idx: %u, flowcache_flag:%x, session index: %u, src_intf:%u\n",
                     is_reverse, is_forward, opaque->upf.flow_index, opaque->upf.pdr_index,
                     opaque->upf.flowcache_flag, opaque->upf.session_index,
                     opaque->upf.src_intf);

            u16 eth_type = upf_get_eth_type(data);
            u32 is_ip_pkt = 0;
            u32 is_payload_v4 = 0;
            if ((eth_type == ETHERNET_TYPE_IP4) || (eth_type == ETHERNET_TYPE_IP6))
            {
                 is_ip_pkt = 1;
                 is_payload_v4 = is_v4_packet (data +  vnet_buffer (b0)->l2.l2_len);
            }

            // if(sess->handover_ongoing)
            // {
            //     upf_trace("upf handover_ongoing! need to match pdr!");

            //     opaque->upf.pdr_index = ~0;
            // }

            if ((opaque->upf.pdr_index == ~0) || upf_flowtable_update_timestamp_check (flow, current_time))
            {
                pdr_idx = search_eth_pdr (sess, active, b0, flow);
                if (pdr_idx == UPF_INVALID_INDEX)
                {
                    upf_debug ("can not find the pdr\n");
                    UPF_STATISTICS_ADD(ETH_PDR_MATCH_FAILED);
                    UPF_PDU_SESS_STATISTICS_ADD(sess, ETH_PDR_MATCH_FAILED);
                }
                else
                {
                    upf_debug ("hit the pdr idx %d\n", pdr_idx);
                    opaque->upf.pdr_index = pdr_idx;
                    if (is_ip_pkt && upf_pdr_detect_dns (b0, active, ETH_TRAFFIC, is_payload_v4))
                        upf_pdr_application_detection_dns (vm, b0, active, ETH_TRAFFIC, is_payload_v4);
                    pkts_counter++;
                }
            }
            else if (is_forward &&
                     ((is_ip_pkt && upf_pdr_is_http_request (vm, b0, flow, active, ETH_TRAFFIC, is_payload_v4)) ||
                     vnet_buffer2 (b0)->__unused2[0] ||
                     ((headers = upf_inflate_hd_func (flow, b0)) != NULL)))
            {
                upf_debug ("Forward Flow app \n");
                upf_ethernet_pdr_application_detection (vm, b0, flow, active, is_ip_pkt, is_payload_v4,
                                                               &flowcache_abort_learning_flag, headers);
                if (headers)
                    vec_free (headers);
            }
            else if (!is_forward && flow->application_id != ~0)
            {
                upf_debug ("Reverse Flow and Appid_idx %u\n", flow->application_id);
                upf_pdr_get_application_rule (vm, b0, flow, active, ETH_TRAFFIC, is_payload_v4, &flowcache_abort_learning_flag);
            }
            else if (flow->upf_learning_type == UPF_FLOW_LEARNING_TYPE_SINGLE && (flow->stats[0].bytes > 4096 || flow->stats[1].bytes > 4096))
            {
                /* stop flow classification after 4k in each direction */
                upf_debug ("####5glan Stopping PDR detect after 4k in each direction for flow id: 0x%x", opaque->upf.flow_index);
                flowcache_abort_learning_flag = 0;
            }
            else if (flow->stats[0].bytes > 4096 && flow->stats[1].bytes > 4096)
            {
                /* stop flow classification after 4k in each direction */
                upf_debug ("####Stopping PDR detect after 4k in each direction for flow id: 0x%x", opaque->upf.flow_index);
                flowcache_abort_learning_flag = 0;
            }

            if (1 == flowcache_abort_learning_flag)
                upf_flowcache_abort_learning (b0);

            if (opaque->upf.pdr_index != ~0)
            {
                next0 = PD_NEXT_ETH_PROCESS;
                pkts_counter++;

                // if(!sess->handover_ongoing)
                // {
                //     if (flow->key.session_index == opaque->upf.session_index)
                //         flow->pdr_index[is_reverse] = opaque->upf.pdr_index;
                //     else
                //         flow->second_pdr_index[is_reverse] = opaque->upf.pdr_index;
                // }
                // else
                // {
                //     if(flow->pdr_index[0] != (u32)~0 || flow->pdr_index[1] != (u32)~0
                //         ||flow->second_pdr_index[0] != (u32)~0 || flow->second_pdr_index[1] != (u32)~0)
                //     {
                //         upf_trace("upf handover_ongoing! need to reset current flow!");
                //         upf_flowtable_reset(flow);
                //     }
                // }
                if (flow->key.session_index == opaque->upf.session_index)
                    flow->pdr_index[is_reverse] = opaque->upf.pdr_index;
                else
                    flow->second_pdr_index[is_reverse] = opaque->upf.pdr_index;
                
                upf_flow_id_related_to_pdr (sess, opaque->upf.pdr_index, opaque->upf.flow_index, b0, is_reverse);
            }

trace:
            if (PREDICT_FALSE (b0->flags & VLIB_BUFFER_IS_TRACED))
            {
                upf_pdr_trace_t *tr = vlib_add_trace (vm, node, b0, sizeof (*tr));
                tr->up_seid = sess ? sess->up_seid : ~0;
                tr->pdr_idx = opaque->upf.pdr_index;
                tr->next = next0;
            }

            /* frame mgmt */
            from++;
            to_next++;
            n_left_from--;
            n_left_to_next--;
            vlib_validate_buffer_enqueue_x1 (vm, node, next_index, to_next, n_left_to_next, bi0, next0);
        }
        vlib_put_next_frame (vm, node, next_index, n_left_to_next);
    }

    return frame->n_vectors;
}

static char *upf_pdr_detect_error_strings[] = {
#define _(sym, string) string,
    foreach_pdr_detect_error
#undef _
};

static u8 *
format_pdr_detect_trace (u8 *s, va_list *args)
{
  CLIB_UNUSED (vlib_main_t * vm) = va_arg (*args, vlib_main_t *);
  CLIB_UNUSED (vlib_node_t * node) = va_arg (*args, vlib_node_t *);
  upf_pdr_trace_t *t = va_arg (*args, upf_pdr_trace_t *);
  s = format (s, "up_seid 0x%lx, pdr_idx %x, next %d", t->up_seid, t->pdr_idx,
              t->next);
  return s;
}

VLIB_NODE_FN (upf_pdr_detect_ip4_node)
(vlib_main_t *vm, vlib_node_runtime_t *node, vlib_frame_t *from_frame)
{
  return upf_pdr_detect_ip (vm, node, from_frame, /* is_ip4 */ 1);
}

VLIB_NODE_FN (upf_pdr_detect_ip6_node)
(vlib_main_t *vm, vlib_node_runtime_t *node, vlib_frame_t *from_frame)
{
  return upf_pdr_detect_ip (vm, node, from_frame, /* is_ip4 */ 0);
}

VLIB_NODE_FN (upf_pdr_detect_ethernet_node)
(vlib_main_t *vm, vlib_node_runtime_t *node, vlib_frame_t *from_frame)
{
  return upf_pdr_detect_ethernet (vm, node, from_frame);
}

/* *INDENT-OFF* */
VLIB_REGISTER_NODE (upf_pdr_detect_ip4_node) = {
    .name = "upf-pdr-detect",
    .vector_size = sizeof (u32),
    .type = VLIB_NODE_TYPE_INTERNAL,
    .format_trace = format_pdr_detect_trace,
    .n_errors = PDR_DETECT_N_ERROR,
    .error_strings = upf_pdr_detect_error_strings,
    .n_next_nodes = PD_NEXT_N_NEXT,
    .next_nodes = {
        [PD_NEXT_DROP] = "error-drop",
        [PD_NEXT_PROCESS] = "upf-ip4-process",
    }};

VLIB_REGISTER_NODE (upf_pdr_detect_ip6_node) = {
    .name = "upf-pdr-detect6",
    .vector_size = sizeof (u32),
    .type = VLIB_NODE_TYPE_INTERNAL,
    .format_trace = format_pdr_detect_trace,
    .n_errors = PDR_DETECT_N_ERROR,
    .error_strings = upf_pdr_detect_error_strings,
    .n_next_nodes = PD_NEXT_N_NEXT,
    .next_nodes = {
        [PD_NEXT_DROP] = "error-drop",
        [PD_NEXT_PROCESS] = "upf-ip6-process",
    }};

VLIB_REGISTER_NODE (upf_pdr_detect_ethernet_node) = {
    .name = "upf-pdr-detect-ethernet",
    .vector_size = sizeof (u32),
    .type = VLIB_NODE_TYPE_INTERNAL,
    .format_trace = format_pdr_detect_trace,
    .n_errors = PDR_DETECT_N_ERROR,
    .error_strings = upf_pdr_detect_error_strings,
    .n_next_nodes = PD_NEXT_ETH_N_NEXT,
    .next_nodes = {
        [PD_NEXT_ETH_DROP] = "error-drop",
        [PD_NEXT_ETH_PROCESS] = "upf-ethernet-process",
    }};	
/* *INDENT-ON* */

/*begin add by huqingyang 2021.4.30 for dns Sniffer  */
uword *gHasUpfDnsSnifferBuff = NULL;
u32    gHasUpfDnsSnifferTtl = 43200; //12 h = 43200 sec
u32    gHasUpfDnsSnifferMaxNumb = 60000;
u32    gHasUpfDnsSnifferCount = 0;


/********************************************************
*function        : upf_dns_sniffer_add
*parameter       : vpstDnsSniffer
*IN_OUT          :      IN
*type            : UPF_DNS_SNIFFER_TYPE *
*author          : huqingyang
*create data     : 2021.4.30
*help            :
********************************************************/
u32 upf_dns_sniffer_add(UPF_DNS_SNIFFER_TYPE *vpstDnsSniffer)
{
    uword *puwDnsBuff = NULL;    

    UPF_DNS_SNIFFER_TYPE *pDnsBuff = NULL;  
    UPF_DNS_SNIFFER_TYPE *pstDns;
    struct timeval stTime;
    if (NULL == vpstDnsSniffer)
    {
        upf_err ("upf_dns_sniffer_add : input null\n");
        return -1;
    }

    /* first use need create */
    if (gHasUpfDnsSnifferBuff == NULL)
    {
        gHasUpfDnsSnifferBuff = hash_create_string (0, sizeof (uword));
        if (NULL == gHasUpfDnsSnifferBuff)
        {
            upf_err ("gHasUpfDnsSnifferBuff create fail\n");
            return -1;
        }
    }

    if (gHasUpfDnsSnifferCount > gHasUpfDnsSnifferMaxNumb)
    {
        upf_err ("upf_dns_sniffer_add err: gHasUpfDnsSnifferCount(%u) > gHasUpfDnsSnifferMaxNumb(%u) \n",gHasUpfDnsSnifferCount,gHasUpfDnsSnifferMaxNumb);
        return -1;
    }
    
    
    gettimeofday(&stTime,NULL);
    vpstDnsSniffer->uiTimeSec = stTime.tv_sec;
    pDnsBuff = (UPF_DNS_SNIFFER_TYPE *)malloc(sizeof(UPF_DNS_SNIFFER_TYPE));
    memcpy(pDnsBuff, vpstDnsSniffer, sizeof(UPF_DNS_SNIFFER_TYPE));
    
    puwDnsBuff =  hash_get_mem (gHasUpfDnsSnifferBuff, pDnsBuff->strKey);
    if (NULL == puwDnsBuff)
    {
        gHasUpfDnsSnifferCount++;
    }
    else
    {
        pstDns = (UPF_DNS_SNIFFER_TYPE*) (puwDnsBuff[0]);
        if (NULL != pstDns)
        {      
            hash_unset_mem (gHasUpfDnsSnifferBuff, pDnsBuff->strKey); 
            free(pstDns);
        }
    }
    hash_set_mem (gHasUpfDnsSnifferBuff, pDnsBuff->strKey, (uword) pDnsBuff);   
    return 0;
}


/********************************************************
*function        : upf_dns_sniffer_del_by_name
*parameter       : vstrName
*IN_OUT          :      IN
*type            : char *
*author          : huqingyang
*create data     : 2021.4.30
*help            :
********************************************************/
u32 upf_dns_sniffer_del_by_key(char *vstrKey)
{
    uword *pDnsBuff = NULL;    
    UPF_DNS_SNIFFER_TYPE *pstDns;
    
    if (NULL == vstrKey)
    {
        upf_err ("upf_dns_sniffer_add : input null\n");
        return -1;
    }

    if (NULL == gHasUpfDnsSnifferBuff)
    {
        upf_err ("gHasUpfDnsSnifferBuff == NULL\n");
        return -1;
    }

    pDnsBuff =  hash_get_mem (gHasUpfDnsSnifferBuff, vstrKey);
    if (NULL == pDnsBuff)
    {
        return 0;
    }
    pstDns = (UPF_DNS_SNIFFER_TYPE*) (pDnsBuff[0]);
    hash_unset_mem (gHasUpfDnsSnifferBuff, vstrKey); 
    free(pstDns);
    if (gHasUpfDnsSnifferCount > 0)
    {
        gHasUpfDnsSnifferCount--;
    }
    
    return 0;
}

/********************************************************
*function        : upf_dns_sniffer_aging_by_key
*parameter       : vstrName
*IN_OUT          :      IN
*type            : char *
*author          : huqingyang
*create data     : 2021.4.30
*help            :
********************************************************/
u32 upf_dns_sniffer_aging_by_key(char *vstrKey)
{
    uword *pDnsBuff = NULL;    
    UPF_DNS_SNIFFER_TYPE *pstDns;
    struct timeval stTime;
    u32 uiRet = 0;
    
    if (NULL == vstrKey)
    {
        upf_err ("upf_dns_sniffer_add : input null\n");
        return -1;
    }

    if (NULL == gHasUpfDnsSnifferBuff)
    {
        upf_err ("gHasUpfDnsSnifferBuff == NULL\n");
        return -1;
    }

    pDnsBuff =  hash_get_mem (gHasUpfDnsSnifferBuff, vstrKey);
    if (NULL == pDnsBuff)
    {
        return 0;
    }
    
    pstDns = (UPF_DNS_SNIFFER_TYPE*) (pDnsBuff[0]);

    if (UPF_DNS_SNIFFER_STATIC == pstDns->uiStaticFlag)
    {
        return 0;
    }
    
    gettimeofday(&stTime,NULL);
    if (stTime.tv_sec > (pstDns->uiTimeSec + gHasUpfDnsSnifferTtl))
    {
        uiRet = upf_dns_sniffer_del_by_key(vstrKey);
        if (0 != uiRet)
        {
            upf_err ("upf_dns_sniffer_del_by_key is err\n");
        }
    }
    
    return 0;
}



/********************************************************
*function        : upf_dns_sniffer_get_by_name
*parameter       : vstrName      pstDnsSniffer
*IN_OUT          : IN            OUT
*type            : char*         UPF_DNS_SNIFFER_TYPE*
*author          : huqingyang
*create data     : 2021.4.30
*help            :
********************************************************/
u32 upf_dns_sniffer_get_by_key(char *vstrKey, UPF_DNS_SNIFFER_TYPE *vpstDnsSniffer)
{
    uword *pDnsBuff = NULL;
    
    UPF_DNS_SNIFFER_TYPE *pstDns;
    
    if ((NULL==vstrKey) || (NULL == vpstDnsSniffer))
    {
        upf_err ("upf_dns_sniffer_get_by_name : input null\n");
        return -1;
    }

    if (NULL == gHasUpfDnsSnifferBuff)
    {
        upf_err ("gHasUpfDnsSnifferBuff == NULL\n");
        return -1;
    }

    pDnsBuff =  hash_get_mem (gHasUpfDnsSnifferBuff, vstrKey);
    if (NULL == pDnsBuff)
    {
        upf_err ("NULL != pDnsBuff\n");
        return -1;
    }
    pstDns = (UPF_DNS_SNIFFER_TYPE*) (pDnsBuff[0]);
    memcpy(vpstDnsSniffer->strKey, pstDns->strKey, strlen(pstDns->strKey));   
    memcpy(vpstDnsSniffer->strHostName, pstDns->strHostName, strlen( pstDns->strHostName));
    vpstDnsSniffer->uiIp = pstDns->uiIp;
    vpstDnsSniffer->uiTimeSec = pstDns->uiTimeSec;
    vpstDnsSniffer->uiStaticFlag = pstDns->uiStaticFlag;
    return 0;
}


/********************************************************
*function        : upf_dns_sniffer_if_catch_by_name_addr
*parameter       : vstrName      vIpAddr       vpFlag
*IN_OUT          : IN            IN             OUT
*type            : char*         u32            u32 *
*author          : huqingyang
*create data     : 2021.4.30
*help            :
********************************************************/
u32 upf_dns_sniffer_if_catch_by_name_addr(char *vstrName, u32 vIpAddr, u32 *vpFlag)
{
    uword *puwDns = NULL;
    char strKey[UPF_DNS_SNIFFER_NAME_LEN] = {0};
    u32 uiRet = 0;
    
    *vpFlag = false;
    
    if ((NULL == vstrName) || (NULL == vstrName))
    {
        upf_err ("upf_dns_sniffer_if_catch_by_name_addr : input null\n");
        return -1;
    }

    
    if (NULL == gHasUpfDnsSnifferBuff)
    {
        upf_err ("gHasUpfDnsSnifferBuff == NULL\n");
        return -1;
    }
     
    sprintf(strKey,"%s-%u",vstrName,vIpAddr); 
    uiRet = upf_dns_sniffer_aging_by_key(strKey);
    if (0 != uiRet)
    {
    
        upf_err ("upf_dns_sniffer_aging_by_key is err\n");
        return -1;
    }

    puwDns =  hash_get_mem (gHasUpfDnsSnifferBuff, strKey);
    if (NULL != puwDns)
    {
        *vpFlag = true;
    }
    else
    {
        *vpFlag = false;
    }

    return 0;
}





/********************************************************
*function        : upf_dns_sniffer_hand_msg
*parameter       : vpbuff          is_ip4       
*IN_OUT          : IN              IN           
*type            : vlib_buffer_t*  u8           
*author          : huqingyang
*create data     : 2021.4.30
*help            :
********************************************************/
int upf_dns_sniffer_hand_msg(vlib_buffer_t *vpbuff, u8 is_ip4)
{
    u32 offs = upf_buffer_opaque (vpbuff)->upf.data_offset;
    ip4_header_t *ip4 = NULL;
    ip6_header_t *ip6 = NULL;
    dns_header_t  *pstDnsHdr;
    u32 uiLoop;
    UPF_DNS_BODY_WITHOUT_NAME_T *pstRR;
    char aucHostName[UPF_DNS_SNIFFER_NAME_LEN] = {0};
    u8 *pPosTmp;
    u8 *pPos;
    UPF_DNS_SNIFFER_TYPE stDnsSniffer = {0};
    u32 uiRet = 0;

    if (NULL == vpbuff)
    {
        upf_err ("upf_dns_sniffer_hand_msg input null\n");
        return -1;
    }

    /* ip */
    if (is_ip4)
    {
        ip4 = (ip4_header_t *)(vlib_buffer_get_current (vpbuff) + offs);
        pstDnsHdr = ip4_next_header (ip4);
    }
    else
    {
        ip6 = (ip6_header_t *)(vlib_buffer_get_current (vpbuff) + offs);
        pstDnsHdr = ip6_next_header (ip6);
    }

    //The trusted dns list check
    u32 srcaddress = ip4->src_address.as_u32;
    upf_main_t *gtm = &g_upf_main;
    //dns_server_s *dns_server;
    dns_server_key_s dns_server_key = {0};

    dns_server_key.server_ip = srcaddress;
    uword *p = hash_get_mem (gtm->dns_server_index, &dns_server_key);
    if (!p)
    {
        return 0;
    }

    /* udp */
    pstDnsHdr = (dns_header_t *)((u8 *)pstDnsHdr +sizeof(udp_header_t));

    /* Dns header*/
    pPos = (u8 *)(pstDnsHdr +1);

    /* Dns host name */
    pPosTmp = pPos;
    while(*pPos) pPos++;
    if(PREDICT_FALSE(pPos - pPosTmp > HOSTNAME_LEN))
    {
        upf_err ("dns sniffer aucHostName too long\n");
        return -1;
    }
    memcpy(aucHostName, pPosTmp, pPos - pPosTmp);
    dns_labels_to_name(aucHostName);
    memcpy(stDnsSniffer.strHostName, aucHostName, strlen(aucHostName));
    
    /* queries type and class*/
    pPos = pPos + 5;
    
    for(uiLoop=0;uiLoop<clib_net_to_host_u16(pstDnsHdr->anscount);uiLoop++)
    {
        if((pPos[0] & 0xC0) != 0xC0)
        {
            while(*pPos) pPos++;
            pPos++;
        }
        else
        {
            pPos += 2;
        }

        pstRR = (UPF_DNS_BODY_WITHOUT_NAME_T *)pPos;
        if(clib_net_to_host_u16(pstRR->type) == DNS_TYPE_A && clib_net_to_host_u16(pstRR->class) == DNS_CLASS_IN)
        {
            //dns_rr_to_whitelist(pstRR,dns_sniffer_rule,uiLoop);
            upf_debug ("pstRR %u.%u.%u.%u \n",pstRR->rdata[0],pstRR->rdata[1],pstRR->rdata[2],pstRR->rdata[3]);
            memcpy(&stDnsSniffer.uiIp, pstRR->rdata, 4);
            memset(stDnsSniffer.strKey,0,UPF_DNS_SNIFFER_NAME_LEN);
            sprintf(stDnsSniffer.strKey,"%s-%u", stDnsSniffer.strHostName, stDnsSniffer.uiIp);
            uiRet = upf_dns_sniffer_add(&stDnsSniffer);
            if (0 != uiRet)
            {
                upf_err ("upf_dns_sniffer_hand_msg:0 != uiRet");
                return -1;
            }
            u32 sidx = upf_buffer_opaque (vpbuff)->upf.session_index;
            CHECK_POOL_IS_VALID_RET(g_upf_main.sessions, sidx, 0);
            upf_session_t *sess = pool_elt_at_index (g_upf_main.sessions, sidx);
            upf_pfcp_events_publish (PFCP_RPC_PUBLISH_DNS_SNIFFER, sess,
                                     &stDnsSniffer);
        }
        pPos += (sizeof(dns_rr_t)+clib_net_to_host_u16(pstRR->rdlength));
    }
    
    return 0;
}


/********************************************************
*function        : upf_dns_sniffer_get_name_by_ip
*parameter       : vuiIp         vpstrName
*IN_OUT          : IN            OUT
*type            : u32           char*
*author          : huqingyang
*create data     : 2021.4.30
*help            :
********************************************************/
u32 upf_dns_sniffer_get_name_by_ip(u32 vuiIp, char *vpstrName)
{
    char  *key = NULL;
    char  *value = NULL;
    
    UPF_DNS_SNIFFER_TYPE *pstDns;
    
    if (NULL == vpstrName)
    {
        upf_err ("upf_dns_sniffer_get_name_by_ip : input null\n");
        return -1;
    }

    if (NULL == gHasUpfDnsSnifferBuff)
    {
        upf_err ("gHasUpfDnsSnifferBuff == NULL\n");
        return -1;
    }

    hash_foreach_mem(key, value, gHasUpfDnsSnifferBuff,
    ({  
        pstDns = (UPF_DNS_SNIFFER_TYPE *)value;
        if (pstDns->uiIp == vuiIp)
        {
            memcpy(vpstrName, pstDns->strHostName, strlen(pstDns->strHostName));
            return 0;
        }
    }));  
    upf_err ("upf_dns_sniffer_get_name_by_ip can not find input ip in sniffer buff");
    return -1;
}

/********************************************************
*function        : upf_dns_sniffer_get_name_by_url
*parameter       : vpstrUrl         vpstrName
*IN_OUT          : IN               OUT
*type            : char*            char*
*author          : huqingyang
*create data     : 2021.5.25
*help            :
********************************************************/
u32 upf_dns_sniffer_get_name_by_url(char *vpstrUrl, char *vpstrName)
{
    char acHttps[10]    = "https://";
    char acUrlHttps[10] = {0};
    int  iCmpRet        = 0;
    char acAllName[256] = {0};
    u32  ulLoop = 0;
    
    if ((NULL == vpstrUrl) || (NULL == vpstrName))
    {
        upf_err ("upf_dns_sniffer_get_name_by_url : input null\n");
        return -1;
    }

    /* remove https://   like  https://www.123.com -> www.123.com */
    if (strlen(vpstrUrl) <= strlen(acHttps))
    {
        memcpy(acAllName, vpstrUrl, strlen(vpstrUrl));
    }
    else
    {
        memcpy(acUrlHttps, vpstrUrl, strlen(acHttps));
        iCmpRet = strcmp(acUrlHttps,acHttps);
        if (iCmpRet)
        {
            memcpy(acAllName, vpstrUrl, strlen(vpstrUrl));
        }
        else
        {
            memcpy(acAllName, (vpstrUrl + strlen(acHttps)), (strlen(vpstrUrl) - strlen(acHttps)));
        }
    
    }

    /* remove path only use host www.123.com/abc -> www.123.com*/
    for (ulLoop = 0 ; ulLoop < strlen(acAllName); ulLoop++)
    {
        if (acAllName[ulLoop] == '/')
        {
            break;
        }
    }
    
    memcpy(vpstrName, acAllName, ulLoop);

    return 0;
}
/*end add by huqingyang 2021.4.30 for dns Sniffe */

u32 upf_ip6_address_is_equal (const ip6_address_t * a, const ip6_address_t * b, u8 len)
{
  if (len == 0 || len > sizeof(ip6_address_t))
    len = 64;    //defauld is 64

  if (len > 64)
  {
    if (a->as_u64[0] != b->as_u64[0] || (a->as_u64[1] >> (128 - len) != b->as_u64[1] >> (128 - len)))
        return 0;
  }
  else
  {
    if (a->as_u64[0] >> (64 - len) != b->as_u64[0] >> (64 - len))
      return 0;
  }

  return 1;
}


// VLIB_NODE_FUNCTION_MULTIARCH(upf_pdr_detect_ip4_node, upf_pdr_detect_ip);

/*
 * fd.io coding-style-patch-verification: ON
 *
 * Local Variables:
 * eval: (c-set-style "gnu")
 * End:
 */
