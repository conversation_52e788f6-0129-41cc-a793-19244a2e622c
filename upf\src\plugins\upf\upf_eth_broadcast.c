#include <vnet/vnet.h>
#include <vnet/plugin/plugin.h>
#include <vpp/app/version.h>
#include <vnet/classify/vnet_classify.h>
#include <vnet/ip/ip6_link.h>
#include <vnet/ip/ip6_hop_by_hop.h>
#include <vnet/ip/reass/ip4_sv_reass.h>
#include <vnet/ip/reass/ip4_full_reass.h>
#include <vnet/ip/reass/ip6_sv_reass.h>
#include <vnet/ip/reass/ip6_full_reass.h>
#include <vnet/ip-neighbor/ip_neighbor.h>
#include <vpp/stats/stat_segment.h>
#include <math.h>
#include <string.h>
#include <search.h>

#include <upf/upf.h>
#include <upf/upf_pfcp.h>
#include <upf/pfcp.h>
#include <upf/upf_pfcp.h>
#include <upf/upf_pfcp_server.h>
#include <upf/upf_pfcp_api.h>
#include <upf/flowtable.h>
#include <upf/upf_eth_broadcast.h>

static u32 **eth_broadcast_clones;

extern upf_nwi_t *upf_lookup_nwi (u8 *name);

static clib_error_t *
upf_show_eth_broadcast_command_fn (vlib_main_t *vm, unformat_input_t *main_input,
                             vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  upf_main_t *gtm = &g_upf_main;
  clib_error_t *error = NULL;
  u8 *nwi_name;
  u32 c_vlan, s_vlan;
  u32 nwi_name_flag = 0, c_vlan_flag = 0, s_vlan_flag = 0;
  upf_eth_broadcast_domain_t *domain_members;
  upf_eth_broadcast_key_u *p_key, key;
  u32 *idx;

  if (unformat_user (main_input, unformat_line_input, line_input))
    {
        while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
        {
            if (unformat (line_input, "nwi %s", &nwi_name))
                nwi_name_flag = 1;
            else if (unformat (line_input, "c_vlan %u", &c_vlan))
                c_vlan_flag = 1;
            else if (unformat (line_input, "s_vlan %u", &s_vlan))
                s_vlan_flag = 1;
            else
            {
                error = unformat_parse_error (line_input);
                unformat_free (line_input);
                goto done;
            }
        }
        unformat_free (line_input);
    }

    if (nwi_name_flag && (c_vlan_flag || s_vlan_flag))
    {
        upf_nwi_t *nwi = upf_lookup_nwi (iupf_name_to_labels (nwi_name));
        key.nwi_idx = nwi - gtm->nwis;
        key.c_vlan = c_vlan;
        key.s_vlan = s_vlan;
        hash_pair_t *hp = hash_get_pair (gtm->hash_eth_broadcast_id_by_key, &key);
        if (hp)
        {
            domain_members = (void *)(hp->value[0]);
            vlib_cli_output (vm, "nwi_idx:%u, c_vlan:%u, s_vlan:%u\n", key.nwi_idx, key.c_vlan, key.s_vlan);
            vec_foreach (idx, domain_members->sess_idxs)
            {
                vlib_cli_output (vm, "    session index: %u ", *idx);
            }
        }
    }
    else
    {
        hash_foreach_mem (p_key, domain_members, gtm->hash_eth_broadcast_id_by_key,
        ({
            vlib_cli_output (vm, "nwi_idx:%u, c_vlan:%u, s_vlan:%u\n", p_key->nwi_idx, p_key->c_vlan, p_key->s_vlan);
            vec_foreach (idx, domain_members->sess_idxs)
            {
                vlib_cli_output (vm, "    session index: %u ", *idx);
            }
        })); 
    }

done:
    return error;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_show_eth_broadcast_command, static) = {
    .path = "show upf eth-broadcast",
    .short_help = "show upf eth-broadcast [nwi] [c_vlan] [s_vlan]",
    .function = upf_show_eth_broadcast_command_fn,
};
/* *INDENT-ON* */

u32 upf_eth_broadcast_clone_init (vlib_main_t *vm)
{
    vec_validate (eth_broadcast_clones, vlib_num_workers());
    return 0;
}

void upf_add_member_to_broadcast_table(upf_eth_broadcast_mem_report_t *data_info)
{
    upf_eth_broadcast_domain_t *domain_members = NULL;

    hash_pair_t *hp = hash_get_pair (g_upf_main.hash_eth_broadcast_id_by_key, &data_info->key);
    if (hp)
    {
        domain_members = (void *)(hp->value[0]);
        upf_trace("already exist ethernet broadcast domain, s_vlan:%u, c_vlan:%u nwi_idx:%u, sess_idx:%u", 
            domain_members->key.s_vlan, domain_members->key.c_vlan, domain_members->key.nwi_idx, data_info->sess_idx);
    }
    else
    {
        domain_members = clib_mem_alloc(sizeof(upf_eth_broadcast_domain_t));
        domain_members->key = data_info->key;
        domain_members->sess_idxs = NULL;
        upf_eth_broadcast_key_u *p_key = clib_mem_alloc(sizeof(upf_eth_broadcast_key_u));
        *p_key = data_info->key;
        hash_set_mem(g_upf_main.hash_eth_broadcast_id_by_key, p_key, domain_members);
        upf_trace("create ethernet broadcast domain, s_vlan:%u, c_vlan:%u nwi_idx:%u, sess_idx:%u", 
            data_info->key.s_vlan, data_info->key.s_vlan, data_info->key.nwi_idx, data_info->sess_idx);
    }

    APPEND_U_INDEX(domain_members->sess_idxs, data_info->sess_idx);
}

void upf_add_member_send_to_pfcp(vlib_buffer_t *b, upf_eth_broadcast_key_u *key)
{
    upf_session_t *sess;
    upf_eth_broadcast_mem_report_t *data_info;
    upf_eth_broadcast_key_u *p_key;

    if (upf_buffer_opaque (b)->upf.session_index == ~0)
    {
        upf_trace("invalid session index:%u", upf_buffer_opaque (b)->upf.session_index);
        return;
    }

    sess = sx_get_by_index(upf_buffer_opaque (b)->upf.session_index);
    if (!sess)
        return;

    vec_foreach (p_key, sess->key_list)
    {
        if (p_key->key == key->key)
        {
            upf_trace("ethernet broadcast domain member already exist! s_vlan:%u, c_vlan:%u nwi_idx:%u, sess_idx:%u", 
                key->s_vlan, key->s_vlan, key->nwi_idx, upf_buffer_opaque (b)->upf.session_index);
            return;
        }
    }
    APPEND_U_INDEX(sess->key_list, *key);

    upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
    memset (msg, 0, sizeof (*msg));
    data_info = clib_mem_alloc_aligned_no_fail (sizeof(upf_eth_broadcast_mem_report_t), CLIB_CACHE_LINE_BYTES);
    memset (data_info, 0, sizeof(upf_eth_broadcast_mem_report_t));
    msg->msg_id = PFCP_RPC_PUBLISH_ETH_BROADCAST;
    data_info->key = *key;
    data_info->sess_idx = upf_buffer_opaque (b)->upf.session_index;
    msg->data = data_info;

    for (int i = 0; i < g_upf_main.num_pfcp_threads; i++)
    {
        upf_trace ("PFCP_RPC_PUBLISH_ETH_BROADCAST, sess_idx:%u\n", data_info->sess_idx);
        send_sx_msg_to_pfcp_thread(i, msg);
    }

    return;
}

void upf_del_eth_broadcast_domain(upf_eth_broadcast_key_u key)
{
    upf_eth_broadcast_key_u *key_copy = NULL;
    upf_eth_broadcast_domain_t *value_copy = NULL;
    hash_pair_t *hp = NULL;

    hp = hash_get_pair (g_upf_main.hash_eth_broadcast_id_by_key, &key);
    if (hp == NULL)
    {
        upf_err ("The ethernet broadcast domain is not exist.");
        return;
    }

    key_copy = (void *)(hp->key);
    value_copy = (void *)(hp->value[0]);
    hash_unset_mem(g_upf_main.hash_eth_broadcast_id_by_key, &key);
    clib_mem_free(key_copy);
    clib_mem_free(value_copy);

    return;
}

void upf_del_member_from_broadcast_table(upf_session_t *sess)
{
    u32 *idx;
    u32 sess_idx = sess - g_upf_main.sessions;
    upf_eth_broadcast_key_u *key = NULL;

    /* delete member from all broadcast domain which include the sess */
    vec_foreach (key, sess->key_list)
    {
        hash_pair_t *hp = hash_get_pair (g_upf_main.hash_eth_broadcast_id_by_key, key);
        if (hp == NULL)
        {
            upf_err ("The ethernet broadcast domain is not exist.");
            continue;
        }
        upf_eth_broadcast_domain_t *domain_members = (void *)(hp->value[0]);
        if (!domain_members)
            continue;
        vec_foreach (idx, domain_members->sess_idxs)
        {
            if (*idx == sess_idx)
            {
                vec_del1 (domain_members->sess_idxs, idx - domain_members->sess_idxs);
                if (!vec_len(domain_members->sess_idxs))
                {
                    upf_del_eth_broadcast_domain(*key);
                    upf_trace("delete ethernet broadcast domain:nwi_idx:%u, s_vlan:%u, c_vlan:%u", key->nwi_idx, key->s_vlan, key->c_vlan);
                }
                return;
            }
        }
    }
    vec_free(sess->key_list);

    return;
}

void upf_eth_broadcast_get_user_num(u32 sess_idx, 
    u32 *sess_idxs, u8 direction, u32 *user_num)
{
    u32 *idx = NULL;
    u32 sess_num = 0;

    vec_foreach (idx, sess_idxs)
    {
        if (*idx == sess_idx)
        {
            continue;
        }
        sess_num++;
    }

    /* ul must broadcast to N6 port */
    if (direction == UPF_UL)/* ul packet */
        sess_num +=1;

    *user_num = sess_num;

    return;
}



upf_broadcast_t *upf_broadcast_hash_get_pair(uword *h, upf_broadcast_key_t *ip_key)
{
    hash_pair_t *hp = hash_get_pair (h, ip_key);
    if (hp)
    {
        upf_trace("upf_broadcast_hash_get_pair get succeed! ip_key:%U", format_ip4_address, &ip_key->ip4);
        return (void *)(hp->value[0]);
    }
    else
    {
        upf_err("upf_broadcast_hash_get_pair get failed! ip_key:%U", format_ip4_address, &ip_key->ip4);
        return NULL;
    }
}


void upf_broadcast_add_mem_to_pfcp(u32 index, upf_broadcast_key_t *key)
{
    upf_broadcast_key_t *p_key;
    bool match_flag = false;

    if (index == ~0)
    {
        upf_err("invalid index:%u", index);
        return;
    }

    upf_session_t *sess = sx_get_by_index(index);
    if (!sess)
        return;

    upf_trace("[sess->broadcast_ip_list] index:%u key:%U", index, format_ip4_address, &key->ip4);
    
    /*this step should not return if sess existed the key when timer process released the broadcast_ip_list*/
    vec_foreach (p_key, sess->broadcast_ip_list)
    {
        if (0 == upf_broadcast_keys_cmp(p_key, key))
        {
            upf_trace("[sess->broadcast_ip_list] existed! index:%u key:%U", index, format_ip4_address, &key->ip4);
            match_flag = true;
            break;
        }
    }

    if(!match_flag)
        APPEND_U_INDEX(sess->broadcast_ip_list, *key);

    upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
    memset (msg, 0, sizeof (*msg));

    upf_broadcast_msg_t *buf = clib_mem_alloc_aligned_no_fail (sizeof(upf_broadcast_msg_t), CLIB_CACHE_LINE_BYTES);
    memset (buf, 0, sizeof(upf_broadcast_msg_t));
    upf_broadcast_t *data = &buf->broadcast;

    msg->msg_id = PFCP_RPC_PUBLISH_BROADCAST_IP_TABLE;

    data->key = *key;
    data->sess_index = index;

    msg->data = (u8 *)buf;

    for (int i = 0; i < g_upf_main.num_pfcp_threads; i++)
    {
        upf_trace ("PFCP_RPC_PUBLISH_BROADCAST_IP_TABLE, sess_idx:%u key:%U\n", data->sess_index, format_ip4_address, &key->ip4);
        send_sx_msg_to_pfcp_thread(i, msg);
    }
    return;
}


/* return: 0-success; 1-fail */
u32 upf_broadcast_hash_add_del(uword **hh, void *kv, u32 is_add)
{
    upf_broadcast_t *broadcast_forw = NULL;
    upf_broadcast_key_t *key = (upf_broadcast_key_t *)kv;
    uword *h = *hh;
    uword *new = NULL;

    hash_pair_t *hp = hash_get_pair (h, key);
    if (hp)
    {
        broadcast_forw = (void *)(hp->value[0]);
        if (!is_add)
        {
            upf_trace("delete record: %U, sess_idx:%u",format_ip4_address, &key->ip4, broadcast_forw->sess_index);

            upf_broadcast_key_t *key_copy = (void *)(hp->key);
            upf_broadcast_t *value_copy = (void *)(hp->value[0]);
            new = hash_unset_mem(h, key);
            if (PREDICT_FALSE(h != new))
            {
                *hh = new;
            }
            clib_mem_free(key_copy);
            clib_mem_free(value_copy);
            return 0;
        }
        else
        {
            upf_trace("upf_broadcast_hash_add_del update upf_broadcast_t table");
            *broadcast_forw = *(upf_broadcast_t *)kv;
        }
    }
    else
    {
        if (is_add)
        {
            broadcast_forw = clib_mem_alloc(sizeof(upf_broadcast_t));
            *broadcast_forw = *(upf_broadcast_t *)kv;
            upf_broadcast_key_t *p_key = clib_mem_alloc(sizeof(upf_broadcast_key_t));
            *p_key = *key;
            new = hash_set_mem(h, p_key, broadcast_forw);
            if (PREDICT_FALSE(h != new))
            {
                *hh = new;
            }
            upf_trace("add record: %U, sess_idx:%u",format_ip4_address, &key->ip4, broadcast_forw->sess_index);
            return 0;
        }
    }

    return 1;
}


void upf_broadcast_del_mem_by_sess(upf_session_t *sess)
{
    upf_broadcast_key_t *key = NULL;

    /* delete broadcast by ip which include the sess */
    vec_foreach (key, sess->broadcast_ip_list)
    {
        upf_broadcast_hash_add_del(&g_upf_main.hash_eth_broadcast_by_key, key, DELETE);
    }
    vec_free(sess->broadcast_ip_list);
    return;
}

static clib_error_t *
upf_show_broadcast_forw_command_fn(vlib_main_t *vm, unformat_input_t *main_input,
                             vlib_cli_command_t *cmd)
{
  upf_main_t *gtm = &g_upf_main;
  clib_error_t *error = NULL;

  upf_broadcast_t *upf_broadcast;
  upf_broadcast_key_t *p_key = NULL;

    hash_foreach_mem (p_key, upf_broadcast, gtm->hash_eth_broadcast_by_key,
        ({
            vlib_cli_output (vm, "broadcast ip:%U  sess_index:%u", format_ip4_address,&p_key->ip4,upf_broadcast->sess_index);
        })); 
    return error;
}


VLIB_CLI_COMMAND (upf_show_broadcast_forw_command, static) = {
    .path = "show upf broadcast forward",
    .short_help = "show upf broadcast forward",
    .function = upf_show_broadcast_forw_command_fn,
};


u32 upf_eth_broadcast_get_dl_pdr(vlib_buffer_t *b)
{
	u32 next = UPF_ETH_BROADCAST_NEXT_DROP;
	
	upf_session_t *sess = NULL;
    struct rules *active = NULL;
    upf_pdr_t *pdr0 = NULL;
    upf_far_t *far0 = NULL;

	upf_buffer_opaque (b)->upf.flow_index = (u32)~0; //reset flow_index
	upf_buffer_opaque (b)->upf.pdr_index = (u32)~0;

    sess = sx_get_by_index(upf_buffer_opaque (b)->upf.session_index);
    if (!sess)
    {
        upf_trace ("get session by index fail, session index:%u\n", upf_buffer_opaque (b)->upf.session_index);
        return next;
    }

    active = upf_get_rules (sess, SX_ACTIVE);
	
    if (!active)
    {
        upf_debug ("NULL active");
        return next;
    }
    
    vec_foreach (pdr0, active->pdr)
    {
        if (pdr0->pdi.src_intf != SRC_INTF_CORE)
        {
        	continue;
        }
		vec_foreach (far0, active->far)
        {
            if (far0->forward.dst_intf == DST_INTF_ACCESS)
            {
				upf_buffer_opaque (b)->upf.pdr_index = pdr0 - active->pdr;
                upf_trace("get valid dl pdr index %u",upf_buffer_opaque (b)->upf.pdr_index);
				return UPF_ETH_BROADCAST_NEXT_ETH_PROCESS;
            }
        }

    }
    upf_err("get invalid dl pdr index!");
	return next;
}

/* before this node is looking up unicast-forward-table, missed unicast-forward-table */
static uword upf_eth_broadcast_node_fn (vlib_main_t *vm, vlib_node_runtime_t *node,
                   vlib_frame_t *from_frame)
{
    u32 next = 0;
    u32 *from = vlib_frame_vector_args (from_frame);
    u32 *to_next = NULL;
    u32 n_left_from = from_frame->n_vectors;
    u32 next_index = node->cached_next_index;
    u32 n_left_to_next;
    
    upf_main_t *um = &g_upf_main;
    upf_session_t *sess = NULL; 
#define _(sym, str) u64 CPT_##sym = 0;
    foreach_upf_eth_broadcast_error
#undef _

    upf_trace("ethernet broadcast, n_left_from:%u", n_left_from);

    while (n_left_from > 0)
    {
        next_index = UPF_ETH_BROADCAST_NEXT_DROP;
        vlib_get_next_frame (vm, node, next_index, to_next, n_left_to_next);

        while ((n_left_from > 0) && (n_left_to_next > 0))
        {
            u32 bi = from[0];
			to_next[0] = from[0];
			from += 1;
            to_next += 1;
            n_left_from -= 1;
            n_left_to_next -= 1;

            vlib_buffer_t *b = vlib_get_buffer (vm, bi);
            
            vlib_buffer_t * c1 = NULL;
            u32 ci1 = 0;
            u32 sess_index = (u32)~0;
            ethernet_header_t *eth = vlib_buffer_get_current (b);
            ethernet_arp_header_t *arp = (ethernet_arp_header_t *)((u8 *)eth + sizeof(ethernet_header_t));
            upf_broadcast_key_t src_key = {0};
            upf_broadcast_key_t dst_key = {0};
            upf_broadcast_t *broadcast_forw_dst = NULL;

            next = UPF_ETH_BROADCAST_NEXT_DROP;

            u8 direction = UPF_DL;
            if (upf_buffer_opaque (b)->upf.session_index != ~0)
                direction = UPF_UL;

            //origin pkt need to drop
            vlib_validate_buffer_enqueue_x1(vm, node, next_index, to_next, n_left_to_next, bi, next);

            /*
            for UL:
                1.src arp learning for the relationship of src ip with sessIndex.
                2.clone one pkt to N6.
                3.using dst arp ip to find hash_eth_broadcast_by_key.
                    if succ, clone one pkt to specific UE.
                    if failed,clone multi pkts to all UEs.
            */
            if(direction == UPF_UL)
            {
                sess_index = upf_buffer_opaque (b)->upf.session_index;

                upf_trace("src pkt sess_index %u",sess_index);

                if(arp->opcode == clib_host_to_net_u16 (ETHERNET_ARP_OPCODE_request))
                {
                    src_key.ip4 = arp->ip4_over_ethernet[0].ip4;
                    dst_key.ip4 =  arp->ip4_over_ethernet[1].ip4;
                }

                upf_trace("src ip_key:%U dst ip_key:%U", format_ip4_address, &src_key.ip4,format_ip4_address, &dst_key.ip4);

                //src ip learning
                upf_broadcast_t *broadcast_forw_src = upf_broadcast_hash_get_pair(g_upf_main.hash_eth_broadcast_by_key, &src_key);
                if(!broadcast_forw_src || (sess_index != broadcast_forw_src->sess_index))
                {
                    upf_trace("get hash_eth_broadcast_by_key failed! or not the same session_index");
                    upf_broadcast_add_mem_to_pfcp(sess_index, &src_key);
                }

                //firstly:clone one pkts to n6
                 goto upf_clone_pkt_to_n6;

                //secondly: clone to specific UE
                broadcast_forw_dst = upf_broadcast_hash_get_pair(g_upf_main.hash_eth_broadcast_by_key, &dst_key);
                if(broadcast_forw_dst)
                {
                    goto upf_clone_pkt_to_single_ue;
                    continue;
                }

                //thirdly: clone to multi UEs
                goto upf_clone_pkt_to_multi_ue;
                continue;
            }

            /*
            for DL:
                using dst arp ip to find hash_eth_broadcast_by_key.
                    if succ, clone one pkt to specific UE.
                    if failed,clone multi pkts to all UEs.
            */
            if(direction == UPF_DL)
            {
                sess_index = upf_buffer_opaque (b)->upf.session_index;
                if(arp->opcode == clib_host_to_net_u16 (ETHERNET_ARP_OPCODE_request))
                {
                    dst_key.ip4 =  arp->ip4_over_ethernet[1].ip4;
                }
                
                upf_trace("dst ip_key:%U",format_ip4_address, &dst_key.ip4);

                broadcast_forw_dst = upf_broadcast_hash_get_pair(g_upf_main.hash_eth_broadcast_by_key, &dst_key);

                //firstly:clone pkt to specific UE
                if(broadcast_forw_dst)
                {   
                    goto upf_clone_pkt_to_single_ue;
                    continue;
                }

                //secondly:clone pkts to per UEs
                goto upf_clone_pkt_to_multi_ue;
                continue;
            }
            
upf_clone_pkt_to_n6:
                    upf_trace("clone single pkt to N6");
                    c1 = vlib_buffer_copy (vm, b);
                    if (c1 == NULL)
                        continue;

                    ci1 = vlib_get_buffer_index (vm, c1);

                    upf_buffer_opaque (c1)->upf = upf_buffer_opaque (b)->upf;
                    upf_buffer_opaque (c1)->upf.session_index = (u32)~0;
                    upf_buffer_opaque (c1)->upf.src_intf = UPF_UL;

                    next = UPF_ETH_BROADCAST_NEXT_L2_INPUT;

                    to_next[0] = ci1;
                    to_next += 1;

                    n_left_to_next -= 1;
                    CPT_COPIES_FORWARD++;
                    vlib_validate_buffer_enqueue_x1(vm, node, next_index, to_next, n_left_to_next, ci1, next);
                    
                    if (PREDICT_FALSE(0 == n_left_to_next))
                    {
                        vlib_put_next_frame (vm, node, next_index, n_left_to_next);
                        vlib_get_next_frame (vm, node, next_index, to_next, n_left_to_next);
                    }

upf_clone_pkt_to_single_ue:
                    upf_trace("clone single pkt to UE");
                    c1 = vlib_buffer_copy (vm, b);
                    if (c1 == NULL)
                        continue;
                    
                    ci1 = vlib_get_buffer_index (vm, c1);
                    upf_buffer_opaque (c1)->upf = upf_buffer_opaque (b)->upf;
                    upf_buffer_opaque (c1)->upf.session_index = (broadcast_forw_dst != NULL)? broadcast_forw_dst->sess_index : (u32)~0;
                    upf_buffer_opaque (c1)->upf.src_intf = UPF_DL;

                    next = upf_eth_broadcast_get_dl_pdr(c1);
                    
                    to_next[0] = ci1;
                    to_next += 1;

                    n_left_to_next -= 1;
                    CPT_COPIES_FORWARD++;
                    vlib_validate_buffer_enqueue_x1(vm, node, next_index, to_next, n_left_to_next, ci1, next);
                    
                    if (PREDICT_FALSE(0 == n_left_to_next))
                    {
                        vlib_put_next_frame (vm, node, next_index, n_left_to_next);
                        vlib_get_next_frame (vm, node, next_index, to_next, n_left_to_next);
                    }

upf_clone_pkt_to_multi_ue:
                upf_trace("clone multi pkts to UEs");
                vec_foreach(sess, um->sessions)
                {
                    vlib_buffer_t * c1 = NULL;
                    u32 ci1 = 0;
                    if(!upf_pfcp_session_is_valid(sess - um->sessions) || ip46_address_is_zero (&sess->cp_address))
                        continue;
                    
                    if(upf_buffer_opaque (b)->upf.session_index == sess - um->sessions)
                        continue;

                    c1 = vlib_buffer_copy (vm, b);

                    if (c1 == NULL)
                        continue;

                    ci1 = vlib_get_buffer_index (vm, c1);

                    upf_buffer_opaque (c1)->upf = upf_buffer_opaque (b)->upf;
                    upf_buffer_opaque (c1)->upf.session_index = sess - um->sessions;
                    upf_buffer_opaque (c1)->upf.src_intf = UPF_DL;

                    next = upf_eth_broadcast_get_dl_pdr(c1);

                    to_next[0] = ci1;
                    to_next += 1;

                    n_left_to_next -= 1;
                    CPT_COPIES_FORWARD++;
                    vlib_validate_buffer_enqueue_x1(vm, node, next_index, to_next, n_left_to_next, ci1, next);
                    
                    if (PREDICT_FALSE(0 == n_left_to_next))
                    {
                        vlib_put_next_frame (vm, node, next_index, n_left_to_next);
                        vlib_get_next_frame (vm, node, next_index, to_next, n_left_to_next);
                    }
                }


        }
        vlib_put_next_frame (vm, node, next_index, n_left_to_next);
    }

#define _(sym, str) vlib_node_increment_counter (vm, node->node_index, UPF_ETH_BROADCAST_ERROR_##sym, CPT_##sym);
    foreach_upf_eth_broadcast_error
#undef _

    return from_frame->n_vectors;

}

u8 *format_eth_broacast_trace (u8 *s, va_list *args)
{
    CLIB_UNUSED (vlib_main_t * vm) = va_arg (*args, vlib_main_t *);
    CLIB_UNUSED (vlib_node_t * node) = va_arg (*args, vlib_node_t *);
    upf_eth_broadcast_trace_t *t = va_arg (*args, upf_eth_broadcast_trace_t *);
    ethernet_header_t *eth = (ethernet_header_t *)t->packet_data;
    u32 indent = format_get_indent (s);

    s = format (
      s, "sx_index %u, up_seid 0x%016" PRIx64 " pdr_id %u, far_id %u\n%U src:%U, dst:%U",
      t->session_index, t->up_seid, t->pdr_id, t->far_id,
      format_white_space, indent,
      format_mac_address, eth->src_address, format_mac_address, eth->dst_address);
    return s;
}

static char *upf_eth_broadcast_error_strings[] = {
#define _(sym, string) string,
    foreach_upf_eth_broadcast_error
#undef _
};

/* *INDENT-OFF* */
VLIB_REGISTER_NODE (upf_eth_broadcast_node) = {
    .function = upf_eth_broadcast_node_fn,
    .name = "upf-ethernet-broadcast",
    .vector_size = sizeof (u32),
    .format_trace = format_eth_broacast_trace,
    .type = VLIB_NODE_TYPE_INTERNAL,
    .n_errors = UPF_ETH_BROADCAST_N_ERROR,
    .error_strings = upf_eth_broadcast_error_strings,
    .n_next_nodes = UPF_ETH_BROADCAST_NEXT_N_NEXT,
    .next_nodes = {
        [UPF_ETH_BROADCAST_NEXT_DROP] = "error-drop",
        [UPF_ETH_BROADCAST_NEXT_L2_INPUT] = "l2-input",
        [UPF_ETH_BROADCAST_NEXT_ETH_INPUT] = "upf-eth-input",
        [UPF_ETH_BROADCAST_NEXT_VXLAN4_ENCAP] = "vxlan4-encap",
        [UPF_ETH_BROADCAST_NEXT_VXLAN6_ENCAP] = "vxlan6-encap",
        [UPF_ETH_BROADCAST_NEXT_ETH_PROCESS] = "upf-ethernet-process",
    }};
/* *INDENT-ON* */

/*
 * fd.io coding-style-patch-verification: ON
 *
 * Local Variables:
 * eval: (c-set-style "gnu")
 * End:
 */
 
