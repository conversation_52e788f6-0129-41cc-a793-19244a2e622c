


/*
 * Copyright (c) 2016 Qosmos and/or its affiliates.
 * Copyright (c) 2018 Travelping GmbH
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at:
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <vppinfra/dlist.h>
#include <vppinfra/types.h>
#include <vppinfra/vec.h>
#include <vnet/ip/ip4_packet.h>
/* added by liuyu MANAGE_STATE 2022-11-01 */
#include <vnet/ip/lookup.h>

#include "upf.h"
#include "flowtable.h"
#include "flowtable_tcp.h"
#include "upf_pfcp.h"
#include "upf_pfcp_server.h"

extern void upf_inflate_del_func (flow_entry_t *flow);

always_inline void
upf_flow_cache_fill (flowtable_main_t *fm, flowtable_main_per_cpu_t *fmt)
{
  int i;
  flow_entry_t *f;

  clib_spinlock_lock (&fm->flows_lock);

  if (PREDICT_FALSE (fm->flows_cpt >= fm->flows_max))
    {
      // clib_warning("Numer of flows %d is overflow. ",fm->flows_cpt);
      clib_spinlock_unlock (&fm->flows_lock);
      return;
    }

  for (i = 0; i < FLOW_CACHE_SZ; i++)
    {
      pool_get_aligned (fm->flows, f, CLIB_CACHE_LINE_BYTES);
      vec_add1 (fmt->flow_cache, f - fm->flows);
    }
  fm->flows_cpt += FLOW_CACHE_SZ;

  clib_spinlock_unlock (&fm->flows_lock);
}

always_inline void
upf_flow_cache_empty (flowtable_main_t *fm, flowtable_main_per_cpu_t *fmt)
{
  int i;
  flow_entry_t *f;

  clib_spinlock_lock (&fm->flows_lock);

  for (i = vec_len (fmt->flow_cache) - 1; i > FLOW_CACHE_SZ; i--)
    {
      u32 f_index = vec_pop (fmt->flow_cache);
      f = fm->flows + f_index;
      clib_spinlock_free (&f->lock);
      pool_put_index (fm->flows, f_index);
    }
  fm->flows_cpt -= FLOW_CACHE_SZ;

  clib_spinlock_unlock (&fm->flows_lock);
}

always_inline flow_entry_t *
upf_flow_entry_alloc (flowtable_main_t *fm, flowtable_main_per_cpu_t *fmt)
{
  u32 f_index;
  flow_entry_t *f;

  if (vec_len (fmt->flow_cache) == 0)
    upf_flow_cache_fill (fm, fmt);

  if (PREDICT_FALSE ((vec_len (fmt->flow_cache) == 0)))
    return NULL;

  f_index = vec_pop (fmt->flow_cache);
  CHECK_POOL_IS_VALID_RET(fm->flows, f_index, NULL);
  f = pool_elt_at_index (fm->flows, f_index);

  return f;
}
always_inline void
upf_flow_entry_free_rcu (struct upf_rcu_entry_st *head)
{
  struct flow_entry *f = container_of (head, struct flow_entry, rcu_entry);

  u32 cpu_index = os_get_thread_index ();
  flowtable_main_t *fm = &g_flowtable_main;
  flowtable_main_per_cpu_t *fmt = &fm->per_cpu[cpu_index];

  upf_trace("[IN] cpu_index:%u, session_index:%u", cpu_index, f->key.session_index);

  if (f->flowcache[FT_ORIGIN])
    vec_free (f->flowcache[FT_ORIGIN]);
  if (f->flowcache[FT_REVERSE])
    vec_free (f->flowcache[FT_REVERSE]);

  if (upf_pfcp_session_is_valid (f->key.session_index))
    {
      upf_main_t *gtm = &g_upf_main;
      upf_session_t *sess = NULL;
      struct rules *active = NULL;
      upf_pdr_t *pdr;
      u32 index;

      CHECK_POOL_IS_VALID_NORET(gtm->sessions, f->key.session_index);
      sess = pool_elt_at_index (gtm->sessions, f->key.session_index);
      if (0 != sess->lock)
        {
          clib_spinlock_lock (&sess->lock);
          active = upf_get_rules (sess, SX_ACTIVE);

          for (int i = 0; i < FT_ORDER_MAX; i++)
            {
              if ((~0 != f->pdr_index[i]) &&
                  (f->pdr_index[i] < vec_len (active->pdr)))
                {
                  pdr = vec_elt_at_index (active->pdr, f->pdr_index[i]);
                  CHECK_VEC_VALUE_ISVALID_NORET(active->pdr, pdr);
                  if (~0 != (index = vec_search (pdr->flow_table_ids,
                                                 f - fm->flows)))
                    vec_del1 (pdr->flow_table_ids, index);
                }
            }
          clib_spinlock_unlock (&sess->lock);
        }
    }
  // clib_spinlock_free (&f->lock);
  upf_inflate_del_func (f);

  vec_add1 (fmt->flow_cache, f - fm->flows);

  if (vec_len (fmt->flow_cache) > 2 * FLOW_CACHE_SZ)
    upf_flow_cache_empty (fm, fmt);
}

always_inline void
upf_flow_entry_free (flowtable_main_t *fm, flowtable_main_per_cpu_t *fmt,
                 flow_entry_t *f)
{
  upf_call_rcu_func (&f->rcu_entry, upf_flow_entry_free_rcu);
}

always_inline void
upf_flowtable_entry_remove (flowtable_main_t *fm, flow_entry_t *f)
{
  BVT (clib_bihash_kv) kv;

  clib_memcpy (kv.key, f->key.key, sizeof (kv.key));
  clib_bihash_add_del_64_8 (&fm->flows_ht, &kv, 0 /* is_add */);
}

always_inline int
upf_expire_single_flow (flowtable_main_t *fm, flowtable_main_per_cpu_t *fmt,
                    flow_entry_t *f, dlist_elt_t *e, u32 now)
{
  ASSERT (f->timer_index == (e - fmt->timers));

  /* timers unlink */
  clib_dlist_remove (fmt->timers, e - fmt->timers);

  /*upf_trace ("[IN] Flow Timeout Check %p: %u (%u) > %u (%u)", f,
             f->active + f->lifetime,
             (f->active + f->lifetime) % fm->timer_max_lifetime, now,
             fmt->time_index);*/

  if (f->active + f->lifetime > now)
    {
      /* There was activity on the entry, so the idle timeout
     has not passed. Enqueue for another time period. */
      u32 timer_slot_head_index;

      timer_slot_head_index =
          (f->active + f->lifetime) % fm->timer_max_lifetime;
      //upf_trace ("Flow Reshedule %p to %u", f, timer_slot_head_index);
      clib_dlist_addtail (fmt->timers, timer_slot_head_index, f->timer_index);
      return 0;
    }
  else
    {
      //upf_trace ("Flow Remove %p", f);
      pool_put (fmt->timers, e);

      /* hashtable unlink */
      upf_flowtable_entry_remove (fm, f);

      /* free to flow cache && pool (last) */
      upf_flow_entry_free (fm, fmt, f);
      return 1;
    }
}

u64
upf_flowtable_timer_expire (flowtable_main_t *fm, flowtable_main_per_cpu_t *fmt,
                        u32 now)
{
  u64 expire_cpt;
  u64 expire_check_cpt;
  flow_entry_t *f;
  u32 *time_slot_curr_index;
  dlist_elt_t *time_slot_curr;
  u32 index;

  time_slot_curr_index = vec_elt_at_index (fmt->timer_wheel, fmt->time_index);
  CHECK_VEC_VALUE_ISVALID_RET(fmt->timer_wheel, time_slot_curr_index, 0);

  if (PREDICT_FALSE (dlist_is_empty (fmt->timers, *time_slot_curr_index)))
    return 0;

  expire_cpt = 0;
  expire_check_cpt = 0;
  CHECK_POOL_IS_VALID_RET(fmt->timers, *time_slot_curr_index, 0);
  time_slot_curr = pool_elt_at_index (fmt->timers, *time_slot_curr_index);

  index = time_slot_curr->next;
  while (index != *time_slot_curr_index && expire_check_cpt < TIMER_MAX_EXPIRE)
    {
      CHECK_POOL_IS_VALID_CONTINUE(fmt->timers, index);
      dlist_elt_t *e = pool_elt_at_index (fmt->timers, index);
      f = pool_elt_at_index (fm->flows, e->value);
      if (pool_is_free_index (fm->flows, e->value))
        continue;
      index = e->next;
      if (0 != upf_expire_single_flow (fm, fmt, f, e, now))
        expire_cpt++;
      expire_check_cpt++;
    }

  return expire_cpt;
}

u16
upf_flowtable_lifetime_calculate (flowtable_main_t *fm, flow_key_t const *key)
{
  switch (key->proto)
    {
    case IP_PROTOCOL_ICMP:
      return fm->timer_lifetime[FT_TIMEOUT_TYPE_ICMP];

    case IP_PROTOCOL_UDP:
      return fm->timer_lifetime[FT_TIMEOUT_TYPE_UDP];

    case IP_PROTOCOL_TCP:
      return fm->timer_lifetime[FT_TIMEOUT_TYPE_TCP];

    default:
      return ip46_address_is_ip4 (&key->ip[FT_ORIGIN])
                 ? fm->timer_lifetime[FT_TIMEOUT_TYPE_IPV4]
                 : fm->timer_lifetime[FT_TIMEOUT_TYPE_IPV6];
    }

  return fm->timer_lifetime[FT_TIMEOUT_TYPE_UNKNOWN];
}

static void
upf_recycle_flow (flowtable_main_t *fm, flowtable_main_per_cpu_t *fmt, u32 now)
{
  u32 next;

  upf_trace("[IN] now:%u", now);

  now = now % fm->timer_max_lifetime;
  next = (now + 1) % fm->timer_max_lifetime;
  while (PREDICT_FALSE (next != now))
    {
      flow_entry_t *f;
      u32 *slot_index = vec_elt_at_index (fmt->timer_wheel, next);
      CHECK_VEC_VALUE_ISVALID_CONTINUE(fmt->timer_wheel, slot_index);  
      
      if (PREDICT_FALSE (dlist_is_empty (fmt->timers, *slot_index)))
        {
          next = (next + 1) % fm->timer_max_lifetime;
          continue;
        }
      CHECK_POOL_IS_VALID_CONTINUE(fmt->timers, *slot_index);
      dlist_elt_t *head = pool_elt_at_index (fmt->timers, *slot_index);
      CHECK_POOL_IS_VALID_CONTINUE(fmt->timers, head->next);
      dlist_elt_t *e = pool_elt_at_index (fmt->timers, head->next);

      CHECK_POOL_IS_VALID_CONTINUE(fm->flows, e->value);
      f = pool_elt_at_index (fm->flows, e->value);
      upf_expire_single_flow (fm, fmt, f, e, now);
      return;
    }

  /*
   * unreachable:
   * this should be called if there is no free flows, so we're bound to have
   * at least *one* flow within the timer wheel (cpu cache is filled at init).
   */
  upf_info ("upf_recycle_flow did not find any flow to recycle !");
}

flow_entry_t *
upf_flowtable_entry_lookup (clib_bihash_kv_64_8_t *kv)
{
  if (PREDICT_FALSE (
          clib_bihash_search_inline_64_8 (&g_flowtable_main.flows_ht, kv) == 0))
    {
      CHECK_POOL_IS_VALID_RET(g_flowtable_main.flows, kv->value, NULL);
      return pool_elt_at_index (g_flowtable_main.flows, kv->value);
    }

  return NULL;
}

/* TODO: replace with a more appropriate hashtable */
flow_entry_t *
upf_flowtable_entry_create (flowtable_main_t *fm, flowtable_main_per_cpu_t *fmt,
                        clib_bihash_kv_64_8_t *kv, u32 const now,
                        u8 is_reverse, int *recycled)
{
  flow_entry_t *f;
  dlist_elt_t *timer_entry;
  vlib_main_t *vm = vlib_get_main ();

  upf_trace("[IN] now:%u, is_reverse:%u, ", now, is_reverse);

  /* create new flow */
  f = upf_flow_entry_alloc (fm, fmt);
  if (PREDICT_FALSE (f == NULL))
    {
      upf_recycle_flow (fm, fmt, now);
      *recycled = 1;

      f = upf_flow_entry_alloc (fm, fmt);
      if (PREDICT_FALSE (f == NULL))
        {
          clib_warning ("flowtable failed to recycle a flow");
          return NULL;
        }
    }

  clib_memset (f, 0, sizeof (*f));
  clib_memcpy (f->key.key, kv->key, sizeof (f->key.key));
  f->is_reverse = is_reverse;
  f->thread_index = vm->thread_index;
  f->lifetime = upf_flowtable_lifetime_calculate (fm, &f->key);
  f->active = now;
  clib_memset (&f->pdr_index, ~0, sizeof (f->pdr_index));
  clib_memset (&f->second_pdr_index, ~0, sizeof (f->second_pdr_index));
  clib_spinlock_init (&f->lock);
  f->flowcache[0] = NULL;
  f->flowcache[1] = NULL;
  f->application_id = ~0;
  f->urr_id = ~0;

  f->is_update = ~0;
  f->update_timestamp = ~0;
  f->redirect_type = 0;
  /* BEGIN: Add by wangjunjie02 for http enhancement on 20210524 */
  f->http_header_enhancement_flag = 0;
  f->http_header_insert_length = 0;
  /* END: Add by wangjunjie02 for http enhancement on 20210524 */

  f->https_header_enhancement_flag = 0;
  f->https_header_insert_length = 0;
  f->vn_sess_idx = ~0;
  f->vn_pdr_idx = ~0;

  /* insert in timer list */
  pool_get (fmt->timers, timer_entry);
  timer_entry->value = f - fm->flows;         /* index within the flow pool */
  f->timer_index = timer_entry - fmt->timers; /* index within the timer pool */
  upf_timer_wheel_insert_flow (fm, fmt, f);

  /* insert in hash */
  kv->value = f - fm->flows;
  clib_bihash_add_del_64_8 (&fm->flows_ht, kv, 1 /* is_add */);

  return f;
}

void
upf_flowtable_timer_update (flowtable_main_t *fm, flowtable_main_per_cpu_t *fmt,
                          u32 now)
{
  u32 new_index = now % fm->timer_max_lifetime;

  if (PREDICT_FALSE (fmt->time_index == ~0))
    {
      fmt->time_index = new_index;
      return;
    }

  if (new_index != fmt->time_index)
    {
      /* reschedule all remaining flows on current time index
       * at the begining of the next one */

      u32 *curr_slot_index =
          vec_elt_at_index (fmt->timer_wheel, fmt->time_index);
      CHECK_VEC_VALUE_ISVALID_NORET(fmt->timer_wheel, curr_slot_index);

      CHECK_POOL_IS_VALID_NORET(fmt->timers, *curr_slot_index);
      dlist_elt_t *curr_head =
          pool_elt_at_index (fmt->timers, *curr_slot_index);

      u32 *next_slot_index = vec_elt_at_index (fmt->timer_wheel, new_index);
      CHECK_VEC_VALUE_ISVALID_NORET(fmt->timer_wheel, next_slot_index);

      CHECK_POOL_IS_VALID_NORET(fmt->timers, *next_slot_index);
      dlist_elt_t *next_head =
          pool_elt_at_index (fmt->timers, *next_slot_index);

      if (PREDICT_FALSE (dlist_is_empty (fmt->timers, *curr_slot_index)))
        {
          fmt->time_index = new_index;
          return;
        }

      CHECK_POOL_IS_VALID_NORET(fmt->timers, curr_head->prev);
      dlist_elt_t *curr_prev =
          pool_elt_at_index (fmt->timers, curr_head->prev);

      CHECK_POOL_IS_VALID_NORET(fmt->timers, curr_head->next);
      dlist_elt_t *curr_next =
          pool_elt_at_index (fmt->timers, curr_head->next);

      /* insert timer list of current time slot at the begining of the next
       * slot */
      if (PREDICT_FALSE (dlist_is_empty (fmt->timers, *next_slot_index)))
        {
          next_head->next = curr_head->next;
          next_head->prev = curr_head->prev;
          curr_prev->next = *next_slot_index;
          curr_next->prev = *next_slot_index;
        }
      else
        {
          CHECK_POOL_IS_VALID_NORET(fmt->timers, next_head->next);
          dlist_elt_t *next_next =
              pool_elt_at_index (fmt->timers, next_head->next);
          curr_prev->next = next_head->next;
          next_head->next = curr_head->next;
          next_next->prev = curr_head->prev;
          curr_next->prev = *next_slot_index;
        }

      /* reset current time slot as an empty list */
      memset (curr_head, 0xff, sizeof (*curr_head));

      fmt->time_index = new_index;
    }
}

void
upf_flow_cache_rcu_free (struct upf_rcu_entry_st *head)
{
  struct flowcache_rcu_info *info =
      container_of (head, struct flowcache_rcu_info, rcu_entry);

  if (info->flowcache[FT_ORIGIN])
    vec_free (info->flowcache[FT_ORIGIN]);
  if (info->flowcache[FT_REVERSE])
    vec_free (info->flowcache[FT_REVERSE]);

  clib_mem_free (info);
}

void
upf_flowtable_reset (flow_entry_t *flow)
{
  // struct flowcache_rcu_info *info;

  if (!flow->lock)
    return;
  clib_spinlock_lock (&flow->lock);
  memset (&flow->pdr_index, ~0, sizeof (flow->pdr_index));
  memset (&flow->stats, 0, sizeof (flow->stats));
  flow->application_id = ~0;
  flow->urr_id = ~0;

  if (flow->flowcache[FT_ORIGIN])
    vec_free (flow->flowcache[FT_ORIGIN]);
  if (flow->flowcache[FT_REVERSE])
    vec_free (flow->flowcache[FT_REVERSE]);
  clib_spinlock_unlock (&flow->lock);
#if 0
  info = clib_mem_alloc_no_fail (sizeof (*info));
  info->flowcache[FT_ORIGIN] = flow->flowcache[FT_ORIGIN];
  info->flowcache[FT_REVERSE] = flow->flowcache[FT_REVERSE];
  upf_call_rcu_func (&info->rcu_entry, upf_flow_cache_rcu_free);
#endif
}
u8 *
upf_format_flow_key (u8 *s, va_list *args)
{
  flow_key_t *key = va_arg (*args, flow_key_t *);

  return format (s, "proto 0x%x, %U-%u <-> %U-%u, session %llu", key->proto,
                 format_ip46_address, &key->ip[FT_ORIGIN], IP46_TYPE_ANY,
                 clib_net_to_host_u16 (key->port[FT_ORIGIN]),
                 format_ip46_address, &key->ip[FT_REVERSE], IP46_TYPE_ANY,
                 clib_net_to_host_u16 (key->port[FT_REVERSE]),
                 key->session_index);
}

u8 *
upf_format_flow (u8 *s, va_list *args)
{
  flow_entry_t *flow = va_arg (*args, flow_entry_t *);
  int is_reverse = flow->is_reverse;
  flowtable_main_t *fm = &g_flowtable_main;
  u32 now = vlib_time_now (vlib_get_main ());

  return format (s,
                 "[%u] %U, UL pkt %u, DL pkt %u, "
                 "Forward PDR %u, Reverse PDR %u, "
                 "deactive time %u",
                 flow - fm->flows, upf_format_flow_key, &flow->key,
                 flow->stats[is_reverse].pkts,
                 flow->stats[is_reverse ^ FT_REVERSE].pkts,
                 flow->pdr_index[is_reverse],
                 flow->pdr_index[is_reverse ^ FT_REVERSE], now - flow->active);
}

static clib_error_t *
upf_vnet_flow_timeout_update (flowtable_timeout_type_t type, u16 timeout)
{
  return upf_flowtable_lifetime_update (type, timeout);
}

static u16
upf_vnet_get_flow_timeout (flowtable_timeout_type_t type)
{
  return flowtable_lifetime_get (type);
}

// Add for eth single trace by liupeng on 2022-06-27 below
u8 get_single_trace_interface_type_in(upf_pdr_t *pdr, u16 task_id, upf_single_trace_push_t *grab, bool *gtpflag)
{
    if ((pdr == NULL) || (grab == NULL))
    {
        upf_err("buffer or grab is NULL .\n");
        return 0;
    }

    u8 is_push = 0;
    if ((pdr->pdi.source_interface_type >= 11 && pdr->pdi.source_interface_type <= 14)
        || pdr->pdi.source_interface_type == 0)
    {
        //N3
        if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N3)
        {
            grab->interface_type = UPF_SINGLE_TRACE_INTF_N3;
            is_push = 1;
        }
    }
    else if (pdr->pdi.source_interface_type == 15)
    {
        //N9
        if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N9)
        {
            grab->interface_type = UPF_SINGLE_TRACE_INTF_N9;
            is_push = 1;
        }
        *gtpflag = false;
    }
    else if (pdr->pdi.source_interface_type == 17 || pdr->pdi.source_interface_type == 16)
    {
        //N6
        if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N6)
        {
            grab->interface_type = UPF_SINGLE_TRACE_INTF_N6;
            is_push = 1;
        }
    }
    else
    {
        upf_debug ("Unknown source interface type:%u !", pdr->pdi.source_interface_type);
    }

    return is_push;
}

u8 get_single_trace_interface_type_out(upf_far_t *far, u16 task_id, upf_single_trace_push_t * grab)
{
    if ((far == NULL) || (grab == NULL))
    {
        upf_err("far or grab is NULL .\n");
        return 0;
    }

    u8 is_push = 0;
    if ((far->forward.dest_interface_type >= 11 && far->forward.dest_interface_type <= 14)
        || far->forward.dest_interface_type == 0)
    {
        //N3
        if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N3)
        {
            grab->interface_type = UPF_SINGLE_TRACE_INTF_N3;
            is_push = 1;
        }
    }
    else if (far->forward.dest_interface_type == 15)
    {
        //N9
        if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N9)
        {
            grab->interface_type = UPF_SINGLE_TRACE_INTF_N9;
            is_push = 1;
        }
    }
    else if (far->forward.dest_interface_type == 17 || far->forward.dest_interface_type == 16)
    {
        //N6
        if (g_upf_single_trace[task_id].interface_type & UPF_SINGLE_TRACE_INTF_N6)
        {
            grab->interface_type = UPF_SINGLE_TRACE_INTF_N6;
            is_push = 1;
        }
    }
    else
    {
        upf_debug ("Unknown destination interface type:%u !", far->forward.dest_interface_type);
    }

    return is_push;
}
// Add for eth single trace by liupeng on 2022-06-27 above

static clib_error_t *
upf_flow_timeout_set_command_fn (vlib_main_t *vm, unformat_input_t *main_input,
                             vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  u16 timeout = 0;
  clib_error_t *error = NULL;
  flowtable_timeout_type_t type = FT_TIMEOUT_TYPE_UNKNOWN;

  /* Get a line of input. */
  if (!unformat_user (main_input, unformat_line_input, line_input))
    return error;

  /* begin added by liuyu MANAGE_STATE 2022-11-01 */
  char manageState[20] = {0};
  
  if (iupf_get_manage_state_from_file(vm, manageState) == true)
  {
      vlib_cli_output (vm, "set cmd err, current manage state is %s\n", manageState);
      unformat_free (line_input);
      return error;
  }
  /* end added by liuyu MANAGE_STATE 2022-11-01 */

  while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
    {
      /* modified by liuyu UPF_CMD_MOD 200-08-22 */
      if (unformat (line_input, "ipv4 %u", &timeout))
        {
          type = FT_TIMEOUT_TYPE_IPV4;
          break;
        }
      /* modified by liuyu UPF_CMD_MOD 200-08-22 */
      if (unformat (line_input, "ipv6 %u", &timeout))
        {
          type = FT_TIMEOUT_TYPE_IPV6;
          break;
        }
      if (unformat (line_input, "icmp %u", &timeout))
        {
          type = FT_TIMEOUT_TYPE_ICMP;
          break;
        }
      if (unformat (line_input, "udp %u", &timeout))
        {
          type = FT_TIMEOUT_TYPE_UDP;
          break;
        }
      if (unformat (line_input, "tcp %u", &timeout))
        {
          type = FT_TIMEOUT_TYPE_TCP;
          break;
        }
      else
        {
          error = unformat_parse_error (line_input);
          goto done;
        }
    }
  error = upf_vnet_flow_timeout_update (type, timeout);
  /* begin added by liuyu CMD_SUCESS_RES 2023-02-23 */
  if (error == NULL)
  {
      vlib_cli_output (vm, "%s\n", CLI_CMD_SUCCESS_RES);
      return error;
  }
  /* end added by liuyu CMD_SUCESS_RES 2023-02-23 */

done:
  unformat_free (line_input);

  return error;
}
/* begin modified by liuyu UPF_CMD_MOD 200-08-22 */
/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_flow_timeout_set_command, static) = {
    .path = "upf set flowtable overtime",
    .short_help = "upf set flowtable overtime (ipv4 | ipv6 | icmp | udp | "
                  "tcp) <sec>",
    .function = upf_flow_timeout_set_command_fn,
};
/* *INDENT-ON* */
/* end modified by liuyu UPF_CMD_MOD 200-08-22 */
static clib_error_t *
upf_flow_timeout_show_command_fn (vlib_main_t *vm, unformat_input_t *input,
                                  vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  u16 timeout = 0;
  clib_error_t *error = NULL;
  flowtable_timeout_type_t type = FT_TIMEOUT_TYPE_UNKNOWN;

  /* Get a line of input. */
  if (!unformat_user (input, unformat_line_input, line_input))
    return error;

  while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
    {
      if (unformat (line_input, "ip4"))
        {
          type = FT_TIMEOUT_TYPE_IPV4;
          break;
        }
      if (unformat (line_input, "ip6"))
        {
          type = FT_TIMEOUT_TYPE_IPV6;
          break;
        }
      if (unformat (line_input, "icmp"))
        {
          type = FT_TIMEOUT_TYPE_ICMP;
          break;
        }
      if (unformat (line_input, "udp"))
        {
          type = FT_TIMEOUT_TYPE_UDP;
          break;
        }
      if (unformat (line_input, "tcp"))
        {
          type = FT_TIMEOUT_TYPE_TCP;
          break;
        }
      else
        {
          error = unformat_parse_error (line_input);
          goto done;
        }
    }

  timeout = upf_vnet_get_flow_timeout (type);
  vlib_cli_output (vm, "%u", timeout);

done:
  unformat_free (line_input);

  return error;
}
/* begin modified by liuyu UPF_CMD_MOD 2022-08-22 */
/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_flow_timeout_show_command, static) = {
    .path = "show upf flowtable overtime",
    .short_help =
        "show upf flowtable overtime (ip4 | ip6 | icmp | udp | tcp)",
    .function = upf_flow_timeout_show_command_fn,
};
/* *INDENT-ON* */
/* end modified by liuyu UPF_CMD_MOD 2022-08-22 */

/*
 * fd.io coding-style-patch-verification: ON
 *
 * Local Variables:
 * eval: (c-set-style "gnu")
 * End:
 */
