/*
 * Copyright (c) 2017 Travelping GmbH
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <vlib/vlib.h>
#include <vnet/vnet.h>
#include <vnet/pg/pg.h>
#include <vppinfra/error.h>
#include <vppinfra/hash.h>
#include <vppinfra/vec.h>

#include "upf.h"
#include "upf_pfcp.h"
#include "upf_pfcp_server.h"

/*?
 * configure session persistence for N4 link anomaly scenarios
 *
 * @cliexpar
 * enable session persistence for N4 link anomaly scenarios:
 * @clistart
 * set upf n4-persist enable
 * set upf n4-persist disable
 * set upf n4-persist time 300
 * set upf n4-persist max-missed-heartbeats 5
 * @cliend
 * @cliexcmd{set upf n4-persist <enable|disable|time <seconds>|max-missed-heartbeats <count>>}
?*/
// added by caozhongwei 2025-07-11
static clib_error_t *
upf_n4_persist_command_fn (vlib_main_t * vm,
                         unformat_input_t * input,
                         vlib_cli_command_t * cmd)
{
  upf_main_t *gtm = &g_upf_main;
  u8 enable_disable_set = 0;
  u8 enable = 0;
  u32 persist_time = 0;
  u32 max_missed_heartbeats = 0;
  u8 got_time = 0;
  u8 got_max_missed = 0;

  while (unformat_check_input (input) != UNFORMAT_END_OF_INPUT)
    {
      if (unformat (input, "enable"))
        {
          enable = 1;
          enable_disable_set = 1;
        }
      else if (unformat (input, "disable"))
        {
          enable = 0;
          enable_disable_set = 1;
        }
      else if (unformat (input, "time %u", &persist_time))
        got_time = 1;
      else if (unformat (input, "max-missed-heartbeats %u", &max_missed_heartbeats))
        got_max_missed = 1;
      else
        return clib_error_return (0, "unknown input `%U'",
                                 format_unformat_error, input);
    }

  // Apply changes based on what was specified
  if (enable_disable_set)
    {
      gtm->n4_persist.enabled = enable;
      // Ensure default values are set when enabling (they should already be set during init)
      if (enable)
        {
          if (gtm->n4_persist.persist_time == 0)
            gtm->n4_persist.persist_time = 300;  // default 300 seconds
          if (gtm->n4_persist.max_missed_heartbeats == 0)
            gtm->n4_persist.max_missed_heartbeats = 3;  // default 3 times
        }
    }

  if (got_time)
    gtm->n4_persist.persist_time = persist_time;

  if (got_max_missed)
    gtm->n4_persist.max_missed_heartbeats = max_missed_heartbeats;

  vlib_cli_output (vm, "N4 session persist %s, time: %u seconds, max missed heartbeats: %u",
                  gtm->n4_persist.enabled ? "enabled" : "disabled",
                  gtm->n4_persist.persist_time,
                  gtm->n4_persist.max_missed_heartbeats);

  return 0;
}

VLIB_CLI_COMMAND (upf_n4_persist_command, static) =
{
  .path = "set upf n4-persist",
  .short_help = "set upf n4-persist <enable|disable|time <seconds>|max-missed-heartbeats <count>>",
  .function = upf_n4_persist_command_fn,
};

/*?
 * show session persistence configuration for N4 link anomaly scenarios
 *
 * @cliexpar
 * show current n4-persist configuration:
 * @clistart
 * show upf n4-persist
 * @cliend
 * @cliexcmd{show upf n4-persist}
?*/
static clib_error_t *
upf_show_n4_persist_command_fn (vlib_main_t * vm,
                               unformat_input_t * input,
                               vlib_cli_command_t * cmd)
{
  upf_main_t *gtm = &g_upf_main;
  f64 now = unix_time_now();

  vlib_cli_output (vm, "N4 session persistence configuration:");
  vlib_cli_output (vm, "  Status: %s",
                  gtm->n4_persist.enabled ? "enabled" : "disabled");
  vlib_cli_output (vm, "  Persistence time: %u seconds",
                  gtm->n4_persist.persist_time);
  vlib_cli_output (vm, "  Max missed heartbeats: %u",
                  gtm->n4_persist.max_missed_heartbeats);

  // Determine current link status: use explicit link_status flag,
  // because link may be set to anomaly via T1 exhaustion even if missed_heartbeats < threshold
  const char* link_status_str = gtm->n4_persist.link_status ? "normal" : "anomaly";

  vlib_cli_output (vm, "  Current link status: %s", link_status_str);
  vlib_cli_output (vm, "  Current missed heartbeats: %u",
                  gtm->n4_persist.missed_heartbeats);

  // Show persistence information only when link is anomaly and persistence is enabled
  if (gtm->n4_persist.link_status == 0 && gtm->n4_persist.enabled)
  {
    if (gtm->n4_persist.timeout_timestamp > 0)
    {
      f64 remaining = gtm->n4_persist.timeout_timestamp - now;
      if (remaining > 0)
        vlib_cli_output (vm, "  Session persist remaining time: %.2f seconds", remaining);
      else
        vlib_cli_output (vm, "  Session persist time expired");
    }
    else
    {
      vlib_cli_output (vm, "  Session persist timeout not set");
    }
  }

  return 0;
}

VLIB_CLI_COMMAND (upf_show_n4_persist_command, static) =
{
  .path = "show upf n4-persist",
  .short_help = "show upf n4-persist",
  .function = upf_show_n4_persist_command_fn,
};

// Test-only CLI to simulate N4 link anomaly/normal for unit tests
static clib_error_t *
upf_test_n4_persist_command_fn (vlib_main_t * vm,
                               unformat_input_t * input,
                               vlib_cli_command_t * cmd)
{
  upf_main_t *gtm = &g_upf_main;
  u8 set_anomaly = 0, set_normal = 0;
  u32 pt = 0; u8 have_pt = 0;

  while (unformat_check_input (input) != UNFORMAT_END_OF_INPUT)
    {
      if (unformat (input, "link anomaly"))
        set_anomaly = 1;
      else if (unformat (input, "link normal"))
        set_normal = 1;
      else if (unformat (input, "persist_time %u", &pt))
        have_pt = 1;
      else
        return clib_error_return (0, "unknown input");
    }

  if (!set_anomaly && !set_normal)
    return clib_error_return (0, "must specify link anomaly|normal");

  if (set_anomaly)
    {
      gtm->n4_persist.link_status = 0;
      if (gtm->n4_persist.enabled)
        {
          f64 now = unix_time_now ();
          f64 win = have_pt ? pt : gtm->n4_persist.persist_time;
          gtm->n4_persist.timeout_timestamp = now + win;
        }
    }
  else
    {
      gtm->n4_persist.link_status = 1;
      gtm->n4_persist.missed_heartbeats = 0;
      gtm->n4_persist.timeout_timestamp = 0;
    }

  return 0;
}

VLIB_CLI_COMMAND (upf_test_n4_persist_command, static) =
{
  .path = "test upf n4-persist",
  .short_help = "test upf n4-persist link [anomaly|normal] [persist_time <sec>]",
  .function = upf_test_n4_persist_command_fn,
};
