


/*
 * Copyright (c) 2018 Travelping GmbH
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at:
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <inttypes.h>

#include <vppinfra/error.h>
#include <vppinfra/hash.h>
#include <vnet/vnet.h>
#include <vnet/ip/ip.h>
#include <vnet/fib/ip4_fib.h>
#include <vnet/fib/ip6_fib.h>
#include <vnet/ethernet/ethernet.h>
#include <vnet/vxlan/vxlan.h>
#include <vnet/gre/gre.h>
#include <vpp/stats/stat_segment.h>
#include <arpa/inet.h>


#include <upf/upf.h>
#include <upf/upf_pfcp.h>
#include <upf/upf_pfcp_server.h>

#include <upf/upf_5glan.h>
#include <upf/upf_pfcp_api.h>
#include <vlib/unix/unix.h>
/* end added by liuyu LICENSE_EXPIRE_ALARM 2022-02-14 */
#include <upf/upf_duplicate_buffer.h>
#include <upf/flowtable_eth_node.h>

#include "flowtable_eth_node.h"
#include "upf_https_client_hello.h"
#include "upf_pfcp.h"

//#include <upf/upf_http_redirect_server.h>
vlib_node_registration_t upf_process_ip4_node;
vlib_node_registration_t upf_process_ip6_node;

vlib_node_registration_t upf_process_ethernet_node;  

#define UPF_PROCESS_IP_N_NEXT (UPF_PROCESS_IP_NEXT_LAST)

typedef enum
{
    UPF_PROCESS_ETH_NEXT_DROP,
    UPF_PROCESS_ETH_NEXT_GTP_IP4_ENCAP,
    UPF_PROCESS_ETH_NEXT_GTP_IP6_ENCAP,
    UPF_PROCESS_ETH_NEXT_FLOWTABLE_ETH_INPUT,
    UPF_PROCESS_ETH_NEXT_5GLAN_ETH,
    UPF_PROCESS_ETH_NEXT_N6_ENCAP,
    UPF_PROCESS_ETH_NEXT_N,
}upf_process_eth_next_t;

#define foreach_upf_process_next \
  _ (DROP, "error-drop")            \
  _ (GTP_IP4_ENCAP, "upf4-encap")   \
  _ (GTP_IP6_ENCAP, "upf6-encap")   \
  _ (IP4_INPUT, "ip4-input")        \
  _ (IP6_INPUT, "ip6-input")        \
  _ (IP4_LOOKUP, "ip4-lookup")      \
  _ (IP6_LOOKUP, "ip6-lookup")      \
  _ (IP4_LOCAL, "ip4-local")        \
  _ (IP6_LOCAL, "ip6-local")        \
  _ (IP4_FRAG, "ip4-frag")          \
  _ (IP6_FRAG, "ip6-frag")          \
  _ (VXLAN4_ENCAP, "vxlan4-encap")  \
  _ (VXLAN6_ENCAP, "vxlan6-encap")  \
  _ (UPF_5GLAN_IP4, "upf-5glan-ip4")\
  _ (UPF_5GLAN_IP6, "upf-5glan-ip6")\
  _ (N6_ENCAP, "upf-n6-encap")      \
  _ (L2_INPUT, "l2-input")          \

/* Statistics (not all errors) */
#define foreach_upf_process_error                 \
  _ (NO_LISTENER, "no redirect server available") \
  _ (PROCESS, "good packets process")             \
  _ (DROP, "packets dropped")                     \
  _ (BUFFERING, "buffering packets")              \
  _ (MAX_BUFFERING, "maximum data buffering exceeded")

static char *g_upf_process_error_strings[] = {
#define _(sym, string) string,
    foreach_upf_process_error
#undef _
};

typedef enum
{
#define _(sym, str) UPF_PROCESS_ERROR_##sym,
  foreach_upf_process_error
#undef _
      UPF_PROCESS_N_ERROR,
} upf_process_error_t;

typedef struct
{
  u64 up_seid;
  u64 cp_seid;
  u32 pdr_id;
  u32 far_id;
  u8 cached;
  u8 resv[3];
  u32 next;
  u8 packet_data[64 - 1 * sizeof (u32)];
} upf_process_trace_t;

u32 upf_far_proxy_handle(vlib_main_t *vm, vlib_buffer_t *b, u8 direction, upf_session_t *sx, upf_pdr_t *pdr, upf_far_t **far0);

u8 *
format_upf_ip_process_trace (u8 *s, va_list *args)
{
  CLIB_UNUSED (vlib_main_t * vm) = va_arg (*args, vlib_main_t *);
  CLIB_UNUSED (vlib_node_t * node) = va_arg (*args, vlib_node_t *);
  upf_process_trace_t *t = va_arg (*args, upf_process_trace_t *);

  if (t->next == UPF_PROCESS_IP_NEXT_L2_INPUT)
  {
      u32 indent = format_get_indent (s);
      s = format (
          s, "up_seid:0x%lx cp_seid:0x%lx pdr_id %u far_id %u cached:%u next:%u\n%U%U",
          t->up_seid, t->cp_seid, t->pdr_id, t->far_id, t->cached, t->next,
          format_white_space, indent,
          format_ethernet_header,
          t->packet_data, sizeof (t->packet_data));
  }
  else
  {
      ip4_header_t *ih4 = (ip4_header_t *)t->packet_data;
      u32 indent = format_get_indent (s);
      s = format (
          s, "up_seid:0x%lx cp_seid:0x%lx pdr_id %u far_id %u cached:%u next:%u\n%U%U",
          t->up_seid, t->cp_seid, t->pdr_id, t->far_id, t->cached, t->next,
          format_white_space, indent,
          (ih4->ip_version_and_header_length & 0xF0) == 0x60 ? format_ip6_header : format_ip4_header,
          t->packet_data, sizeof (t->packet_data));
  }
  return s;
}

u8 *
format_upf_eth_process_trace (u8 *s, va_list *args)
{
  CLIB_UNUSED (vlib_main_t * vm) = va_arg (*args, vlib_main_t *);
  CLIB_UNUSED (vlib_node_t * node) = va_arg (*args, vlib_node_t *);
  upf_process_trace_t *t = va_arg (*args, upf_process_trace_t *);

  s = format (
      s, "up_seid 0x%lx cp_seid:0x%lx pdr_id %d far_id %d cached:%u next:%u\n",
      t->up_seid, t->cp_seid, t->pdr_id, t->far_id, t->cached, t->next);
  return s;
}

static int
upf_buffering_packet (upf_session_t *sess, upf_far_t *far, upf_bar_t *bar,
                          u32 bi, u8 direction, upf_pdr_t *pdr)
{
  vlib_main_t *vm = vlib_get_main ();
  upf_main_t *um = &g_upf_main;
  f64 now = vlib_time_now (vm);

  ASSERT (direction < UPF_DIRECTION_MAX);

  clib_spinlock_lock (&sess->lock);
  if (um->far_buffering_n >= um->max_buffering_n)
    {
      upf_err ("buffer full, far_buffering_n %u downlink_buf_n %u\n",
                 um->far_buffering_n, far->downlink_buf_n);
      clib_spinlock_unlock (&sess->lock);
      UPF_STATISTICS_ADD(DROP_FOR_EXCEED_UPF_BUFFERING_COUNT);
      UPF_PDU_SESS_STATISTICS_ADD(sess, DROP_FOR_EXCEED_UPF_BUFFERING_COUNT);
      return 1;
    }

  if (bar)
    {
      if (direction == UPF_DL)
        {
          if (bar->dl_buffering_duration != 0)
            {
              if (bar->dl_buffering_start_time == 0)
                bar->dl_buffering_start_time =
                    vlib_time_now (vlib_get_main ());

              if (now - bar->dl_buffering_start_time >
                  bar->dl_buffering_duration)
                {
                  upf_debug ("bar start %f now %f duration %u\n",
                             bar->dl_buffering_start_time, now,
                             bar->dl_buffering_duration);
                  upf_pfcp_downlink_data_report (sess, pdr);
                  UPF_PDU_SESS_STATISTICS_ADD(sess, SENT_DL_DATA_REPORT_TO_SMF);
                  // return -1;
                }
            }

          if (far->downlink_buf_n >= bar->dl_buffering_suggested_packet_count)
            {
              upf_debug (
                  "bar buffer full, dl_buffering_suggested_packet_count %u "
                  "downlink_buf_n %u\n",
                  bar->dl_buffering_suggested_packet_count,
                  far->downlink_buf_n);
              clib_spinlock_unlock (&sess->lock);
              UPF_STATISTICS_ADD(DROP_FOR_EXCEED_BAR_BUFFERING_COUNT);
              UPF_PDU_SESS_STATISTICS_ADD(sess, DROP_FOR_EXCEED_BAR_BUFFERING_COUNT);
              return 1;
            }

          clib_atomic_fetch_add (&far->downlink_buf_n, 1);
        }
      else
        {
          clib_atomic_fetch_add (&far->uplink_buf_n, 1);
        }

      vec_add1 (far->buffer_bi[direction], bi);

      clib_atomic_fetch_add (&um->far_buffering_n, 1);
    }
  else if (direction == UPF_DL)
    {
      // Add for session level default buffing packets number by fanbinfeng on 20210817 begin
      if (far->downlink_buf_n < UPF_BUF_PKTS_NUM_PER_SESSION)
        {
          clib_atomic_fetch_add (&far->downlink_buf_n, 1);
          vec_add1 (far->buffer_bi[direction], bi);
          clib_atomic_fetch_add (&um->far_buffering_n, 1);
        }
        else // buffer full, drop it
        {
            clib_spinlock_unlock (&sess->lock);
            UPF_STATISTICS_ADD(DROP_FOR_EXCEED_UPF_BUF_PKTS_NUM_PER_SESSION);
            UPF_PDU_SESS_STATISTICS_ADD(sess, DROP_FOR_EXCEED_UPF_BUF_PKTS_NUM_PER_SESSION);
            return 1;
        }
      // Add for session level default buffing packets number by fanbinfeng on 20210817 begin
    }
  clib_spinlock_unlock (&sess->lock);
  return 0;
}

always_inline void
upf_tcp_mss_clamping (tcp_header_t *tcp, ip_csum_t *sum)
{
  u8 *data;
  u8 opt_len, opts_len, kind;
  u16 mss;
  upf_main_t *um = &g_upf_main;

  if (!(um->mss_clamping && tcp_syn (tcp)))
    return;

  opts_len = (tcp_doff (tcp) << 2) - sizeof (tcp_header_t);
  data = (u8 *)(tcp + 1);
  for (; opts_len > 0; opts_len -= opt_len, data += opt_len)
    {
      kind = data[0];

      if (kind == TCP_OPTION_EOL)
        break;
      else if (kind == TCP_OPTION_NOOP)
        {
          opt_len = 1;
          continue;
        }
      else
        {
          if (opts_len < 2)
            return;
          opt_len = data[1];

          if (opt_len < 2 || opt_len > opts_len)
            return;
        }

      if (kind == TCP_OPTION_MSS)
        {
          mss = *(u16 *)(data + 2);
          if (clib_net_to_host_u16 (mss) > um->mss_clamping)
            {
              *sum = ip_csum_update (*sum, mss, um->mss_value_net,
                                     ip4_header_t, length);
              clib_memcpy_fast (data + 2, &um->mss_value_net, 2);
            }
          return;
        }
    }
}

/*char g_alarm_id_name_map[UPF_ALARM_MAX-UPF_ALARM_BASE][1024] = 
{
  {UPF_ALARM_N3_DROP, "n3 message drop"},
  {UPF_ALARM_N6_DROP, "n6 message drop"},
  {UPF_ALARM_N9_DROP, "n9 message drop"},
  {UPF_ALARM_ASSOCIATION_TIMEOUT, "n4 association timeout"},
  {UPF_ALARM_GTP_TUNNEL_TIMEOUT, "gtp tunnel timeout"},
  {UPF_ALARM_UL_UEIP_CHECK_FAIL, "ul ueip check fail"},
  {UPF_ALARM_DL_SESSION_CHECK_FAIL, "dl session check fail"},
  {UPF_ALARM_MAIN_STANDBY_SWAP, "main standby swap"},
  {UPF_ALARM_FLOW_OVERLOAD_PROTECT, "flow overload protect"},
  {UPF_ALARM_CPU_OVERLOAD, "cpu overload"},
  {UPF_ALARM_PREDEF_CONFLICT, "predef conflict"},
  {UPF_ALARM_LICENSE_LIMIT, "license limit"},
  {UPF_ALARM_LICENSE_OFF, "license off"},
};*/

char g_alarm_id_name_map[UPF_ALARM_MAX-UPF_ALARM_BASE-1][1024] = 
{
  {"n3 message drop"},
  {"n6 message drop"},
  {"n9 message drop"},
  {"n4 association timeout"},
  {"gtp tunnel timeout"},
  {"ul ueip check fail"},
  {"dl session check fail"},
  {"main standby swap"},
  {"flow overload protect"},
  {"cpu overload"},
  {"predef conflict"},
  {"unused alarm id"},
  {"asscoation setup success ratio low"},
  {"asscoation update success ratio low"},
  {"asscoation release success ratio low"},
  {"pfd management success ratio low"},
  {"session establishment success ratio low"},
  {"session modify success ratio low"},
  {"session delete success ratio low"},
  {"license wil expire"},
  {"license has expired"},
  {"predef rule appid conflict"},
  {"run independent overtime"},
  {"ipsec tunnel fault"},
  {"upf cpu overload"},
};

extern int g_alarm_info_flag;
void alarm_info(upf_alarm_notify_t *alarm_msg)
{
    FILE *fp = NULL;
    char alarm_info[4096] = {0};
    //struct tm *tm;

    if (!g_alarm_info_flag)
    {
        return;
    }
    
    fp = fopen("/var/log/vpp/upf_alarm.log", "a+");
    if (fp == NULL)
    {
        upf_err ("fopen /var/log/vpp/upf_alarm.log fail");
        return;
    }

    /* upf_trace ("[alarm id:%d] [alarm_type:%s] [alarm_level:%d] [current time:%lu] [om ip:%U]"
        "[ne type:%u] [ne_instance_id:%u] [alarm detial:%s]\n", alarm->alarm_id, (alarm->alarm_type ? "recovery" : "generation"), 
        alarm->alarm_level, alarm->current_time, format_ip46_address, &alarm->om_ip, IP46_TYPE_ANY, alarm->ne_type, alarm->ne_instance_id, 
        alarm->alarm_detail);*/
        /*UPF_ALARM_LEVEL_CRITICAL = 1,
        UPF_ALARM_LEVEL_SERIOUS,
        UPF_ALARM_LEVEL_GENERAL,
        UPF_ALARM_LEVEL_WARNING,
        UPF_ALARM_LEVEL_EVENT,*/
        

 /* tm = ;

  msg = format (
      msg, "%4d-%02d-%02dT%02d:%02d:%02d.%06dZ %s [%s:%u:%u:%s:%wd] ",
      tm->tm_year + 1900, tm->tm_mon + 1, tm->tm_mday, tm->tm_hour, tm->tm_min,
      tm->tm_sec, tv.tv_usec, ulog_level_strings[level], um->hostname, um->pid,
      vlib_get_thread_index (), function_name, line_number);
  */

    sprintf((char *)alarm_info, "%-15s: %u\n%-15s: %s\n%-15s: %s\n%-15s: %s\n%-15s: %s%-15s: %s\n\n", 
        "Alarm ID", alarm_msg->alarm_id, 
        "Alarm Name", g_alarm_id_name_map[alarm_msg->alarm_id-UPF_ALARM_BASE-1],
        "Alarm Type", alarm_msg->alarm_type ? "clearAlarm" : "activateAlarm", 
        "Alarm Level", alarm_msg->alarm_level==1 ? "Critical" : alarm_msg->alarm_level==2 ? "Serious" : alarm_msg->alarm_level==3 ? "General" : alarm_msg->alarm_level==4 ? "Warning" : "Event", 
        "Alarm Time", asctime(localtime((const time_t *)&alarm_msg->current_time)), 
        "Alarm Context", alarm_msg->alarm_detail);
        
    //memcpy(str, "12sfsdfsdfsdgdfcvxcvzczvcv", sizeof("12sfsdfsdfsdgdfcvxcvzczvcv\n"));
    
    if (fwrite (alarm_info, strlen (alarm_info), 1, fp) != 1)
    {
      upf_err ("fwrite /var/log/vpp/upf_alarm.log fail");
    }

    fclose(fp);
}

void upf_alarm_notify(u32 alarm_id, u32 alarm_type, u32 alarm_level, void *data)
{
    upf_performance_measurement_t *upf_statistics = NULL;
    pfcp_node_id_t *node_id = NULL;
    ip46_address_t *remote_ip = NULL;
    upf_ip_check_t *tmp = NULL;
    upf_pkt_ip_t *pkt_ip = NULL;
    upf_swap_alarm_t *swap_alarm = NULL;
    upf_flow_overload_protect_t *flow_overload = NULL;
    upf_cpu_overload_t *cpu_overload = NULL;
    upf_alarm_notify_t alarm_msg;
    u8 *str = NULL;
    /* deleted by liuyu LICENSE_SCRAP_CODE 2022-02-17 */
    f64 *cpu_usage;
    upf_predef_alarm_t *predef_alarm = NULL;
    n4_msg_alarm_t *n4_msg_alarm = NULL;
    upf_predef_appid_alarm_t *predef_appid_alarm = NULL;
    
    /* begin added by liuyu ALARM_REPORT_CONTROL 2022-04-06 */
    if (0 == g_upf_alarm_id_switch[0])
    {
        upf_debug("report to agent failed.");
        return;
    }
    
    if (0 == g_upf_alarm_id_switch[alarm_id - UPF_ALARM_BASE])
    {
        upf_debug("report to agent failed, alarm id = %u", alarm_id);
        return;
    }
    /* end added by liuyu ALARM_REPORT_CONTROL 2022-04-06 */

    memset(&alarm_msg, 0, sizeof(alarm_msg));
    alarm_msg.alarm_id = alarm_id;
    alarm_msg.alarm_type = alarm_type;
    alarm_msg.current_time = time(NULL);
    alarm_msg.om_ip.ip4.as_u32 = **********;        //***************
    alarm_msg.ne_type = 0;  //upf
    alarm_msg.ne_instance_id = g_local_ne_id;

    alarm_msg.alarm_level = alarm_level;
    switch (alarm_id)
    {
        case UPF_ALARM_N3_DROP:
            upf_statistics = (upf_performance_measurement_t *)data;
            if (NULL != upf_statistics)
            {
                sprintf((char *)alarm_msg.alarm_detail, "DropedPackets=%d,RecivedPackets=%d,PacketLossRate=%0.2f", 
                    upf_statistics->n3_statistics.drop_packets, upf_statistics->n3_statistics.recv_packets, 
                    upf_statistics->n3_statistics.drop_percent);
            }
            break;
        case UPF_ALARM_N6_DROP:
            upf_statistics = (upf_performance_measurement_t *)data;
            if (NULL != upf_statistics)
            {
                sprintf((char *)alarm_msg.alarm_detail, "DropedPackets=%d,RecivedPackets=%d,PacketLossRate=%0.2f", 
                    upf_statistics->n6_statistics.drop_packets, upf_statistics->n6_statistics.recv_packets, 
                    upf_statistics->n6_statistics.drop_percent);
            }
            break;
        case UPF_ALARM_N9_DROP:
            upf_statistics = (upf_performance_measurement_t *)data;
            if (NULL != upf_statistics)
            {
                sprintf((char *)alarm_msg.alarm_detail, "DropedPackets=%d,RecivedPackets=%d,PacketLossRate=%0.2f", 
                    upf_statistics->n9_statistics.drop_packets, upf_statistics->n9_statistics.recv_packets, 
                    upf_statistics->n9_statistics.drop_percent);
            }
            break;
        case UPF_ALARM_ASSOCIATION_TIMEOUT:
            node_id = (pfcp_node_id_t *)data;
            if (NULL != node_id)
            {
                upf_info("%U\n", format_ip46_address, &node_id->ip, IP46_TYPE_ANY);
                str= format (str, "node_id=%U", upf_format_node_id, node_id);
                sprintf((char *)alarm_msg.alarm_detail, "%s", str);
                vec_free(str);
            }
            else
            {
                sprintf((char *)alarm_msg.alarm_detail, "node_id=%s", "***************");
            }
            break;
        case UPF_ALARM_GTP_TUNNEL_TIMEOUT:
            remote_ip = (ip46_address_t *)data;
            if (NULL != remote_ip)
            {
                upf_debug("%U\n", format_ip46_address, remote_ip, IP46_TYPE_ANY);
                str = format(str, "remote_ip=%U", format_ip46_address, remote_ip, IP46_TYPE_ANY);
                sprintf((char *)alarm_msg.alarm_detail, "%s", str);
                vec_free(str);
            }
            else
            {
                sprintf((char *)alarm_msg.alarm_detail, "remote_ip=%s", "***************");
            }
            break;
        case UPF_ALARM_UL_UEIP_CHECK_FAIL:
            tmp = (upf_ip_check_t *)data;
            if (NULL != tmp)
            {
                str = format(str, "pdi_ue_ip=%U, pkt_src_ip=%U", format_ip4_address, &tmp->ue_ip, format_ip4_address, &tmp->pkt_src_ip);
                sprintf((char *)alarm_msg.alarm_detail, "%s", str);
                vec_free(str);
            }
            else
            {
                sprintf((char *)alarm_msg.alarm_detail, "pdi_ue_ip=%s, pkt_src_ip=%s", "***************", "***************");
            }
            break;
        case UPF_ALARM_DL_SESSION_CHECK_FAIL:
            pkt_ip = (upf_pkt_ip_t *)data;
            if (NULL != pkt_ip)
            {
                str = format(str, "pkt_src_ip=%U, pkt_dst_ip=%U", format_ip4_address, &pkt_ip->src_ip, format_ip4_address, &pkt_ip->dst_ip);
                sprintf((char *)alarm_msg.alarm_detail, "%s", str);
                vec_free(str);
            }
            else
            {
                sprintf((char *)alarm_msg.alarm_detail, "pkt_src_ip=%s, pkt_dst_ip=%s", "***************", "***************");
            }
            break;
        case UPF_ALARM_MAIN_STANDBY_SWAP:
            swap_alarm = (upf_swap_alarm_t *)data;
            if (NULL != swap_alarm)
            {
                str = format(str, "Active standby switching: from=%U to=%U, Cause=%u", format_ip46_address,
                    &swap_alarm->from_ip, IP46_TYPE_ANY, format_ip46_address, &swap_alarm->to_ip, IP46_TYPE_ANY, swap_alarm->fail_id);
                sprintf((char *)alarm_msg.alarm_detail, "%s", str);
                vec_free(str);
            }
            break;
        case UPF_ALARM_FLOW_OVERLOAD_PROTECT:
            flow_overload = (upf_flow_overload_protect_t *)data;
            if (NULL != flow_overload)
            {
                str = format(str, "Flow overload protect alarm: ul_mbr=%u, dl_mbr=%u", flow_overload->mbr.ul, flow_overload->mbr.dl);
                sprintf((char *)alarm_msg.alarm_detail, "%s", str);
                vec_free(str);
            }
            break;
        case UPF_ALARM_CPU_OVERLOAD:
            cpu_usage = (f64 *)data;
            if (NULL != cpu_usage)
            {
                sprintf((char *)alarm_msg.alarm_detail, "CpuUsage=%.2lf %%,Threshold=%u %%", *cpu_usage, g_upf_cpu_usage_threshold);
            }
            break;
        case UPF_ALARM_PREDEF_CONFLICT:
            predef_alarm = (upf_predef_alarm_t *)data;
            if (NULL != predef_alarm)
            {
                sprintf((char *)alarm_msg.alarm_detail, "rule_name=%s, old_far_id=%u, new_far_id=%u", predef_alarm->rule_name, predef_alarm->old_far_id, predef_alarm->new_far_id);
            }
            break;
        case UPF_ALARM_ASSCO_SETUP_SUCC_RATIO_LOW:
          case UPF_ALARM_ASSCO_UPDATE_SUCC_RATIO_LOW:
          case UPF_ALARM_ASSCO_RELEASE_SUCC_RATIO_LOW:
          case UPF_ALARM_PFD_MANAGEMENT_SUCC_RATIO_LOW: 
          case UPF_ALARM_SESSION_EST_SUCC_RATIO_LOW:
          case UPF_ALARM_SESSION_MODIFY_SUCC_RATIO_LOW:
          case UPF_ALARM_SESSION_DELETE_SUCC_RATIO_LOW:
            n4_msg_alarm = (n4_msg_alarm_t *)data;
            if (NULL != n4_msg_alarm)
            {
                sprintf((char *)alarm_msg.alarm_detail, "total_cnt=%u,success_cnt=%u,success_ratio=%u", n4_msg_alarm->total_cnt, n4_msg_alarm->success_cnt, n4_msg_alarm->success_ratio);
            }
            break;
        case UPF_ALARM_PREDEF_APPID_CONFLICT:
            predef_appid_alarm = (upf_predef_appid_alarm_t *)data;
            if (NULL != predef_appid_alarm)
            {
                sprintf((char *)alarm_msg.alarm_detail, "rule_name=%s, old_app_id=%s, new_app_id=%s", predef_appid_alarm->rule_name, 
                (char *)&predef_appid_alarm->old_app_id, (char *)&predef_appid_alarm->new_app_id);
            }
            break;
        /* end added by liuyu LICENSE_FUNC 2021-12-17 */

        case UPF_ALARM_RUN_INDEPENDENT:
            break;

        // add for ipsec support send tunnel fault pass dpd by liupeng on 2022-11-10 below
        case UPF_ALARM_IPSEC_TUNNEL_FAULT:
        {
            if (data != NULL)
            {
                sprintf((char *)alarm_msg.alarm_detail, "ipsec-tunnel-name=%s", (char *)data);
            }
            upf_debug("alarm_detail=[%s]", alarm_msg.alarm_detail);
            break;
        }
        // add for ipsec support send tunnel fault pass dpd by liupeng on 2022-11-10 above
        case UPF_ALARM_CPU_OVERLOAD_PRIVATE:
            cpu_overload = (upf_cpu_overload_t *)data;
            if (NULL != cpu_overload)
            {
                sprintf((char *)alarm_msg.alarm_detail, "Threshold=%u %%", g_upf_cpu_overload.usage_threshold);
                upf_debug("alarm_detail=[%s]", alarm_msg.alarm_detail);
            }
            break;
        default:
            return;
    }

    alarm_info(&alarm_msg);
    upf_pfcp_events_publish (PFCP_RPC_PUBLISH_ALARM_NOTIFY, NULL, &alarm_msg);
}


 nwi_traffic_stat_t *
 upf_get_nwi_traffic_statistic (nwi_traffic_stat_t** nwi_stat, uword** nwi_stat_index, uword pdrnwi)
 {
   upf_main_t *gtm = &g_upf_main;
   nwi_traffic_stat_t *nwi_stat_node = NULL;
   uword *p;
   upf_nwi_t *nwi = NULL;
     
   if (NULL == *nwi_stat_index)
   {
       *nwi_stat_index = hash_create_string (0, sizeof (uword));
   }

   if (!pool_is_free_index (gtm->nwis, pdrnwi))
   {
       nwi = pool_elt_at_index (gtm->nwis, pdrnwi); 
   }
   
   if (NULL == nwi)
   {
       upf_err ("nwi is not find, pdr.nwi is %u!\n", pdrnwi);
       return nwi_stat_node;
   }
   
   p = hash_get_mem (*nwi_stat_index, nwi->name);
 
   if (!p)
   {
       pool_get (*nwi_stat, nwi_stat_node);

       memset(nwi_stat_node, 0, sizeof(nwi_traffic_stat_t));

       nwi_stat_node->nwi = vec_dup (nwi->name);
       
       hash_set_mem (*nwi_stat_index, nwi_stat_node->nwi, nwi_stat_node - *nwi_stat);
       
       p = hash_get_mem (*nwi_stat_index, nwi->name);
   }

   CHECK_POOL_IS_VALID_RET(*nwi_stat, p[0], NULL);
   nwi_stat_node = pool_elt_at_index (*nwi_stat, p[0]);

   return nwi_stat_node;
}

  dnn_traffic_stat_t *
  upf_get_dnn_traffic_statistic (dnn_traffic_stat_t** dnn_stat, uword** dnn_stat_index, u8 *dnn_name)
  {
    upf_main_t *gtm = &g_upf_main;
    dnn_traffic_stat_t *dnn_stat_node = NULL;
    uword *p;
    upf_dnn_t *dnn = NULL;
    int hit = 0;
    u8* dnn_tmp = NULL;
      
    if (NULL == *dnn_stat_index)
    {
        *dnn_stat_index = hash_create_string (0, sizeof (uword));
    }

    vec_foreach (dnn, gtm->dnn)
    {
        if (dnn_tmp)
        {
            vec_free(dnn_tmp);
            dnn_tmp = NULL;
        }
        if (clib_strstr((char *)dnn->name, ".gprs"))
        {
            dnn_tmp = format (dnn_tmp, "%s", dnn->name);
        }
        else
        {
            if (g_home_plmn.mnc > 99)
            {
                dnn_tmp = format (dnn_tmp, "%s.mnc%u.mcc%u.gprs", dnn->name, g_home_plmn.mnc, g_home_plmn.mcc);
            }
            else
            {
                dnn_tmp = format (dnn_tmp, "%s.mnc0%u.mcc%u.gprs", dnn->name, g_home_plmn.mnc, g_home_plmn.mcc);
            }
        }
      
        if (vec_is_equal (dnn_tmp, dnn_name))
        {
          hit = 1;
          break;
        }
    }

    if (dnn_tmp)
    {
        vec_free(dnn_tmp);
        dnn_tmp = NULL;
    }

    if (!hit)
    {
        upf_trace("DNN name[%s] not find!", dnn_name);
        return NULL;
    }

    p = hash_get_mem (*dnn_stat_index, dnn->name);
  
    if (!p)
    {
        pool_get (*dnn_stat, dnn_stat_node);
 
        memset(dnn_stat_node, 0, sizeof(dnn_traffic_stat_t));
 
        dnn_stat_node->dnn = vec_dup (dnn->name);
        
        hash_set_mem (*dnn_stat_index, dnn_stat_node->dnn, dnn_stat_node - *dnn_stat);
        
        p = hash_get_mem (*dnn_stat_index, dnn->name);
    }
 
    CHECK_POOL_IS_VALID_RET(*dnn_stat, p[0], NULL);
    dnn_stat_node = pool_elt_at_index (*dnn_stat, p[0]);
 
    return dnn_stat_node;
 }

 u8 get_3gpp_interface_type(pfcp_tgpp_interface_type_t intfc_type)
 {
     switch (intfc_type)
     {
         case 11://N3 3Gpp Access
         case 12://N3 Trusted Non-3Gpp Access
         case 13://N3 Untrusted Non-3Gpp Access
         case 14://N3 for data forwarding
            return N3_INTTEFACE;

         case 15:
             return N9_INTTEFACE;

         case 17:
             return N6_INTTEFACE;
            
         default:
            return BUFF_INTERFACE;
     }
 }

void upf_pfcp_msg_counter(u32 msg_type, pfcp_cause_t cause, upf_pfcp_msg_count_t *pfcp_stat, u8 rx_tx_flag)
{
    if (pfcp_stat == NULL)
    {
        return;
    }
    
    switch (msg_type)
    {
         case PFCP_SESSION_ESTABLISHMENT_REQUEST:
         {
             pfcp_stat->sess_estab.req_times++;
             break;
         }
 
         case PFCP_SESSION_ESTABLISHMENT_RESPONSE:
         {
             pfcp_stat->sess_estab.rsp_times++;
             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 pfcp_stat->sess_estab.handle_succ_times++;
             }
             else
             {
                 pfcp_stat->sess_estab.handle_fail_times++;
                 pfcp_stat->sess_estab.cause_times[cause]++;
             }
             break;
         }
 
         case PFCP_SESSION_MODIFICATION_REQUEST:
         {
             pfcp_stat->sess_modify.req_times++;
             break;
         }
 
         case PFCP_SESSION_MODIFICATION_RESPONSE:
         {
             pfcp_stat->sess_modify.rsp_times++;
             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 pfcp_stat->sess_modify.handle_succ_times++;
             }
             else
             {
                 pfcp_stat->sess_modify.handle_fail_times++;
                 pfcp_stat->sess_modify.cause_times[cause]++;
             }
             break;
         }
 
         case PFCP_SESSION_DELETION_REQUEST:
         {
             pfcp_stat->sess_delete.req_times++;
             break;
         }
 
         case PFCP_SESSION_DELETION_RESPONSE:
         {
             pfcp_stat->sess_delete.rsp_times++;
 
             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 pfcp_stat->sess_delete.handle_succ_times++;
             }
             else
             {
                 pfcp_stat->sess_delete.handle_fail_times++;
                 pfcp_stat->sess_delete.cause_times[cause]++;
             }
             break;
         }
 
         case PFCP_SESSION_REPORT_REQUEST:
         {
             pfcp_stat->sess_report.req_times++;
             break;
         }
 
         case PFCP_SESSION_REPORT_RESPONSE:
         {
             pfcp_stat->sess_report.rsp_times++;
             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 pfcp_stat->sess_report.handle_succ_times++;
             }
             else
             {
                 pfcp_stat->sess_report.handle_fail_times++;
                 pfcp_stat->sess_report.cause_times[cause]++;
             }
             break;
         }

         case PFCP_ASSOCIATION_SETUP_REQUEST:
         {
             pfcp_stat->association_setup.req_times++;
             break;
         }
 
         case PFCP_ASSOCIATION_SETUP_RESPONSE:
         {
             pfcp_stat->association_setup.rsp_times++;
             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 pfcp_stat->association_setup.handle_succ_times++;
             }
             else
             {
                 pfcp_stat->association_setup.handle_fail_times++;
                 pfcp_stat->association_setup.cause_times[cause]++;
             }
             break;
         }

         case PFCP_ASSOCIATION_UPDATE_REQUEST:
         {
             pfcp_stat->association_update.req_times++;
             break;
         }
 
         case PFCP_ASSOCIATION_UPDATE_RESPONSE:
         {
             pfcp_stat->association_update.rsp_times++;
 
             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 pfcp_stat->association_update.handle_succ_times++;
             }
             else
             {
                 pfcp_stat->association_update.handle_fail_times++;
                 pfcp_stat->association_update.cause_times[cause]++;
             }
             break;
         }

         case PFCP_ASSOCIATION_RELEASE_REQUEST:
         {
             pfcp_stat->association_release.req_times++;
             break;
         }
 
         case PFCP_ASSOCIATION_RELEASE_RESPONSE:
         {
             pfcp_stat->association_release.rsp_times++;
             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 pfcp_stat->association_release.handle_succ_times++;
             }
             else
             {
                 pfcp_stat->association_release.handle_fail_times++;
                 pfcp_stat->association_release.cause_times[cause]++;
             }
             break;
         }

         case PFCP_PFD_MANAGEMENT_REQUEST:
         {
             pfcp_stat->pfd_managenent.req_times++;
             break;
         }
 
         case PFCP_PFD_MANAGEMENT_RESPONSE:
         {
             pfcp_stat->pfd_managenent.rsp_times++;
             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 pfcp_stat->pfd_managenent.handle_succ_times++;
             }
             else
             {
                 pfcp_stat->pfd_managenent.handle_fail_times++;
                 pfcp_stat->pfd_managenent.cause_times[cause]++;
             }
             break;
         }

         case PFCP_HEARTBEAT_REQUEST:
         {
             pfcp_stat->heartbeat[rx_tx_flag].req_times++;
             break;
         }

         case PFCP_HEARTBEAT_RESPONSE:
         {
             pfcp_stat->heartbeat[rx_tx_flag].rsp_times++;
             pfcp_stat->heartbeat[rx_tx_flag].handle_succ_times++;
             break;
         }

         case PFCP_NODE_REPORT_REQUEST:
         {
             pfcp_stat->node_report.req_times++;
             break;
         }
 
         case PFCP_NODE_REPORT_RESPONSE:
         {
             pfcp_stat->node_report.rsp_times++;
             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 pfcp_stat->node_report.handle_succ_times++;
             }
             else
             {
                 pfcp_stat->node_report.handle_fail_times++;
                 pfcp_stat->node_report.cause_times[cause]++;
             }
             break;
         }

         default:
             break;
    }
    return;
}

void upf_pfcp_statistic       (u32 msg_type, pfcp_cause_t cause, u8 rx_tx_flag)
{
     //flowtable_main_t *fm = &g_flowtable_main;
     u32 cpu_index = os_get_thread_index ();
     upf_performance_measurement_t *upf_stat = NULL;
     
     upf_stat = &g_flowtable_main.per_cpu[cpu_index].upf_stat;
     upf_pfcp_msg_counter (msg_type, cause, &upf_stat->n4_statistics, rx_tx_flag);
     return;
}

void upf_nwi_pfcp_statistic       (u32 s_nssai, u32 msg_type, pfcp_cause_t cause, u8 rx_tx_flag)
{
    flowtable_main_t *fm = &g_flowtable_main;
    upf_main_t *gtm = &g_upf_main;
    u32 cpu_index = os_get_thread_index ();
    //upf_session_t *session = NULL;
    nwi_traffic_stat_t *nwi_stat = NULL;
    upf_nwi_t *nwi = NULL;

    if (!(g_upf_auxiliary_switch & UPF_STATISTICS_SWITCH))
    {
        return;
    }

    if (!g_upf_nwi_stat_flag)
    {
        return;
    }
    
    /*if (~0 != session_index)
    {
        session = upf_session_lookup (session_index);

        if (session == NULL)
        {
            return;
        }
    }*/

    nwi = upf_lookup_nwi_by_s_nssai(s_nssai);

    if (nwi == NULL)
    {
        return;
    }
    
    nwi_stat = upf_get_nwi_traffic_statistic(&fm->per_cpu[cpu_index].nwi_stat, &fm->per_cpu[cpu_index].nwi_stat_index_by_name, nwi - gtm->nwis);

    if (NULL == nwi_stat)
    {
        return;
    }

    upf_pfcp_msg_counter (msg_type, cause, &nwi_stat->n4_stat, rx_tx_flag);
    
    /*switch (msg_type)
    {
        case PFCP_SESSION_ESTABLISHMENT_REQUEST:
        {
            nwi_stat->n4_stat.sess_estab.req_times++;

            break;
        }

        case PFCP_SESSION_ESTABLISHMENT_RESPONSE:
        {
            nwi_stat->n4_stat.sess_estab.rsp_times++;

            if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
            {
                nwi_stat->n4_stat.sess_estab.handle_succ_times++;
            }
            else if ((cause >= PFCP_CAUSE_REQUEST_REJECTED) && (cause < PFCP_CAUSE_MAX))
            {
                nwi_stat->n4_stat.sess_estab.handle_fail_times++;
            }
            else
            {
                ;
            }
            

            break;
        }

        case PFCP_SESSION_MODIFICATION_REQUEST:
        {
            nwi_stat->n4_stat.sess_modify.req_times++;

            break;
        }

        case PFCP_SESSION_MODIFICATION_RESPONSE:
        {
            nwi_stat->n4_stat.sess_modify.rsp_times++;

            if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
            {
                nwi_stat->n4_stat.sess_modify.handle_succ_times++;
            }
            else if ((cause >= PFCP_CAUSE_REQUEST_REJECTED) && (cause < PFCP_CAUSE_MAX))
            {
                nwi_stat->n4_stat.sess_modify.handle_fail_times++;
            }
            else
            {
                ;
            }
            
            break;
        }

        case PFCP_SESSION_DELETION_REQUEST:
        {
            nwi_stat->n4_stat.sess_delete.req_times++;

            break;
        }

        case PFCP_SESSION_DELETION_RESPONSE:
        {
            nwi_stat->n4_stat.sess_delete.rsp_times++;

            if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
            {
                nwi_stat->n4_stat.sess_delete.handle_succ_times++;
            }
            else if ((cause >= PFCP_CAUSE_REQUEST_REJECTED) && (cause < PFCP_CAUSE_MAX))
            {
                nwi_stat->n4_stat.sess_delete.handle_fail_times++;
            }
            else
            {
                ;
            }

            break;
        }

        case PFCP_SESSION_REPORT_REQUEST:
        {
            nwi_stat->n4_stat.sess_report.req_times++;

            break;
        }

        case PFCP_SESSION_REPORT_RESPONSE:
        {
            nwi_stat->n4_stat.sess_report.rsp_times++;

            if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
            {
                nwi_stat->n4_stat.sess_report.handle_succ_times++;
            }
            else if ((cause >= PFCP_CAUSE_REQUEST_REJECTED) && (cause < PFCP_CAUSE_MAX))
            {
                nwi_stat->n4_stat.sess_report.handle_fail_times++;
            }
            else
            {
                ;
            }
            
            break;
        }
        
        defualt:
        {
            break;
        }
    }*/

    return;
}

 void upf_dnn_pfcp_statistic       (u8 *dnn, u32 msg_type, pfcp_cause_t cause, u8 rx_tx_flag)
 {
     flowtable_main_t *fm = &g_flowtable_main;
     //upf_main_t *gtm = &g_upf_main;
     u32 cpu_index = os_get_thread_index ();
     //upf_session_t *session = NULL;
     dnn_traffic_stat_t *dnn_stat = NULL;

     if (!(g_upf_auxiliary_switch & UPF_STATISTICS_SWITCH))
     {
         return;
     }
     
     if (!g_upf_dnn_stat_flag)
     {
         return;
     }
 
     /*if (~0 != session_index)
     {
         session = upf_session_lookup (session_index);
 
         if (session == NULL)
         {
             return;
         }
     }*/

     if (dnn == NULL)
     {
         return;
     }
     
     dnn_stat = upf_get_dnn_traffic_statistic(&fm->per_cpu[cpu_index].dnn_stat, &fm->per_cpu[cpu_index].dnn_stat_index_by_name, dnn);
 
     if (NULL == dnn_stat)
     {
         return;
     }

     upf_pfcp_msg_counter (msg_type, cause, &dnn_stat->n4_stat, rx_tx_flag);
     
     /*switch (msg_type)
     {
         case PFCP_SESSION_ESTABLISHMENT_REQUEST:
         {
             dnn_stat->n4_stat.sess_estab.req_times++;
 
             break;
         }
 
         case PFCP_SESSION_ESTABLISHMENT_RESPONSE:
         {
             dnn_stat->n4_stat.sess_estab.rsp_times++;
 
             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 dnn_stat->n4_stat.sess_estab.handle_succ_times++;
             }
             else if ((cause >= PFCP_CAUSE_REQUEST_REJECTED) && (cause < PFCP_CAUSE_MAX))
             {
                 dnn_stat->n4_stat.sess_estab.handle_fail_times++;
             }
             else
             {
                 ;
             }
 
             break;
         }
 
         case PFCP_SESSION_MODIFICATION_REQUEST:
         {
             dnn_stat->n4_stat.sess_modify.req_times++;
 
             break;
         }
 
         case PFCP_SESSION_MODIFICATION_RESPONSE:
         {
             dnn_stat->n4_stat.sess_modify.rsp_times++;

             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 dnn_stat->n4_stat.sess_modify.handle_succ_times++;
             }
             else if ((cause >= PFCP_CAUSE_REQUEST_REJECTED) && (cause < PFCP_CAUSE_MAX))
             {
                 dnn_stat->n4_stat.sess_modify.handle_fail_times++;
             }
             else
             {
                 ;
             }
 
             break;
         }
 
         case PFCP_SESSION_DELETION_REQUEST:
         {
             dnn_stat->n4_stat.sess_delete.req_times++;
 
             break;
         }
 
         case PFCP_SESSION_DELETION_RESPONSE:
         {
             dnn_stat->n4_stat.sess_delete.rsp_times++;

             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 dnn_stat->n4_stat.sess_delete.handle_succ_times++;
             }
             else if ((cause >= PFCP_CAUSE_REQUEST_REJECTED) && (cause < PFCP_CAUSE_MAX))
             {
                 dnn_stat->n4_stat.sess_delete.handle_fail_times++;
             }
             else
             {
                 ;
             }
             
             break;
         }
 
         case PFCP_SESSION_REPORT_REQUEST:
         {
             dnn_stat->n4_stat.sess_report.req_times++;
 
             break;
         }
 
         case PFCP_SESSION_REPORT_RESPONSE:
         {
             dnn_stat->n4_stat.sess_report.rsp_times++;

             if ((cause >= PFCP_CAUSE_REQUEST_ACCEPTED) && (cause < PFCP_CAUSE_REQUEST_REJECTED))
             {
                 dnn_stat->n4_stat.sess_report.handle_succ_times++;
             }
             else if ((cause >= PFCP_CAUSE_REQUEST_REJECTED) && (cause < PFCP_CAUSE_MAX))
             {
                 dnn_stat->n4_stat.sess_report.handle_fail_times++;
             }
             else
             {
                 ;
             }
 
             break;
         }
         
         defualt:
         {
             break;
         }
     }*/
 
     return;
 }

 
 void upf_nwi_traffic_statistic (upf_pdr_t *pdr, upf_far_t *far, u16 packet_length, u8 is_drop, u8 is_recv)
 {
     flowtable_main_t *fm = &g_flowtable_main;
     u32 cpu_index = os_get_thread_index ();
     nwi_traffic_stat_t *nwi_stat = NULL;
     
     if (NULL == pdr || NULL == far)
     {
         upf_err ("param is null!\n");
         return;
     }

     if (!(g_upf_auxiliary_switch & UPF_STATISTICS_SWITCH))
     {
         return;
     }
     
     if (!g_upf_nwi_stat_flag)
     {
         return;
     }
 
     nwi_stat = upf_get_nwi_traffic_statistic(&fm->per_cpu[cpu_index].nwi_stat, &fm->per_cpu[cpu_index].nwi_stat_index_by_name, pdr->pdi.nwi);

     if (NULL == nwi_stat)
     {
         return;
     }

     if (is_drop)
     {
         switch (far->forward.dst_intf)
         {
            case DST_INTF_ACCESS:

                switch (get_3gpp_interface_type(far->forward.dest_interface_type))
                {
                    case N3_INTTEFACE:
                        nwi_stat->n3_stat.drop_packets++;
                        break;
                    case N9_INTTEFACE:
                        nwi_stat->n9_stat.drop_packets++;
                        break;
                    default:
                        break;
                }
                
                nwi_stat->dl_stat.drop_packets++;

                break;

            case DST_INTF_CORE:

                switch (get_3gpp_interface_type(far->forward.dest_interface_type))
                {
                    case N6_INTTEFACE:
                        nwi_stat->n6_stat.drop_packets++;
                        break;
                    case N9_INTTEFACE:
                        nwi_stat->n9_stat.drop_packets++;
                        break;
                    default:
                        break;
                }
                
                nwi_stat->ul_stat.drop_packets++;

                break;

            case DST_INTF_CP:
                nwi_stat->n9_stat.drop_packets++;

                switch (pdr->pdi.src_intf)
                {
                    case SRC_INTF_ACCESS:
                        nwi_stat->ul_stat.drop_packets++;
            
                        break;
    
                    case SRC_INTF_CORE:
                        nwi_stat->dl_stat.drop_packets++;
        
                        break;

                     default:
                         break;
                }

                break;
        
            default:
                break;
        }
         
        return;
     }
     
     if (is_recv)
     {
         switch (pdr->pdi.src_intf)
         {
            case SRC_INTF_ACCESS:
                
                switch (get_3gpp_interface_type(pdr->pdi.source_interface_type))
                {
                    case N3_INTTEFACE:
                        nwi_stat->n3_stat.recv_packets++;
                        nwi_stat->n3_stat.recv_bytes += packet_length;
                        break;
                    case N9_INTTEFACE:
                        nwi_stat->n9_stat.recv_packets++;
                        nwi_stat->n9_stat.recv_bytes += packet_length;
                        break;
                    default:
                        break;
                }
                             
                nwi_stat->ul_stat.recv_packets++;
                nwi_stat->ul_stat.recv_bytes += packet_length;

                break;

            case SRC_INTF_CORE:

                switch (get_3gpp_interface_type(pdr->pdi.source_interface_type))
                {
                    case N6_INTTEFACE:
                        nwi_stat->n6_stat.recv_packets++;
                        nwi_stat->n6_stat.recv_bytes += packet_length;
                        break;
                    case N9_INTTEFACE:
                        nwi_stat->n9_stat.recv_packets++;
                        nwi_stat->n9_stat.recv_bytes += packet_length;
                        break;
                    default:
                        break;
                }
            
                nwi_stat->dl_stat.recv_packets++;
                nwi_stat->dl_stat.recv_bytes += packet_length;

                break;

            case SRC_INTF_CP:
                nwi_stat->n9_stat.recv_packets++;
                nwi_stat->n9_stat.recv_bytes += packet_length;

                switch (far->forward.dst_intf)
                {
                    case DST_INTF_ACCESS:
                        nwi_stat->dl_stat.recv_packets++;
                        nwi_stat->dl_stat.recv_bytes += packet_length;

                        break;

                    case DST_INTF_CORE:
                        nwi_stat->ul_stat.recv_packets++;
                        nwi_stat->ul_stat.recv_bytes += packet_length;

                        break;

                    default:
                        break;
                }

                break;
             }
     }
     else
     {
         switch (far->forward.dst_intf)
         {
            case DST_INTF_ACCESS:

                switch (get_3gpp_interface_type(far->forward.dest_interface_type))
                {
                    case N3_INTTEFACE:
                        nwi_stat->n3_stat.send_packets++;
                        nwi_stat->n3_stat.send_bytes += packet_length;
                        break;
                    case N9_INTTEFACE:
                        nwi_stat->n9_stat.send_packets++;
                        nwi_stat->n9_stat.send_bytes += packet_length;
                        break;
                    default:
                        break;
                }

                nwi_stat->dl_stat.send_packets++;
                nwi_stat->dl_stat.send_bytes += packet_length;

                break;

            case DST_INTF_CORE:
                switch (get_3gpp_interface_type(far->forward.dest_interface_type))
                {
                    case N6_INTTEFACE:
                        nwi_stat->n6_stat.send_packets++;
                        nwi_stat->n6_stat.send_bytes += packet_length;
                        break;
                    case N9_INTTEFACE:
                        nwi_stat->n9_stat.send_packets++;
                        nwi_stat->n9_stat.send_bytes += packet_length;
                        break;
                    default:
                        break;
                }

                nwi_stat->ul_stat.send_packets++;
                nwi_stat->ul_stat.send_bytes += packet_length;

                break;

            case DST_INTF_CP:
                nwi_stat->n9_stat.send_packets++;
                nwi_stat->n9_stat.send_bytes += packet_length;

                switch (pdr->pdi.src_intf)
                {
                    case SRC_INTF_ACCESS:
                        nwi_stat->ul_stat.send_packets++;
                        nwi_stat->ul_stat.send_bytes += packet_length;

                        break;

                    case SRC_INTF_CORE:
                        nwi_stat->dl_stat.send_packets++;
                        nwi_stat->dl_stat.send_bytes += packet_length;

                        break;

                    default:
                        break;
                }

                break;

            default:
                break;
         }
     }

     
     /*if (!is_drop)
     {
          switch (pdr->pdi.src_intf)
         {
             case SRC_INTF_ACCESS:
                 nwi_stat->n3_stat.recv_packets++;
                nwi_stat->n3_stat.recv_bytes += packet_length;
             
                nwi_stat->ul_stat.recv_packets++;
                nwi_stat->ul_stat.recv_bytes += packet_length;

                break;

            case SRC_INTF_CORE:
                nwi_stat->n6_stat.recv_packets++;
                nwi_stat->n6_stat.recv_bytes += packet_length;
            
                nwi_stat->dl_stat.recv_packets++;
                nwi_stat->dl_stat.recv_bytes += packet_length;

                break;

            case SRC_INTF_CP:
                nwi_stat->n9_stat.recv_packets++;
                nwi_stat->n9_stat.recv_bytes += packet_length;

                break;

            default:
                break;
         }

         switch (far->forward.dst_intf)
         {
             case DST_INTF_ACCESS:
                 nwi_stat->n3_stat.send_packets++;
                nwi_stat->n3_stat.send_bytes += packet_length;

                nwi_stat->dl_stat.send_packets++;
                nwi_stat->dl_stat.send_bytes += packet_length;

                break;

            case DST_INTF_CORE:
                nwi_stat->n6_stat.send_packets++;
                nwi_stat->n6_stat.send_bytes += packet_length;

                nwi_stat->ul_stat.send_packets++;
                nwi_stat->ul_stat.send_bytes += packet_length;

                break;

            case DST_INTF_CP:
                nwi_stat->n9_stat.send_packets++;
                nwi_stat->n9_stat.send_bytes += packet_length;

                break;

            default:
                break;
         }
     }
     else
     {
         switch (far->forward.dst_intf)
         {
             case DST_INTF_ACCESS:
                 nwi_stat->n3_stat.send_packets--;
                nwi_stat->n3_stat.send_bytes -= packet_length;
                 nwi_stat->n3_stat.drop_packets++;

                nwi_stat->dl_stat.drop_packets++;
                nwi_stat->dl_stat.send_packets--;
                nwi_stat->dl_stat.send_bytes -= packet_length;

                break;

            case DST_INTF_CORE:
                nwi_stat->n6_stat.send_packets--;
                nwi_stat->n6_stat.send_bytes -= packet_length;
                nwi_stat->n6_stat.drop_packets++;

                nwi_stat->ul_stat.drop_packets++;
                nwi_stat->ul_stat.send_packets--;
                nwi_stat->ul_stat.send_bytes -= packet_length;

                break;

            case DST_INTF_CP:
                nwi_stat->n9_stat.send_packets--;
                nwi_stat->n9_stat.send_bytes -= packet_length;
                nwi_stat->n9_stat.drop_packets++;

                break;

            default:
                break;
         }
     }*/
     
     return;
 }

 void upf_dnn_traffic_statistic (vlib_buffer_t *b, upf_pdr_t *pdr, upf_far_t *far, u16 packet_length, u8 is_drop, u8 is_recv)
 {
     flowtable_main_t *fm = &g_flowtable_main;
     u32 cpu_index = os_get_thread_index ();
     dnn_traffic_stat_t *dnn_stat = NULL;
     u32 sidx = ~0;
     upf_session_t *sess = NULL;
     upf_main_t *gtm = &g_upf_main;
     
     if (NULL == pdr || NULL == far)
     {
         upf_err ("param is null!\n");
         return;
     }

     if (!g_upf_dnn_stat_flag) 
     {
         return;
     }

     sidx = upf_buffer_opaque (b)->upf.session_index;
     if (sidx == (u32)~0)
     {
        upf_err("sess index:%u", sidx);
        return;
     }

     CHECK_POOL_IS_VALID_NORET(gtm->sessions, sidx);
     sess = pool_elt_at_index (gtm->sessions, sidx);
    
     dnn_stat = upf_get_dnn_traffic_statistic(&fm->per_cpu[cpu_index].dnn_stat, &fm->per_cpu[cpu_index].dnn_stat_index_by_name, sess->dnn);

     if (NULL == dnn_stat)
     {
         return;
     }

     if (is_drop)
     {
         switch (far->forward.dst_intf)
         {
            case DST_INTF_ACCESS:

                switch (get_3gpp_interface_type(far->forward.dest_interface_type))
                {
                    case N3_INTTEFACE:
                        dnn_stat->n3_stat.drop_packets++;
                        break;
                    case N9_INTTEFACE:
                        dnn_stat->n9_stat.drop_packets++;
                        break;
                    default:
                        break;
                }
                
                dnn_stat->dl_stat.drop_packets++;

                break;

            case DST_INTF_CORE:

                switch (get_3gpp_interface_type(far->forward.dest_interface_type))
                {
                    case N6_INTTEFACE:
                        dnn_stat->n6_stat.drop_packets++;
                        break;
                    case N9_INTTEFACE:
                        dnn_stat->n9_stat.drop_packets++;
                        break;
                    default:
                        break;
                }
                
                dnn_stat->ul_stat.drop_packets++;

                break;

            case DST_INTF_CP:
                dnn_stat->n9_stat.drop_packets++;

                switch (pdr->pdi.src_intf)
                {
                    case SRC_INTF_ACCESS:
                        dnn_stat->ul_stat.drop_packets++;
            
                        break;
    
                    case SRC_INTF_CORE:
                        dnn_stat->dl_stat.drop_packets++;
        
                        break;

                     default:
                         break;
                }

                break;
        
            default:
                break;
        }
         
        return;
     }
     
     if (is_recv)
     {
         switch (pdr->pdi.src_intf)
         {
            case SRC_INTF_ACCESS:
                
                switch (get_3gpp_interface_type(pdr->pdi.source_interface_type))
                {
                    case N3_INTTEFACE:
                        dnn_stat->n3_stat.recv_packets++;
                        dnn_stat->n3_stat.recv_bytes += packet_length;
                        break;
                    case N9_INTTEFACE:
                        dnn_stat->n9_stat.recv_packets++;
                        dnn_stat->n9_stat.recv_bytes += packet_length;
                        break;
                    default:
                        break;
                }
                             
                dnn_stat->ul_stat.recv_packets++;
                dnn_stat->ul_stat.recv_bytes += packet_length;

                break;

            case SRC_INTF_CORE:

                switch (get_3gpp_interface_type(pdr->pdi.source_interface_type))
                {
                    case N6_INTTEFACE:
                        dnn_stat->n6_stat.recv_packets++;
                        dnn_stat->n6_stat.recv_bytes += packet_length;
                        break;
                    case N9_INTTEFACE:
                        dnn_stat->n9_stat.recv_packets++;
                        dnn_stat->n9_stat.recv_bytes += packet_length;
                        break;
                    default:
                        break;
                }
            
                dnn_stat->dl_stat.recv_packets++;
                dnn_stat->dl_stat.recv_bytes += packet_length;

                break;

            case SRC_INTF_CP:
                dnn_stat->n9_stat.recv_packets++;
                dnn_stat->n9_stat.recv_bytes += packet_length;

                switch (far->forward.dst_intf)
                {
                    case DST_INTF_ACCESS:
                        dnn_stat->dl_stat.recv_packets++;
                        dnn_stat->dl_stat.recv_bytes += packet_length;

                        break;

                    case DST_INTF_CORE:
                        dnn_stat->ul_stat.recv_packets++;
                        dnn_stat->ul_stat.recv_bytes += packet_length;

                        break;

                    default:
                        break;
                }

                break;
             }
     }
     else
     {
         switch (far->forward.dst_intf)
         {
            case DST_INTF_ACCESS:

                switch (get_3gpp_interface_type(far->forward.dest_interface_type))
                {
                    case N3_INTTEFACE:
                        dnn_stat->n3_stat.send_packets++;
                        dnn_stat->n3_stat.send_bytes += packet_length;
                        break;
                    case N9_INTTEFACE:
                        dnn_stat->n9_stat.send_packets++;
                        dnn_stat->n9_stat.send_bytes += packet_length;
                        break;
                    default:
                        break;
                }

                dnn_stat->dl_stat.send_packets++;
                dnn_stat->dl_stat.send_bytes += packet_length;

                break;

            case DST_INTF_CORE:
                switch (get_3gpp_interface_type(far->forward.dest_interface_type))
                {
                    case N6_INTTEFACE:
                        dnn_stat->n6_stat.send_packets++;
                        dnn_stat->n6_stat.send_bytes += packet_length;
                        break;
                    case N9_INTTEFACE:
                        dnn_stat->n9_stat.send_packets++;
                        dnn_stat->n9_stat.send_bytes += packet_length;
                        break;
                    default:
                        break;
                }

                dnn_stat->ul_stat.send_packets++;
                dnn_stat->ul_stat.send_bytes += packet_length;

                break;

            case DST_INTF_CP:
                dnn_stat->n9_stat.send_packets++;
                dnn_stat->n9_stat.send_bytes += packet_length;

                switch (pdr->pdi.src_intf)
                {
                    case SRC_INTF_ACCESS:
                        dnn_stat->ul_stat.send_packets++;
                        dnn_stat->ul_stat.send_bytes += packet_length;

                        break;

                    case SRC_INTF_CORE:
                        dnn_stat->dl_stat.send_packets++;
                        dnn_stat->dl_stat.send_bytes += packet_length;

                        break;

                    default:
                        break;
                }

                break;

            default:
                break;
         }
     }

     
     /*if (!is_drop)
     {
          switch (pdr->pdi.src_intf)
         {
             case SRC_INTF_ACCESS:
                 dnn_stat->n3_stat.recv_packets++;
                dnn_stat->n3_stat.recv_bytes += packet_length;
             
                dnn_stat->ul_stat.recv_packets++;
                dnn_stat->ul_stat.recv_bytes += packet_length;

                break;

            case SRC_INTF_CORE:
                dnn_stat->n6_stat.recv_packets++;
                dnn_stat->n6_stat.recv_bytes += packet_length;
            
                dnn_stat->dl_stat.recv_packets++;
                dnn_stat->dl_stat.recv_bytes += packet_length;

                break;

            case SRC_INTF_CP:
                dnn_stat->n9_stat.recv_packets++;
                dnn_stat->n9_stat.recv_bytes += packet_length;

                break;

            default:
                break;
         }

         switch (far->forward.dst_intf)
         {
             case DST_INTF_ACCESS:
                 dnn_stat->n3_stat.send_packets++;
                dnn_stat->n3_stat.send_bytes += packet_length;

                dnn_stat->dl_stat.send_packets++;
                dnn_stat->dl_stat.send_bytes += packet_length;

                break;

            case DST_INTF_CORE:
                dnn_stat->n6_stat.send_packets++;
                dnn_stat->n6_stat.send_bytes += packet_length;

                dnn_stat->ul_stat.send_packets++;
                dnn_stat->ul_stat.send_bytes += packet_length;

                break;

            case DST_INTF_CP:
                dnn_stat->n9_stat.send_packets++;
                dnn_stat->n9_stat.send_bytes += packet_length;

                break;

            default:
                break;
         }
     }
     else
     {
         switch (far->forward.dst_intf)
         {
             case DST_INTF_ACCESS:
                 dnn_stat->n3_stat.send_packets--;
                dnn_stat->n3_stat.send_bytes -= packet_length;
                 dnn_stat->n3_stat.drop_packets++;

                dnn_stat->dl_stat.drop_packets++;
                dnn_stat->dl_stat.send_packets--;
                dnn_stat->dl_stat.send_bytes -= packet_length;

                break;

            case DST_INTF_CORE:
                dnn_stat->n6_stat.send_packets--;
                dnn_stat->n6_stat.send_bytes -= packet_length;
                dnn_stat->n6_stat.drop_packets++;

                dnn_stat->ul_stat.drop_packets++;
                dnn_stat->ul_stat.send_packets--;
                dnn_stat->ul_stat.send_bytes -= packet_length;

                break;

            case DST_INTF_CP:
                dnn_stat->n9_stat.send_packets--;
                dnn_stat->n9_stat.send_bytes -= packet_length;
                dnn_stat->n9_stat.drop_packets++;

                break;

            default:
                break;
         }
     }*/
     
     return;
 }

void upf_traffic_bandwidth_calculate(upf_bandwith_t *bandwidth, u32 used_time)
{
    bandwidth->l3_rate = (f32)bandwidth->l3_bytes*8/used_time;
    bandwidth->l2_rate = (f32)bandwidth->l2_bytes*8/used_time;
    bandwidth->l1_rate = (f32)bandwidth->l1_bytes*8/used_time;
    bandwidth->packet_rate = (f32)bandwidth->packets/used_time;
    bandwidth->l3_bytes = 0;
    bandwidth->l2_bytes = 0;
    bandwidth->l1_bytes = 0;
    bandwidth->packets = 0;

    return;
}

void upf_interface_traffic_statistic (upf_session_t *sess, u32 pfcp_thread_index, upf_pdr_t *pdr, upf_far_t *far, u32 packet_length, u32 current_time, u8 is_ip4)
{
    flowtable_main_t *fm = &g_flowtable_main;
    u32 cpu_index = os_get_thread_index ();
    upf_performance_measurement_t *upf_stat = &fm->per_cpu[cpu_index].upf_stat;
    upf_pdu_sess_stat_t *ue_stat = &sess->pdu_sess_stat_t;
    upf_performance_measurement_t upf_stat_tmp;
    memset(&upf_stat_tmp, 0, sizeof(upf_stat_tmp));
    upf_alarm_msg_t *upf_alarm = 0;
    u32 used_time = current_time - upf_stat->old_time;
    u32 ue_stat_used_time = current_time - ue_stat->old_time;

    if (NULL == pdr || NULL == far)
    {
        upf_err ("param is null!");
        return;
    }

    upf_trace ("packets flow from %U to %U, packet_length=%u/n", upf_format_tgpp_interface_type, &pdr->pdi.source_interface_type, 
        upf_format_tgpp_interface_type, &far->forward.dest_interface_type, packet_length);

    /* SRC_INTF_ACCESS(0) means data is uplink,that from ue to dn */
    if (SRC_INTF_ACCESS == pdr->pdi.src_intf)
    {
        switch (pdr->pdi.source_interface_type)
        {
            case 0://S1-U
            case 11://N3 3Gpp Access
            case 12://N3 Trusted Non-3Gpp Access
            case 13://N3 Untrusted Non-3Gpp Access
            case 14://N3 for data forwarding
                upf_traffic_stat(ue_stat->n3_statistics, packet_length, 0);
                upf_traffic_stat(upf_stat->n3_statistics, packet_length, 0);
                if (ue_stat_used_time >= UPF_INTERVAL_TIME)
                {
                    upf_traffic_bandwidth_calculate(&ue_stat->n3_statistics.recv_bandwidth, ue_stat_used_time);
                    ue_stat->old_time = current_time;
                }
                if (used_time >= UPF_INTERVAL_TIME)
                {
                    upf_traffic_bandwidth_calculate(&upf_stat->n3_statistics.recv_bandwidth, used_time);
                    upf_stat->old_time = current_time;
                }
                iupf_get_total_statistics_data(&upf_stat_tmp);
                if (g_upf_alarm_state[UPF_ALARM_N3_DROP - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_PRODUCE && 
                    upf_stat_tmp.n3_statistics.drop_percent < (upf_stat_tmp.n3_statistics.drop_limit - upf_stat_tmp.n3_statistics.drop_limit/10))
                {
                    upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
                    memset (upf_alarm, 0, sizeof (*upf_alarm));

                    upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_performance_measurement_t), CLIB_CACHE_LINE_BYTES);
                    memset (upf_alarm->data, 0, sizeof(upf_performance_measurement_t));
                    upf_alarm->alarm_id = UPF_ALARM_N3_DROP;
                    upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
                    upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
                    memcpy(upf_alarm->data, &upf_stat_tmp, sizeof(upf_performance_measurement_t));
                    pfcp_send_sx_alarm_to_thread(pfcp_thread_index, upf_alarm);
                    g_upf_alarm_state[UPF_ALARM_N3_DROP - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_RECOVER;
                }

                if (15 == far->forward.dest_interface_type)  //N9
                {
                    upf_traffic_stat(upf_stat->n9_statistics, packet_length, 1);
                    if (used_time >= UPF_INTERVAL_TIME)
                    {
                        upf_traffic_bandwidth_calculate(&upf_stat->n9_statistics.send_bandwidth, used_time);
                    }
                } else if (17 == far->forward.dest_interface_type || 16 == far->forward.dest_interface_type)  //N6
                {
                    packet_length -= GTPU_HEADER_LENGTH;

                    if (pdr->pdi.qfi)
                    {
                        packet_length -= 8;
                    }
                    
                    upf_traffic_stat(ue_stat->n6_statistics, packet_length, 1);                      
                    upf_traffic_stat(upf_stat->n6_statistics, packet_length, 1);

                    if(is_ip4 == 0)
                    {
                        upf_traffic_stat_ipv6(ue_stat->n6_statistics, packet_length, 1);
                        upf_traffic_stat_ipv6(upf_stat->n6_statistics, packet_length, 1);
                    }
                    if (ue_stat_used_time >= UPF_INTERVAL_TIME)
                    {
                        upf_traffic_bandwidth_calculate(&ue_stat->n6_statistics.send_bandwidth, ue_stat_used_time);
                        if(is_ip4 == 0)
                        {
                            upf_traffic_bandwidth_calculate(&ue_stat->n6_statistics.send_bandwidth_ipv6, ue_stat_used_time);
                        }
                    }
                    if (used_time >= UPF_INTERVAL_TIME)
                    {
                        upf_traffic_bandwidth_calculate(&upf_stat->n6_statistics.send_bandwidth, used_time);
                        if(is_ip4 == 0)
                        {
                            upf_traffic_bandwidth_calculate(&upf_stat->n6_statistics.send_bandwidth_ipv6, used_time);
                        }
                    }
                }
                break;
            case 15://N9
                upf_traffic_stat(upf_stat->n9_statistics, packet_length, 0);
                if (used_time >= UPF_INTERVAL_TIME)
                {
                    upf_traffic_bandwidth_calculate(&upf_stat->n9_statistics.recv_bandwidth, used_time);
                    upf_stat->old_time = current_time;
                }
                iupf_get_total_statistics_data(&upf_stat_tmp);
                if (g_upf_alarm_state[UPF_ALARM_N9_DROP - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_PRODUCE && 
                    upf_stat_tmp.n9_statistics.drop_percent < (upf_stat_tmp.n9_statistics.drop_limit - upf_stat_tmp.n9_statistics.drop_limit/10))
                {
                    upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
                    memset (upf_alarm, 0, sizeof (*upf_alarm));

                    upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_performance_measurement_t), CLIB_CACHE_LINE_BYTES);
                    memset (upf_alarm->data, 0, sizeof (*upf_alarm->data));
                    upf_alarm->alarm_id = UPF_ALARM_N9_DROP;
                    upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
                    upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
                    memcpy(upf_alarm->data, &upf_stat_tmp, sizeof(upf_performance_measurement_t));
                    pfcp_send_sx_alarm_to_thread(pfcp_thread_index, upf_alarm);
                    g_upf_alarm_state[UPF_ALARM_N9_DROP - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_RECOVER;
                }

                if (17 == far->forward.dest_interface_type)  //N6
                {
                    packet_length += GTPU_HEADER_LENGTH;
                    upf_traffic_stat(upf_stat->n6_statistics, packet_length, 1);
                    if(is_ip4 == 0)
                    {
                        upf_traffic_stat_ipv6(upf_stat->n6_statistics, packet_length, 1);
                    }
                    if (used_time >= UPF_INTERVAL_TIME)
                    {
                        upf_traffic_bandwidth_calculate(&upf_stat->n6_statistics.send_bandwidth, used_time);
                        if(is_ip4 == 0)
                        {
                            upf_traffic_bandwidth_calculate(&upf_stat->n6_statistics.send_bandwidth_ipv6, used_time);
                        }
                    }
                }
                break;
            default :
                upf_debug ("Unknown source interface type!");
                break;
        }
    }
    else
    {
        switch (pdr->pdi.source_interface_type)
        {
            case 15://N9
                upf_traffic_stat(upf_stat->n9_statistics, packet_length, 0);
                if (used_time >= UPF_INTERVAL_TIME)
                {
                    upf_traffic_bandwidth_calculate(&upf_stat->n9_statistics.recv_bandwidth, used_time);
                    upf_stat->old_time = current_time;
                }
                iupf_get_total_statistics_data(&upf_stat_tmp);
                if (g_upf_alarm_state[UPF_ALARM_N9_DROP - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_PRODUCE && 
                    upf_stat_tmp.n9_statistics.drop_percent < (upf_stat_tmp.n9_statistics.drop_limit - upf_stat_tmp.n9_statistics.drop_limit/10))
                {
                    upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
                    memset (upf_alarm, 0, sizeof (*upf_alarm));

                    upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_performance_measurement_t), CLIB_CACHE_LINE_BYTES);
                    memset (upf_alarm->data, 0, sizeof (*upf_alarm->data));
                    upf_alarm->alarm_id = UPF_ALARM_N9_DROP;
                    upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
                    upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
                    memcpy(upf_alarm->data, &upf_stat_tmp, sizeof(upf_performance_measurement_t));
                    pfcp_send_sx_alarm_to_thread(pfcp_thread_index, upf_alarm);
                    g_upf_alarm_state[UPF_ALARM_N9_DROP - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_RECOVER;
                }

                if (11 <= far->forward.dest_interface_type && far->forward.dest_interface_type <= 14)  //N3
                {
                    upf_traffic_stat(upf_stat->n3_statistics, packet_length, 1);
                    if (used_time >= UPF_INTERVAL_TIME)
                    {
                        upf_traffic_bandwidth_calculate(&upf_stat->n3_statistics.send_bandwidth, used_time);
                    }
                }
                break;
            case 16://S-Gi
            case 17://N6
                upf_traffic_stat(ue_stat->n6_statistics, packet_length, 0);
                upf_traffic_stat(upf_stat->n6_statistics, packet_length, 0);
                if(is_ip4 == 0)
                {
                    upf_traffic_stat_ipv6(ue_stat->n6_statistics, packet_length, 0);
                    upf_traffic_stat_ipv6(upf_stat->n6_statistics, packet_length, 0);
                }
                if (ue_stat_used_time >= UPF_INTERVAL_TIME)
                {
                    upf_traffic_bandwidth_calculate(&ue_stat->n6_statistics.recv_bandwidth, ue_stat_used_time);
                    if(is_ip4 == 0)
                    {
                        upf_traffic_bandwidth_calculate(&ue_stat->n6_statistics.recv_bandwidth_ipv6, ue_stat_used_time);
                    }
                    ue_stat->old_time = current_time;
                }
                if (used_time >= UPF_INTERVAL_TIME)
                {
                    upf_traffic_bandwidth_calculate(&upf_stat->n6_statistics.recv_bandwidth, used_time);
                    if(is_ip4 == 0)
                    {
                        upf_traffic_bandwidth_calculate(&upf_stat->n6_statistics.recv_bandwidth_ipv6, used_time);
                    }
                    upf_stat->old_time = current_time;
                }
                iupf_get_total_statistics_data(&upf_stat_tmp);
                if (g_upf_alarm_state[UPF_ALARM_N6_DROP - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_PRODUCE && 
                    upf_stat_tmp.n6_statistics.drop_percent < (upf_stat_tmp.n6_statistics.drop_limit - upf_stat_tmp.n6_statistics.drop_limit/10))
                {
                    upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
                    memset (upf_alarm, 0, sizeof (*upf_alarm));

                    upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_performance_measurement_t), CLIB_CACHE_LINE_BYTES);
                    memset (upf_alarm->data, 0, sizeof (*upf_alarm->data));
                    upf_alarm->alarm_id = UPF_ALARM_N6_DROP;
                    upf_alarm->alarm_type = UPF_ALARM_TYPE_RECOVER;
                    upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
                    memcpy(upf_alarm->data, &upf_stat_tmp, sizeof(upf_performance_measurement_t));
                    pfcp_send_sx_alarm_to_thread(pfcp_thread_index, upf_alarm);
                    g_upf_alarm_state[UPF_ALARM_N6_DROP - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_RECOVER;
                }

                if ((11 <= far->forward.dest_interface_type && far->forward.dest_interface_type <= 14)
                        || far->forward.dest_interface_type == 0)  //N3 or S1-U
                {
                    packet_length += GTPU_HEADER_LENGTH;
                    upf_traffic_stat(ue_stat->n3_statistics, packet_length, 1);
                    upf_traffic_stat(upf_stat->n3_statistics, packet_length, 1);
                    if (ue_stat_used_time >= UPF_INTERVAL_TIME)
                    {
                        upf_traffic_bandwidth_calculate(&ue_stat->n3_statistics.send_bandwidth, ue_stat_used_time);
                    }
                    if (used_time >= UPF_INTERVAL_TIME)
                    {
                        upf_traffic_bandwidth_calculate(&upf_stat->n3_statistics.send_bandwidth, used_time);
                    }
                } else if (15 == far->forward.dest_interface_type)  //N9
                {
                    packet_length += GTPU_HEADER_LENGTH;
                    upf_traffic_stat(upf_stat->n9_statistics, packet_length, 1);
                    if (used_time >= UPF_INTERVAL_TIME)
                    {
                        upf_traffic_bandwidth_calculate(&upf_stat->n9_statistics.send_bandwidth, used_time);
                    }
                }
                break;
            default :
                upf_debug ("Unknown source interface type!");
                break;
        }
    }

    return;
}

void upf_interface_drop_statistic (upf_session_t *sess, u32 pfcp_thread_index, upf_pdr_t *pdr, upf_far_t *far, u32 packet_length, u32 is_upf_process, u8 is_ip4)
{
    flowtable_main_t *fm = &g_flowtable_main;
    u32 cpu_index = os_get_thread_index ();
    upf_performance_measurement_t *upf_stat = &fm->per_cpu[cpu_index].upf_stat;
    upf_performance_measurement_t upf_stat_tmp;
    upf_alarm_msg_t *upf_alarm = 0;
    upf_pdu_sess_stat_t *ue_stat = &sess->pdu_sess_stat_t;

    if (NULL == pdr || NULL == far)
    {
        upf_err ("pdr is null!");
        return;
    }

    upf_debug ("packets flow from %U to %U, packet_length=%u/n", upf_format_tgpp_interface_type, &pdr->pdi.source_interface_type, 
        upf_format_tgpp_interface_type, &far->forward.dest_interface_type, packet_length);

    /* SRC_INTF_ACCESS(0) means data is uplink,that from ue to dn */
    if (SRC_INTF_ACCESS == pdr->pdi.src_intf)
    {
        switch (pdr->pdi.source_interface_type)
        {
            case 0://S1-U
            case 11://N3 3Gpp Access
            case 12://N3 Trusted Non-3Gpp Access
            case 13://N3 Untrusted Non-3Gpp Access
            case 14://N3 for data forwarding
                iupf_get_total_statistics_data(&upf_stat_tmp);
                if (g_upf_alarm_state[UPF_ALARM_N3_DROP - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_RECOVER && 
                    upf_stat_tmp.n3_statistics.drop_percent > upf_stat_tmp.n3_statistics.drop_limit)
                {
                    upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
                    memset (upf_alarm, 0, sizeof (*upf_alarm));

                    upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_performance_measurement_t), CLIB_CACHE_LINE_BYTES);
                    memset (upf_alarm->data, 0, sizeof(upf_performance_measurement_t));
                    upf_alarm->alarm_id = UPF_ALARM_N3_DROP;
                    upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
                    upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
                    memcpy(upf_alarm->data, &upf_stat_tmp, sizeof(upf_performance_measurement_t));
                    pfcp_send_sx_alarm_to_thread(pfcp_thread_index, upf_alarm);
                    g_upf_alarm_state[UPF_ALARM_N3_DROP - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_PRODUCE;
                }

                if (15 == far->forward.dest_interface_type)  //N9
                {
                    if (is_upf_process)
                    {
                        packet_length += GTPU_HEADER_LENGTH;
                    }

                    upf_traffic_stat(upf_stat->n9_statistics, -(int)packet_length, 1);
                }
                else if (17 == far->forward.dest_interface_type||16 == far->forward.dest_interface_type)  //N6 or S-Gi
                {
                    upf_traffic_stat(ue_stat->n6_statistics, -(int)packet_length, 1);
                    upf_traffic_stat(upf_stat->n6_statistics, -(int)packet_length, 1);
                    if(is_ip4 == 0)
                    {
                        upf_traffic_stat_ipv6(ue_stat->n6_statistics, -(int)packet_length, 1);
                        upf_traffic_stat_ipv6(upf_stat->n6_statistics, -(int)packet_length, 1);
                    }
                }
                break;
            case 15://N9
                //upf_stat->n9_statistics.recv_bytes -= packet_length;
                iupf_get_total_statistics_data(&upf_stat_tmp);
                if (g_upf_alarm_state[UPF_ALARM_N9_DROP - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_RECOVER && 
                    upf_stat_tmp.n9_statistics.drop_percent > upf_stat_tmp.n9_statistics.drop_limit)
                {
                    upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
                    memset (upf_alarm, 0, sizeof (*upf_alarm));

                    upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_performance_measurement_t), CLIB_CACHE_LINE_BYTES);
                    memset (upf_alarm->data, 0, sizeof (*upf_alarm->data));
                    upf_alarm->alarm_id = UPF_ALARM_N9_DROP;
                    upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
                    upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
                    memcpy(upf_alarm->data, &upf_stat_tmp, sizeof(upf_performance_measurement_t));
                    pfcp_send_sx_alarm_to_thread(pfcp_thread_index, upf_alarm);
                    g_upf_alarm_state[UPF_ALARM_N9_DROP - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_PRODUCE;
                }

                if (17 == far->forward.dest_interface_type)  //N6
                {
                    upf_traffic_stat(upf_stat->n6_statistics, -(int)packet_length, 1);
                    if(is_ip4 == 0)
                    {
                        upf_traffic_stat_ipv6(upf_stat->n6_statistics, -(int)packet_length, 1);
                    }
                }
                break;
            default :
                upf_err ("Unknown source interface type!");
                break;
        }
    }
    else
    {
        switch (pdr->pdi.source_interface_type)
        {
            case 15://N9
                //upf_stat->n9_statistics.recv_bytes -= packet_length;
                iupf_get_total_statistics_data(&upf_stat_tmp);
                if (g_upf_alarm_state[UPF_ALARM_N9_DROP - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_RECOVER && 
                    upf_stat_tmp.n9_statistics.drop_percent > upf_stat_tmp.n9_statistics.drop_limit)
                {
                    upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
                    memset (upf_alarm, 0, sizeof (*upf_alarm));

                    upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_performance_measurement_t), CLIB_CACHE_LINE_BYTES);
                    memset (upf_alarm->data, 0, sizeof (*upf_alarm->data));
                    upf_alarm->alarm_id = UPF_ALARM_N9_DROP;
                    upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
                    upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
                    memcpy(upf_alarm->data, &upf_stat_tmp, sizeof(upf_performance_measurement_t));
                    pfcp_send_sx_alarm_to_thread(pfcp_thread_index, upf_alarm);
                    g_upf_alarm_state[UPF_ALARM_N9_DROP - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_PRODUCE;
                }

                if (11 <= far->forward.dest_interface_type && far->forward.dest_interface_type <= 14)  //N3
                {
                    upf_traffic_stat(upf_stat->n3_statistics, -(int)packet_length, 1);
                }
                break;
            case 16://S-Gi
            case 17://N6
                //upf_stat->n6_statistics.recv_bytes -= packet_length;
                iupf_get_total_statistics_data(&upf_stat_tmp);
                if (g_upf_alarm_state[UPF_ALARM_N6_DROP - UPF_ALARM_BASE - 1].alarm_state == UPF_ALARM_TYPE_RECOVER && 
                    upf_stat_tmp.n6_statistics.drop_percent > upf_stat_tmp.n6_statistics.drop_limit)
                {
                    upf_alarm = clib_mem_alloc_no_fail (sizeof (*upf_alarm));
                    memset (upf_alarm, 0, sizeof (*upf_alarm));

                    upf_alarm->data = clib_mem_alloc_aligned_no_fail (sizeof(upf_performance_measurement_t), CLIB_CACHE_LINE_BYTES);
                    memset (upf_alarm->data, 0, sizeof (*upf_alarm->data));
                    upf_alarm->alarm_id = UPF_ALARM_N6_DROP;
                    upf_alarm->alarm_type = UPF_ALARM_TYPE_PRODUCE;
                    upf_alarm->alarm_level = UPF_ALARM_LEVEL_GENERAL;
                    memcpy(upf_alarm->data, &upf_stat_tmp, sizeof(upf_performance_measurement_t));
                    pfcp_send_sx_alarm_to_thread(pfcp_thread_index, upf_alarm);
                    g_upf_alarm_state[UPF_ALARM_N6_DROP - UPF_ALARM_BASE - 1].alarm_state = UPF_ALARM_TYPE_PRODUCE;
                }

                if ((11 <= far->forward.dest_interface_type && far->forward.dest_interface_type <= 14)
                    || far->forward.dest_interface_type == 0)  //N3 or S1-U
                {
                    if (is_upf_process)
                    {
                        packet_length += GTPU_HEADER_LENGTH;
                    }
                    upf_traffic_stat(ue_stat->n3_statistics, -(int)packet_length, 1);
                    upf_traffic_stat(upf_stat->n3_statistics, -(int)packet_length, 1);
                }
                else if (15 == far->forward.dest_interface_type)  //N9
                {
                    if (is_upf_process)
                    {
                        packet_length += GTPU_HEADER_LENGTH;
                    }
                    upf_traffic_stat(upf_stat->n9_statistics, -(int)packet_length, 1);
                }
                break;
            default :
                upf_err ("Unknown source interface type!");
                break;
        }
    }
}

// Add for Bw rule by liupeng on 2021-08-13 below
void upf_bw_rule_traffic_statistic (upf_bw_rule_measurement_t *upf_bw_rule_stat, u32 direction, u32 packet_length, u32 current_time)
{
    if (upf_bw_rule_stat == NULL)
    {
        upf_debug ("upf_bw_rule_stat pointer is null!");
        return;
    }

    u32 used_time = current_time - upf_bw_rule_stat->old_time;
    /* SRC_INTF_ACCESS(0) means data is uplink,that from ue to dn */
    if (direction == UPF_UL)
    {
        upf_bw_rule_stat->send_bandwidth.l3_bytes += packet_length;
        upf_bw_rule_stat->send_bandwidth.l2_bytes += (packet_length + UPF_L2_EXTEND_SIZE);
        upf_bw_rule_stat->send_bandwidth.l1_bytes += (packet_length + UPF_L2_EXTEND_SIZE + UPF_L1_EXTEND_SIZE);
        if (used_time >= UPF_INTERVAL_TIME)
        {
            upf_traffic_bandwidth_calculate(&upf_bw_rule_stat->send_bandwidth, used_time);
            upf_bw_rule_stat->old_time = current_time;
        }

    }
    else if (direction == UPF_DL)// downlink UPF_DL
    {
        upf_bw_rule_stat->recv_bandwidth.l3_bytes += packet_length;
        upf_bw_rule_stat->recv_bandwidth.l2_bytes += (packet_length + UPF_L2_EXTEND_SIZE);
        upf_bw_rule_stat->recv_bandwidth.l1_bytes += (packet_length + UPF_L2_EXTEND_SIZE+UPF_L1_EXTEND_SIZE);
        if (used_time >= UPF_INTERVAL_TIME)
        {   
            upf_traffic_bandwidth_calculate(&upf_bw_rule_stat->recv_bandwidth, used_time);
            upf_bw_rule_stat->old_time = current_time;
        }

    }
    else
    {
        upf_err("error direction.\n");
        return;
    }

    return;
}
// Add for Bw rule by liupeng on 2021-08-13 above
/* begin: Add by wangjunjie02 for http header enhancement on ******** */
void upf_dl_modify_tcp_header_seq_num(vlib_main_t *vm, vlib_buffer_t * b, u32 is_ip4)
{
    if (NULL == b || NULL == vm)
    {
        return;
    }

    flowtable_main_t *fm = &g_flowtable_main;
        
    if (~0 == upf_buffer_opaque (b)->upf.flow_index || NULL == fm->flows)
    {
        return;
    }

    CHECK_POOL_IS_VALID_NORET(fm->flows, upf_buffer_opaque (b)->upf.flow_index);    
    flow_entry_t *flow = pool_elt_at_index (fm->flows, upf_buffer_opaque (b)->upf.flow_index);
    if (NULL == flow)
    {
        return;
    }
    
    if ( 0 != flow->http_header_enhancement_flag)
    {
        if (upf_pkt_is_tcp_syn(b))
        {
            flow->http_header_enhancement_flag = 0;
            flow->http_header_insert_length = 0;
        }
        else
        {
            ip4_header_t *ip4 = vlib_buffer_get_current (b);
            ip6_header_t *ip6 = vlib_buffer_get_current (b);
            tcp_header_t *tcp = is_ip4
                                  ? (tcp_header_t *)ip4_next_header (ip4)
                                  : (tcp_header_t *)ip6_next_header (ip6);
            
            if ((flow->http_header_insert_length > 0) && (tcp->ack_number > flow->http_header_insert_length))
            {
                tcp->ack_number = clib_net_to_host_u32 (tcp->ack_number) - flow->http_header_insert_length;
                tcp->ack_number = clib_host_to_net_u32(tcp->ack_number);
                tcp->checksum = 0;
                if (is_ip4)
                {
                    tcp->checksum = 0;
                    tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);
                }
                else
                {
                    int bogus_length = 0;
                    tcp->checksum = ip6_tcp_udp_icmp_compute_checksum (vm, b, ip6, &bogus_length);
                }
            }
        }
        
    }

    if (0 != flow->https_header_enhancement_flag)
    {
        if (upf_pkt_is_tcp_syn(b))
        {
            flow->https_header_enhancement_flag = 0;
            flow->https_header_insert_length = 0;
        }
        else
        {
            ip4_header_t *ip4 = vlib_buffer_get_current (b);
            ip6_header_t *ip6 = vlib_buffer_get_current (b);
            tcp_header_t *tcp = is_ip4
                                  ? (tcp_header_t *)ip4_next_header (ip4)
                                  : (tcp_header_t *)ip6_next_header (ip6);
            
            if ((flow->https_header_insert_length > 0) && (tcp->ack_number > flow->https_header_insert_length))
            {
                tcp->ack_number = clib_net_to_host_u32 (tcp->ack_number) - flow->https_header_insert_length;
                tcp->ack_number = clib_host_to_net_u32(tcp->ack_number);
                tcp->checksum = 0;
                if (is_ip4)
                {
                    tcp->checksum = 0;
                    tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);
                }
                else
                {
                    int bogus_length = 0;
                    tcp->checksum = ip6_tcp_udp_icmp_compute_checksum (vm, b, ip6, &bogus_length);
                }
            }
        }
    }
        
    return;
}

void upf_ul_modify_tcp_header_seq_num(vlib_main_t *vm, vlib_buffer_t * b, u32 is_ip4)
{
    if (NULL == b || NULL == vm)
    {
        return;
    }

    flowtable_main_t *fm = &g_flowtable_main;
    if (~0 == upf_buffer_opaque (b)->upf.flow_index || NULL == fm->flows)
    {
        return;
    }

    CHECK_POOL_IS_VALID_NORET(fm->flows, upf_buffer_opaque (b)->upf.flow_index);
    flow_entry_t *flow = pool_elt_at_index (fm->flows, upf_buffer_opaque (b)->upf.flow_index);
    if (NULL == flow)
    {
        return;
    }
    
    if ( 0 != flow->http_header_enhancement_flag)
    {
        if (upf_pkt_is_tcp_syn(b))
        {
            flow->http_header_enhancement_flag = 0;
            flow->http_header_insert_length = 0;
        }
        else
        {
            ip4_header_t *ip4 = vlib_buffer_get_current (b);
            ip6_header_t *ip6 = vlib_buffer_get_current (b);
            tcp_header_t *tcp = is_ip4
                                  ? (tcp_header_t *)ip4_next_header (ip4)
                                  : (tcp_header_t *)ip6_next_header (ip6);
            
            if (flow->http_header_insert_length > 0)
            {
                tcp->seq_number = clib_net_to_host_u32 (tcp->seq_number) + flow->http_header_insert_length;
                tcp->seq_number = clib_host_to_net_u32(tcp->seq_number);
                tcp->checksum = 0;
                if (is_ip4)
                {
                    tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);
                }
                else
                {
                    int bogus_length = 0;
                    tcp->checksum = ip6_tcp_udp_icmp_compute_checksum (vm, b, ip6, &bogus_length);
                }
            }
        }
    }

    if (0 != flow->https_header_enhancement_flag)
    {
        if (upf_pkt_is_tcp_syn(b))
        {
            flow->https_header_enhancement_flag = 0;
            flow->https_header_insert_length = 0;
        }
        else
        {
            ip4_header_t *ip4 = vlib_buffer_get_current (b);
            ip6_header_t *ip6 = vlib_buffer_get_current (b);
            tcp_header_t *tcp = is_ip4
                                  ? (tcp_header_t *)ip4_next_header (ip4)
                                  : (tcp_header_t *)ip6_next_header (ip6);
            
            if (flow->https_header_insert_length > 0)
            {
                tcp->seq_number = clib_net_to_host_u32 (tcp->seq_number) + flow->https_header_insert_length;
                tcp->seq_number = clib_host_to_net_u32(tcp->seq_number);
                tcp->checksum = 0;
                if (is_ip4)
                {
                    tcp->checksum = ip4_tcp_udp_compute_checksum (vm, b, ip4);
                }
                else
                {
                    int bogus_length = 0;
                    tcp->checksum = ip6_tcp_udp_icmp_compute_checksum (vm, b, ip6, &bogus_length);
                }
            }
        }
    }
        
    return;
}

/* end: Add by wangjunjie02 for http header enhancement on ******** */

u32 upf_alarm_search (u32 alarm_type, void *data)
{
    int rv = 0;
    BVT (clib_bihash_kv) kv, value;

    memset(&value, 0, sizeof(value));
    memset(&kv, 0, sizeof(kv));

    if (alarm_type == UPF_ALARM_UL_UEIP_CHECK_FAIL)
    {
        upf_ip_check_t *ip = (upf_ip_check_t *)data;
        kv.key[0] = ((u64) ip->ue_ip.as_u32 << 32) | ip->pkt_src_ip.as_u32;
        kv.key[1] = 0;
        kv.key[2] = 0;
        rv = BV (clib_bihash_search) ((BVT (clib_bihash) *)&g_upf_alarm_db.ul_ueip_check, &kv, &value);
        if (rv == 0)
        {
            kv.value = time(NULL);
            rv = BV (clib_bihash_add_del) ((BVT (clib_bihash) *)&g_upf_alarm_db.ul_ueip_check, &kv, 1 /* is_add */ );
            return 0;
        }
    }
    else if (alarm_type == UPF_ALARM_DL_SESSION_CHECK_FAIL)
    {
        upf_pkt_ip_t *ip = (upf_pkt_ip_t *)data;
        kv.key[0] = ((u64) ip->src_ip.as_u32 << 32) | ip->dst_ip.as_u32;
        kv.key[1] = 0;
        kv.key[2] = 0;
        rv = BV (clib_bihash_search) ((BVT (clib_bihash) *)&g_upf_alarm_db.dl_session_check, &kv, &value);
        if (rv == 0)
        {
            kv.value = time(NULL);
            rv = BV (clib_bihash_add_del) ((BVT (clib_bihash) *)&g_upf_alarm_db.dl_session_check, &kv, 1 /* is_add */ );
        }
    }

    if (rv == 0)
    {
        return 0;
    }

    return (u32)~0;
}

u32 upf_add_del_alarm_key (u32 alarm_type, void *data, u8 is_add)
{
    int rv = 0;
    BVT (clib_bihash_kv) kv;

    memset(&kv, 0, sizeof(kv));

    kv.value = time(NULL);

    if (alarm_type == UPF_ALARM_UL_UEIP_CHECK_FAIL)
    {
        upf_ip_check_t *ip = (upf_ip_check_t *)data;
        //upf_debug("ue_ip:%U, pkt_src_ip:%U\n", format_ip4_address, &ip->ue_ip, format_ip4_address, &ip->pkt_src_ip);
        kv.key[0] = ((u64) ip->ue_ip.as_u32 << 32) | ip->pkt_src_ip.as_u32;
        kv.key[1] = 0;
        kv.key[2] = 0;
        //upf_debug("key:%lu,value:%lu\n", kv.key, kv.value);
        if (!is_add)
        {
            rv = BV (clib_bihash_add_del) ((BVT (clib_bihash) *)&g_upf_alarm_db.ul_ueip_check, &kv, 0 /* is_add */ );
        }
        else
        {
            rv = BV (clib_bihash_add_del) ((BVT (clib_bihash) *)&g_upf_alarm_db.ul_ueip_check, &kv, 1 /* is_add */ );
        }
    }
    else if (alarm_type == UPF_ALARM_DL_SESSION_CHECK_FAIL)
    {
        upf_pkt_ip_t *ip = (upf_pkt_ip_t *)data;
        //upf_debug("src_ip:%U, dst_ip:%U\n", format_ip4_address, &ip->src_ip, format_ip4_address, &ip->dst_ip);
        kv.key[0] = ((u64) ip->src_ip.as_u32 << 32) | ip->dst_ip.as_u32;
        kv.key[1] = 0;
        kv.key[2] = 0;
        //upf_debug("key:%lu, value:%lu\n", kv.key, kv.value);
        if (!is_add)
        {
            rv = BV (clib_bihash_add_del) ((BVT (clib_bihash) *)&g_upf_alarm_db.dl_session_check, &kv, 0 /* is_add */ );
        }
        else
        {
            rv = BV (clib_bihash_add_del) ((BVT (clib_bihash) *)&g_upf_alarm_db.dl_session_check, &kv, 1 /* is_add */ );
        }
    }

    if (rv)
    {
        upf_err("Bihash table %s fail,rv:%d\n", is_add ? "add":"del", rv);
    }

   return 0;
}

static void upf_process_parse_grab_msg(vlib_buffer_t *b, u32 pdr_id, u32 far_id, upf_single_trace_push_t *grab)
{
  u8 is_ip4 = is_v4_packet ((u8 *)(vlib_buffer_get_current (b) + upf_buffer_opaque (b)->upf.data_offset));
  ip4_header_t *ip4 = vlib_buffer_get_current (b) + upf_buffer_opaque (b)->upf.data_offset;
  ip6_header_t *ip6 = vlib_buffer_get_current (b) + upf_buffer_opaque (b)->upf.data_offset;
  tcp_header_t *tcp = is_ip4 ? (tcp_header_t *)ip4_next_header (ip4) : (tcp_header_t *)ip6_next_header (ip6);

  grab->src_port = clib_host_to_net_u16(tcp->src_port);
  grab->dst_port = clib_host_to_net_u16(tcp->dst_port);
  grab->pdr_id = pdr_id;
  grab->far_id = far_id;
  if (is_ip4)
  {
      grab->protocol = ip4->protocol;
      grab->src_addr.ip4.data_u32 = ip4->src_address.data_u32;
      grab->dst_addr.ip4.data_u32 = ip4->dst_address.data_u32;
  }
  else
  {
      grab->protocol = ip6->protocol;
      ip46_address_set_ip6(&grab->src_addr, &ip6->src_address);
      ip46_address_set_ip6(&grab->dst_addr, &ip6->dst_address);
  }

  return;
}

u32 upf_process_get_sess_pdr_far(vlib_buffer_t *b, upf_session_t **sess, struct rules **active, upf_pdr_t **pdr, upf_far_t **far)
{
    *sess = sx_get_by_index(upf_buffer_opaque (b)->upf.session_index);
    if (!*sess)
    {
        upf_trace ("get session by index fail, session index:%u\n", upf_buffer_opaque (b)->upf.session_index);
        return 1;
    }

    *active = upf_get_rules (*sess, SX_ACTIVE);
    if (!*active)
    {
        upf_debug ("NULL active");
        return 1;
    }

    if (PREDICT_TRUE (upf_buffer_opaque (b)->upf.pdr_index < vec_len ((*active)->pdr)))
    {
        *pdr = (*active)->pdr + upf_buffer_opaque (b)->upf.pdr_index;
    }
    else
    {
        upf_err ("Error pdr_index: %d", upf_buffer_opaque (b)->upf.pdr_index);
        return 1;
    }

    *far = upf_get_far_by_id ((*active), (*pdr)->far_id);
    if (PREDICT_FALSE (NULL == *far))
    {
        upf_debug ("NULL far");
        return 1;
    }

    if (PREDICT_FALSE (!((*sess)->up_inactive_timer.status & DATA_ACTIVE)))
      clib_atomic_fetch_or (&(*sess)->up_inactive_timer.status, DATA_ACTIVE);

    return 0;
}

static u32 upf_pkt_outer_header_removal(vlib_buffer_t *b, u8 outer_header_removal, u32 *advance_len)
{
    u32 ret = 0;

    if ((NULL == b) || (NULL == advance_len))
    {
        upf_err("The parameter[vlib_buffer_t *b] is NULL!");
        return 1;
    }

    switch (outer_header_removal)
    {
        case OUTER_HEADER_REMOVAL_GTP_IP4: /* GTP-U/UDP/IPv4 */
        {
            if (PREDICT_FALSE ((upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK) != BUFFER_GTP_UDP_IP4))
            {
                ret = 1;
                upf_debug ("buffer flag=%d\n", (upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK));
                break;
            }
            *advance_len = upf_buffer_opaque (b)->upf.data_offset;
            break;
        }

        case OUTER_HEADER_REMOVAL_GTP_IP6: /* GTP-U/UDP/IPv6 */
        {
            if (PREDICT_FALSE ((upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK) != BUFFER_GTP_UDP_IP6))
            {
                ret = 1;
                upf_debug ("buffer flag=%d\n", (upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK));
                break;
            }
            *advance_len = upf_buffer_opaque (b)->upf.data_offset;
            break;
        }

        case OUTER_HEADER_REMOVAL_UDP_IP4: /* UDP/IPv4 */
        {
            if (is_v4_packet ((u8 *)(vlib_buffer_get_current (b) + upf_buffer_opaque (b)->upf.data_offset)))
            {
                ip4_header_t *ip4 = (ip4_header_t *)(vlib_buffer_get_current (b) + upf_buffer_opaque (b)->upf.data_offset);
                if (ip4 && ip4->protocol == IP_PROTOCOL_UDP)
                    upf_buffer_opaque (b)->upf.flags = BUFFER_UDP_IP4;
            }
            if (PREDICT_FALSE ((upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK) != BUFFER_UDP_IP4))
            {
                ret = 1;
                upf_debug ("buffer flag=%d\n", (upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK));
                break;
            }
            *advance_len = sizeof (ip4_header_t) + sizeof (udp_header_t);
            break;
        }

        case OUTER_HEADER_REMOVAL_UDP_IP6: /* UDP/IPv6 */
        {
            /* add by liudong in 20210618 for mark non-ip flag begin */
            if (is_v6_packet ((u8 *)(vlib_buffer_get_current (b) + upf_buffer_opaque (b)->upf.data_offset)))
            {
                ip6_header_t *ip6 = (ip6_header_t *)(vlib_buffer_get_current (b) + upf_buffer_opaque (b)->upf.data_offset);
                if (ip6 && ip6->protocol == IP_PROTOCOL_UDP)
                    upf_buffer_opaque (b)->upf.flags = BUFFER_UDP_IP6;
            }
            /* add by liudong in 20210618 for mark non-ip flag end */
            if (PREDICT_FALSE ((upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK) != BUFFER_UDP_IP6))
            {
                ret = 1;
                upf_debug ("buffer flag=%d\n", (upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK));
                break;
            }
            *advance_len = sizeof (ip6_header_t) + sizeof (udp_header_t);
            break;
        }

        case OUTER_HEADER_REMOVAL_IP4:
        {
            if (PREDICT_FALSE ((upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK) != BUFFER_HAS_IP4_HDR))
            {
                ret = 1;
                upf_debug ("buffer flag=%d\n", (upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK));
                break;
            }
            *advance_len = sizeof (ip4_header_t);
            break;
        }

        case OUTER_HEADER_REMOVAL_IP6:
        {
            if (PREDICT_FALSE ((upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK) != BUFFER_HAS_IP6_HDR))
            {
                ret = 1;
                upf_debug ("buffer flag=%d\n", (upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK));
                break;
            }
            *advance_len = sizeof (ip6_header_t);
            break;
        }

        case OUTER_HEADER_REMOVAL_GTP: /* GTPU/UDP/IP*/
        {
            if (PREDICT_FALSE (((upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK) != BUFFER_GTP_UDP_IP4) &&
                               (((upf_buffer_opaque (b)->upf.flags &  BUFFER_HDR_MASK) != BUFFER_GTP_UDP_IP6))))
            {
                ret = 1;
                upf_debug ("buffer flag=%d\n", (upf_buffer_opaque (b)->upf.flags & BUFFER_HDR_MASK));
                break;
            }
            *advance_len = upf_buffer_opaque (b)->upf.data_offset;
            break;
        }

        case OUTER_HEADER_REMOVAL_TAG_POP:
        {
            /* neil.fan@20220527 modify: according to 29244 table 8.2.64-1 NOTE 5, ... The VLAN POP operation
             * removes the VLAN tag from the top of the VLAN stack (outer VLAN tag), ...
             */
            upf_vlan_buffer_decap(b);
            break;
        }

        case OUTER_HEADER_REMOVAL_TAG_POP_POP:
            upf_vlan_buffer_decap_twice(b);
            break;

        default:
            break;
    }

    if (*advance_len)
    {
        vlib_buffer_advance (b, *advance_len);
        upf_buffer_opaque (b)->upf.data_offset = 0;
    }

    return ret;
}

int dns_cache_flag = 0;
int vxlan_flag = 0;

#define UPF_PROCESS_OK                0
#define UPF_PROCESS_CPT_DROP          1
#define UPF_PROCESS_CPT_BUFFERING     2
#define UPF_PROCESS_CPT_BUF_FULL_DROP 3
#define UPF_PROCESS_GO_TRACE          4    //only go trace, no handle later code logic
#define UPF_PROCESS_GO_ON             5

void upf_traffic_statistics(upf_session_t *sess, u32 pfcp_thread_index, upf_pdr_t *pdr, upf_far_t *far, u16 pkt_len, u32 current_time, vlib_buffer_t *b)
{
    if (!(g_upf_auxiliary_switch & UPF_STATISTICS_SWITCH))
        return;

    u8 is_ip4 = 0;
    if (is_v4_packet (vlib_buffer_get_current (b)))
    {
        is_ip4 = 1;
    }
    else
    {
        is_ip4 = 0;
    }

    upf_interface_traffic_statistic(sess, pfcp_thread_index, pdr, far, pkt_len, current_time, is_ip4);

    if (!upf_buffer_opaque (b)->upf.stat_flag)
    {
        upf_nwi_traffic_statistic(pdr, far, pkt_len, 0, 1);
        upf_dnn_traffic_statistic(b, pdr, far, pkt_len, 0, 1);
        upf_buffer_opaque (b)->upf.stat_flag = 1;
    }
}

typedef enum
{
    STATISTICS_POST_DROP,
    STATISTICS_POST_GTP_ENCAP,
    STATISTICS_POST_OTHER,
}upf_proc_statis_t;

void upf_traffic_statistics_post(upf_session_t *sess, u32 pfcp_thread_index, upf_pdr_t *pdr, upf_far_t *far, u16 pkt_len,
                                 upf_proc_statis_t statis, vlib_buffer_t *b)
{
    if (NULL == pdr || NULL == far)
    {
        upf_err ("pdr is null!");
        return;
    }
    
    if (!(g_upf_auxiliary_switch & UPF_STATISTICS_SWITCH))
        return;

    u8 is_ip4 = 0;
    if (is_v4_packet (vlib_buffer_get_current (b)))
    {
        is_ip4 = 1;
    }
    else
    {
        is_ip4 = 0;
    }

    if (STATISTICS_POST_DROP == statis)
    {
        upf_interface_drop_statistic(sess, pfcp_thread_index, pdr, far, pkt_len, 1, is_ip4);
    }

    if (STATISTICS_POST_GTP_ENCAP != statis)
    {
        if (STATISTICS_POST_DROP != statis)
        {
            upf_nwi_traffic_statistic(pdr, far, pkt_len, 0, 0);
            upf_dnn_traffic_statistic(b, pdr, far, pkt_len, 0, 0);
        }
        else
        {
            if (upf_buffer_opaque (b)->upf.stat_flag == 1)
            {
                upf_nwi_traffic_statistic(pdr, far, pkt_len, 1, 0);
                upf_dnn_traffic_statistic(b, pdr, far, pkt_len, 1, 0);
                //upf_debug("pdrid: %u, farid:%u, src_intf:%u, dst_intf:%u", pdr->id, far->id, pdr->pdi.src_intf, far->forward.dst_intf);
            }
        }
    }
}

//begin liukang add for acl rule 2022/08/04

int upf_ue_acl_rule_exist(u8 *aclRuleId)
{
    ue_acl_rule_t *acl_rule;
    upf_main_t *sm = &g_upf_main;
    ue_acl_rule_t *acl_rule_list=sm->ue_acl_rules;
    vec_foreach (acl_rule, acl_rule_list)
    {
        if(strcmp((char *)aclRuleId,acl_rule->aclRuleId)==0)
        {
            return -1;
        }
    }
    return 1;
}

int upf_ip4_address_comp(u32 ip4,ue_ip4_address_mask_t *Address4)
{
    ue_ip4_address_mask_t *tempAddress4;
    ue_ip4_address_mask_t *Address4List = Address4;
    if(vec_len(Address4List)==0)
    {
        return 1;
    }
    vec_foreach (tempAddress4, Address4List)
    {
        //printf("ip4=%u,begin_ip4_address=%u,end_ip4_address=%u\n!",ip4,tempAddress4->begin_ip4_address,tempAddress4->end_ip4_address);
        if(ip4>=tempAddress4->begin_ip4_address && ip4<=tempAddress4->end_ip4_address)
        {
            return 1;
        }
    }
    return 0;
}

int upf_ip6_address_comp(u32 ip6[4],ue_ip6_address_mask_t *Address6)
{
    ue_ip6_address_mask_t *tempAddress6;
    ue_ip6_address_mask_t *Address6List = Address6;
    if(vec_len(Address6List)==0)
    {
        return 1;
    }
    vec_foreach (tempAddress6, Address6List)
    {
        if(!(ip6[0]>=tempAddress6->begin_ip6_address[0] && ip6[0]<=tempAddress6->end_ip6_address[0]))
        {
            continue;
        }
        if(!(ip6[1]>=tempAddress6->begin_ip6_address[1] && ip6[1]<=tempAddress6->end_ip6_address[1]))
        {
            continue;
        }
        if(!(ip6[2]>=tempAddress6->begin_ip6_address[2] && ip6[2]<=tempAddress6->end_ip6_address[2]))
        {
            continue;
        }
        if(ip6[3]>=tempAddress6->begin_ip6_address[3] && ip6[3]<=tempAddress6->end_ip6_address[3])
        {
            return 1;
        }
    }
    return 0;
}

int upf_l4_port_comp(u16 port,ue_tcp_port_t *port_list)
{
    ue_tcp_port_t *tmp_port;
    ue_tcp_port_t *portlist = port_list;
    if(vec_len(portlist)==0)
    {
        return 1;
    }
    vec_foreach (tmp_port, portlist)
    {
        if(port >=tmp_port->begin_port && port <=tmp_port->end_port)
        {
            return 1;
        }
    }
    return 0;
}


u32 upf_process_traffic_shape(vlib_main_t *vm, vlib_buffer_t *b, upf_session_t *sx, upf_pdr_t *pdr, upf_far_t *far,
                              u32 next, u8 direction)
{
    if (next != UPF_PROCESS_IP_NEXT_GTP_IP4_ENCAP && next != UPF_PROCESS_IP_NEXT_GTP_IP6_ENCAP)
    {
        if (upf_nwi_mbr_process (pdr, far, vlib_buffer_length_in_chain(vm, b)))
        {
            upf_debug ("nwi mbr process failed, drop it!\n");
            UPF_STATISTICS_ADD(EXECUTE_NWI_MBR_FAILED);
            UPF_PDU_SESS_STATISTICS_ADD(sx, EXECUTE_NWI_MBR_FAILED);
            return 1;
        }
    }

    if ((g_upf_dnn_switch) && (upf_dnn_process (vm, sx, b, direction)))
    {
        upf_debug ("dnn process failed, drop it!\n");
        UPF_STATISTICS_ADD(EXECUTE_DNN_FAILED);
        UPF_PDU_SESS_STATISTICS_ADD(sx, EXECUTE_DNN_FAILED);
        return 1;
    }
    
    if ((g_upf_s_nssai_switch) && (upf_s_nssai_process (vm, sx, b, direction)))
    {
        upf_debug ("nssai process failed, drop it!\n");
        UPF_STATISTICS_ADD(EXECUTE_NSSAI_FAILED);
        UPF_PDU_SESS_STATISTICS_ADD(sx, EXECUTE_NSSAI_FAILED);
        return 1;
    }
    
    if (upf_flow_overload_protect_proc (vm, sx, b, pdr, far, direction))
    {
        upf_debug ("flow overload protect process failed, drop it!\n");
        UPF_STATISTICS_ADD(EXECUTE_FLOW_OVERLOAD_FAILED);
        UPF_PDU_SESS_STATISTICS_ADD(sx, EXECUTE_FLOW_OVERLOAD_FAILED);
        return 1;
    }

    if (upf_cpu_overload_protect_proc (vm, b))
    {
        upf_debug ("cpu overload protect process failed, drop it!\n");
        UPF_STATISTICS_ADD(EXECUTE_CPU_OVERLOAD_FAILED);
        UPF_PDU_SESS_STATISTICS_ADD(sx, EXECUTE_CPU_OVERLOAD_FAILED);
        return 1;
    }

    if (g_upf_dos_syn.state && upf_pkt_is_tcp_syn(b) && (upf_dos_syn_process (vm, sx, b, direction)))
    {
        upf_debug ("dos syn process failed, drop it!\n");
        return 1;
    }

    return 0;
}

void upf_process_trace(vlib_main_t *vm, vlib_node_runtime_t *node, vlib_buffer_t *b, upf_session_t *sx,
                       upf_pdr_t *pdr, upf_far_t *far, u8 cached, u32 next)
{
    upf_process_trace_t *tr = vlib_add_trace (vm, node, b, sizeof (*tr));
    tr->up_seid = sx ? sx->up_seid : 0;
    tr->cp_seid = sx ? sx->cp_seid : 0;
    tr->pdr_id = pdr ? pdr->id : ~0;
    tr->far_id = far ? far->id : ~0;
    tr->cached = cached;
    tr->next = next;
    clib_memcpy (tr->packet_data, vlib_buffer_get_current (b), sizeof (tr->packet_data));
}

u32 upf_process_ttl_check(vlib_buffer_t *b)
{
    if (PREDICT_FALSE((++upf_buffer_node_ttl (b)->ttl.upf_process_node) >= g_upf_message_repeat_times))
    {
        upf_err("The message appears in the loop and is discarded! ttl:%u, repeat_times:%u", 
            upf_buffer_node_ttl (b)->ttl.upf_process_node, g_upf_message_repeat_times);
       return 1;
    }
    return 0;
}

u32 upf_far_encap_vlan(upf_far_t *far, vlib_buffer_t *b)
{
    flowcache_insert_vlantag_t *vlantag_data = NULL;

    if (far->forward.outer_header_creation.description & OUTER_HEADER_CREATION_C_TAG)
    {
        if (!upf_n6_vlan_list_is_empty() && upf_lookup_upf_n6_vlan_list(NULL, 
            *(u32 *)&far->forward.outer_header_creation.c_vlan, far->forward.nwi) == NULL)
        {
            upf_debug ("cvlan 0x%x not config in n6 vlan list, drop it!\n", *(u32 *)&far->forward.outer_header_creation.c_vlan);
            return 1;
        }

        upf_vlan_buffer_encap(b, &far->forward.outer_header_creation.c_vlan);

        vlantag_data = upf_flowcache_add_action (b, FLOWCACHE_ACTION_INSERT_VLANTAG, sizeof (flowcache_insert_vlantag_t));
        vlantag_data->flags |= OUTER_HEADER_CREATION_C_TAG;
        vlantag_data->c_vlan = far->forward.outer_header_creation.c_vlan;
    }

    if (far->forward.outer_header_creation.description & OUTER_HEADER_CREATION_S_TAG)
    {
         if (!upf_n6_vlan_list_is_empty() && upf_lookup_upf_n6_vlan_list(NULL, 
            *(u32 *)&far->forward.outer_header_creation.s_vlan, far->forward.nwi) == NULL)
        {
            upf_debug ("svlan 0x%x not config in n6 vlan list, drop it!\n", *(u32 *)&far->forward.outer_header_creation.s_vlan);
            return 1;
        }

        upf_vlan_buffer_encap(b, &far->forward.outer_header_creation.s_vlan);

        vlantag_data = upf_flowcache_add_action (b, FLOWCACHE_ACTION_INSERT_VLANTAG, sizeof (flowcache_insert_vlantag_t));
        vlantag_data->flags |= OUTER_HEADER_CREATION_S_TAG;
        vlantag_data->s_vlan = far->forward.outer_header_creation.s_vlan;
    }

    return 0;
}

u32 upf_nwi_encap_vlan(upf_far_t *far, vlib_buffer_t *b)
{
    upf_main_t *gtm = &g_upf_main;
    upf_nwi_t *nwi = NULL;
    //vnet_main_t *vnm = vnet_get_main ();
    //vnet_sw_interface_t template;
    //u32 vlan_len;
    //u8 *data;
        
    if (far->forward.nwi != ~0)
    {
      if (PREDICT_TRUE(!pool_is_free_index(gtm->nwis, far->forward.nwi)))
      {
          nwi = pool_elt_at_index (gtm->nwis, far->forward.nwi);
      }
    }
    
    if (nwi && nwi->vlan_id)
    {
        //vlan_len = sizeof (nwi->vlan_id);

        //vlib_buffer_advance (b, -vlan_len);
        //data = vlib_buffer_get_current (b);

        //clib_memcpy_fast (data, (u8 *)&nwi->vlan_id, sizeof(nwi->vlan_id));

        
        //upf_vlan_buffer_encap(b, (ethernet_vlan_header_tv_t *)&(nwi->vlan_id));
        //clib_memset (&template, 0, sizeof (template));
        //template.sub.eth.raw_flags = 0;

        
  
    }
    
    return 0;
}


u32 upf_process_vxlan_handle(vlib_main_t *vm, u32 is_ip4, upf_pdr_t *pdr, u32 *next)
{
    if (!((vxlan_flag ==1) && (SRC_INTF_ACCESS == pdr->pdi.src_intf))) //liukang add for vxlan 2021/12/01
        return UPF_PROCESS_OK;

    vxlan_main_t *vxm = &vxlan_main;
    vnet_main_t *vnm = vxm->vnet_main;
    char *tmp_name = "vxlan_tunnel0";
    u8 *if_name = format (0, "%s", tmp_name);
    u32 hw_if_index;
    vxlan_tunnel_t *t0 = NULL;

    uword *p = hash_get (vnm->interface_main.hw_interface_by_name, if_name);
    if(p == NULL)
    {
        upf_err("can not found vxlan tunnel interface !\n");
        vec_free (if_name);
    }
    else
    {
        hw_if_index = p[0];
        vnet_hw_interface_t *hi0 =vnet_get_hw_interface (vnm, hw_if_index);
        vec_free (if_name);
        t0 = &vxm->tunnels[hi0->dev_instance];
        if(t0->is_ip4)
        {
             *next = UPF_PROCESS_IP_NEXT_VXLAN4_ENCAP;
        }
        else
        {
             *next = UPF_PROCESS_IP_NEXT_VXLAN6_ENCAP;
        }
        return UPF_PROCESS_GO_TRACE; /* vxlan goto trace */
    }

    return UPF_PROCESS_OK;
}

void upf_process_next_vn_init(vlib_buffer_t *b, upf_far_t *far0)
{
    upf_buffer_node_ttl(b)->ttl.vn_nwi_idx = far0->forward.nwi;
    upf_buffer_opaque (b)->upf.src_intf = SRC_INTF_5G_VN;
    upf_buffer_opaque (b)->upf.teid = 0; /* neil.fan@20220325 add to solve teid dismatch at 5glan step2 pdi match */
    clib_memset(&(upf_buffer_opaque (b)->upf.gtp_flags), 0, sizeof(gtpu_flags_t));

    if (far0->forward.flags & FAR_F_OUTER_HEADER_CREATION)
    {
        u16 description = far0->forward.outer_header_creation.description;
        if (PREDICT_FALSE (description & OUTER_HEADER_CREATION_N19_IND))
            upf_buffer_node_ttl(b)->ttl.vn_mark = VN_MARK_N19;
        if (PREDICT_FALSE (description & OUTER_HEADER_CREATION_N6_IND))
            upf_buffer_node_ttl(b)->ttl.vn_mark = VN_MARK_N6;

        if (description & OUTER_HEADER_CREATION_ANY_TAG)
            upf_far_encap_vlan(far0, b);
    }
}

u32 upf_process_far_eth_outer_hdr_create(vlib_main_t *vm, vlib_buffer_t *b, upf_far_t *far, u32 *next)
{
    pfcp_outer_header_creation_t *ohc = &far->forward.outer_header_creation;
    if (ohc->description & OUTER_HEADER_CREATION_ANY_TAG)
    {
        if (upf_far_encap_vlan(far, b))
            return UPF_PROCESS_CPT_DROP;
    }

    if (ohc->description & OUTER_HEADER_CREATION_GTP_IP4)
    {
        *next = UPF_PROCESS_ETH_NEXT_GTP_IP4_ENCAP;
        return UPF_PROCESS_OK;
    }
    else if (ohc->description & OUTER_HEADER_CREATION_GTP_IP6)
    {
        *next = UPF_PROCESS_ETH_NEXT_GTP_IP6_ENCAP;
        return UPF_PROCESS_OK;
    }
    else
        return UPF_PROCESS_GO_ON;
}

u32 upf_process_far_ip_outer_hdr_create(vlib_main_t *vm, vlib_buffer_t *b, upf_far_t *far, u32 *next)
{
    upf_main_t *um = &g_upf_main;
    pfcp_outer_header_creation_t *ohc = &far->forward.outer_header_creation;

    if (ohc->description & OUTER_HEADER_CREATION_GTP_IP4)
    {
        /* inner ip (i.e. UE and DN) fregment */
        if (PREDICT_FALSE(vlib_buffer_length_in_chain (vm, b) > um->gtpu_tunnel_mtu))
        {
            vnet_buffer (b)->ip_frag.mtu = um->gtpu_tunnel_mtu;
            
            if (is_v4_packet (vlib_buffer_get_current (b)))
            {
                vnet_buffer (b)->ip_frag.next_index = um->ip4_frag_next_encap4;
                *next = UPF_PROCESS_IP_NEXT_IP4_FRAG;
                UPF_STATISTICS_ADD(SEND_TO_FRAG4);
            }
            else
            {
                vnet_buffer (b)->ip_frag.next_index = um->ip6_frag_next_encap4;
                *next = UPF_PROCESS_IP_NEXT_IP6_FRAG;
                UPF_STATISTICS_ADD(SEND_TO_FRAG6);
            }
        }
        else
        {
            /* begin: Add by wangjunjie02 for http header enhancement on ******** */
            upf_dl_modify_tcp_header_seq_num(vm, b, is_v4_packet (vlib_buffer_get_current (b)));
            /* end: Add by wangjunjie02 for http header enhancement on ******** */
            *next = UPF_PROCESS_IP_NEXT_GTP_IP4_ENCAP;
        }
    }
    else if (ohc->description & OUTER_HEADER_CREATION_GTP_IP6)
    {
        /* inner ip (i.e. UE and DN) fregment */
        if (PREDICT_FALSE(vlib_buffer_length_in_chain (vm, b) > um->gtpu_tunnel_mtu))
        {
            vnet_buffer (b)->ip_frag.mtu = um->gtpu_tunnel_mtu;
            if (is_v4_packet (vlib_buffer_get_current (b)))
            {
                vnet_buffer (b)->ip_frag.next_index = um->ip4_frag_next_encap6;
                *next = UPF_PROCESS_IP_NEXT_IP4_FRAG;
                UPF_STATISTICS_ADD(SEND_TO_FRAG4);
            }
            else
            {
                vnet_buffer (b)->ip_frag.next_index = um->ip6_frag_next_encap6;
                *next = UPF_PROCESS_IP_NEXT_IP6_FRAG;
                UPF_STATISTICS_ADD(SEND_TO_FRAG6);
            }
        }
        else
        {
            *next = UPF_PROCESS_IP_NEXT_GTP_IP6_ENCAP;
        }
    }
    else if (ohc->description & OUTER_HEADER_CREATION_UDP_IP4)
    {
        *next = UPF_PROCESS_IP_NEXT_GTP_IP4_ENCAP;
    }
    else if (ohc->description & OUTER_HEADER_CREATION_UDP_IP6)
    {
        *next = UPF_PROCESS_IP_NEXT_GTP_IP6_ENCAP;
    }

    return UPF_PROCESS_OK;
}

u32 upf_far_redirect_handle(vlib_main_t *vm, u32 is_ip4, upf_far_t *far, vlib_buffer_t *b, u32 *next)
{
    ip4_header_t *ip4;
    ip6_header_t *ip6;

    if (is_ip4)
        ip4 = vlib_buffer_get_current (b);
    else
        ip6 = vlib_buffer_get_current (b);

    switch (far->forward.redirect_information.type)
    {
        case REDIRECT_INFORMATION_IPv4:
          if (is_ip4)
          {
              u32 old0, new0;
              ip_csum_t sum0;
              sum0 = ip4->checksum;
              old0 = ip4->dst_address.as_u32;
              new0 = far->forward.redirect_information.ip.ip4.as_u32;
              ip4->dst_address.as_u32 = far->forward.redirect_information.ip.ip4.as_u32;
              sum0 = ip_csum_update (sum0, old0, new0,ip4_header_t /* structure */, dst_address /* changed member */);
              ip4->checksum = ip_csum_fold (sum0);
              *next = UPF_PROCESS_IP_NEXT_IP4_LOOKUP;
          }
          break;

        case REDIRECT_INFORMATION_IPv6:
          upf_info ("Do not support Redirect Information type REDIRECT_INFORMATION_IPv6");
          *next = UPF_PROCESS_IP_NEXT_IP4_LOOKUP;
          break;

        case REDIRECT_INFORMATION_HTTP:
        {
            u8 protocol = is_ip4 ? ip4->protocol : ip6->protocol;
            if (protocol == IP_PROTOCOL_TCP)
            {
              /* begin:add by wangjunjie02 for http redirect more or once on 20210522 */
              flowtable_main_t *fm = &g_flowtable_main;
               flow_entry_t *flow = NULL;
               if(upf_buffer_opaque (b)->upf.flow_index != ~0)
               {
                   if (pool_is_free_index(fm->flows, upf_buffer_opaque (b)->upf.flow_index))
                   {
                        upf_debug ("flow index %u may be invalid!", upf_buffer_opaque (b)->upf.flow_index);
                        break;
                   }
                   flow = pool_elt_at_index (fm->flows, upf_buffer_opaque (b)->upf.flow_index);
                   if(flow == NULL)
                   {
                       upf_debug ("flow is null!");
                       break;
                   }
               }
               else
               {
                   upf_debug ("upf.flow_index is invalid!");
                   break;
               }

              /* once */
              if (1 == g_upf_redirect_type)
              {
                  if (flow->redirect_type == 1)
                  {
                      *next = UPF_PROCESS_IP_NEXT_IP4_LOCAL;
                      upf_debug ("HTTP redirection paceck to local");
                  }
                  else
                  {
                      if (0 == far->Redirect)
                      {
                          flow->redirect_type = 1;
                          far->Redirect = 1;
                          *next = UPF_PROCESS_IP_NEXT_IP4_LOCAL;
                          upf_debug ("HTTP redirection paceck to local");
                      }
                      else
                      {
                          upf_debug ("HTTP forword paceck to loclup");
                          *next = UPF_PROCESS_IP_NEXT_IP4_LOOKUP;
                      }
                  }
              }
              else
              {
                  if (0 != far->Redirect)
                      far->Redirect = 0;
                  upf_debug ("HTTP redirection paceck to local");
                  *next = UPF_PROCESS_IP_NEXT_IP4_LOCAL;
              }
            }
            else
            {
                // TODO: DROP ??
                upf_debug ("input");
                *next = UPF_PROCESS_IP_NEXT_IP4_LOOKUP;
                break;
            }
            break;
        }
        case REDIRECT_INFORMATION_SIP:
            upf_info ("Do not support Redirect Information type SIP");
            *next = UPF_PROCESS_IP_NEXT_IP4_LOOKUP;
            break;
    }

    return UPF_PROCESS_OK;
}

// Add for Exact-Routing by liupeng on  2022-07-12 below
void upf_set_exact_routing_user_id(vlib_buffer_t *b, flowcache_fib_index_t *data, 
                                   upf_user_id_t *user_id, u8 direction)
{
    vnet_buffer (b)->sw_if_index[VLIB_TX] =
        fib_table_find(FIB_PROTOCOL_IP4, direction == UPF_UL ? user_id->ul_table_id : user_id->dl_table_id);
    data->fib_index = vnet_buffer (b)->sw_if_index[VLIB_TX];
    return;
}

u32 upf_set_exact_routing_user_id_handle(upf_session_t *sx, vlib_buffer_t *b, 
                                         flowcache_fib_index_t *data, u8 user_id_flag, u8 direction)
{
    upf_main_t *gtm = &g_upf_main;
    u32 hit = 0;
    upf_user_id_t *user_id = NULL;
    u32 bfind = USER_ID_FLAG_INVALID;

    switch (user_id_flag)
    {
        case USER_ID_FLAG_IMSI:
        {
            vec_foreach (user_id, gtm->user_id)
            {
                if (user_id->flag == USER_ID_FLAG_IMSI)
                {
                    if (strcmp ((char *)user_id->imsi, (char *)sx->user_id.imsi_str) == 0)
                    {
                        hit = 1;
                        bfind = USER_ID_FLAG_IMSI;
                        break;
                    }
                }
            }
            break;
        }
        case USER_ID_FLAG_MSISDN:
        {
            vec_foreach (user_id, gtm->user_id)
            {
                if (user_id->flag == USER_ID_FLAG_MSISDN)
                {
                    if (strcmp ((char *)user_id->msisdn, (char *)sx->user_id.msisdn_str) == 0)
                    {
                        hit = 1;
                        bfind = USER_ID_FLAG_MSISDN;
                        break;
                    }
                }
            }
            break;
        }
        case USER_ID_FLAG_IMEI:
        {
            vec_foreach (user_id, gtm->user_id)
            {
                if (user_id->flag == USER_ID_FLAG_IMEI)
                {
                    if (strcmp ((char *)user_id->imei, (char *)sx->user_id.imei_str) == 0)
                    {
                        hit = 1;
                        bfind = USER_ID_FLAG_IMEI;
                        break;
                    }
                }
            }
            break;
        }
        default:
        {
            upf_err("invalid user id %u", user_id_flag);
            break;
        }
    }
    
    
    if (hit)
    {
        upf_set_exact_routing_user_id(b, data, user_id, direction);
    }

    return bfind;
}


u32 upf_process_exact_routing_user_id_handle(upf_session_t * sx, vlib_buffer_t * b, 
                                             flowcache_fib_index_t * data, u8 direction)
{
    u32 bfind = USER_ID_FLAG_INVALID;
    bfind = upf_set_exact_routing_user_id_handle(sx, b, data, USER_ID_FLAG_IMSI, direction);
    if (bfind == USER_ID_FLAG_IMSI)
    {
        upf_info("imsi exact routing.");
        return EXACT_ROUTING_FIND;
    }
    bfind = upf_set_exact_routing_user_id_handle(sx, b, data, USER_ID_FLAG_MSISDN, direction);
    if (bfind == USER_ID_FLAG_MSISDN)
    {
        upf_info("msisdn exact routing.");
        return EXACT_ROUTING_FIND;
    }
    bfind = upf_set_exact_routing_user_id_handle(sx, b, data, USER_ID_FLAG_IMEI, direction);
    if (bfind == USER_ID_FLAG_IMEI)
    {
        upf_info("imei exact routing.");
        return EXACT_ROUTING_FIND;
    }

    return EXACT_ROUTING_NOT_FIND;
}

u32 upf_process_exact_routing_dnn_handle(upf_session_t *sx, vlib_buffer_t *b, 
                                         flowcache_fib_index_t *data, u8 direction)
{
    upf_main_t *gtm = &g_upf_main;
    if (g_upf_dnn_switch)
    {
        u32 hit = 0;
        upf_dnn_t *dnn = NULL;
        vec_foreach (dnn, gtm->dnn)
        {
            if (vec_is_equal (dnn->name, sx->dnn))
            {
                hit = 1;
                break;
            }
        }
        if (hit)
        {
            vnet_buffer (b)->sw_if_index[VLIB_TX] =
                fib_table_find(FIB_PROTOCOL_IP4, direction == UPF_UL ? dnn->ul_table_id : dnn->dl_table_id);
            data->fib_index = vnet_buffer (b)->sw_if_index[VLIB_TX];
            return EXACT_ROUTING_FIND;
        }
    }
    return EXACT_ROUTING_NOT_FIND;
}

void upf_set_exact_routing_acl_rule(vlib_buffer_t *b, flowcache_fib_index_t *data, 
                                    upf_acl_rule_t *acl_rule, u8 direction)
{
    vnet_buffer (b)->sw_if_index[VLIB_TX] =
        fib_table_find(FIB_PROTOCOL_IP4, direction == UPF_UL ? acl_rule->ul_table_id : acl_rule->dl_table_id);
    data->fib_index = vnet_buffer (b)->sw_if_index[VLIB_TX];
    return;
}

void upf_get_flow_info(vlib_buffer_t *b, upf_acl_rule_t *acl_rule, u32 is_ip4, u8 dir)
{
    CHECK_ONE_PTR_NORET_VALID(acl_rule);

    ip4_header_t *ip4 = vlib_buffer_get_current (b);
    ip6_header_t *ip6 = vlib_buffer_get_current (b);
    tcp_header_t *tcp = is_ip4
                          ? (tcp_header_t *)ip4_next_header (ip4)
                          : (tcp_header_t *)ip6_next_header (ip6);
    udp_header_t *udp = is_ip4
                          ? (udp_header_t *)ip4_next_header (ip4)
                          : (udp_header_t *)ip6_next_header (ip6);
                          
    memset(acl_rule, 0, sizeof(upf_acl_rule_t));

    if (dir == UPF_UL)
    {
        if (is_ip4)
        {
            acl_rule->protocol = ip4->protocol;
            acl_rule->dst_ip.ip4.data_u32 = ip4->dst_address.data_u32;
        }
        else
        {
            acl_rule->protocol = ip6->protocol;
            ip46_address_set_ip6(&acl_rule->dst_ip, &ip6->dst_address);
        }

        if(acl_rule->protocol == IP_PROTOCOL_TCP)
        {
            acl_rule->dst_port = clib_net_to_host_u16(tcp->dst_port);
        }
        if(acl_rule->protocol == IP_PROTOCOL_UDP)
        {
            acl_rule->dst_port = clib_net_to_host_u16(udp->dst_port);
        }
    }
    else
    {
        if (is_ip4)
        {
            acl_rule->protocol = ip4->protocol;
            
            acl_rule->dst_ip.ip4.data_u32 = ip4->src_address.data_u32;
        }
        else
        {
            acl_rule->protocol = ip6->protocol;
            ip46_address_set_ip6(&acl_rule->dst_ip, &ip6->src_address);
        }

        if(acl_rule->protocol == IP_PROTOCOL_TCP)
        {
            acl_rule->dst_port = clib_net_to_host_u16(tcp->src_port);
        }
        if(acl_rule->protocol == IP_PROTOCOL_UDP)
        {
            acl_rule->dst_port = clib_net_to_host_u16(udp->src_port);
        }
    }

    return;
}


u32 upf_set_exact_routing_l3_4_handle(vlib_buffer_t *b, flowcache_fib_index_t *data, 
                                      u8 acl_rule_flag, u8 direction, u32 is_ip4)
{
    upf_main_t *gtm = &g_upf_main;
    u32 hit = 0;
    upf_acl_rule_t *acl_rule = NULL;
    u32 bfind = ACL_RULE_INVALID;

    upf_acl_rule_t tmp_acl_rule = {0};
    upf_get_flow_info(b, &tmp_acl_rule, is_ip4, direction);

    
    switch (acl_rule_flag)
    {
        case ACL_RULE_L4: // l4
        {
            vec_foreach (acl_rule, gtm->acl_rule)
            {
                if (acl_rule->dst_port == tmp_acl_rule.dst_port)
                {
                    hit = 1;
                    bfind = ACL_RULE_L4;
                    break;
                }
            }
            break;
        }
        case ACL_RULE_L3: //l3
        {
            vec_foreach (acl_rule, gtm->acl_rule)
            {
                if (ip46_address_cmp(&acl_rule->dst_ip, &tmp_acl_rule.dst_ip) == 0)
                {
                    hit = 1;
                    bfind = ACL_RULE_L3;
                    break;
                }
            }
            break;
        }
        default:
        {
            upf_err("invalid acl rule %u", acl_rule_flag);
            break;
        }
    }
    
    if (hit)
    {
        upf_set_exact_routing_acl_rule(b, data, acl_rule, direction);
    }

    return bfind;
}

void is_http (vlib_main_t *vm, vlib_buffer_t *b, u8 is_ip4)
{
    u32 offs = upf_buffer_opaque (b)->upf.data_offset;
    ip4_header_t *ip4 = NULL;
    ip6_header_t *ip6 = NULL;
    u8 *proto_hdr;
    u8 *tcp_payload;
    word len;

    if (is_ip4)
    {
        ip4 = (ip4_header_t *)(vlib_buffer_get_current (b) + offs);
        proto_hdr = ip4_next_header (ip4);
        len = clib_net_to_host_u16 (ip4->length) - sizeof (ip4_header_t);
    }
    else
    {
        ip6 = (ip6_header_t *)(vlib_buffer_get_current (b) + offs);
        proto_hdr = ip6_next_header (ip6);
        len = clib_net_to_host_u16 (ip6->payload_length);
    }

    if (ip4->protocol == IP_PROTOCOL_TCP)
    {
        len -= tcp_header_bytes ((tcp_header_t *)proto_hdr);
        offs = proto_hdr - (u8 *)vlib_buffer_get_current (b) +
                tcp_header_bytes ((tcp_header_t *)proto_hdr); 
    }
    else
       return;

    if (len < vlib_buffer_length_in_chain (vm, b) - offs || len <= 0)
        return;

    tcp_payload = vlib_buffer_get_current (b) + offs;
    if (upf_is_http_request (&tcp_payload, &len))
    {
        upf_buffer_opaque (b)->upf.http = 1;
        return;
    }
    else if (upf_is_https_client_hello (&tcp_payload, &len))
    {
        upf_buffer_opaque (b)->upf.https = 1;
        return;
    }
    
    return;
}

u32 upf_set_exact_routing_http_l7_handle(vlib_main_t *vm, vlib_buffer_t *b, flowcache_fib_index_t *data, 
                                    u8 acl_rule_flag, u8 direction, u32 is_ip4)
{
    int ret = 0;
    ip4_header_t *ip4 = NULL;
    ip6_header_t *ip6 = NULL;
    tcp_header_t *tcp;
    const char *http;
    const char *method;
    size_t method_len;
    const char *path;
    size_t path_len;
    int minor_version;
    struct upf_phr_header_t headers[32];
    size_t num_headers = sizeof (headers) / sizeof (headers[0]);
    u32 header_length;
    u32 http_length;
    u8 url[64] = {0};  
    upf_acl_rule_t *acl_rule = NULL;
    upf_main_t *gtm = &g_upf_main;
    u32 hit = 0;

    is_http(vm, b, is_ip4);
    if (upf_buffer_opaque (b)->upf.http)
    {
        if (is_ip4)
        {
            ip4 = (ip4_header_t *)vlib_buffer_get_current (b);
            tcp = ip4_next_header (ip4);
            http = (const char *)tcp + tcp_header_bytes (tcp);
            header_length = ip4_header_bytes (ip4) + tcp_header_bytes (tcp);
            http_length = vlib_buffer_length_in_chain (vm, b) - header_length;
        }
        else
        {
            ip6 = (ip6_header_t *)vlib_buffer_get_current (b);
            tcp = ip6_next_header (ip6);
            http = (const char *)tcp + tcp_header_bytes (tcp);
            header_length = sizeof (*ip6) + tcp_header_bytes (tcp);
            http_length = vlib_buffer_length_in_chain (vm, b) - header_length;
        }

        ret = upf_phr_parse_request_func (http, http_length, &method, &method_len, &path,
                                 &path_len, &minor_version, headers, &num_headers, 0);
        if (ret < 0)
        {
            upf_debug ("parse http header failed, ret %d\n", ret);
            return ACL_RULE_INVALID;
        }

        if (!upf_get_url(headers, 32, url))
        {
            upf_debug ("get url(%s) fail.", url);
            return ACL_RULE_INVALID;
        }
        vec_foreach (acl_rule, gtm->acl_rule)
        {
            if (acl_rule->flag == 0)
            {
                if (strcmp ((char *)acl_rule->url, (char *)url) == 0)
                {
                    hit = 1;
                    break;
                }
            }
        }

        if (hit)
        {
            upf_set_exact_routing_acl_rule(b, data, acl_rule, direction);
            return ACL_RULE_HTTP_L7;
        }

    }

    return ACL_RULE_INVALID;
}

u32 upf_https_get_url(RAW_EXTENSION *upf_ext_headers, int ext_num, u8 *url)
{
    u32 i = 0;
    for (i = 0; i < ext_num; ++i)
    {
        if(upf_ext_headers[i].type == TLSEXT_TYPE_server_name)
        {
            memcpy(url, upf_ext_headers[i].hostname, upf_ext_headers[i].length);
            upf_debug("https host name(%s) find.", url);
            return 1;
        }
    }
    upf_debug("https host name not find.");
    return 0;
}

u32 upf_set_exact_routing_https_l7_handle(vlib_main_t *vm, vlib_buffer_t *b, flowcache_fib_index_t *data, 
                                    u8 acl_rule_flag, u8 direction, u32 is_ip4)
{
    ip4_header_t *ip4 = NULL;
    ip6_header_t *ip6 = NULL;
    tcp_header_t *tcp;
    const unsigned char *https;
    u32 header_length;
    u32 https_length;
    RAW_EXTENSION upf_ext_headers[100] = {0};
    UPF_EXTENSION upf_sub_ext_headers[100] = {0};
    int ext_num = 0;
    int sub_ext_num = 0;
    upf_acl_rule_t *acl_rule = NULL;
    upf_main_t *gtm = &g_upf_main;
    u32 hit = 0;
    u8 url[64] = {0};  

    is_http(vm, b, is_ip4);
    if (upf_buffer_opaque (b)->upf.https)
    {        
        int ret = 0;
        ret = upf_https_client_hello_is_segment(vm,b);
        if (ret == 1)
        {
            upf_debug("segment pkt just return!\n");
            return ACL_RULE_INVALID; //segment pkt just return
        }
        
        if (is_ip4)
        {
            ip4 = (ip4_header_t *)vlib_buffer_get_current (b);
            tcp = ip4_next_header (ip4);
            https = (const unsigned char *)tcp + tcp_header_bytes (tcp);
            header_length = ip4_header_bytes (ip4) + tcp_header_bytes (tcp);
            https_length = vlib_buffer_length_in_chain (vm, b) - header_length;
        }
        else
        {
            ip6 = (ip6_header_t *)vlib_buffer_get_current (b);
            tcp = ip6_next_header (ip6);
            https = (const unsigned char *)tcp + tcp_header_bytes (tcp);
            header_length = sizeof (*ip6) + tcp_header_bytes (tcp);
            https_length = vlib_buffer_length_in_chain (vm, b) - header_length;
        }
        
        if (parse_upf_extensions(https, https_length, upf_ext_headers, upf_sub_ext_headers, &ext_num, &sub_ext_num) != 0)
        {
            upf_err ("parse upf extensions failed\n");
            return ACL_RULE_INVALID;
        }

        if (!upf_https_get_url(upf_ext_headers, ext_num, url))
        {
            upf_debug ("get https url(%s) fail.", url);
            return ACL_RULE_INVALID;
        }

        vec_foreach (acl_rule, gtm->acl_rule)
        {
            if (acl_rule->flag == 0)
            {
                if (strcmp ((char *)acl_rule->url, (char *)url) == 0)
                {
                    hit = 1;
                    break;
                }
            }
        }

        if (hit)
        {
            upf_set_exact_routing_acl_rule(b, data, acl_rule, direction);
            return ACL_RULE_HTTPS_L7;
        }

    }

    return ACL_RULE_INVALID;
}


u32 upf_process_exact_routing_acl_rule_handle(vlib_main_t *vm, vlib_buffer_t *b, 
                                              flowcache_fib_index_t *data, u8 direction, u32 is_ip4)
{
    u32 bfind = ACL_RULE_INVALID;
    bfind = upf_set_exact_routing_http_l7_handle(vm, b, data, ACL_RULE_HTTP_L7, direction, is_ip4);
    if (bfind == ACL_RULE_HTTP_L7)
    {
        upf_info("l7 http exact routing.");
        return EXACT_ROUTING_FIND;
    }
    bfind = upf_set_exact_routing_https_l7_handle(vm, b, data, ACL_RULE_HTTPS_L7, direction, is_ip4);
    if (bfind == ACL_RULE_HTTPS_L7)
    {
        upf_info("l7 https exact routing.");
        return EXACT_ROUTING_FIND;
    }
    
    bfind = upf_set_exact_routing_l3_4_handle(b, data, ACL_RULE_L4, direction, is_ip4);
    if (bfind == ACL_RULE_L4)
    {
        upf_info("l4 exact routing.");
        return EXACT_ROUTING_FIND;
    }
    bfind = upf_set_exact_routing_l3_4_handle(b, data, ACL_RULE_L3, direction, is_ip4);
    if (bfind == ACL_RULE_L3)
    {
        upf_info("l3 exact routing.");
        return EXACT_ROUTING_FIND;
    }

    return EXACT_ROUTING_NOT_FIND;
}

u32 upf_process_exact_routing_rat_type_handle(upf_session_t *sx, vlib_buffer_t *b, 
                                              flowcache_fib_index_t *data, u8 direction)
{
    upf_main_t *gtm = &g_upf_main;
    u32 hit = 0;
    upf_rat_type_t *rat_type_rule = NULL;
    vec_foreach (rat_type_rule, gtm->rat_type)
    {
        if (rat_type_rule->rat_type == sx->rat_type)
        {
            hit = 1;
            break;
        }
    }
    if (hit)
    {
        vnet_buffer (b)->sw_if_index[VLIB_TX] =
            fib_table_find(FIB_PROTOCOL_IP4, direction == UPF_UL ? rat_type_rule->ul_table_id : rat_type_rule->dl_table_id);
        data->fib_index = vnet_buffer (b)->sw_if_index[VLIB_TX];
        return EXACT_ROUTING_FIND;
    }
    return EXACT_ROUTING_NOT_FIND;
}
void upf_process_exact_routing_handle(vlib_main_t *vm, upf_session_t *sx, vlib_buffer_t *b, 
                                      flowcache_fib_index_t *data, u8 direction, u32 is_ip4)
{
     u32 ret = EXACT_ROUTING_NOT_FIND;
    // 1: find dnn
    ret = upf_process_exact_routing_dnn_handle(sx, b, data, direction);
    if (ret)
    {
        upf_info("dnn exact routing, fib_index=%u", data->fib_index);
        return;
    }
    
    // 2: user id imsi->msisdn->imei
    ret = upf_process_exact_routing_user_id_handle(sx, b, data, direction);
    if (ret)
    {
        upf_info("user id exact routing, fib_index=%u", data->fib_index);
        return;
    }

    // 3: acl_rule l7->l4->l3
    ret = upf_process_exact_routing_acl_rule_handle(vm, b, data, direction, is_ip4);
    if (ret)
    {
        upf_info("acl rule exact routing, fib_index=%u", data->fib_index);
        return;
    }

    // 4: rat-type
    ret = upf_process_exact_routing_rat_type_handle(sx, b, data, direction);
    if (ret)
    {
        upf_info("rat type exact routing, fib_index=%u", data->fib_index);
        return;
    }
}
// Add for Exact-Routing by liupeng on  2022-07-12 above

u32 upf_far_ip_uplink_handle(vlib_main_t *vm, u32 is_ip4, u8 direction, upf_session_t *sx, upf_far_t *far, vlib_buffer_t *b, u32 *next)
{
    if (upf_buffer_opaque (b)->upf.http && far->forward.header_enrichment)
    {
        if (0 != upf_pfcp_http_header_enrichment (vm, b, far, sx, is_ip4))
        {
            upf_debug ("http header enrichment drop.");
            return UPF_PROCESS_CPT_DROP;
        }
        upf_flowcache_abort_learning (b);
    }
    /* begin: Add by wangjunjie02 for http header enhancement on ******** */
    else
    {
        //upf_ul_modify_tcp_header_seq_num(vm, b, is_ip4);
    }
    /* end: Add by wangjunjie02 for http header enhancement on ******** */
    
    if (upf_buffer_opaque (b)->upf.https)
    {
        if (upf_https_header_enrichment (vm, b, far, sx, is_ip4) == -1)
        {
            upf_debug ("https header enrichment drop.");
            return UPF_PROCESS_CPT_DROP;
        }
        upf_flowcache_abort_learning (b);
    }
    else
    {
        //upf_ul_modify_tcp_header_seq_num(vm, b, is_ip4);
    }

    ip4_header_t *ip4 = NULL;
    ip6_header_t *ip6 = NULL;
    if (is_ip4)
        ip4 = vlib_buffer_get_current (b);
    else
        ip6 = vlib_buffer_get_current (b);

    if (ip6 && KEY_LOG_SWITCH(KL_SWITCH_DST_NAT66))
    {
        if (!upf_dst_nat66_map (&ip6->dst_address))
            upf_nat_update_checksums (vm, b, 0);
    }

    tcp_header_t *tcp = is_ip4 ? (tcp_header_t *)ip4_next_header (ip4) : (tcp_header_t *)ip6_next_header (ip6);

    if (is_ip4)
    {
        if (ip4->protocol == IP_PROTOCOL_TCP)
        {
            ip_csum_t sum0;
            sum0 = tcp->checksum;
            upf_tcp_mss_clamping (tcp, &sum0);
            tcp->checksum = ip_csum_fold (sum0);
        }
        b->flags &= ~(VNET_BUFFER_F_OFFLOAD_TCP_CKSUM | VNET_BUFFER_F_OFFLOAD_UDP_CKSUM | VNET_BUFFER_F_OFFLOAD_IP_CKSUM);

        if (KEY_LOG_SWITCH(KL_SWITCH_UL_SW_IDX_LOCAL0))
            vnet_buffer (b)->sw_if_index[VLIB_RX] = 0;

        *next = UPF_PROCESS_IP_NEXT_IP4_INPUT;
    }
    else
    {
        b->flags &= ~(VNET_BUFFER_F_OFFLOAD_TCP_CKSUM | VNET_BUFFER_F_OFFLOAD_UDP_CKSUM);
        *next = UPF_PROCESS_IP_NEXT_IP6_INPUT;
    }

    if (far->forward.table_id >= UPF_5GLAN_SESS_TABLE_BASE && !far->forward.fib_index)
    {
        if (0 != upf_fib_index_by_table_id(far->forward.table_id, &far->forward.fib_index))
        {
            upf_info ("vrf(table_id) %d in not (yet) defined", far->forward.table_id);
        }
    }

    vnet_buffer (b)->sw_if_index[VLIB_TX] = far->forward.fib_index;
    flowcache_fib_index_t *data = upf_flowcache_add_action (b, FLOWCACHE_ACTION_UPF_FOW_FIB, sizeof (u32));
    data->fib_index = far->forward.fib_index;

    if (KEY_LOG_SWITCH (KL_SWITCH_EXACT_ROUTING))
    {
        upf_process_exact_routing_handle(vm, sx, b, data, direction, is_ip4);
        if (data->fib_index == (u32)~0)
        {
            *next = UPF_PROCESS_IP_NEXT_DROP;
        }
    }

    if (far->forward.flags & FAR_F_TRANSPORT_LEVEL_MARKING)
    {
        ip_csum_t sum0;
        flowcache_transport_level_marking_t *data;
        data = upf_flowcache_add_action (b, FLOWCACHE_ACTION_TRANSPORT_LEVEL_MARKING,
            sizeof (flowcache_transport_level_marking_t));
        data->is_ip4 = is_ip4;
        data->transport_level_marking = far->forward.transport_level_marking;
        if (is_ip4)
        {
            /* Fix the IP4 checksum and tos */
            sum0 = ip4->checksum;
            u8 new = far->forward.transport_level_marking;/* old_l0 always 0, see the rewrite setup */
            u8 old = ip4->tos;
            sum0 = ip_csum_update (sum0, old, new, ip4_header_t, tos);/* changed member */
            ip4->checksum = ip_csum_fold (sum0);
            ip4->tos = new;
        }
        else
        {
            ip6_set_dscp_network_order (ip6, (far->forward.transport_level_marking >> 2) & 0x3F);
        }
        vnet_buffer2 (b)->qos.source = QOS_SOURCE_VLAN;
        vnet_buffer2 (b)->qos.bits = (far->forward.transport_level_marking >> 2) & 0x3F;
        b->flags |= VNET_BUFFER_F_QOS_DATA_VALID;
    }

    return UPF_PROCESS_OK;
}

void send_clear_buff_to_pfcp_thread(u32 sess_index, u32 far_id)
{
    upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
    memset (msg, 0, sizeof (*msg));

    upf_clear_buff_to_pfcp_t *buf = clib_mem_alloc_aligned_no_fail (sizeof(upf_clear_buff_to_pfcp_t), CLIB_CACHE_LINE_BYTES);
    memset (buf, 0, sizeof(upf_clear_buff_to_pfcp_t));

    msg->msg_id = PFCP_RPC_PUBLISH_DEL_BUFFER;
    buf->session_index = sess_index;
    buf->far_id = far_id;

    msg->data = (u8 *)buf;

    for (int i = 0; i < g_upf_main.num_pfcp_threads; i++)
    {
        upf_err("PFCP_RPC_PUBLISH_DEL_BUFFER, sess_idx:%u, far_id:%u\n", buf->session_index, far_id);
        send_sx_msg_to_pfcp_thread(i, msg);
    }
}

u32 upf_far_buffer_handle(vlib_main_t *vm, upf_session_t *sx, upf_pdr_t *pdr, upf_far_t *far, vlib_buffer_t *b, u32 bi, u8 direction)
{
    upf_main_t *um = &g_upf_main;

    struct rules *active = upf_get_rules (sx, SX_ACTIVE);
    upf_bar_t *bar = upf_get_bar_by_id (active, far->bar_id);
    u64 time_now = unix_time_now_nsec();

    if(um->sess_report_interval && ((time_now - sx->session_report_time) >= um->sess_report_interval))
    {
        send_clear_buff_to_pfcp_thread(upf_buffer_opaque (b)->upf.session_index, far->id);
        UPF_PDU_SESS_STATISTICS_ADD(sx, SENT_BUFFER_CLEAR_TIMES);
        UPF_STATISTICS_ADD(SENT_BUFFER_CLEAR_TIMES);
    }

    if (um->max_buffering_n && upf_buffering_packet (sx, far, bar, bi, direction, pdr))
    {
        upf_debug ("far buffer full, drop it!\n");
        UPF_STATISTICS_ADD(DROP_FOR_FAR_FULLED);
        UPF_PDU_SESS_STATISTICS_ADD(sx, DROP_FOR_FAR_FULLED);
        return UPF_PROCESS_CPT_BUF_FULL_DROP;
    }

    if (direction == UPF_DL && far->apply_action & F_APPLY_NOCP)
    {
        if ((PDN_TYPE_IPv4 <= sx->pdn_type) && (sx->pdn_type <= PDN_TYPE_IPv4v6))
        {
            if (is_v4_packet (vlib_buffer_get_current (b)))
            {
                ip4_header_t *ip4 = vlib_buffer_get_current (b);
                pdr->downlink_data_service_information.paging_policy_indication = (ip4->tos >> 2) & 0x3F;
                pdr->downlink_data_service_information.flags |= F_DDSI_PPI;
            }
            else
            {
                ip6_header_t *ip6 = vlib_buffer_get_current (b);
                pdr->downlink_data_service_information.paging_policy_indication = (ip6_traffic_class (ip6) >> 2) & 0x3F;
                pdr->downlink_data_service_information.flags |= F_DDSI_PPI;
            }

            u32 *qer_id;
            vec_foreach (qer_id, pdr->qer_ids)
            {
                upf_qer_t *qer0 = upf_get_qer_by_id (active, *qer_id);
                if (!qer0)
                    continue;
                if (qer0->flags & SX_QER_QOS_FLOW_IDENTIFIER)
                {
                    pdr->downlink_data_service_information.flags |= F_DDSI_QFII;
                    pdr->downlink_data_service_information.qfi = qer0->qos_flow_identifier;
                    break;
                }
            }
        }

        if (bar)
        {
            if (um->max_buffering_n == 0 || far->downlink_buf_n == 1)
            {
                if (bar->downlink_data_notification_delay == 0)
                {
                    upf_pfcp_downlink_data_report (sx, pdr);
                    UPF_PDU_SESS_STATISTICS_ADD(sx, SENT_DL_DATA_REPORT_TO_SMF);
                }
                else
                {
                    upf_pfcp_delay_downlink_report (sx, bar->downlink_data_notification_delay);
                    UPF_PDU_SESS_STATISTICS_ADD(sx, SENT_DL_DATA_REPORT_TO_SMF);
                }
            }
        }
        else
        {
            if (um->max_buffering_n == 0 || far->downlink_buf_n == 1)
            {
                upf_pfcp_downlink_data_report (sx, pdr);
                UPF_PDU_SESS_STATISTICS_ADD(sx, SENT_DL_DATA_REPORT_TO_SMF);
            }
        }
    }
    if (um->max_buffering_n)
    {
        upf_trace ("session 0x%lx far %p, have buffered %d packets\n", sx->up_seid, far->id, far->downlink_buf_n);
    }

    return UPF_PROCESS_CPT_BUFFERING;
}

void upf_process_far_duplicate_handle(vlib_main_t *vm, vlib_node_runtime_t *node, vlib_buffer_t *b, u32 is_ip4,
                                      upf_session_t *sx, upf_pdr_t *pdr, upf_far_t *far)
{
    process_duplicate_buffer(vm, node, is_ip4, b, pdr, far, 0, NULL, ~0);
    flowcache_duplicate_buffer_t *data;
    data = upf_flowcache_add_action (b, FLOWCACHE_ACTION_DUPLICATE_BUFFER, sizeof (flowcache_duplicate_buffer_t));
    data->is_ip4 = is_ip4;
    data->sess_idx = sx - g_upf_main.sessions;
    data->pdr_id = pdr->id;
    data->far_id = far->id;
}

u32 upf_process_eth_far_handle(vlib_main_t *vm, vlib_node_runtime_t *node, u32 bi, upf_session_t *sx,
                               upf_pdr_t *pdr, upf_far_t *far, u32 *next, u8 *direction)
{
    if (NULL == pdr || NULL == far)
    {
        upf_err("The para pdr or far is null.");
        return UPF_PROCESS_CPT_DROP;
    }

    vlib_buffer_t *b = vlib_get_buffer (vm, bi);

    if (PREDICT_TRUE (far->apply_action & F_APPLY_FORW))
    {
        if (far->forward.flags & FAR_F_PROXYING)
        {
            /* neil.fan@20220513 add for arp/ns proxying:
             * 1. For uplink, replace far with the downlink far of this session, send back to UE.
             * 2. For downlink, replace far with the uplink far of this session, send back to DN.
             * 3. Revert the direction.
             */
            if (upf_far_proxy_handle(vm, b, *direction, sx, pdr, &far))
            {
                *direction = (*direction == UPF_UL) ? UPF_DL : UPF_UL;
            }
        }

        if (far->forward.dst_intf == DST_INTF_5G_VN)
        {
            upf_process_next_vn_init(b, far);
            *next = UPF_PROCESS_ETH_NEXT_5GLAN_ETH;
            return UPF_PROCESS_OK;
        }

        if (far->forward.flags & FAR_F_OUTER_HEADER_CREATION)
        {
            u32 r = upf_process_far_eth_outer_hdr_create(vm, b, far, next);
            if (r != UPF_PROCESS_GO_ON)
                return r;
        }

        if ((far->forward.dst_intf == DST_INTF_CORE) && (sx->flags & SX_TUNNEL_ID))
        {
            *next = UPF_PROCESS_ETH_NEXT_N6_ENCAP;
            return UPF_PROCESS_OK;
        }

        vnet_buffer (b)->sw_if_index[VLIB_RX] = g_upf_main.sw_if_index;
        upf_buffer_node_ttl(b)->ttl.eth_mark = ETH_MARK_FAR_IN;
        *next = UPF_PROCESS_ETH_NEXT_FLOWTABLE_ETH_INPUT;
        return UPF_PROCESS_OK;
    }
    else if (far->apply_action & F_APPLY_BUFF)
    {
        return upf_far_buffer_handle(vm, sx, pdr, far, b, bi, *direction);
    }
    else
    {
        upf_debug ("far is not forward, not buffer, drop it!\n");
        return UPF_PROCESS_CPT_DROP;
    }
}

u32 upf_process_ip_far_handle(vlib_main_t *vm, vlib_node_runtime_t *node, u32 bi, upf_session_t *sx,
                              upf_pdr_t *pdr, upf_far_t *far, u32 *next)
{
    ip4_header_t *ip4;
    ip6_header_t *ip6;
    udp_header_t *udp;

    if (NULL == pdr || NULL == far)
    {
        upf_err("The para pdr or far is null.");
        return UPF_PROCESS_CPT_DROP;
    }

    vlib_buffer_t *b = vlib_get_buffer (vm, bi);
    u8 direction = IS_DL (pdr, far) ? UPF_DL : UPF_UL;
    u32 is_ip4 = is_v4_packet (vlib_buffer_get_current (b));

    if (is_ip4)
        ip4 = vlib_buffer_get_current (b);
    else
        ip6 = vlib_buffer_get_current (b);
    udp = is_ip4 ? (udp_header_t *)ip4_next_header (ip4) : (udp_header_t *)ip6_next_header (ip6);

    if (KEY_LOG_SWITCH(KL_DUPLICATE_BUF) && PREDICT_FALSE (far->apply_action & F_APPLY_DUPL))
        upf_process_far_duplicate_handle(vm, node, b, is_ip4, sx, pdr, far);

    if (PREDICT_TRUE (far->apply_action & F_APPLY_FORW))
    {
        if (upf_process_vxlan_handle(vm, is_ip4, pdr, next))
            return UPF_PROCESS_GO_TRACE;

        if (PREDICT_FALSE (udp->dst_port == clib_host_to_net_u16 (UDP_DST_PORT_dns)) && (dns_cache_flag==1))
        {
            *next = UPF_PROCESS_IP_NEXT_IP4_LOCAL;
            return UPF_PROCESS_GO_TRACE;
        }

        if (far->forward.dst_intf == DST_INTF_5G_VN)
        {
            upf_process_next_vn_init(b, far);
            if (!is_ip4)
                *next = UPF_PROCESS_IP_NEXT_UPF_5GLAN_IP6;
            else
                *next = UPF_PROCESS_IP_NEXT_UPF_5GLAN_IP4;
            upf_debug("the next node is 5GLAN.");
            return UPF_PROCESS_OK;
        }

        if ((far->forward.dst_intf == DST_INTF_CORE) && (sx->flags & SX_TUNNEL_ID))
        {
            *next = UPF_PROCESS_IP_NEXT_N6_ENCAP;
            return UPF_PROCESS_OK;
        }

        if (far->forward.flags & FAR_F_OUTER_HEADER_CREATION)
            return upf_process_far_ip_outer_hdr_create(vm, b, far, next);
        else if (PREDICT_FALSE(far->forward.flags & FAR_F_REDIRECT_INFORMATION))
            return upf_far_redirect_handle(vm, is_ip4, far, b, next);
        else
            return upf_far_ip_uplink_handle(vm, is_ip4, direction, sx, far, b, next);
    }
    else if (far->apply_action & F_APPLY_BUFF)
    {
        return upf_far_buffer_handle(vm, sx, pdr, far, b, bi, direction);
    }
    else
    {
        upf_err ("far is not forward, not buffer, drop it!\n");
        UPF_STATISTICS_ADD(FAR_ACTION_INVALID);
        UPF_PDU_SESS_STATISTICS_ADD(sx, FAR_ACTION_INVALID);
        return UPF_PROCESS_CPT_DROP;
    }
}

static void upf_dataguard_slave_handle(vlib_main_t *vm, vlib_buffer_t *b, u32 next, u32 is_ip4)
{
    upf_main_t *um = &g_upf_main;
    u32 instance_id = 0;
    u32 is_encap_ip4 = 0;
    instance_id = is_ip4 ? um->dataguard_gre_id_v4 : um->dataguard_gre_id_v6;
    if (next == UPF_PROCESS_IP_NEXT_IP4_LOOKUP || next == UPF_PROCESS_IP_NEXT_IP6_LOOKUP ||
        next == UPF_PROCESS_IP_NEXT_IP4_INPUT || next == UPF_PROCESS_IP_NEXT_IP6_INPUT)
    {
        if (g_upf_main.flow_to_gre == 1)
        {
            u32 rv = add_gre_header_l3(vm, b, instance_id, is_ip4 ? VNET_LINK_IP4 : VNET_LINK_IP6, &is_encap_ip4);
            if (rv)
            {
                upf_err("add_gre_header_l3 fail!");
            }
        }
        flowcache_upf_encap_gre_t *data = upf_flowcache_add_action (
              b, FLOWCACHE_ACTION_UPF_ENCAP_GRE, sizeof (flowcache_upf_encap_gre_t));
        data->is_ip4 = is_ip4;
        data->instance_id = instance_id;
    }
}

static uword
upf_ip_process (vlib_main_t *vm, vlib_node_runtime_t *node, vlib_frame_t *from_frame, u32 is_ip4)
{
    upf_single_trace_push_t grab = {0};
    u32 *from = vlib_frame_vector_args (from_frame);
    u32 *to_next;
    u32 n_left_from = from_frame->n_vectors;
    u32 next_index = node->cached_next_index;
    u32 current_time = (u32)vlib_time_now (vm);
    upf_main_t *um = &g_upf_main;
#define _(sym, str) u64 CPT_##sym = 0;
        foreach_upf_process_error
#undef _

    while (n_left_from > 0)
    {
        u32 n_left_to_next;
        vlib_get_next_frame (vm, node, next_index, to_next, n_left_to_next);

        while (n_left_from > 0 && n_left_to_next > 0)
        {
            u32 bi = from[0];
            u8 cached = 0;
            to_next[0] = bi;
            from += 1;
            to_next += 1;
            n_left_from -= 1;
            n_left_to_next -= 1;
            upf_session_t *sess = NULL;
            struct rules *active = NULL;
            upf_pdr_t *pdr = NULL;
            upf_far_t *far = NULL;
            u32 pfcp_thread_index = 0;
            u32 next = UPF_PROCESS_IP_NEXT_DROP;
            upf_proc_statis_t statis = STATISTICS_POST_OTHER;

            vlib_buffer_t *b = vlib_get_buffer (vm, bi);
            if (upf_process_get_sess_pdr_far(b, &sess, &active, &pdr, &far))
                goto trace;

            if (upf_process_ttl_check(b))
                goto trace;

            u8 direction = IS_DL (pdr, far) ? UPF_DL : UPF_UL;
            u8 src_intf = upf_buffer_opaque (b)->upf.src_intf;
            upf_trace ("up_seid:0x%lx, pdr: %u, far: %u, direction:%u src_intf:%u\n",
                sess->up_seid, pdr->id, far->id, direction, src_intf);

            pfcp_thread_index = sess->thread_index - g_upf_main.first_pfcp_thread_index;
            upf_traffic_statistics(sess, pfcp_thread_index, pdr, far, vlib_buffer_length_in_chain (vm, b), current_time, b);

            upf_process_parse_grab_msg(b, pdr->id, far->id, &grab);
            if (g_single_trace_flag)
            {
                grab.direction = UPF_PKT_DIRECTION_IN;
                upf_dp_grab_msg_push(b, sess, &grab, is_ip4);
            }

            upf_flowcache_start_learning (b);

            if ((active->srr) && upf_srrs_process (sess, active, pdr, far, b))
            {
                upf_debug ("srr process drop packet!\n");
                goto trace;
            }

            u32 advance_len = 0;
            if (upf_pkt_outer_header_removal(b, pdr->outer_header_removal, &advance_len))
                goto trace;
            if (advance_len)
            {
                flowcache_advance_t *data = upf_flowcache_add_action (b, FLOWCACHE_ACTION_ADVANCE, sizeof (flowcache_advance_t));
                gtpu_flags_t *gtp_flags = &(upf_buffer_opaque (b)->upf.gtp_flags);
                if (PREDICT_FALSE(gtp_flags->qmp && (gtp_flags->container_len > GTP_PDU_SESS_CONTAINER_BASIC)))
                    data->len = advance_len - (gtp_flags->container_len - GTP_PDU_SESS_CONTAINER_BASIC);
                else
                    data->len = advance_len;
                if (is_v4_packet (vlib_buffer_get_current (b)) != is_ip4)
                    data->encap_next = FT_NEXT_IP_LOOKUP;
            }

            is_ip4 = is_v4_packet (vlib_buffer_get_current (b));

            u32 ret = upf_process_ip_far_handle(vm, node, bi, sess, pdr, far, &next);
            switch (ret)
            {
                case UPF_PROCESS_CPT_BUF_FULL_DROP:
                    b->error = node->errors[UPF_PROCESS_ERROR_MAX_BUFFERING];
                    CPT_MAX_BUFFERING++;
                    /* no break, and drop it because of buffer full */
                case UPF_PROCESS_CPT_DROP:
                    goto next_drop;

                case UPF_PROCESS_CPT_BUFFERING:
                    CPT_BUFFERING++;
                    cached = 1;
                    break;
                case UPF_PROCESS_GO_TRACE:
                   goto trace;
                default:
                   break;
            }

            if (sess->user_id.flags & USER_ID_IMSI)
            {
                upf_log_ex ("imsi: %s ue_ip: %U,from intf_type %d to intf_type %d, pdr %u far %u\n",
                            sess->user_id.imsi_str, format_ip46_address, &sess->ue_address, IP46_TYPE_ANY,
                            pdr->pdi.source_interface_type, far->forward.dest_interface_type, pdr->id, far->id);
            }

            //upf_nwi_encap_vlan(far, b);

            if (upf_qers_process (sess, vm, active, pdr, b, direction))
            {
                upf_err ("qer process failed, drop it!\n");
                UPF_STATISTICS_ADD(EXECUTE_QER_FAILED);
                UPF_PDU_SESS_STATISTICS_ADD(sess, EXECUTE_QER_FAILED);
                goto next_drop;
            }

            if (upf_urrs_process (vm, sess, active, pdr, b, direction, src_intf))
            {
                upf_err ("urr process failed, drop it!\n");
                UPF_STATISTICS_ADD(EXECUTE_URR_FAILED);
                UPF_PDU_SESS_STATISTICS_ADD(sess, EXECUTE_URR_FAILED);
                goto next_drop;
            }

            if (upf_process_traffic_shape(vm, b, sess, pdr, far, next, direction))
            {
                upf_debug ("upf_process_traffic_shape, drop it!\n");
                UPF_STATISTICS_ADD(TRAFFIC_SHAPE_FAILED);
                UPF_PDU_SESS_STATISTICS_ADD(sess, TRAFFIC_SHAPE_FAILED);
                goto next_drop;
            }

            if (um->dataguard_switch == DATAGUARD_SWITCH_ON && um->dataguard_status == DATAGUARD_STATUS_BACKUP)
            {
                upf_dataguard_slave_handle(vm, b, next, is_ip4);
            }

            CPT_PROCESS++;
            goto trace;

next_drop:
            next = UPF_PROCESS_IP_NEXT_DROP;
trace:
            if (next == UPF_PROCESS_IP_NEXT_DROP)
            {
                statis = STATISTICS_POST_DROP;
                CPT_DROP++;
            }
            else if (next == UPF_PROCESS_IP_NEXT_GTP_IP4_ENCAP || next == UPF_PROCESS_IP_NEXT_GTP_IP6_ENCAP)
                statis = STATISTICS_POST_GTP_ENCAP;
            upf_traffic_statistics_post(sess, pfcp_thread_index, pdr, far, vlib_buffer_length_in_chain (vm, b), statis, b);

            if ((g_single_trace_flag) && (statis != STATISTICS_POST_GTP_ENCAP))
            {
                if (sess && sess->single_trace_flag != g_single_trace_flag)
                {
                    grab.direction = UPF_PKT_DIRECTION_ANY;
                    upf_dp_grab_msg_update(sess, &grab, is_ip4);
                }
                grab.direction = UPF_PKT_DIRECTION_OUT;
                upf_dp_grab_msg_push(b, sess, &grab, is_ip4);
            }

            if (PREDICT_FALSE (b->flags & VLIB_BUFFER_IS_TRACED))
                upf_process_trace(vm, node, b, sess, pdr, far, cached, next);

            if (next == UPF_PROCESS_IP_NEXT_IP4_LOOKUP || next == UPF_PROCESS_IP_NEXT_IP6_LOOKUP ||
                next == UPF_PROCESS_IP_NEXT_IP4_INPUT || next == UPF_PROCESS_IP_NEXT_IP6_INPUT ||
                next == UPF_PROCESS_IP_NEXT_VXLAN4_ENCAP || next == UPF_PROCESS_IP_NEXT_VXLAN6_ENCAP ||
                next == UPF_PROCESS_IP_NEXT_L2_INPUT)
            {
                upf_flowcache_end_learning (b);
            }
            else if (next != UPF_PROCESS_IP_NEXT_GTP_IP4_ENCAP && next != UPF_PROCESS_IP_NEXT_GTP_IP6_ENCAP &&
                     next != UPF_PROCESS_IP_NEXT_UPF_5GLAN_IP4 && next != UPF_PROCESS_IP_NEXT_UPF_5GLAN_IP6 &&
                     next != UPF_PROCESS_IP_NEXT_N6_ENCAP)
            {
                upf_flowcache_abort_learning (b);
            }

            if (PREDICT_FALSE (cached))
            {
                n_left_to_next++;
                to_next--;
            }
            else
            {
                vlib_validate_buffer_enqueue_x1 (vm, node, next_index, to_next, n_left_to_next, bi, next);
            }
        }

        vlib_put_next_frame (vm, node, next_index, n_left_to_next);
    }

#define _(sym, str) vlib_node_increment_counter (vm, node->node_index, UPF_PROCESS_ERROR_##sym, CPT_##sym);
    foreach_upf_process_error
#undef _

    return from_frame->n_vectors;
}

static uword
upf_ip4_process (vlib_main_t *vm, vlib_node_runtime_t *node,
                 vlib_frame_t *from_frame)
{
  return upf_ip_process (vm, node, from_frame, /* is_ip4 */ 1);
}

static uword
upf_ip6_process (vlib_main_t *vm, vlib_node_runtime_t *node,
                 vlib_frame_t *from_frame)
{
  return upf_ip_process (vm, node, from_frame, /* is_ip4 */ 0);
}

static uword upf_make_arp_reply(vlib_buffer_t *b, u8 direction, upf_pdr_t *pdr, upf_far_t *far0)
{
     ///ethernet_header_t *eth_h = (ethernet_header_t *)vlib_buffer_get_current (b);
     gtpu_mac_address_by_ip_kv_t mac_address_by_ip_kv = {0};
     gtpu_mac_address_by_ip_key_t k = {0};
 
     int l2_len = sizeof(ethernet_header_t);
 
     ethernet_header_t *eth = (ethernet_header_t *)vlib_buffer_get_current (b);
   
     u16 vlan1 = 0, vlan2 = 0;
     u16 etype;
     u64 tag;
     u8 vlan_num = 0;
    
     u16 *t = (u16 *)&tag;
    
     etype = clib_net_to_host_u16(eth->type);
     tag = *(u64 *)(eth + 1);
 
     if(etype == ETHERNET_TYPE_VLAN)
     {
         vlan1 = clib_net_to_host_u16 (t[0]) & 0xFFF;
         etype = clib_net_to_host_u16 (t[1]);
         vlan_num++;
         l2_len = l2_len + 32;
     }
 
     if (etype == ETHERNET_TYPE_VLAN)
     {
         vlan2 = clib_net_to_host_u16 (t[2]) & 0xFFF;
         etype = clib_net_to_host_u16 (t[3]);
         vlan_num++;
         l2_len = l2_len + 32;
     }
 
     if(vlan_num == 2)
     {
         k.c_vid = vlan2;
         k.s_vid = vlan1;
     }
     else
     {
         k.c_vid = vlan1;
         k.s_vid = vlan2;
     }
     
     k.nwi = far0->forward.nwi;
    
    if(etype == ETHERNET_TYPE_ARP)
    {
        ethernet_arp_header_t *arp = (ethernet_arp_header_t *)((u8 *)eth + l2_len);
        if(arp->opcode == clib_host_to_net_u16 (ETHERNET_ARP_OPCODE_request))
        {
            
            clib_memset (&mac_address_by_ip_kv, 0, sizeof (mac_address_by_ip_kv));
            ip46_address_t ip = {0};
            ip.ip4 = arp->ip4_over_ethernet[1].ip4;
            k.ip_addr.ip4 = arp->ip4_over_ethernet[1].ip4;
            mac_address_t mac = {0};
            mac_address_by_ip_kv.k = k;
            mac_address_by_ip_kv.v.as_u64 = ~0;
 
            clib_bihash_search_24_8 (&g_upf_main.mac_address_by_ip,
                   (clib_bihash_kv_24_8_t *)&mac_address_by_ip_kv,
               (clib_bihash_kv_24_8_t *)&mac_address_by_ip_kv);
            if(mac_address_by_ip_kv.v.as_u64 != ~0)
            {
                                    
                clib_memcpy (mac.bytes, mac_address_by_ip_kv.v.mac,sizeof (mac_address_t));
 
                arp->opcode = clib_host_to_net_u16 (ETHERNET_ARP_OPCODE_reply);
                arp->ip4_over_ethernet[1] = arp->ip4_over_ethernet[0];
                mac_address_from_bytes (&arp->ip4_over_ethernet[0].mac, mac.bytes);
                clib_mem_unaligned (&arp->ip4_over_ethernet[0].ip4.data_u32, u32) = ip.ip4.data_u32;
 
                clib_memcpy (eth->dst_address,eth->src_address,6);
                clib_memcpy (eth->src_address,mac.bytes,6);
                if(direction == UPF_DL)
                {
                    vnet_buffer (b)->sw_if_index[VLIB_RX] = vnet_buffer (b)->sw_if_index[VLIB_TX];
                }
                else
                {
                    switch (pdr->outer_header_removal)
                    {
                      case 0: /* GTP-U/UDP/IPv4 */
                         upf_buffer_opaque (b)->upf.src_intf = SRC_INTF_CORE;
                        break;
          
                      case 1: /* GTP-U/UDP/IPv6 */
                         upf_buffer_opaque (b)->upf.src_intf = SRC_INTF_CORE;
                        break;
                     }
                }
                return 1;
            }
                                
         }
     }
     return 0;
}


static uword upf_make_ip6_neighbour_advertisement(vlib_main_t *vm, vlib_buffer_t *b, u8 direction, upf_pdr_t *pdr, upf_far_t *far0)
{
   //ethernet_header_t *eth_h = (ethernet_header_t *)vlib_buffer_get_current (b);

    gtpu_mac_address_by_ip_key_t k = {0};
    int l2_len = sizeof(ethernet_header_t);

    ethernet_header_t *eth = (ethernet_header_t *)vlib_buffer_get_current (b);
  
    u16 vlan1 = 0, vlan2 = 0;
    u16 etype;
    u64 tag;
    u8 vlan_num = 0;
   
    u16 *t = (u16 *)&tag;
   
    etype = clib_net_to_host_u16(eth->type);
    tag = *(u64 *)(eth + 1);

    if(etype == ETHERNET_TYPE_VLAN)
    {
        vlan1 = clib_net_to_host_u16 (t[0]) & 0xFFF;
        etype = clib_net_to_host_u16 (t[1]);
        vlan_num++;
        l2_len = l2_len + 32;
    }

    if (etype == ETHERNET_TYPE_VLAN)
    {
        vlan2 = clib_net_to_host_u16 (t[2]) & 0xFFF;
        etype = clib_net_to_host_u16 (t[3]);
        vlan_num++;
        l2_len = l2_len + 32;
    }

    if(vlan_num == 2)
    {
        k.c_vid = vlan2;
        k.s_vid = vlan1;
    }
    else
    {
        k.c_vid = vlan1;
        k.s_vid = vlan2;
    }
    
    k.nwi =  far0->forward.nwi;

   if(etype == ETHERNET_TYPE_IP6)
   {
       ip6_header_t *ip0 = (ip6_header_t *)((u8 *)eth + l2_len);
       icmp46_header_t *icmp0;
       icmp6_type_t type0;
       icmp0 = ip6_next_header (ip0);
       type0 = icmp0->type;
       mac_address_t mac = {0};
       int bogus_length;
       
       if(type0 == ICMP6_neighbor_solicitation)
       {
           gtpu_mac_address_by_ip_kv_t mac_address_by_ip_kv = {0};
           clib_memset (&mac_address_by_ip_kv, 0, sizeof (mac_address_by_ip_kv));
           ip46_address_t ip = {0};
           icmp6_neighbor_solicitation_or_advertisement_header_t *h0;
           icmp6_neighbor_discovery_ethernet_link_layer_address_option_t *o0;
           h0 = ip6_next_header (ip0);

           u32  options_len0, error0;
           u32 ip6_sadd_link_local, ip6_sadd_unspecified;
           icmp6_neighbor_discovery_option_type_t option_type;
           option_type = ICMP6_NEIGHBOR_DISCOVERY_OPTION_source_link_layer_address;


           options_len0 = clib_net_to_host_u16 (ip0->payload_length) - sizeof (h0[0]);

           error0 = ICMP6_ERROR_NONE;

           ip6_sadd_link_local = ip6_address_is_link_local_unicast (&ip0->src_address);
           ip6_sadd_unspecified = ip6_address_is_unspecified (&ip0->src_address);

            //Check that source address is unspecified, link-local or else on-link
           if (!ip6_sadd_unspecified && !ip6_sadd_link_local)
           {
             //      ip6_main_t *im = &ip6_main;
              //u32 src_adj_index0 = ip6_src_lookup_for_packet (im, b[0], ip0);
           }

           o0 = (void *) (h0 + 1);
           o0 = ((options_len0 == 8 && o0->header.type == option_type && o0->header.n_data_u64s == 1) ? o0 : 0);

           //If src address unspecified or link local, donot learn neighbor MAC
           if (PREDICT_TRUE (error0 == ICMP6_ERROR_NONE && o0 != 0 && !ip6_sadd_unspecified))
           {
           
               ip.ip6 = h0->target_address;
               k.ip_addr.ip6 = h0->target_address;
               //clib_memcpy (mac_address_by_ip_kv.key, &ip,sizeof (ip46_address_t));
               mac_address_by_ip_kv.k = k;
               mac_address_by_ip_kv.v.as_u64 = ~0;

               clib_bihash_search_24_8 (&g_upf_main.mac_address_by_ip,
                      (clib_bihash_kv_24_8_t *)&mac_address_by_ip_kv,
                      (clib_bihash_kv_24_8_t *)&mac_address_by_ip_kv);

               if(mac_address_by_ip_kv.v.as_u64 != ~0)
               {
                   clib_memcpy (mac.bytes, mac_address_by_ip_kv.v.mac, sizeof (mac_address_t));
                   clib_memcpy (o0->ethernet_address, mac.bytes, 6);
                   o0->header.type = ICMP6_NEIGHBOR_DISCOVERY_OPTION_target_link_layer_address;

                   h0->advertisement_flags = clib_host_to_net_u32
                        (ICMP6_NEIGHBOR_ADVERTISEMENT_FLAG_SOLICITED
                         | ICMP6_NEIGHBOR_ADVERTISEMENT_FLAG_OVERRIDE);
                   h0->icmp.type = ICMP6_neighbor_advertisement;

                   ip0->dst_address = ip0->src_address;

                   ip0->src_address = h0->target_address;
                   ip0->hop_limit = 255;

                   h0->icmp.checksum = 0;
                   h0->icmp.checksum =
                       ip6_tcp_udp_icmp_compute_checksum (vm, 0, ip0,
                                   &bogus_length);

                   clib_memcpy (eth->dst_address,eth->src_address,6);
                   clib_memcpy (eth->src_address,mac.bytes,6);
                   if(direction == UPF_DL)
                   {
                   }
                   else
                   {
                       switch (pdr->outer_header_removal)
                       {
                         case 0: /* GTP-U/UDP/IPv4 */
                             upf_buffer_opaque (b)->upf.src_intf = SRC_INTF_CORE;
                           break;
             
                         case 1: /* GTP-U/UDP/IPv6 */
                             upf_buffer_opaque (b)->upf.src_intf = SRC_INTF_CORE;
                           break;
                           }
                   }
                   return 1;
                   
               }
           }   
       }
   }
   return 0;
}

u32 upf_far_proxy_handle(vlib_main_t *vm, vlib_buffer_t *b, u8 direction, upf_session_t *sx, upf_pdr_t *pdr, upf_far_t **far0)
{
    u16 type = upf_get_eth_type(vlib_buffer_get_current (b));
    u8 flags = (*far0)->forward.proxying.flags;

    if ((type == ETHERNET_TYPE_ARP) && (flags & F_PROXY_ARP))
    {
        if (upf_make_arp_reply(b, direction, pdr, *far0))
        {
            upf_buffer_opaque (b)->upf.data_offset = 0;
            goto replace_far;
        }
    }
    else if ((type == ETHERNET_TYPE_IP6) && (flags & F_PROXY_IP6_NS))
    {
        if (upf_make_ip6_neighbour_advertisement(vm, b, direction, pdr, *far0))
        {
            upf_buffer_opaque (b)->upf.data_offset = 0;
            goto replace_far;
        }
    }

    return 0;

replace_far:
    if (upf_get_spec_pdr_far(sx, SRC_INTF_CORE, &(upf_buffer_opaque (b)->upf.pdr_index), DST_INTF_ACCESS, far0))
    {
        upf_debug("upf_get_access_far fail, up_seid:0x%lx type:0x%x", sx->up_seid, type);
        return 0; /* get access far failed, to broadcast this ethernet frame */
    }
    upf_debug("proxying replace far ok, up_seid:0x%lx type:0x%x", sx->up_seid, type);

    return 1;
}

static uword
upf_ethernet_process (vlib_main_t *vm, vlib_node_runtime_t *node, vlib_frame_t *from_frame)
{
    // Add for eth single trace by liupeng on 2022-06-27 below
    upf_single_trace_push_t grab = {0};
    // Add for eth single trace by liupeng on 2022-06-27 above
    u32 *from = vlib_frame_vector_args (from_frame);
    u32 *to_next;
    u32 n_left_from = from_frame->n_vectors;
    u32 next_index = node->cached_next_index;
    u32 current_time = (u32)vlib_time_now (vm);
#define _(sym, str) u64 CPT_##sym = 0;
        foreach_upf_process_error
#undef _

    while (n_left_from > 0)
    {
        u32 n_left_to_next;
        vlib_get_next_frame (vm, node, next_index, to_next, n_left_to_next);
 
        while (n_left_from > 0 && n_left_to_next > 0)
        {
            u32 bi = from[0];
            u8 cached = 0;
            to_next[0] = bi;
            from += 1;
            to_next += 1;
            n_left_from -= 1;
            n_left_to_next -= 1;
            upf_session_t *sess = NULL;
            struct rules *active = NULL;
            upf_pdr_t *pdr = NULL;
            upf_far_t *far = NULL;
            u32 pfcp_thread_index = 0;
            u32 next = UPF_PROCESS_ETH_NEXT_DROP;
            upf_proc_statis_t statis = STATISTICS_POST_OTHER;

            vlib_buffer_t *b = vlib_get_buffer (vm, bi);
            if (upf_process_get_sess_pdr_far(b, &sess, &active, &pdr, &far))
                goto trace;

            if (upf_process_ttl_check(b))
                goto trace;

            u8 direction = IS_DL (pdr, far) ? UPF_DL : UPF_UL;
            u8 src_intf = upf_buffer_opaque (b)->upf.src_intf;
            upf_trace ("up_seid:0x%lx, pdr: %u, far: %u, direction:%u src_intf:%u\n",
                sess->up_seid, pdr->id, far->id, direction, src_intf);

            pfcp_thread_index = sess->thread_index - g_upf_main.first_pfcp_thread_index;
            upf_traffic_statistics(sess, pfcp_thread_index, pdr, far, vlib_buffer_length_in_chain (vm, b), current_time, b);
            // Add for eth single trace by liupeng on 2022-06-27 below
            grab.far_id = far->id;
            grab.pdr_id = pdr->id;
            if (g_single_trace_flag)
            {
                if (sess->single_trace_flag != g_single_trace_flag)
                {
                    upf_debug("grab msg list in seesion[sidx:] updata\n");
                    grab.direction = UPF_PKT_DIRECTION_ANY;
                    upf_dp_grab_msg_update(sess, &grab, 1);
                }
                grab.direction = UPF_PKT_DIRECTION_IN;
                upf_dp_grab_eth_msg_push(b, sess, &grab);
            }
            // Add for eth single trace by liupeng on 2022-06-27 above

            upf_flowcache_start_learning (b);

            u32 advance_len = 0;
            if (upf_pkt_outer_header_removal(b, pdr->outer_header_removal, &advance_len))
                goto trace;
            if (advance_len)
            {
                flowcache_advance_t *data = upf_flowcache_add_action (b, FLOWCACHE_ACTION_ADVANCE, sizeof (flowcache_advance_t));
                gtpu_flags_t *gtp_flags = &(upf_buffer_opaque (b)->upf.gtp_flags);
                if (PREDICT_FALSE(gtp_flags->qmp && (gtp_flags->container_len > GTP_PDU_SESS_CONTAINER_BASIC)))
                    data->len = advance_len - (gtp_flags->container_len - GTP_PDU_SESS_CONTAINER_BASIC);
                else
                    data->len = advance_len;
                data->encap_next = UPF_FLOWTABLE_ETH_NEXT_L2_INPUT;
            }

            u32 ret = upf_process_eth_far_handle(vm, node, bi, sess, pdr, far, &next, &direction);
            switch (ret)
            {
                case UPF_PROCESS_CPT_BUF_FULL_DROP:
                    b->error = node->errors[UPF_PROCESS_ERROR_MAX_BUFFERING];
                    CPT_MAX_BUFFERING++;
                    /* no break, and drop it because of buffer full */
                case UPF_PROCESS_CPT_DROP:
                    goto next_drop;

                case UPF_PROCESS_CPT_BUFFERING:
                    CPT_BUFFERING++;
                    cached = 1;
                    break;
                case UPF_PROCESS_GO_TRACE:
                   goto trace;
                 default:
                   break;
            }

            upf_nwi_encap_vlan(far, b);
            
            if (upf_qers_process (sess, vm, active, pdr, b, direction))
            {
                upf_debug ("qer process failed, drop it!\n");
                UPF_STATISTICS_ADD(EXECUTE_QER_FAILED);
                UPF_PDU_SESS_STATISTICS_ADD(sess, EXECUTE_QER_FAILED);
                goto next_drop;
            }

            if (upf_urrs_process (vm, sess, active, pdr, b, direction, src_intf))
            {
                upf_debug ("urr process failed, drop it!\n");
                UPF_STATISTICS_ADD(EXECUTE_URR_FAILED);
                UPF_PDU_SESS_STATISTICS_ADD(sess, EXECUTE_URR_FAILED);
                goto next_drop;
            }

            CPT_PROCESS++;
            goto trace;

next_drop:
            next = UPF_PROCESS_ETH_NEXT_DROP;
trace:
            if (next == UPF_PROCESS_ETH_NEXT_DROP)
            {
                upf_flowcache_abort_learning (b);
                statis = STATISTICS_POST_DROP;
                CPT_DROP++;
            }
            else if (next == UPF_PROCESS_ETH_NEXT_GTP_IP4_ENCAP || next == UPF_PROCESS_ETH_NEXT_GTP_IP6_ENCAP)
                statis = STATISTICS_POST_GTP_ENCAP;
            upf_traffic_statistics_post(sess, pfcp_thread_index, pdr, far, vlib_buffer_length_in_chain (vm, b), statis, b);

            // Add for eth single trace by liupeng on 2022-06-27 below
            if ((g_single_trace_flag) && (statis != STATISTICS_POST_GTP_ENCAP))
            {
                if (sess && sess->single_trace_flag != g_single_trace_flag)
                {
                    grab.direction = UPF_PKT_DIRECTION_ANY;
                    upf_dp_grab_msg_update(sess, &grab, 1);
                }
                grab.direction = UPF_PKT_DIRECTION_OUT;
                upf_dp_grab_eth_msg_push(b, sess, &grab);
            }
            // Add for eth single trace by liupeng on 2022-06-27 ABOVE
            if (PREDICT_FALSE (b->flags & VLIB_BUFFER_IS_TRACED))
                upf_process_trace(vm, node, b, sess, pdr, far, cached, next);

            if (PREDICT_FALSE (cached))
            {
               n_left_to_next++;
               to_next--;
            }
            else
            {
               vlib_validate_buffer_enqueue_x1 (vm, node, next_index, to_next, n_left_to_next, bi, next);
            }
        }
        vlib_put_next_frame (vm, node, next_index, n_left_to_next);
    }

#define _(sym, str) vlib_node_increment_counter (vm, node->node_index, UPF_PROCESS_ERROR_##sym, CPT_##sym);
    foreach_upf_process_error
#undef _

    return from_frame->n_vectors;
}

/* *INDENT-OFF* */
VLIB_REGISTER_NODE (upf_process_ip4_node) = {
    .function = upf_ip4_process,
    .name = "upf-ip4-process",
    .vector_size = sizeof (u32),
    .format_trace = format_upf_ip_process_trace,
    .type = VLIB_NODE_TYPE_INTERNAL,
    .n_errors = ARRAY_LEN (g_upf_process_error_strings),
    .error_strings = g_upf_process_error_strings,
    .n_next_nodes = UPF_PROCESS_IP_N_NEXT,
    .next_nodes =
        {
#define _(s, n) [UPF_PROCESS_IP_NEXT_##s] = n,
            foreach_upf_process_next
#undef _
        },
};
/* *INDENT-ON* */

// VLIB_NODE_FUNCTION_MULTIARCH (upf_process_ip4_node, upf_ip4_process);

/* *INDENT-OFF* */
VLIB_REGISTER_NODE (upf_process_ip6_node) = {
    .function = upf_ip6_process,
    .name = "upf-ip6-process",
    .vector_size = sizeof (u32),
    .format_trace = format_upf_ip_process_trace,
    .type = VLIB_NODE_TYPE_INTERNAL,
    .n_errors = ARRAY_LEN (g_upf_process_error_strings),
    .error_strings = g_upf_process_error_strings,
    .n_next_nodes = UPF_PROCESS_IP_N_NEXT,
    .next_nodes =
        {
#define _(s, n) [UPF_PROCESS_IP_NEXT_##s] = n,
            foreach_upf_process_next
#undef _
        },
};
/* *INDENT-ON* */

/* *INDENT-OFF* */
VLIB_REGISTER_NODE (upf_process_ethernet_node) = {
    .function = upf_ethernet_process,
    .name = "upf-ethernet-process",
    .vector_size = sizeof (u32),
    .format_trace = format_upf_eth_process_trace,
    .type = VLIB_NODE_TYPE_INTERNAL,
    .n_errors = ARRAY_LEN (g_upf_process_error_strings),
    .error_strings = g_upf_process_error_strings,
    .n_next_nodes = UPF_PROCESS_ETH_NEXT_N,
    .next_nodes =
        {
            [UPF_PROCESS_ETH_NEXT_DROP] = "error-drop",
            [UPF_PROCESS_ETH_NEXT_GTP_IP4_ENCAP] = "upf4-encap",
            [UPF_PROCESS_ETH_NEXT_GTP_IP6_ENCAP] = "upf6-encap",
            [UPF_PROCESS_ETH_NEXT_FLOWTABLE_ETH_INPUT] = "flowtable-eth-input",
            [UPF_PROCESS_ETH_NEXT_5GLAN_ETH] = "upf-5glan-eth",
            [UPF_PROCESS_ETH_NEXT_N6_ENCAP] = "upf-n6-encap",
        },
};
/* *INDENT-ON* */

uword
unformat_upf_dns_cache_flag (unformat_input_t * input, va_list * args)
{
  u32 *result = va_arg (*args, u32 *);
  u32 flags = 0;

  if (unformat (input, "enable"))
    flags = 1;
  else if (unformat (input, "disable"))
    flags = 0;
  else
    return 0;

  *result = flags;
  return 1;
}

static clib_error_t *
set_upf_dns_cache (vlib_main_t * vm, unformat_input_t * input, vlib_cli_command_t * cmd)
{
  clib_error_t *error;
  u32 flags;

  if (!unformat (input, "%U", unformat_upf_dns_cache_flag, &flags))
    {
      error = clib_error_return (0, "unknown flags `%U'", format_unformat_error, input);
      goto done;
    }

    dns_cache_flag = flags;

  printf("dns_cache_flag = %d\n",dns_cache_flag);
  return (NULL);
done:
  return error;
}

VLIB_CLI_COMMAND (upf_dns_cache_command, static) = {
  .path = "upf dns cache",
  .short_help = "upf dns cache [enable|disable]",
  .function = set_upf_dns_cache,
};

uword
unformat_upf_vxlan_flag (unformat_input_t * input, va_list * args)
{
  u32 *result = va_arg (*args, u32 *);
  u32 flags = 0;

  if (unformat (input, "enable"))
    flags = 1;
  else if (unformat (input, "disable"))
    flags = 0;
  else
    return 0;

  *result = flags;
  return 1;
}

static clib_error_t *
set_upf_vxlan (vlib_main_t * vm, unformat_input_t * input, vlib_cli_command_t * cmd)
{
  
  clib_error_t *error;
  u32 flags;

  if (!unformat (input, "%U", unformat_upf_vxlan_flag, &flags))
    {
      error = clib_error_return (0, "unknown flags `%U'", format_unformat_error, input);
      goto done;
    }

    vxlan_flag = flags;

  printf("vxlan_flag = %d\n",vxlan_flag);
  return (NULL);
done:
  return error;
}

VLIB_CLI_COMMAND (upf_vxlan_command, static) = {
  .path = "upf vxlan",
  .short_help = "upf vxlan [enable|disable]",
  .function = set_upf_vxlan,
};

/*
 * fd.io coding-style-patch-verification: ON
 *
 * Local Variables:
 * eval: (c-set-style "gnu")
 * End:
 */
