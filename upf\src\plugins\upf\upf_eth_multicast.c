#include <vnet/vnet.h>
#include <vnet/plugin/plugin.h>
#include <vpp/app/version.h>
#include <vnet/classify/vnet_classify.h>
#include <vnet/ip/ip6_link.h>
#include <vnet/ip/ip6_hop_by_hop.h>
#include <vnet/ip/reass/ip4_sv_reass.h>
#include <vnet/ip/reass/ip4_full_reass.h>
#include <vnet/ip/reass/ip6_sv_reass.h>
#include <vnet/ip/reass/ip6_full_reass.h>
#include <vnet/ip-neighbor/ip_neighbor.h>
#include <vpp/stats/stat_segment.h>
#include <math.h>
#include <string.h>
#include <search.h>

#include <upf/upf.h>
#include <upf/upf_pfcp.h>
#include <upf/pfcp.h>
#include <upf/upf_pfcp.h>
#include <upf/upf_pfcp_server.h>
#include <upf/upf_pfcp_api.h>
#include <upf/flowtable.h>
#include <upf/upf_eth_multicast.h>
#include <igmp/igmp_pkt.h>

static u32 **eth_multicast_clones;

extern upf_nwi_t *upf_lookup_nwi (u8 *name);

static clib_error_t *
upf_show_eth_multicast_command_fn (vlib_main_t *vm, unformat_input_t *main_input,
                             vlib_cli_command_t *cmd)
{
  unformat_input_t _line_input, *line_input = &_line_input;
  upf_main_t *gtm = &g_upf_main;
  clib_error_t *error = NULL;
  u8 *nwi_name, *mac;
  u32 c_vlan, s_vlan;
  u32 nwi_name_flag = 0, c_vlan_flag = 0, s_vlan_flag = 0, mac_flag = 0;
  upf_eth_multicast_t *domain_members;
  upf_eth_multicast_key_t *p_key, key;
  u32 *idx;
  u32 limit = 10;

  if (unformat_user (main_input, unformat_line_input, line_input))
    {
        while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
        {
            if (unformat (line_input, "nwi %s", &nwi_name))
                nwi_name_flag = 1;
            else if (unformat (line_input, "c_vlan %u", &c_vlan))
                c_vlan_flag = 1;
            else if (unformat (line_input, "s_vlan %u", &s_vlan))
                s_vlan_flag = 1;
            else if (unformat (line_input, "mac %s", &mac))
                mac_flag = 1;
            else if (unformat (line_input, "limit %s", &limit))
                ;
            else
            {
                error = unformat_parse_error (line_input);
                unformat_free (line_input);
                goto done;
            }
        }
        unformat_free (line_input);
    }

    if (nwi_name_flag && (c_vlan_flag || s_vlan_flag) && mac_flag)
    {
        upf_nwi_t *nwi = upf_lookup_nwi (iupf_name_to_labels (nwi_name));
        key.nwi_idx = nwi - gtm->nwis;
        key.c_vid = c_vlan;
        key.s_vid = s_vlan;
        memcpy(key.mac.bytes, mac, sizeof(key.mac.bytes));
        hash_pair_t *hp = hash_get_pair (gtm->hash_eth_multicast_id_by_key, &key);
        if (hp)
        {
            domain_members = (void *)(hp->value[0]);
            vlib_cli_output (vm, "type:%s, nwi_idx%u, c_vlan:%u, s_vlan:%u, mac:%U\n", 
                domain_members->type?"static":"dynamic", key.nwi_idx, key.c_vid, key.s_vid, format_mac_address, key.mac.bytes);
            vec_foreach (idx, domain_members->index)
            {
                vlib_cli_output (vm, "    session index: %u ", *idx);
            }
        }
    }
    else
    {
        hash_foreach_mem (p_key, domain_members, gtm->hash_eth_multicast_id_by_key,
        ({
            vlib_cli_output (vm, "type:%s, nwi_idx%u, c_vlan:%u, s_vlan:%u, mac:%U\n", 
                domain_members->type?"static":"dynamic", p_key->nwi_idx, p_key->c_vid, p_key->s_vid, format_mac_address, p_key->mac.bytes);
            vec_foreach (idx, domain_members->index)
            {
                vlib_cli_output (vm, "    session index: %u ", *idx);
            }
        }));
    }

done:
    return error;
}

/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_show_eth_multicast_command, static) = {
    .path = "show upf eth-multicast",
    .short_help = "show upf eth-multicast [nwi] [c_vlan] [s_vlan] [mac] [limit]",
    .function = upf_show_eth_multicast_command_fn,
};
/* *INDENT-ON* */

u32 upf_eth_multicast_clone_init (vlib_main_t *vm)
{
    vec_validate (eth_multicast_clones, vlib_num_workers());
    return 0;
}

void upf_add_member_to_multicast_table(upf_eth_multicast_t *data_info)
{
    UPF_STATISTICS_ADD(PFCP_RCV_ADD_GROUP_MEM);

    upf_eth_multicast_t *domain_members = NULL;

    hash_pair_t *hp = hash_get_pair (g_upf_main.hash_eth_multicast_id_by_key, &data_info->key);
    upf_eth_multicast_key_t *p_key = &data_info->key;

    upf_trace("add nwi_idx:%u, c_vlan:%u, s_vlan:%u, mac:%U, sess_index:%u", 
                 p_key->nwi_idx, p_key->c_vid, p_key->s_vid, format_mac_address, p_key->mac.bytes,data_info->report_index );

    upf_eth_multicast_key_t *sess_key = NULL;
    upf_session_t *sess = NULL;

    if (data_info->report_index != (u32)~0)
    {
        sess = sx_get_by_index(data_info->report_index);
        
        if(sess)
        {
            vec_foreach (sess_key, sess->key_multicast_list)
            {
                if (sess_key->as_u64[0] == p_key->as_u64[0] && sess_key->as_u64[1] == p_key->as_u64[1])
                {
                    UPF_STATISTICS_ADD(SESS_SAME_DOMAIN_MEMBER_EXISTED);

                    upf_trace("ethernet dst multicast domain member already exist! s_vlan:%u, c_vlan:%u nwi_idx:%u, mac:%U, sess_idx:%u", 
                        sess_key->s_vid, sess_key->c_vid, sess_key->nwi_idx, format_mac_address, sess_key->mac.bytes, data_info->report_index); 
                    return;
                }
            }
            APPEND_U_INDEX(sess->key_multicast_list, *p_key);
        }
    }

    if (hp)
    {
        UPF_STATISTICS_ADD(MULTICAST_GROUP_DOMAIN_EXISTED);
        domain_members = (void *)(hp->value[0]);
        upf_trace("already exist ethernet multicast domain, s_vlan:%u, c_vlan:%u nwi_idx:%u, sess_idx:%u", 
            domain_members->key.s_vid, domain_members->key.c_vid, domain_members->key.nwi_idx, data_info->report_index);
        
        u32 *idx;
        vec_foreach (idx, domain_members->index)
        {
            if(*idx == data_info->report_index)
            {
                UPF_STATISTICS_ADD(MULTICAST_GROUP_MEM_EXISTED);
                return;
            }
        }
        APPEND_U_INDEX(domain_members->index, data_info->report_index);
    }
    else
    {
        UPF_STATISTICS_ADD(MULTICAST_GROUP_MEM_CREATED);
        domain_members = clib_mem_alloc(sizeof(upf_eth_multicast_t));
        domain_members->key = data_info->key;
        domain_members->index = NULL;
        APPEND_U_INDEX(domain_members->index, data_info->report_index);
        upf_eth_multicast_key_t *dst_key = clib_mem_alloc(sizeof(upf_eth_multicast_key_t));

        *dst_key = data_info->key;
        hash_set_mem(g_upf_main.hash_eth_multicast_id_by_key, dst_key, domain_members);
        upf_trace("create ethernet multicast domain, s_vlan:%u, c_vlan:%u nwi_idx:%u, sess_idx:%u", 
            data_info->key.s_vid, data_info->key.c_vid, data_info->key.nwi_idx, data_info->report_index);
    }
}

void upf_multicast_add_mem_to_pfcp(vlib_buffer_t *b, upf_eth_multicast_key_t *key)
{
    upf_eth_multicast_t *data_info;

    upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
    memset (msg, 0, sizeof (*msg));
    data_info = clib_mem_alloc_aligned_no_fail (sizeof(upf_eth_multicast_t), CLIB_CACHE_LINE_BYTES);
    memset (data_info, 0, sizeof(upf_eth_multicast_t));
    msg->msg_id = PFCP_RPC_PUBLISH_ETH_MULTICAST_ADD;
    data_info->key = *key;
    data_info->report_index = upf_buffer_opaque (b)->upf.session_index;
    msg->data = data_info;

    for (int i = 0; i < g_upf_main.num_pfcp_threads; i++)
    {
        upf_trace ("PFCP_RPC_PUBLISH_ETH_MULTICAST, sess_idx:%u\n", data_info->report_index);
        send_sx_msg_to_pfcp_thread(i, msg);
    }
    UPF_STATISTICS_ADD(WORK_SEND_ADD_GROUP_MEM);
    return;
}

void upf_del_eth_multicast_domain(upf_eth_multicast_key_t *key)
{
    upf_eth_multicast_key_t *key_copy = NULL;
    upf_eth_multicast_t *value_copy = NULL;
    hash_pair_t *hp = NULL;

    hp = hash_get_pair (g_upf_main.hash_eth_multicast_id_by_key, key);
    if (hp == NULL)
    {
        upf_err ("The ethernet multicast domain is not exist.");
        return;
    }

    //del member for pdu sess
    upf_eth_multicast_t *domain_members = (void *)(hp->value[0]);
    u32 *idx = NULL;
    upf_session_t *sess = NULL;
    upf_eth_multicast_key_t *sess_key = NULL;
    if(domain_members)
    {
        vec_foreach (idx, domain_members->index)
        {
            upf_trace ("upf_del_eth_multicast_domain_list session index: %u ", *idx);
            sess = sx_get_by_index(*idx);
            if(sess && sess->key_multicast_list)
            {
                vec_foreach (sess_key, sess->key_multicast_list)
                {
                    if (sess_key->as_u64[0] == key->as_u64[0] && sess_key->as_u64[1] == key->as_u64[1])
                    {
                        vec_del1 (sess->key_multicast_list, sess_key - sess->key_multicast_list);

                        upf_trace("upf_del_eth_multicast_domain s_vlan:%u, c_vlan:%u nwi_idx:%u, mac:%U", 
                            sess_key->s_vid, sess_key->c_vid, sess_key->nwi_idx, format_mac_address, sess_key->mac.bytes); 
                    }
                }
            }
        }
    }

    key_copy = (void *)(hp->key);
    value_copy = (void *)(hp->value[0]);
    hash_unset_mem(g_upf_main.hash_eth_multicast_id_by_key, key);
    clib_mem_free(key_copy);
    clib_mem_free(value_copy);
    return;
}

void upf_del_member_to_multicast_table(upf_eth_multicast_t *data_info)
{
    u32 *idx;
    upf_session_t *sess = NULL;
    hash_pair_t *hp = hash_get_pair (g_upf_main.hash_eth_multicast_id_by_key, &data_info->key);
    upf_eth_multicast_key_t *p_key = &data_info->key;
    upf_eth_multicast_key_t *sess_key;
    
    if (data_info->report_index != (u32)~0)
    {
        sess = sx_get_by_index(data_info->report_index);
        if(sess && sess->key_multicast_list)
        {
            vec_foreach (sess_key, sess->key_multicast_list)
            {
                if (sess_key->as_u64[0] == p_key->as_u64[0] && sess_key->as_u64[1] == p_key->as_u64[1])
                {
                    vec_del1 (sess->key_multicast_list, sess_key - sess->key_multicast_list);
                }
            }

        }
    }

    upf_trace("pfcp delete ethernet multicast domain member nwi_idx%u, c_vlan:%u, s_vlan:%u, mac:%U, sess_index:%u", 
                 p_key->nwi_idx, p_key->c_vid, p_key->s_vid, format_mac_address, p_key->mac.bytes,data_info->report_index );

    if (hp == NULL)
    {
        upf_err ("The ethernet multicast domain is not exist.");
        return;
    }
    upf_eth_multicast_t *domain_members = (void *)(hp->value[0]);
    if (!domain_members)
        return;
    vec_foreach (idx, domain_members->index)
    {
        if (*idx == data_info->report_index)
        {
            UPF_STATISTICS_ADD(PFCP_RCV_DEL_GROUP_DOMAIN_MEM);
            vec_del1 (domain_members->index, idx - domain_members->index);
            if (!vec_len(domain_members->index))
            {
                UPF_STATISTICS_ADD(PFCP_RCV_DEL_GROUP_DOMAIN);
                upf_del_eth_multicast_domain(p_key);
                upf_trace("pfcp delete ethernet multicast domain:nwi_idx:%u, s_vlan:%u, c_vlan:%u, mac:%U, sess_index:%u", 
                    p_key->nwi_idx, p_key->s_vid, p_key->c_vid, format_mac_address, p_key->mac.bytes,data_info->report_index);
            }
            return;
        }
    }
    return ;
}

void upf_multicast_del_mem_to_pfcp(vlib_buffer_t *b, upf_eth_multicast_key_t *key)
{
    upf_eth_multicast_t *data_info;

    upf_send_msg_t *msg = clib_mem_alloc_no_fail (sizeof (*msg));
    memset (msg, 0, sizeof (*msg));
    data_info = clib_mem_alloc_aligned_no_fail (sizeof(upf_eth_multicast_t), CLIB_CACHE_LINE_BYTES);
    memset (data_info, 0, sizeof(upf_eth_multicast_t));
    msg->msg_id = PFCP_RPC_PUBLISH_ETH_MULTICAST_DEL;
    data_info->key = *key;
    data_info->report_index = upf_buffer_opaque (b)->upf.session_index;
    msg->data = data_info;

    for (int i = 0; i < g_upf_main.num_pfcp_threads; i++)
    {
        upf_trace ("PFCP_RPC_PUBLISH_ETH_MULTICAST_DEL");
        send_sx_msg_to_pfcp_thread(i, msg);
        UPF_STATISTICS_ADD(WORK_SEND_DEL_GROUP_MEM);
    }
    return;
}

void upf_del_member_from_multicast_table(upf_session_t *sess)
{
    u32 *idx;
    u32 sess_idx = sess - g_upf_main.sessions;
    upf_eth_multicast_key_t *key = NULL;

    /* delete member from all broadcast domain which include the sess */
    vec_foreach (key, sess->key_multicast_list)
    {
        upf_trace("delete ethernet multicast domain member:nwi_idx:%u, s_vlan:%u, c_vlan:%u, mac:%U, sess_index:%u", 
            key->nwi_idx, key->s_vid, key->c_vid, format_mac_address, key->mac.bytes,sess_idx);

        hash_pair_t *hp = hash_get_pair (g_upf_main.hash_eth_multicast_id_by_key, key);
        if (hp == NULL)
        {
            upf_trace ("The ethernet multicast domain is not exist.");
            continue;
        }
        upf_eth_multicast_t *domain_members = (void *)(hp->value[0]);
        if (!domain_members)
        {
            continue;
        }
        vec_foreach (idx, domain_members->index)
        {
            if (*idx == sess_idx)                                          
            {
                vec_del1 (domain_members->index, idx - domain_members->index);
                if (!vec_len(domain_members->index))
                {
                    upf_del_eth_multicast_domain(key);
                    upf_trace("delete ethernet multicast domain:nwi_idx:%u, s_vlan:%u, c_vlan:%u, mac:%U, sess_index:%u", 
                        key->nwi_idx, key->s_vid, key->c_vid, format_mac_address, key->mac.bytes,sess_idx);
                    break;
                }
                
            }
        }
    }
    vec_free(sess->key_multicast_list);

    return;
}

void upf_eth_multicast_get_user_num(u32 sess_idx, 
    u32 *sess_idxs, u32 *user_num)
{
    u32 *idx = NULL;
    u32 sess_num = 0;

    vec_foreach (idx, sess_idxs)
    {
        if (*idx == sess_idx)
        {
            continue;
        }
        sess_num++;
    }

    *user_num = sess_num;

    return;
}

void handle_eth_igmp_snooping(vlib_buffer_t *b0, upf_eth_multicast_key_t *key)
{
    igmp_membership_report_v3_t *igmp3_header = NULL;
    upf_igmp_membership_report_v2_t *igmp2_header = NULL;
    u32 i = 0;
    u8 eth_mac[6] = {0};
    u32 igmpHdrOffset = upf_buffer_opaque (b0)->upf.data_offset + sizeof(ethernet_header_t)+ sizeof (ip4_header_t) + 4;
    
    //ip header >> options(4byte)
    igmp3_header = vlib_buffer_get_current (b0) + igmpHdrOffset;
    if (igmp3_header->header.type == IGMP_TYPE_membership_report_v3)
    {
        UPF_STATISTICS_ADD(IP4_IGMP_V3);
        for (i = 0; i < clib_net_to_host_u16(igmp3_header->n_groups); i++)
        {
            if ((igmp3_header->groups[i].type == IGMP_MEMBERSHIP_GROUP_mode_is_include) 
                 ||(igmp3_header->groups[i].type == IGMP_MEMBERSHIP_GROUP_change_to_include))
            {
                UPF_STATISTICS_ADD(IP4_IGMP_V3_INCLUDE);
                // accord multicast address get multicast mac
                ip4_multicast_ethernet_address(eth_mac, &igmp3_header->groups[i].group_address);

                // del mac & session_index relation
                clib_memcpy_fast(key->mac.bytes, eth_mac, sizeof(key->mac.bytes));
                upf_multicast_del_mem_to_pfcp(b0, key);
            } 
            else if ((igmp3_header->groups[i].type == IGMP_MEMBERSHIP_GROUP_mode_is_exclude)
                      || (igmp3_header->groups[i].type == IGMP_MEMBERSHIP_GROUP_change_to_exclude))
            {
                UPF_STATISTICS_ADD(IP4_IGMP_V3_EXCLUDE);
                // accord multicast address get multicast mac
                ip4_multicast_ethernet_address(eth_mac, &igmp3_header->groups[i].group_address);

                // add mac & session_index relation
                clib_memcpy_fast(key->mac.bytes, eth_mac, sizeof(key->mac.bytes));
                upf_multicast_add_mem_to_pfcp(b0, key);
            }
            else
            {
                upf_debug("igmp3_header->groups[i].type:%u", igmp3_header->groups[i].type);
            }
        }
                    
    }
    else if (igmp3_header->header.type == IGMP_TYPE_membership_report_v2)
    {
        UPF_STATISTICS_ADD(IP4_IGMP_V2);
        igmp2_header = vlib_buffer_get_current (b0) + igmpHdrOffset;

        // accord multicast address get multicast mac
        ip4_multicast_ethernet_address(eth_mac, &igmp2_header->group_address);
        
        // add mac & session_index relation
        clib_memcpy_fast(key->mac.bytes, eth_mac, sizeof(key->mac.bytes));
        upf_multicast_add_mem_to_pfcp(b0, key);
    }
    else if (igmp3_header->header.type == IGMP_TYPE_leave_group_v2)
    {
        UPF_STATISTICS_ADD(IP4_IGMP_V2_LEAVE_GROUP);
        igmp2_header = vlib_buffer_get_current (b0) + igmpHdrOffset;
        // accord multicast address get multicast mac
        ip4_multicast_ethernet_address(eth_mac, &igmp2_header->group_address);
        
        // del mac & session_index relation
        clib_memcpy_fast(key->mac.bytes, eth_mac, sizeof(key->mac.bytes));
        upf_multicast_del_mem_to_pfcp(b0, key);
        
    }
    else
    {
        upf_debug("Other igmp type! 0x%x", igmp3_header->header.type);
    }
}

void handle_eth_mld_snooping(vlib_buffer_t *b0, upf_eth_multicast_key_t *key)
{
    upf_icmp6_multicast_listener_report_v2_t *icmp6_rh_v2 = NULL;
    upf_icmp6_multicast_listener_report_v1_t *icmp6_rh_v1 = NULL;
    u32 i = 0;
    u8 eth_mac[6] = {0};

    icmp6_rh_v2 = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip6_header_t);

    if (icmp6_rh_v2->icmp.type == ICMP6_multicast_listener_report_v2)
    {
        UPF_STATISTICS_ADD(IP6_IGMP_V2);
        for (i = 0; i < clib_net_to_host_u16(icmp6_rh_v2->num_addr_records); i++)
        {
            if ((icmp6_rh_v2->records[i].type == IGMP_MEMBERSHIP_GROUP_mode_is_include) 
                 ||(icmp6_rh_v2->records[i].type == IGMP_MEMBERSHIP_GROUP_change_to_include))
            {
                UPF_STATISTICS_ADD(IP6_IGMP_V2_INCLUDE);
                // accord multicast address get multicast mac
                ip6_multicast_ethernet_address(eth_mac, IP6_MULTICAST_GROUP_ID_all_hosts);

                // add mac & session_index relation
                clib_memcpy_fast(key->mac.bytes, eth_mac, sizeof(key->mac.bytes));
                upf_multicast_del_mem_to_pfcp(b0, key);
            }
            else if ((icmp6_rh_v2->records[i].type == IGMP_MEMBERSHIP_GROUP_mode_is_exclude)
                   || (icmp6_rh_v2->records[i].type == IGMP_MEMBERSHIP_GROUP_change_to_exclude))
            {
                UPF_STATISTICS_ADD(IP6_IGMP_V2_EXCLUDE);
                // accord multicast address get multicast mac
                ip6_multicast_ethernet_address(eth_mac, IP6_MULTICAST_GROUP_ID_all_hosts);

                // del mac & session_index relation
                clib_memcpy_fast(key->mac.bytes, eth_mac, sizeof(key->mac.bytes));
                upf_multicast_add_mem_to_pfcp(b0, key);
            }
            else
            {
                upf_debug("icmp6_header->records[i].type:%u", icmp6_rh_v2->records[i].type);
            }
        }

    }
    else if (icmp6_rh_v2->icmp.type == ICMP6_multicast_listener_report)
    {
        UPF_STATISTICS_ADD(IP6_IGMP_LISTENER_REPORT);

        icmp6_rh_v1 = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip6_header_t);
        // accord multicast address get multicast mac
        ip6_multicast_ethernet_address(eth_mac, IP6_MULTICAST_GROUP_ID_all_hosts);
        
        // add mac & session_index relation
        clib_memcpy_fast(key->mac.bytes, eth_mac, sizeof(key->mac.bytes));
        upf_multicast_add_mem_to_pfcp(b0, key);
    }
    else if (icmp6_rh_v2->icmp.type == ICMP6_multicast_listener_done)
    {
        UPF_STATISTICS_ADD(IP6_IGMP_LISTENER_DONE);
        icmp6_rh_v1 = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip6_header_t);
        // accord multicast address get multicast mac
        ip6_multicast_ethernet_address(eth_mac, IP6_MULTICAST_GROUP_ID_all_hosts);
        
        // del mac & session_index relation
        clib_memcpy_fast(key->mac.bytes, eth_mac, sizeof(key->mac.bytes));
        upf_multicast_del_mem_to_pfcp(b0, key);
    }
    else
    {
        upf_debug("Other icmp6 type! 0x%x", icmp6_rh_v2->icmp.type);
    }
    
}

u32 upf_eth_multicast_address_info_parse(vlib_buffer_t *b0, upf_eth_multicast_key_t *key)
{
    u32 is_v4 = is_v4_packet ((u8 *)(vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset + sizeof(ethernet_header_t)));
    ip4_header_t *ip4_h = NULL;
    upf_icmp6_multicast_listener_report_v2_t *icmp6_rh_v2 = NULL;

    if (is_v4)
    {
        UPF_STATISTICS_ADD(IP4_MULTICAST);
        ip4_h = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset + sizeof(ethernet_header_t);
        if (ip4_h->protocol == IP_PROTOCOL_IGMP)
        {
            UPF_STATISTICS_ADD(IP4_IGMP);
            handle_eth_igmp_snooping(b0, key);
            UPF_STATISTICS_ADD(MULTICAST_ADD_IP4_GROUP);
            return UPF_ETH_MULTICAST_PROTOCOL;
        }
    }
    else
    {
        UPF_STATISTICS_ADD(IP6_MULTICAST);
        icmp6_rh_v2 = vlib_buffer_get_current (b0) + upf_buffer_opaque (b0)->upf.data_offset + sizeof (ip6_header_t);
        if ((icmp6_rh_v2->ext_hdr.next_hdr == 0x3a) && ((icmp6_rh_v2->alert.type == 0x05) && (icmp6_rh_v2->alert.value == 0)))
        {
            UPF_STATISTICS_ADD(IP6_IGMP);
            handle_eth_mld_snooping(b0, key);
            UPF_STATISTICS_ADD(MULTICAST_ADD_IP6_GROUP);
            return UPF_ETH_MULTICAST_PROTOCOL;
        }
    }
    UPF_STATISTICS_ADD(MULTICAST_DATA_PKG);
    return UPF_ETH_MULTICAST_DATA;
}

u32 upf_eth_multicast_get_dl_pdr(vlib_buffer_t *b)
{
	u32 next = UPF_ETH_MULTICAST_NEXT_DROP;
	
	upf_session_t *sess = NULL;
    struct rules *active = NULL;
    upf_pdr_t *pdr0 = NULL;
    upf_far_t *far0 = NULL;

	upf_buffer_opaque (b)->upf.flow_index = (u32)~0; //reset flow_index
	upf_buffer_opaque (b)->upf.pdr_index = (u32)~0;

    sess = sx_get_by_index(upf_buffer_opaque (b)->upf.session_index);
    if (!sess)
    {
        upf_trace ("get session by index fail, session index:%u\n", upf_buffer_opaque (b)->upf.session_index);
        return next;
    }

    active = upf_get_rules (sess, SX_ACTIVE);
	
    if (!active)
    {
        upf_debug ("NULL active");
        return next;
    }
    
    vec_foreach (pdr0, active->pdr)
    {
        if (pdr0->pdi.src_intf != SRC_INTF_CORE)
        {
        	continue;
        }
		vec_foreach (far0, active->far)
        {
            if ((far0->forward.dst_intf == DST_INTF_ACCESS) && (far0->apply_action & F_APPLY_FORW))
            {
				upf_buffer_opaque (b)->upf.pdr_index = pdr0 - active->pdr;
				return UPF_ETH_MULTICAST_NEXT_ETH_PROCESS;
            }
        }

    }
	return next;
}
static uword upf_eth_multicast_node_fn (vlib_main_t *vm, vlib_node_runtime_t *node,
                   vlib_frame_t *from_frame)
{
    u32 next = 0;
    u32 *from = vlib_frame_vector_args (from_frame);
    u32 *to_next = NULL;
    u32 n_left_from = from_frame->n_vectors;
    u32 next_index = node->cached_next_index;
    u32 n_left_to_next;
    upf_main_t *um = &g_upf_main;

#define _(sym, str) u64 CPT_##sym = 0;
    foreach_upf_eth_multicast_error
#undef _

    upf_trace("ethernet multicast, n_left_from:%u", n_left_from);

    while (n_left_from > 0)
    {
        vlib_get_next_frame (vm, node, next_index, to_next, n_left_to_next);

        while ((n_left_from > 0) && (n_left_to_next > 0))
        {
            u32 user_num = 0;
            u32 bi = from[0];
			to_next[0] = from[0];
			from += 1;
            to_next += 1;
            n_left_from -= 1;
            n_left_to_next -= 1;

            vlib_buffer_t *b = vlib_get_buffer (vm, bi);
            u32 pkt_sess_index = upf_buffer_opaque (b)->upf.session_index;
            u32 is_eth_multicast = UPF_ETH_MULTICAST_DATA;

            next = UPF_ETH_MULTICAST_NEXT_DROP;

            /* 0) pause the key */

            upf_eth_multicast_key_t key;
            memset(&key,0,sizeof(upf_eth_multicast_key_t));
            memset(key.pad,0,sizeof(key.pad));

            ethernet_header_t *eth = vlib_buffer_get_current (b);
            if (eth && eth->type == ntohs (ETHERNET_TYPE_VLAN))
            {
                ethernet_vlan_header_t *vlan = (void *) (eth + 1);
                key.c_vid = clib_net_to_host_u16(vlan->priority_cfi_and_id);
                if (vlan->type == ntohs (ETHERNET_TYPE_DOT1AD))
                {
                    ethernet_vlan_header_t *vlan2 = (void *) (vlan + 1);
                    key.s_vid = clib_net_to_host_u16(vlan2->priority_cfi_and_id);
                }
            }
            else
            {
                key.c_vid = UPF_ETH_DEFAULT_VLAN;
                key.s_vid = UPF_ETH_DEFAULT_VLAN;
            }

            key.nwi_idx = upf_buffer_node_ttl(b)->ttl.vn_nwi_idx;

            is_eth_multicast = upf_eth_multicast_address_info_parse(b, &key);
            if (is_eth_multicast == UPF_ETH_MULTICAST_PROTOCOL)
            {
                UPF_STATISTICS_ADD(UPF_ETH_MULTICAST_PROTOCOL_PKT);
                upf_debug("the pkt is eth multicast protocol.");
                goto error;
            }
            clib_memcpy_fast(key.mac.bytes, eth->dst_address, sizeof(key.mac.bytes));

            /* 1) lookup eth_multicas domain members */
            hash_pair_t *hp = hash_get_pair (um->hash_eth_multicast_id_by_key, &key);
            if (!hp)
            {
                upf_debug("Can not find ethernet multicast doamin, nwi_idx:%u, c_vlan:%u, s_vlan:%u, mac:%U!", 
                    key.nwi_idx, key.c_vid, key.s_vid, format_mac_address, key.mac.bytes);
                CPT_DROP++;
                goto error;
            }
            upf_eth_multicast_t *domain_members = (void *)(hp->value[0]);
            if (!domain_members)
            {
                upf_debug("Can not find ethernet multicast doamin, nwi_idx:%u, c_vlan:%u, s_vlan:%u, mac:%U!", 
                    key.nwi_idx, key.c_vid, key.s_vid, format_mac_address, key.mac.bytes);
                CPT_DROP++;
                goto error;
            }

            /* 2) calculate the number of messages to bo copied */
            upf_eth_multicast_get_user_num(pkt_sess_index, domain_members->index, &user_num);
            upf_trace("eth-multicast-domain mem num:%u", user_num);

            if(user_num == 0)
            {
                UPF_STATISTICS_ADD(NO_OTHER_UE_TO_COPY);
                upf_err("NO other valid sess index existed in current domain!");
                goto error;
            }
			
			vlib_validate_buffer_enqueue_x1(vm, node, next_index, to_next, n_left_to_next, bi, next);
			
            u32 *idx = NULL;
            u8 src_intf = upf_buffer_opaque (b)->upf.src_intf;
            vec_foreach (idx, domain_members->index)
            {               
                if (*idx == pkt_sess_index)
                {
                    continue;
                }

                upf_trace("clone multicast data pkt sess index :%u src_intf :%u", *idx, src_intf);

                vlib_buffer_t * c1 = vlib_buffer_copy (vm, b);
                if (c1 == NULL)
                {
                    continue;
                }
                u32 ci1 = vlib_get_buffer_index (vm, c1);


                 upf_buffer_opaque (c1)->upf = upf_buffer_opaque (b)->upf;
                 upf_buffer_opaque (c1)->upf.session_index = *idx;


                if (*idx != UPF_L2_FORDW_PORT_IDX_N6 && src_intf == SRC_INTF_CORE)
                {
                    next = upf_eth_multicast_get_dl_pdr(c1);
					upf_trace("clone multicast pkt pdr index :%u ", upf_buffer_opaque (c1)->upf.pdr_index);
                }
                else
                {
                    next = UPF_ETH_MULTICAST_NEXT_L2_INPUT;
                }

                CPT_COPIES_FORWARD++;
                to_next[0] = ci1;
                to_next += 1;
                n_left_to_next -= 1;

                vlib_validate_buffer_enqueue_x1(vm, node, next_index, to_next, n_left_to_next, ci1, next);

                if (PREDICT_FALSE(0 == n_left_to_next))
                {
                    vlib_put_next_frame (vm, node, next_index, n_left_to_next);
                    vlib_get_next_frame (vm, node, next_index, to_next, n_left_to_next);
                }
            }
            continue; /* next packet */

error:
            //to_next[0] = bi;
            //to_next += 1;
            //n_left_to_next -= 1;
            if (PREDICT_FALSE (b->flags & VLIB_BUFFER_IS_TRACED))
            {
                upf_eth_multicast_trace_t *tr = vlib_add_trace (vm, node, b, sizeof (*tr));
                tr->session_index = ~0;
                tr->cached = 0;
                clib_memcpy (tr->packet_data, vlib_buffer_get_current (b), sizeof (tr->packet_data));
            }
            vlib_validate_buffer_enqueue_x1 (vm, node, next_index, to_next, n_left_to_next, bi, next);

			//continue; /* next packet */

        }
        vlib_put_next_frame (vm, node, next_index, n_left_to_next);
    }

#define _(sym, str) vlib_node_increment_counter (vm, node->node_index, UPF_ETH_MULTICAST_ERROR_##sym, CPT_##sym);
    foreach_upf_eth_multicast_error
#undef _

    return from_frame->n_vectors;

}

u8 *format_eth_multicast_trace (u8 *s, va_list *args)
{
    CLIB_UNUSED (vlib_main_t * vm) = va_arg (*args, vlib_main_t *);
    CLIB_UNUSED (vlib_node_t * node) = va_arg (*args, vlib_node_t *);
    upf_eth_multicast_trace_t *t = va_arg (*args, upf_eth_multicast_trace_t *);
    ip4_header_t *ih4 = (ip4_header_t *)t->packet_data;
    u32 indent = format_get_indent (s);

    s = format (
      s, "sx_index %u, up_seid 0x%016" PRIx64 " pdr_id %u, far_id %u\n%U%U",
      t->session_index, t->up_seid, t->pdr_id, t->far_id,
      format_white_space, indent,
      (ih4->ip_version_and_header_length & 0xF0) == 0x60 ? format_ip6_header
                                                         : format_ip4_header,
      t->packet_data, sizeof (t->packet_data));
    return s;
}

static char *upf_eth_multicast_error_strings[] = {
#define _(sym, string) string,
    foreach_upf_eth_multicast_error
#undef _
};

/* *INDENT-OFF* */
VLIB_REGISTER_NODE (upf_eth_multicast_node) = {
    .function = upf_eth_multicast_node_fn,
    .name = "upf-ethernet-multicast",
    .vector_size = sizeof (u32),
    .format_trace = format_eth_multicast_trace,
    .type = VLIB_NODE_TYPE_INTERNAL,
    .n_errors = UPF_ETH_MULTICAST_N_ERROR,
    .error_strings = upf_eth_multicast_error_strings,
    .n_next_nodes = UPF_ETH_MULTICAST_NEXT_N_NEXT,
    .next_nodes = {
        [UPF_ETH_MULTICAST_NEXT_DROP] = "error-drop",
        [UPF_ETH_MULTICAST_NEXT_L2_INPUT] = "l2-input",
        [UPF_ETH_MULTICAST_NEXT_ETH_INPUT] = "upf-eth-input",
        [UPF_ETH_MULTICAST_NEXT_ETH_PROCESS] = "upf-ethernet-process",
    }};
/* *INDENT-ON* */

uword
unformat_ethernet_multicast_available_flags (unformat_input_t * input, va_list * args)
{
  u32 *result = va_arg (*args, u32 *);
  u32 flags = 0;

  if (unformat (input, "enable"))
    flags = 1;
  else if (unformat (input, "disable"))
    flags = 0;
  else
    return 0;

  *result = flags;
  return 1;
}


static clib_error_t *
upf_ethernet_multicast_command_fn (vlib_main_t *vm, unformat_input_t *input,
                                    vlib_cli_command_t *cmd)
{
    unformat_input_t _line_input, *line_input = &_line_input;
    clib_error_t *error = 0;
    upf_main_t *gtm = &g_upf_main;

    u8 *name = NULL;
    u8 *s;
    u32 nwiId = 0;
    u8 nwi_flag = 0;
    u32 c_vlan = 0;
    u8  c_vlan_flag = 0;
    u32 s_vlan = 0;
    u8  s_vlan_flag = 0;

    u8 mac_address[6] = {0};
    u8 mac_address_flag = 0;

    u8 *session_index = NULL;
    u8 session_index_flag = 0;

    u32 available = 0;
    u8 available_flag = 0; 

    upf_eth_multicast_key_t eth_multicast_key = {0};
    upf_eth_multicast_t eth_multicast = {0};
    char session_buf[512] = {0};
    u8 session_idx = 0;
    
    /* Get a line of input. */
    if (unformat_user (input, unformat_line_input, line_input))
    {
        while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
        {
            
            if (unformat (line_input, "nwi %_%v%_", &s))
            {
                name = iupf_name_to_labels (s);
                nwi_flag = 1;
                vec_free (s);
            }
            else if (unformat (line_input, "c_vlan %u", &c_vlan))
            {
                c_vlan_flag = 1;
            }
            else if (unformat (line_input, "s_vlan %u", &s_vlan))
            {
                s_vlan_flag = 1;
            }
            else if (unformat (line_input, "mac_address %U", unformat_ethernet_address, mac_address))
            {
                mac_address_flag = 1;
            }
            else if (unformat (line_input, "session_index %s", &session_index))
            {
                session_index_flag = 1;
            }
            else if (unformat (line_input, "available %U", unformat_ethernet_multicast_available_flags,&available))
            {
                available_flag = 1;
            }
            else
            {
              error = unformat_parse_error (line_input);
              goto done;
            }
        }
    }
    else
    {
        return clib_error_return (0, "[err] need input paramter!");
    }
    
    if (nwi_flag != 1) 
    {
        error = clib_error_return (0, "[err] need nwi!");
        goto done;
    }

    if (mac_address_flag != 1) 
    {
        error = clib_error_return (0, "[err] need mac address!");
        goto done;
    }
    
    if (session_index_flag != 1) 
    {
        error = clib_error_return (0, "[err] need session index!");
        goto done;
    }

    if (available_flag != 1) 
    {
        error = clib_error_return (0, "[err] need available");
        goto done;
    }

    upf_nwi_t *nwi = upf_lookup_nwi (name);
    if(nwi == NULL)
    {
        error = clib_error_return (0, "[err] nwi not existed!");
        goto done;
    }
    nwiId = nwi - gtm->nwis;
    eth_multicast_key.nwi_idx = nwiId;
    eth_multicast_key.c_vid = c_vlan;
    eth_multicast_key.s_vid = s_vlan;
    clib_memcpy (eth_multicast_key.mac.bytes, mac_address,sizeof (mac_address_t));

    eth_multicast.type = L2_FORW_STATIC;
    strcpy(session_buf,(char *)session_index);
    char *p;
    char *token;

    p = session_buf;

    while(*p==' ')
        p++;
    token = strchr(p,',');
    while(token != NULL)
    {
        *token = '\0';
        session_idx = atoi(p);
        vec_add1(eth_multicast.index, session_idx);
        p = token;
        p++;
        token = strchr(p,',');
    }

    session_idx = atoi(p);
    vec_add1(eth_multicast.index, session_idx);

    eth_multicast.available = available;
    
    eth_multicast.key = eth_multicast_key;
    upf_add_member_to_multicast_table(&eth_multicast);

done:
  //vec_free (trafficRuleId);
  unformat_free (line_input);
  return error;
}



/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_ethernet_multicast_command, static) = {
    .path = "upf ethernet multicast",
    .short_help = "upf ethernet multicast nwi <nwi> [c_vlan <c_vlan>] [s_vlan <s_vlan>] mac_address <mac_address> session_index <session_index,session_index...> available <enable|disable>",
    .function = upf_ethernet_multicast_command_fn,
};
/* *INDENT-ON* */


static clib_error_t *
upf_del_ethernet_multicast_command_fn (vlib_main_t *vm, unformat_input_t *input,
                                    vlib_cli_command_t *cmd)
{
    unformat_input_t _line_input, *line_input = &_line_input;
    clib_error_t *error = 0;
    upf_main_t *gtm = &g_upf_main;

    u8 *name = NULL;
    u8 *s;
    u32 nwiId = 0;
    u8 nwi_flag = 0;
    u32 c_vlan = 0;
    u8  c_vlan_flag = 0;
    u32 s_vlan = 0;
    u8  s_vlan_flag = 0;

    u8 mac_address[6] = {0};
    u8 mac_address_flag = 0;

    upf_eth_multicast_key_t eth_multicast_key = {0};

    /* Get a line of input. */
    if (unformat_user (input, unformat_line_input, line_input))
    {
        while (unformat_check_input (line_input) != UNFORMAT_END_OF_INPUT)
        {
            
            if (unformat (line_input, "nwi %_%v%_", &s))
            {
                name = iupf_name_to_labels (s);
                nwi_flag = 1;
                vec_free (s);
            }
            else if (unformat (line_input, "c_vlan %u", &c_vlan))
            {
                c_vlan_flag = 1;
            }
            else if (unformat (line_input, "s_vlan %u", &s_vlan))
            {
                s_vlan_flag = 1;
            }
            else if (unformat (line_input, "mac_address %U", unformat_ethernet_address, mac_address))
            {
                mac_address_flag = 1;
            }
            else
            {
              error = unformat_parse_error (line_input);
              goto done;
            }
        }
    }
    else
    {
        return clib_error_return (0, "[err] need input paramter!");
    }
    
    if (nwi_flag != 1) 
    {
        error = clib_error_return (0, "[err] need nwi!");
        goto done;
    }

    if (mac_address_flag != 1) 
    {
        error = clib_error_return (0, "[err] need mac address!");
        goto done;
    }

    upf_nwi_t *nwi = upf_lookup_nwi (name);
    if(nwi == NULL)
    {
        error = clib_error_return (0, "[err] nwi not existed!");
        goto done;
    }
    nwiId = nwi - gtm->nwis;
    eth_multicast_key.nwi_idx = nwiId;
    eth_multicast_key.c_vid = c_vlan;
    eth_multicast_key.s_vid = s_vlan;
    clib_memcpy (eth_multicast_key.mac.bytes, mac_address,sizeof (mac_address_t));

    upf_del_eth_multicast_domain(&eth_multicast_key);

done:
  //vec_free (trafficRuleId);
  unformat_free (line_input);
  return error;
}



/* *INDENT-OFF* */
VLIB_CLI_COMMAND (upf_del_ethernet_multicast_command, static) = {
    .path = "upf del ethernet multicast",
    .short_help = "upf del ethernet multicast nwi <nwi> [c_vlan <c_vlan>] [s_vlan <s_vlan>] mac_address <mac_address>",
    .function = upf_del_ethernet_multicast_command_fn,
};
/* *INDENT-ON* */


/*
 * fd.io coding-style-patch-verification: ON
 *
 * Local Variables:
 * eval: (c-set-style "gnu")
 * End:
 */
 
